/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/papaparse";
exports.ids = ["vendor-chunks/papaparse"];
exports.modules = {

/***/ "(ssr)/./node_modules/papaparse/papaparse.js":
/*!*********************************************!*\
  !*** ./node_modules/papaparse/papaparse.js ***!
  \*********************************************/
/***/ (function(module, exports, __webpack_require__) {

eval("var __WEBPACK_AMD_DEFINE_FACTORY__, __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_AMD_DEFINE_RESULT__;/* @license\nPapa Parse\nv5.5.2\nhttps://github.com/mholt/PapaParse\nLicense: MIT\n*/\n\n(function(root, factory)\n{\n\t/* globals define */\n\tif (true)\n\t{\n\t\t// AMD. Register as an anonymous module.\n\t\t!(__WEBPACK_AMD_DEFINE_ARRAY__ = [], __WEBPACK_AMD_DEFINE_FACTORY__ = (factory),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ = (typeof __WEBPACK_AMD_DEFINE_FACTORY__ === 'function' ?\n\t\t(__WEBPACK_AMD_DEFINE_FACTORY__.apply(exports, __WEBPACK_AMD_DEFINE_ARRAY__)) : __WEBPACK_AMD_DEFINE_FACTORY__),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n\t}\n\telse {}\n\t// in strict mode we cannot access arguments.callee, so we need a named reference to\n\t// stringify the factory method for the blob worker\n\t// eslint-disable-next-line func-name\n}(this, function moduleFactory()\n{\n\t'use strict';\n\n\tvar global = (function() {\n\t\t// alternative method, similar to `Function('return this')()`\n\t\t// but without using `eval` (which is disabled when\n\t\t// using Content Security Policy).\n\n\t\tif (typeof self !== 'undefined') { return self; }\n\t\tif (typeof window !== 'undefined') { return window; }\n\t\tif (typeof global !== 'undefined') { return global; }\n\n\t\t// When running tests none of the above have been defined\n\t\treturn {};\n\t})();\n\n\n\tfunction getWorkerBlob() {\n\t\tvar URL = global.URL || global.webkitURL || null;\n\t\tvar code = moduleFactory.toString();\n\t\treturn Papa.BLOB_URL || (Papa.BLOB_URL = URL.createObjectURL(new Blob([\"var global = (function() { if (typeof self !== 'undefined') { return self; } if (typeof window !== 'undefined') { return window; } if (typeof global !== 'undefined') { return global; } return {}; })(); global.IS_PAPA_WORKER=true; \", '(', code, ')();'], {type: 'text/javascript'})));\n\t}\n\n\tvar IS_WORKER = !global.document && !!global.postMessage,\n\t\tIS_PAPA_WORKER = global.IS_PAPA_WORKER || false;\n\n\tvar workers = {}, workerIdCounter = 0;\n\n\tvar Papa = {};\n\n\tPapa.parse = CsvToJson;\n\tPapa.unparse = JsonToCsv;\n\n\tPapa.RECORD_SEP = String.fromCharCode(30);\n\tPapa.UNIT_SEP = String.fromCharCode(31);\n\tPapa.BYTE_ORDER_MARK = '\\ufeff';\n\tPapa.BAD_DELIMITERS = ['\\r', '\\n', '\"', Papa.BYTE_ORDER_MARK];\n\tPapa.WORKERS_SUPPORTED = !IS_WORKER && !!global.Worker;\n\tPapa.NODE_STREAM_INPUT = 1;\n\n\t// Configurable chunk sizes for local and remote files, respectively\n\tPapa.LocalChunkSize = 1024 * 1024 * 10;\t// 10 MB\n\tPapa.RemoteChunkSize = 1024 * 1024 * 5;\t// 5 MB\n\tPapa.DefaultDelimiter = ',';\t\t\t// Used if not specified and detection fails\n\n\t// Exposed for testing and development only\n\tPapa.Parser = Parser;\n\tPapa.ParserHandle = ParserHandle;\n\tPapa.NetworkStreamer = NetworkStreamer;\n\tPapa.FileStreamer = FileStreamer;\n\tPapa.StringStreamer = StringStreamer;\n\tPapa.ReadableStreamStreamer = ReadableStreamStreamer;\n\tif (typeof PAPA_BROWSER_CONTEXT === 'undefined') {\n\t\tPapa.DuplexStreamStreamer = DuplexStreamStreamer;\n\t}\n\n\tif (global.jQuery)\n\t{\n\t\tvar $ = global.jQuery;\n\t\t$.fn.parse = function(options)\n\t\t{\n\t\t\tvar config = options.config || {};\n\t\t\tvar queue = [];\n\n\t\t\tthis.each(function(idx)\n\t\t\t{\n\t\t\t\tvar supported = $(this).prop('tagName').toUpperCase() === 'INPUT'\n\t\t\t\t\t\t\t\t&& $(this).attr('type').toLowerCase() === 'file'\n\t\t\t\t\t\t\t\t&& global.FileReader;\n\n\t\t\t\tif (!supported || !this.files || this.files.length === 0)\n\t\t\t\t\treturn true;\t// continue to next input element\n\n\t\t\t\tfor (var i = 0; i < this.files.length; i++)\n\t\t\t\t{\n\t\t\t\t\tqueue.push({\n\t\t\t\t\t\tfile: this.files[i],\n\t\t\t\t\t\tinputElem: this,\n\t\t\t\t\t\tinstanceConfig: $.extend({}, config)\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n\n\t\t\tparseNextFile();\t// begin parsing\n\t\t\treturn this;\t\t// maintains chainability\n\n\n\t\t\tfunction parseNextFile()\n\t\t\t{\n\t\t\t\tif (queue.length === 0)\n\t\t\t\t{\n\t\t\t\t\tif (isFunction(options.complete))\n\t\t\t\t\t\toptions.complete();\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tvar f = queue[0];\n\n\t\t\t\tif (isFunction(options.before))\n\t\t\t\t{\n\t\t\t\t\tvar returned = options.before(f.file, f.inputElem);\n\n\t\t\t\t\tif (typeof returned === 'object')\n\t\t\t\t\t{\n\t\t\t\t\t\tif (returned.action === 'abort')\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\terror('AbortError', f.file, f.inputElem, returned.reason);\n\t\t\t\t\t\t\treturn;\t// Aborts all queued files immediately\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse if (returned.action === 'skip')\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tfileComplete();\t// parse the next file in the queue, if any\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse if (typeof returned.config === 'object')\n\t\t\t\t\t\t\tf.instanceConfig = $.extend(f.instanceConfig, returned.config);\n\t\t\t\t\t}\n\t\t\t\t\telse if (returned === 'skip')\n\t\t\t\t\t{\n\t\t\t\t\t\tfileComplete();\t// parse the next file in the queue, if any\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// Wrap up the user's complete callback, if any, so that ours also gets executed\n\t\t\t\tvar userCompleteFunc = f.instanceConfig.complete;\n\t\t\t\tf.instanceConfig.complete = function(results)\n\t\t\t\t{\n\t\t\t\t\tif (isFunction(userCompleteFunc))\n\t\t\t\t\t\tuserCompleteFunc(results, f.file, f.inputElem);\n\t\t\t\t\tfileComplete();\n\t\t\t\t};\n\n\t\t\t\tPapa.parse(f.file, f.instanceConfig);\n\t\t\t}\n\n\t\t\tfunction error(name, file, elem, reason)\n\t\t\t{\n\t\t\t\tif (isFunction(options.error))\n\t\t\t\t\toptions.error({name: name}, file, elem, reason);\n\t\t\t}\n\n\t\t\tfunction fileComplete()\n\t\t\t{\n\t\t\t\tqueue.splice(0, 1);\n\t\t\t\tparseNextFile();\n\t\t\t}\n\t\t};\n\t}\n\n\n\tif (IS_PAPA_WORKER)\n\t{\n\t\tglobal.onmessage = workerThreadReceivedMessage;\n\t}\n\n\n\n\n\tfunction CsvToJson(_input, _config)\n\t{\n\t\t_config = _config || {};\n\t\tvar dynamicTyping = _config.dynamicTyping || false;\n\t\tif (isFunction(dynamicTyping)) {\n\t\t\t_config.dynamicTypingFunction = dynamicTyping;\n\t\t\t// Will be filled on first row call\n\t\t\tdynamicTyping = {};\n\t\t}\n\t\t_config.dynamicTyping = dynamicTyping;\n\n\t\t_config.transform = isFunction(_config.transform) ? _config.transform : false;\n\n\t\tif (_config.worker && Papa.WORKERS_SUPPORTED)\n\t\t{\n\t\t\tvar w = newWorker();\n\n\t\t\tw.userStep = _config.step;\n\t\t\tw.userChunk = _config.chunk;\n\t\t\tw.userComplete = _config.complete;\n\t\t\tw.userError = _config.error;\n\n\t\t\t_config.step = isFunction(_config.step);\n\t\t\t_config.chunk = isFunction(_config.chunk);\n\t\t\t_config.complete = isFunction(_config.complete);\n\t\t\t_config.error = isFunction(_config.error);\n\t\t\tdelete _config.worker;\t// prevent infinite loop\n\n\t\t\tw.postMessage({\n\t\t\t\tinput: _input,\n\t\t\t\tconfig: _config,\n\t\t\t\tworkerId: w.id\n\t\t\t});\n\n\t\t\treturn;\n\t\t}\n\n\t\tvar streamer = null;\n\t\tif (_input === Papa.NODE_STREAM_INPUT && typeof PAPA_BROWSER_CONTEXT === 'undefined')\n\t\t{\n\t\t\t// create a node Duplex stream for use\n\t\t\t// with .pipe\n\t\t\tstreamer = new DuplexStreamStreamer(_config);\n\t\t\treturn streamer.getStream();\n\t\t}\n\t\telse if (typeof _input === 'string')\n\t\t{\n\t\t\t_input = stripBom(_input);\n\t\t\tif (_config.download)\n\t\t\t\tstreamer = new NetworkStreamer(_config);\n\t\t\telse\n\t\t\t\tstreamer = new StringStreamer(_config);\n\t\t}\n\t\telse if (_input.readable === true && isFunction(_input.read) && isFunction(_input.on))\n\t\t{\n\t\t\tstreamer = new ReadableStreamStreamer(_config);\n\t\t}\n\t\telse if ((global.File && _input instanceof File) || _input instanceof Object)\t// ...Safari. (see issue #106)\n\t\t\tstreamer = new FileStreamer(_config);\n\n\t\treturn streamer.stream(_input);\n\n\t\t// Strip character from UTF-8 BOM encoded files that cause issue parsing the file\n\t\tfunction stripBom(string) {\n\t\t\tif (string.charCodeAt(0) === 0xfeff) {\n\t\t\t\treturn string.slice(1);\n\t\t\t}\n\t\t\treturn string;\n\t\t}\n\t}\n\n\n\n\n\n\n\tfunction JsonToCsv(_input, _config)\n\t{\n\t\t// Default configuration\n\n\t\t/** whether to surround every datum with quotes */\n\t\tvar _quotes = false;\n\n\t\t/** whether to write headers */\n\t\tvar _writeHeader = true;\n\n\t\t/** delimiting character(s) */\n\t\tvar _delimiter = ',';\n\n\t\t/** newline character(s) */\n\t\tvar _newline = '\\r\\n';\n\n\t\t/** quote character */\n\t\tvar _quoteChar = '\"';\n\n\t\t/** escaped quote character, either \"\" or <config.escapeChar>\" */\n\t\tvar _escapedQuote = _quoteChar + _quoteChar;\n\n\t\t/** whether to skip empty lines */\n\t\tvar _skipEmptyLines = false;\n\n\t\t/** the columns (keys) we expect when we unparse objects */\n\t\tvar _columns = null;\n\n\t\t/** whether to prevent outputting cells that can be parsed as formulae by spreadsheet software (Excel and LibreOffice) */\n\t\tvar _escapeFormulae = false;\n\n\t\tunpackConfig();\n\n\t\tvar quoteCharRegex = new RegExp(escapeRegExp(_quoteChar), 'g');\n\n\t\tif (typeof _input === 'string')\n\t\t\t_input = JSON.parse(_input);\n\n\t\tif (Array.isArray(_input))\n\t\t{\n\t\t\tif (!_input.length || Array.isArray(_input[0]))\n\t\t\t\treturn serialize(null, _input, _skipEmptyLines);\n\t\t\telse if (typeof _input[0] === 'object')\n\t\t\t\treturn serialize(_columns || Object.keys(_input[0]), _input, _skipEmptyLines);\n\t\t}\n\t\telse if (typeof _input === 'object')\n\t\t{\n\t\t\tif (typeof _input.data === 'string')\n\t\t\t\t_input.data = JSON.parse(_input.data);\n\n\t\t\tif (Array.isArray(_input.data))\n\t\t\t{\n\t\t\t\tif (!_input.fields)\n\t\t\t\t\t_input.fields = _input.meta && _input.meta.fields || _columns;\n\n\t\t\t\tif (!_input.fields)\n\t\t\t\t\t_input.fields =  Array.isArray(_input.data[0])\n\t\t\t\t\t\t? _input.fields\n\t\t\t\t\t\t: typeof _input.data[0] === 'object'\n\t\t\t\t\t\t\t? Object.keys(_input.data[0])\n\t\t\t\t\t\t\t: [];\n\n\t\t\t\tif (!(Array.isArray(_input.data[0])) && typeof _input.data[0] !== 'object')\n\t\t\t\t\t_input.data = [_input.data];\t// handles input like [1,2,3] or ['asdf']\n\t\t\t}\n\n\t\t\treturn serialize(_input.fields || [], _input.data || [], _skipEmptyLines);\n\t\t}\n\n\t\t// Default (any valid paths should return before this)\n\t\tthrow new Error('Unable to serialize unrecognized input');\n\n\n\t\tfunction unpackConfig()\n\t\t{\n\t\t\tif (typeof _config !== 'object')\n\t\t\t\treturn;\n\n\t\t\tif (typeof _config.delimiter === 'string'\n                && !Papa.BAD_DELIMITERS.filter(function(value) { return _config.delimiter.indexOf(value) !== -1; }).length)\n\t\t\t{\n\t\t\t\t_delimiter = _config.delimiter;\n\t\t\t}\n\n\t\t\tif (typeof _config.quotes === 'boolean'\n\t\t\t\t|| typeof _config.quotes === 'function'\n\t\t\t\t|| Array.isArray(_config.quotes))\n\t\t\t\t_quotes = _config.quotes;\n\n\t\t\tif (typeof _config.skipEmptyLines === 'boolean'\n\t\t\t\t|| typeof _config.skipEmptyLines === 'string')\n\t\t\t\t_skipEmptyLines = _config.skipEmptyLines;\n\n\t\t\tif (typeof _config.newline === 'string')\n\t\t\t\t_newline = _config.newline;\n\n\t\t\tif (typeof _config.quoteChar === 'string')\n\t\t\t\t_quoteChar = _config.quoteChar;\n\n\t\t\tif (typeof _config.header === 'boolean')\n\t\t\t\t_writeHeader = _config.header;\n\n\t\t\tif (Array.isArray(_config.columns)) {\n\n\t\t\t\tif (_config.columns.length === 0) throw new Error('Option columns is empty');\n\n\t\t\t\t_columns = _config.columns;\n\t\t\t}\n\n\t\t\tif (_config.escapeChar !== undefined) {\n\t\t\t\t_escapedQuote = _config.escapeChar + _quoteChar;\n\t\t\t}\n\n\t\t\tif (_config.escapeFormulae instanceof RegExp) {\n\t\t\t\t_escapeFormulae = _config.escapeFormulae;\n\t\t\t} else if (typeof _config.escapeFormulae === 'boolean' && _config.escapeFormulae) {\n\t\t\t\t_escapeFormulae =  /^[=+\\-@\\t\\r].*$/;\n\t\t\t}\n\t\t}\n\n\t\t/** The double for loop that iterates the data and writes out a CSV string including header row */\n\t\tfunction serialize(fields, data, skipEmptyLines)\n\t\t{\n\t\t\tvar csv = '';\n\n\t\t\tif (typeof fields === 'string')\n\t\t\t\tfields = JSON.parse(fields);\n\t\t\tif (typeof data === 'string')\n\t\t\t\tdata = JSON.parse(data);\n\n\t\t\tvar hasHeader = Array.isArray(fields) && fields.length > 0;\n\t\t\tvar dataKeyedByField = !(Array.isArray(data[0]));\n\n\t\t\t// If there a header row, write it first\n\t\t\tif (hasHeader && _writeHeader)\n\t\t\t{\n\t\t\t\tfor (var i = 0; i < fields.length; i++)\n\t\t\t\t{\n\t\t\t\t\tif (i > 0)\n\t\t\t\t\t\tcsv += _delimiter;\n\t\t\t\t\tcsv += safe(fields[i], i);\n\t\t\t\t}\n\t\t\t\tif (data.length > 0)\n\t\t\t\t\tcsv += _newline;\n\t\t\t}\n\n\t\t\t// Then write out the data\n\t\t\tfor (var row = 0; row < data.length; row++)\n\t\t\t{\n\t\t\t\tvar maxCol = hasHeader ? fields.length : data[row].length;\n\n\t\t\t\tvar emptyLine = false;\n\t\t\t\tvar nullLine = hasHeader ? Object.keys(data[row]).length === 0 : data[row].length === 0;\n\t\t\t\tif (skipEmptyLines && !hasHeader)\n\t\t\t\t{\n\t\t\t\t\temptyLine = skipEmptyLines === 'greedy' ? data[row].join('').trim() === '' : data[row].length === 1 && data[row][0].length === 0;\n\t\t\t\t}\n\t\t\t\tif (skipEmptyLines === 'greedy' && hasHeader) {\n\t\t\t\t\tvar line = [];\n\t\t\t\t\tfor (var c = 0; c < maxCol; c++) {\n\t\t\t\t\t\tvar cx = dataKeyedByField ? fields[c] : c;\n\t\t\t\t\t\tline.push(data[row][cx]);\n\t\t\t\t\t}\n\t\t\t\t\temptyLine = line.join('').trim() === '';\n\t\t\t\t}\n\t\t\t\tif (!emptyLine)\n\t\t\t\t{\n\t\t\t\t\tfor (var col = 0; col < maxCol; col++)\n\t\t\t\t\t{\n\t\t\t\t\t\tif (col > 0 && !nullLine)\n\t\t\t\t\t\t\tcsv += _delimiter;\n\t\t\t\t\t\tvar colIdx = hasHeader && dataKeyedByField ? fields[col] : col;\n\t\t\t\t\t\tcsv += safe(data[row][colIdx], col);\n\t\t\t\t\t}\n\t\t\t\t\tif (row < data.length - 1 && (!skipEmptyLines || (maxCol > 0 && !nullLine)))\n\t\t\t\t\t{\n\t\t\t\t\t\tcsv += _newline;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn csv;\n\t\t}\n\n\t\t/** Encloses a value around quotes if needed (makes a value safe for CSV insertion) */\n\t\tfunction safe(str, col)\n\t\t{\n\t\t\tif (typeof str === 'undefined' || str === null)\n\t\t\t\treturn '';\n\n\t\t\tif (str.constructor === Date)\n\t\t\t\treturn JSON.stringify(str).slice(1, 25);\n\n\t\t\tvar needsQuotes = false;\n\n\t\t\tif (_escapeFormulae && typeof str === \"string\" && _escapeFormulae.test(str)) {\n\t\t\t\tstr = \"'\" + str;\n\t\t\t\tneedsQuotes = true;\n\t\t\t}\n\n\t\t\tvar escapedQuoteStr = str.toString().replace(quoteCharRegex, _escapedQuote);\n\n\t\t\tneedsQuotes = needsQuotes\n\t\t\t\t\t\t\t|| _quotes === true\n\t\t\t\t\t\t\t|| (typeof _quotes === 'function' && _quotes(str, col))\n\t\t\t\t\t\t\t|| (Array.isArray(_quotes) && _quotes[col])\n\t\t\t\t\t\t\t|| hasAny(escapedQuoteStr, Papa.BAD_DELIMITERS)\n\t\t\t\t\t\t\t|| escapedQuoteStr.indexOf(_delimiter) > -1\n\t\t\t\t\t\t\t|| escapedQuoteStr.charAt(0) === ' '\n\t\t\t\t\t\t\t|| escapedQuoteStr.charAt(escapedQuoteStr.length - 1) === ' ';\n\n\t\t\treturn needsQuotes ? _quoteChar + escapedQuoteStr + _quoteChar : escapedQuoteStr;\n\t\t}\n\n\t\tfunction hasAny(str, substrings)\n\t\t{\n\t\t\tfor (var i = 0; i < substrings.length; i++)\n\t\t\t\tif (str.indexOf(substrings[i]) > -1)\n\t\t\t\t\treturn true;\n\t\t\treturn false;\n\t\t}\n\t}\n\n\n\t/** ChunkStreamer is the base prototype for various streamer implementations. */\n\tfunction ChunkStreamer(config)\n\t{\n\t\tthis._handle = null;\n\t\tthis._finished = false;\n\t\tthis._completed = false;\n\t\tthis._halted = false;\n\t\tthis._input = null;\n\t\tthis._baseIndex = 0;\n\t\tthis._partialLine = '';\n\t\tthis._rowCount = 0;\n\t\tthis._start = 0;\n\t\tthis._nextChunk = null;\n\t\tthis.isFirstChunk = true;\n\t\tthis._completeResults = {\n\t\t\tdata: [],\n\t\t\terrors: [],\n\t\t\tmeta: {}\n\t\t};\n\t\treplaceConfig.call(this, config);\n\n\t\tthis.parseChunk = function(chunk, isFakeChunk)\n\t\t{\n\t\t\t// First chunk pre-processing\n\t\t\tconst skipFirstNLines = parseInt(this._config.skipFirstNLines) || 0;\n\t\t\tif (this.isFirstChunk && skipFirstNLines > 0) {\n\t\t\t\tlet _newline = this._config.newline;\n\t\t\t\tif (!_newline) {\n\t\t\t\t\tconst quoteChar = this._config.quoteChar || '\"';\n\t\t\t\t\t_newline = this._handle.guessLineEndings(chunk, quoteChar);\n\t\t\t\t}\n\t\t\t\tconst splitChunk = chunk.split(_newline);\n\t\t\t\tchunk = [...splitChunk.slice(skipFirstNLines)].join(_newline);\n\t\t\t}\n\t\t\tif (this.isFirstChunk && isFunction(this._config.beforeFirstChunk))\n\t\t\t{\n\t\t\t\tvar modifiedChunk = this._config.beforeFirstChunk(chunk);\n\t\t\t\tif (modifiedChunk !== undefined)\n\t\t\t\t\tchunk = modifiedChunk;\n\t\t\t}\n\t\t\tthis.isFirstChunk = false;\n\t\t\tthis._halted = false;\n\n\t\t\t// Rejoin the line we likely just split in two by chunking the file\n\t\t\tvar aggregate = this._partialLine + chunk;\n\t\t\tthis._partialLine = '';\n\t\t\tvar results = this._handle.parse(aggregate, this._baseIndex, !this._finished);\n\n\t\t\tif (this._handle.paused() || this._handle.aborted()) {\n\t\t\t\tthis._halted = true;\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tvar lastIndex = results.meta.cursor;\n\n\t\t\tif (!this._finished)\n\t\t\t{\n\t\t\t\tthis._partialLine = aggregate.substring(lastIndex - this._baseIndex);\n\t\t\t\tthis._baseIndex = lastIndex;\n\t\t\t}\n\n\t\t\tif (results && results.data)\n\t\t\t\tthis._rowCount += results.data.length;\n\n\t\t\tvar finishedIncludingPreview = this._finished || (this._config.preview && this._rowCount >= this._config.preview);\n\n\t\t\tif (IS_PAPA_WORKER)\n\t\t\t{\n\t\t\t\tglobal.postMessage({\n\t\t\t\t\tresults: results,\n\t\t\t\t\tworkerId: Papa.WORKER_ID,\n\t\t\t\t\tfinished: finishedIncludingPreview\n\t\t\t\t});\n\t\t\t}\n\t\t\telse if (isFunction(this._config.chunk) && !isFakeChunk)\n\t\t\t{\n\t\t\t\tthis._config.chunk(results, this._handle);\n\t\t\t\tif (this._handle.paused() || this._handle.aborted()) {\n\t\t\t\t\tthis._halted = true;\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tresults = undefined;\n\t\t\t\tthis._completeResults = undefined;\n\t\t\t}\n\n\t\t\tif (!this._config.step && !this._config.chunk) {\n\t\t\t\tthis._completeResults.data = this._completeResults.data.concat(results.data);\n\t\t\t\tthis._completeResults.errors = this._completeResults.errors.concat(results.errors);\n\t\t\t\tthis._completeResults.meta = results.meta;\n\t\t\t}\n\n\t\t\tif (!this._completed && finishedIncludingPreview && isFunction(this._config.complete) && (!results || !results.meta.aborted)) {\n\t\t\t\tthis._config.complete(this._completeResults, this._input);\n\t\t\t\tthis._completed = true;\n\t\t\t}\n\n\t\t\tif (!finishedIncludingPreview && (!results || !results.meta.paused))\n\t\t\t\tthis._nextChunk();\n\n\t\t\treturn results;\n\t\t};\n\n\t\tthis._sendError = function(error)\n\t\t{\n\t\t\tif (isFunction(this._config.error))\n\t\t\t\tthis._config.error(error);\n\t\t\telse if (IS_PAPA_WORKER && this._config.error)\n\t\t\t{\n\t\t\t\tglobal.postMessage({\n\t\t\t\t\tworkerId: Papa.WORKER_ID,\n\t\t\t\t\terror: error,\n\t\t\t\t\tfinished: false\n\t\t\t\t});\n\t\t\t}\n\t\t};\n\n\t\tfunction replaceConfig(config)\n\t\t{\n\t\t\t// Deep-copy the config so we can edit it\n\t\t\tvar configCopy = copy(config);\n\t\t\tconfigCopy.chunkSize = parseInt(configCopy.chunkSize);\t// parseInt VERY important so we don't concatenate strings!\n\t\t\tif (!config.step && !config.chunk)\n\t\t\t\tconfigCopy.chunkSize = null;  // disable Range header if not streaming; bad values break IIS - see issue #196\n\t\t\tthis._handle = new ParserHandle(configCopy);\n\t\t\tthis._handle.streamer = this;\n\t\t\tthis._config = configCopy;\t// persist the copy to the caller\n\t\t}\n\t}\n\n\n\tfunction NetworkStreamer(config)\n\t{\n\t\tconfig = config || {};\n\t\tif (!config.chunkSize)\n\t\t\tconfig.chunkSize = Papa.RemoteChunkSize;\n\t\tChunkStreamer.call(this, config);\n\n\t\tvar xhr;\n\n\t\tif (IS_WORKER)\n\t\t{\n\t\t\tthis._nextChunk = function()\n\t\t\t{\n\t\t\t\tthis._readChunk();\n\t\t\t\tthis._chunkLoaded();\n\t\t\t};\n\t\t}\n\t\telse\n\t\t{\n\t\t\tthis._nextChunk = function()\n\t\t\t{\n\t\t\t\tthis._readChunk();\n\t\t\t};\n\t\t}\n\n\t\tthis.stream = function(url)\n\t\t{\n\t\t\tthis._input = url;\n\t\t\tthis._nextChunk();\t// Starts streaming\n\t\t};\n\n\t\tthis._readChunk = function()\n\t\t{\n\t\t\tif (this._finished)\n\t\t\t{\n\t\t\t\tthis._chunkLoaded();\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\txhr = new XMLHttpRequest();\n\n\t\t\tif (this._config.withCredentials)\n\t\t\t{\n\t\t\t\txhr.withCredentials = this._config.withCredentials;\n\t\t\t}\n\n\t\t\tif (!IS_WORKER)\n\t\t\t{\n\t\t\t\txhr.onload = bindFunction(this._chunkLoaded, this);\n\t\t\t\txhr.onerror = bindFunction(this._chunkError, this);\n\t\t\t}\n\n\t\t\txhr.open(this._config.downloadRequestBody ? 'POST' : 'GET', this._input, !IS_WORKER);\n\t\t\t// Headers can only be set when once the request state is OPENED\n\t\t\tif (this._config.downloadRequestHeaders)\n\t\t\t{\n\t\t\t\tvar headers = this._config.downloadRequestHeaders;\n\n\t\t\t\tfor (var headerName in headers)\n\t\t\t\t{\n\t\t\t\t\txhr.setRequestHeader(headerName, headers[headerName]);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (this._config.chunkSize)\n\t\t\t{\n\t\t\t\tvar end = this._start + this._config.chunkSize - 1;\t// minus one because byte range is inclusive\n\t\t\t\txhr.setRequestHeader('Range', 'bytes=' + this._start + '-' + end);\n\t\t\t}\n\n\t\t\ttry {\n\t\t\t\txhr.send(this._config.downloadRequestBody);\n\t\t\t}\n\t\t\tcatch (err) {\n\t\t\t\tthis._chunkError(err.message);\n\t\t\t}\n\n\t\t\tif (IS_WORKER && xhr.status === 0)\n\t\t\t\tthis._chunkError();\n\t\t};\n\n\t\tthis._chunkLoaded = function()\n\t\t{\n\t\t\tif (xhr.readyState !== 4)\n\t\t\t\treturn;\n\n\t\t\tif (xhr.status < 200 || xhr.status >= 400)\n\t\t\t{\n\t\t\t\tthis._chunkError();\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// Use chunckSize as it may be a diference on reponse lentgh due to characters with more than 1 byte\n\t\t\tthis._start += this._config.chunkSize ? this._config.chunkSize : xhr.responseText.length;\n\t\t\tthis._finished = !this._config.chunkSize || this._start >= getFileSize(xhr);\n\t\t\tthis.parseChunk(xhr.responseText);\n\t\t};\n\n\t\tthis._chunkError = function(errorMessage)\n\t\t{\n\t\t\tvar errorText = xhr.statusText || errorMessage;\n\t\t\tthis._sendError(new Error(errorText));\n\t\t};\n\n\t\tfunction getFileSize(xhr)\n\t\t{\n\t\t\tvar contentRange = xhr.getResponseHeader('Content-Range');\n\t\t\tif (contentRange === null) { // no content range, then finish!\n\t\t\t\treturn -1;\n\t\t\t}\n\t\t\treturn parseInt(contentRange.substring(contentRange.lastIndexOf('/') + 1));\n\t\t}\n\t}\n\tNetworkStreamer.prototype = Object.create(ChunkStreamer.prototype);\n\tNetworkStreamer.prototype.constructor = NetworkStreamer;\n\n\n\tfunction FileStreamer(config)\n\t{\n\t\tconfig = config || {};\n\t\tif (!config.chunkSize)\n\t\t\tconfig.chunkSize = Papa.LocalChunkSize;\n\t\tChunkStreamer.call(this, config);\n\n\t\tvar reader, slice;\n\n\t\t// FileReader is better than FileReaderSync (even in worker) - see http://stackoverflow.com/q/24708649/1048862\n\t\t// But Firefox is a pill, too - see issue #76: https://github.com/mholt/PapaParse/issues/76\n\t\tvar usingAsyncReader = typeof FileReader !== 'undefined';\t// Safari doesn't consider it a function - see issue #105\n\n\t\tthis.stream = function(file)\n\t\t{\n\t\t\tthis._input = file;\n\t\t\tslice = file.slice || file.webkitSlice || file.mozSlice;\n\n\t\t\tif (usingAsyncReader)\n\t\t\t{\n\t\t\t\treader = new FileReader();\t\t// Preferred method of reading files, even in workers\n\t\t\t\treader.onload = bindFunction(this._chunkLoaded, this);\n\t\t\t\treader.onerror = bindFunction(this._chunkError, this);\n\t\t\t}\n\t\t\telse\n\t\t\t\treader = new FileReaderSync();\t// Hack for running in a web worker in Firefox\n\n\t\t\tthis._nextChunk();\t// Starts streaming\n\t\t};\n\n\t\tthis._nextChunk = function()\n\t\t{\n\t\t\tif (!this._finished && (!this._config.preview || this._rowCount < this._config.preview))\n\t\t\t\tthis._readChunk();\n\t\t};\n\n\t\tthis._readChunk = function()\n\t\t{\n\t\t\tvar input = this._input;\n\t\t\tif (this._config.chunkSize)\n\t\t\t{\n\t\t\t\tvar end = Math.min(this._start + this._config.chunkSize, this._input.size);\n\t\t\t\tinput = slice.call(input, this._start, end);\n\t\t\t}\n\t\t\tvar txt = reader.readAsText(input, this._config.encoding);\n\t\t\tif (!usingAsyncReader)\n\t\t\t\tthis._chunkLoaded({ target: { result: txt } });\t// mimic the async signature\n\t\t};\n\n\t\tthis._chunkLoaded = function(event)\n\t\t{\n\t\t\t// Very important to increment start each time before handling results\n\t\t\tthis._start += this._config.chunkSize;\n\t\t\tthis._finished = !this._config.chunkSize || this._start >= this._input.size;\n\t\t\tthis.parseChunk(event.target.result);\n\t\t};\n\n\t\tthis._chunkError = function()\n\t\t{\n\t\t\tthis._sendError(reader.error);\n\t\t};\n\n\t}\n\tFileStreamer.prototype = Object.create(ChunkStreamer.prototype);\n\tFileStreamer.prototype.constructor = FileStreamer;\n\n\n\tfunction StringStreamer(config)\n\t{\n\t\tconfig = config || {};\n\t\tChunkStreamer.call(this, config);\n\n\t\tvar remaining;\n\t\tthis.stream = function(s)\n\t\t{\n\t\t\tremaining = s;\n\t\t\treturn this._nextChunk();\n\t\t};\n\t\tthis._nextChunk = function()\n\t\t{\n\t\t\tif (this._finished) return;\n\t\t\tvar size = this._config.chunkSize;\n\t\t\tvar chunk;\n\t\t\tif(size) {\n\t\t\t\tchunk = remaining.substring(0, size);\n\t\t\t\tremaining = remaining.substring(size);\n\t\t\t} else {\n\t\t\t\tchunk = remaining;\n\t\t\t\tremaining = '';\n\t\t\t}\n\t\t\tthis._finished = !remaining;\n\t\t\treturn this.parseChunk(chunk);\n\t\t};\n\t}\n\tStringStreamer.prototype = Object.create(StringStreamer.prototype);\n\tStringStreamer.prototype.constructor = StringStreamer;\n\n\n\tfunction ReadableStreamStreamer(config)\n\t{\n\t\tconfig = config || {};\n\n\t\tChunkStreamer.call(this, config);\n\n\t\tvar queue = [];\n\t\tvar parseOnData = true;\n\t\tvar streamHasEnded = false;\n\n\t\tthis.pause = function()\n\t\t{\n\t\t\tChunkStreamer.prototype.pause.apply(this, arguments);\n\t\t\tthis._input.pause();\n\t\t};\n\n\t\tthis.resume = function()\n\t\t{\n\t\t\tChunkStreamer.prototype.resume.apply(this, arguments);\n\t\t\tthis._input.resume();\n\t\t};\n\n\t\tthis.stream = function(stream)\n\t\t{\n\t\t\tthis._input = stream;\n\n\t\t\tthis._input.on('data', this._streamData);\n\t\t\tthis._input.on('end', this._streamEnd);\n\t\t\tthis._input.on('error', this._streamError);\n\t\t};\n\n\t\tthis._checkIsFinished = function()\n\t\t{\n\t\t\tif (streamHasEnded && queue.length === 1) {\n\t\t\t\tthis._finished = true;\n\t\t\t}\n\t\t};\n\n\t\tthis._nextChunk = function()\n\t\t{\n\t\t\tthis._checkIsFinished();\n\t\t\tif (queue.length)\n\t\t\t{\n\t\t\t\tthis.parseChunk(queue.shift());\n\t\t\t}\n\t\t\telse\n\t\t\t{\n\t\t\t\tparseOnData = true;\n\t\t\t}\n\t\t};\n\n\t\tthis._streamData = bindFunction(function(chunk)\n\t\t{\n\t\t\ttry\n\t\t\t{\n\t\t\t\tqueue.push(typeof chunk === 'string' ? chunk : chunk.toString(this._config.encoding));\n\n\t\t\t\tif (parseOnData)\n\t\t\t\t{\n\t\t\t\t\tparseOnData = false;\n\t\t\t\t\tthis._checkIsFinished();\n\t\t\t\t\tthis.parseChunk(queue.shift());\n\t\t\t\t}\n\t\t\t}\n\t\t\tcatch (error)\n\t\t\t{\n\t\t\t\tthis._streamError(error);\n\t\t\t}\n\t\t}, this);\n\n\t\tthis._streamError = bindFunction(function(error)\n\t\t{\n\t\t\tthis._streamCleanUp();\n\t\t\tthis._sendError(error);\n\t\t}, this);\n\n\t\tthis._streamEnd = bindFunction(function()\n\t\t{\n\t\t\tthis._streamCleanUp();\n\t\t\tstreamHasEnded = true;\n\t\t\tthis._streamData('');\n\t\t}, this);\n\n\t\tthis._streamCleanUp = bindFunction(function()\n\t\t{\n\t\t\tthis._input.removeListener('data', this._streamData);\n\t\t\tthis._input.removeListener('end', this._streamEnd);\n\t\t\tthis._input.removeListener('error', this._streamError);\n\t\t}, this);\n\t}\n\tReadableStreamStreamer.prototype = Object.create(ChunkStreamer.prototype);\n\tReadableStreamStreamer.prototype.constructor = ReadableStreamStreamer;\n\n\n\tfunction DuplexStreamStreamer(_config) {\n\t\tvar Duplex = (__webpack_require__(/*! stream */ \"stream\").Duplex);\n\t\tvar config = copy(_config);\n\t\tvar parseOnWrite = true;\n\t\tvar writeStreamHasFinished = false;\n\t\tvar parseCallbackQueue = [];\n\t\tvar stream = null;\n\n\t\tthis._onCsvData = function(results)\n\t\t{\n\t\t\tvar data = results.data;\n\t\t\tif (!stream.push(data) && !this._handle.paused()) {\n\t\t\t\t// the writeable consumer buffer has filled up\n\t\t\t\t// so we need to pause until more items\n\t\t\t\t// can be processed\n\t\t\t\tthis._handle.pause();\n\t\t\t}\n\t\t};\n\n\t\tthis._onCsvComplete = function()\n\t\t{\n\t\t\t// node will finish the read stream when\n\t\t\t// null is pushed\n\t\t\tstream.push(null);\n\t\t};\n\n\t\tconfig.step = bindFunction(this._onCsvData, this);\n\t\tconfig.complete = bindFunction(this._onCsvComplete, this);\n\t\tChunkStreamer.call(this, config);\n\n\t\tthis._nextChunk = function()\n\t\t{\n\t\t\tif (writeStreamHasFinished && parseCallbackQueue.length === 1) {\n\t\t\t\tthis._finished = true;\n\t\t\t}\n\t\t\tif (parseCallbackQueue.length) {\n\t\t\t\tparseCallbackQueue.shift()();\n\t\t\t} else {\n\t\t\t\tparseOnWrite = true;\n\t\t\t}\n\t\t};\n\n\t\tthis._addToParseQueue = function(chunk, callback)\n\t\t{\n\t\t\t// add to queue so that we can indicate\n\t\t\t// completion via callback\n\t\t\t// node will automatically pause the incoming stream\n\t\t\t// when too many items have been added without their\n\t\t\t// callback being invoked\n\t\t\tparseCallbackQueue.push(bindFunction(function() {\n\t\t\t\tthis.parseChunk(typeof chunk === 'string' ? chunk : chunk.toString(config.encoding));\n\t\t\t\tif (isFunction(callback)) {\n\t\t\t\t\treturn callback();\n\t\t\t\t}\n\t\t\t}, this));\n\t\t\tif (parseOnWrite) {\n\t\t\t\tparseOnWrite = false;\n\t\t\t\tthis._nextChunk();\n\t\t\t}\n\t\t};\n\n\t\tthis._onRead = function()\n\t\t{\n\t\t\tif (this._handle.paused()) {\n\t\t\t\t// the writeable consumer can handle more data\n\t\t\t\t// so resume the chunk parsing\n\t\t\t\tthis._handle.resume();\n\t\t\t}\n\t\t};\n\n\t\tthis._onWrite = function(chunk, encoding, callback)\n\t\t{\n\t\t\tthis._addToParseQueue(chunk, callback);\n\t\t};\n\n\t\tthis._onWriteComplete = function()\n\t\t{\n\t\t\twriteStreamHasFinished = true;\n\t\t\t// have to write empty string\n\t\t\t// so parser knows its done\n\t\t\tthis._addToParseQueue('');\n\t\t};\n\n\t\tthis.getStream = function()\n\t\t{\n\t\t\treturn stream;\n\t\t};\n\t\tstream = new Duplex({\n\t\t\treadableObjectMode: true,\n\t\t\tdecodeStrings: false,\n\t\t\tread: bindFunction(this._onRead, this),\n\t\t\twrite: bindFunction(this._onWrite, this)\n\t\t});\n\t\tstream.once('finish', bindFunction(this._onWriteComplete, this));\n\t}\n\tif (typeof PAPA_BROWSER_CONTEXT === 'undefined') {\n\t\tDuplexStreamStreamer.prototype = Object.create(ChunkStreamer.prototype);\n\t\tDuplexStreamStreamer.prototype.constructor = DuplexStreamStreamer;\n\t}\n\n\n\t// Use one ParserHandle per entire CSV file or string\n\tfunction ParserHandle(_config)\n\t{\n\t\t// One goal is to minimize the use of regular expressions...\n\t\tvar MAX_FLOAT = Math.pow(2, 53);\n\t\tvar MIN_FLOAT = -MAX_FLOAT;\n\t\tvar FLOAT = /^\\s*-?(\\d+\\.?|\\.\\d+|\\d+\\.\\d+)([eE][-+]?\\d+)?\\s*$/;\n\t\tvar ISO_DATE = /^((\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d\\.\\d+([+-][0-2]\\d:[0-5]\\d|Z))|(\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z))|(\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)))$/;\n\t\tvar self = this;\n\t\tvar _stepCounter = 0;\t// Number of times step was called (number of rows parsed)\n\t\tvar _rowCounter = 0;\t// Number of rows that have been parsed so far\n\t\tvar _input;\t\t\t\t// The input being parsed\n\t\tvar _parser;\t\t\t// The core parser being used\n\t\tvar _paused = false;\t// Whether we are paused or not\n\t\tvar _aborted = false;\t// Whether the parser has aborted or not\n\t\tvar _delimiterError;\t// Temporary state between delimiter detection and processing results\n\t\tvar _fields = [];\t\t// Fields are from the header row of the input, if there is one\n\t\tvar _results = {\t\t// The last results returned from the parser\n\t\t\tdata: [],\n\t\t\terrors: [],\n\t\t\tmeta: {}\n\t\t};\n\n\t\tif (isFunction(_config.step))\n\t\t{\n\t\t\tvar userStep = _config.step;\n\t\t\t_config.step = function(results)\n\t\t\t{\n\t\t\t\t_results = results;\n\n\t\t\t\tif (needsHeaderRow())\n\t\t\t\t\tprocessResults();\n\t\t\t\telse\t// only call user's step function after header row\n\t\t\t\t{\n\t\t\t\t\tprocessResults();\n\n\t\t\t\t\t// It's possbile that this line was empty and there's no row here after all\n\t\t\t\t\tif (_results.data.length === 0)\n\t\t\t\t\t\treturn;\n\n\t\t\t\t\t_stepCounter += results.data.length;\n\t\t\t\t\tif (_config.preview && _stepCounter > _config.preview)\n\t\t\t\t\t\t_parser.abort();\n\t\t\t\t\telse {\n\t\t\t\t\t\t_results.data = _results.data[0];\n\t\t\t\t\t\tuserStep(_results, self);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t};\n\t\t}\n\n\t\t/**\n\t\t * Parses input. Most users won't need, and shouldn't mess with, the baseIndex\n\t\t * and ignoreLastRow parameters. They are used by streamers (wrapper functions)\n\t\t * when an input comes in multiple chunks, like from a file.\n\t\t */\n\t\tthis.parse = function(input, baseIndex, ignoreLastRow)\n\t\t{\n\t\t\tvar quoteChar = _config.quoteChar || '\"';\n\t\t\tif (!_config.newline)\n\t\t\t\t_config.newline = this.guessLineEndings(input, quoteChar);\n\n\t\t\t_delimiterError = false;\n\t\t\tif (!_config.delimiter)\n\t\t\t{\n\t\t\t\tvar delimGuess = guessDelimiter(input, _config.newline, _config.skipEmptyLines, _config.comments, _config.delimitersToGuess);\n\t\t\t\tif (delimGuess.successful)\n\t\t\t\t\t_config.delimiter = delimGuess.bestDelimiter;\n\t\t\t\telse\n\t\t\t\t{\n\t\t\t\t\t_delimiterError = true;\t// add error after parsing (otherwise it would be overwritten)\n\t\t\t\t\t_config.delimiter = Papa.DefaultDelimiter;\n\t\t\t\t}\n\t\t\t\t_results.meta.delimiter = _config.delimiter;\n\t\t\t}\n\t\t\telse if(isFunction(_config.delimiter))\n\t\t\t{\n\t\t\t\t_config.delimiter = _config.delimiter(input);\n\t\t\t\t_results.meta.delimiter = _config.delimiter;\n\t\t\t}\n\n\t\t\tvar parserConfig = copy(_config);\n\t\t\tif (_config.preview && _config.header)\n\t\t\t\tparserConfig.preview++;\t// to compensate for header row\n\n\t\t\t_input = input;\n\t\t\t_parser = new Parser(parserConfig);\n\t\t\t_results = _parser.parse(_input, baseIndex, ignoreLastRow);\n\t\t\tprocessResults();\n\t\t\treturn _paused ? { meta: { paused: true } } : (_results || { meta: { paused: false } });\n\t\t};\n\n\t\tthis.paused = function()\n\t\t{\n\t\t\treturn _paused;\n\t\t};\n\n\t\tthis.pause = function()\n\t\t{\n\t\t\t_paused = true;\n\t\t\t_parser.abort();\n\n\t\t\t// If it is streaming via \"chunking\", the reader will start appending correctly already so no need to substring,\n\t\t\t// otherwise we can get duplicate content within a row\n\t\t\t_input = isFunction(_config.chunk) ? \"\" : _input.substring(_parser.getCharIndex());\n\t\t};\n\n\t\tthis.resume = function()\n\t\t{\n\t\t\tif(self.streamer._halted) {\n\t\t\t\t_paused = false;\n\t\t\t\tself.streamer.parseChunk(_input, true);\n\t\t\t} else {\n\t\t\t\t// Bugfix: #636 In case the processing hasn't halted yet\n\t\t\t\t// wait for it to halt in order to resume\n\t\t\t\tsetTimeout(self.resume, 3);\n\t\t\t}\n\t\t};\n\n\t\tthis.aborted = function()\n\t\t{\n\t\t\treturn _aborted;\n\t\t};\n\n\t\tthis.abort = function()\n\t\t{\n\t\t\t_aborted = true;\n\t\t\t_parser.abort();\n\t\t\t_results.meta.aborted = true;\n\t\t\tif (isFunction(_config.complete))\n\t\t\t\t_config.complete(_results);\n\t\t\t_input = '';\n\t\t};\n\n\t\tthis.guessLineEndings = function(input, quoteChar)\n\t\t{\n\t\t\tinput = input.substring(0, 1024 * 1024);\t// max length 1 MB\n\t\t\t// Replace all the text inside quotes\n\t\t\tvar re = new RegExp(escapeRegExp(quoteChar) + '([^]*?)' + escapeRegExp(quoteChar), 'gm');\n\t\t\tinput = input.replace(re, '');\n\n\t\t\tvar r = input.split('\\r');\n\n\t\t\tvar n = input.split('\\n');\n\n\t\t\tvar nAppearsFirst = (n.length > 1 && n[0].length < r[0].length);\n\n\t\t\tif (r.length === 1 || nAppearsFirst)\n\t\t\t\treturn '\\n';\n\n\t\t\tvar numWithN = 0;\n\t\t\tfor (var i = 0; i < r.length; i++)\n\t\t\t{\n\t\t\t\tif (r[i][0] === '\\n')\n\t\t\t\t\tnumWithN++;\n\t\t\t}\n\n\t\t\treturn numWithN >= r.length / 2 ? '\\r\\n' : '\\r';\n\t\t};\n\n\t\tfunction testEmptyLine(s) {\n\t\t\treturn _config.skipEmptyLines === 'greedy' ? s.join('').trim() === '' : s.length === 1 && s[0].length === 0;\n\t\t}\n\n\t\tfunction testFloat(s) {\n\t\t\tif (FLOAT.test(s)) {\n\t\t\t\tvar floatValue = parseFloat(s);\n\t\t\t\tif (floatValue > MIN_FLOAT && floatValue < MAX_FLOAT) {\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn false;\n\t\t}\n\n\t\tfunction processResults()\n\t\t{\n\t\t\tif (_results && _delimiterError)\n\t\t\t{\n\t\t\t\taddError('Delimiter', 'UndetectableDelimiter', 'Unable to auto-detect delimiting character; defaulted to \\'' + Papa.DefaultDelimiter + '\\'');\n\t\t\t\t_delimiterError = false;\n\t\t\t}\n\n\t\t\tif (_config.skipEmptyLines)\n\t\t\t{\n\t\t\t\t_results.data = _results.data.filter(function(d) {\n\t\t\t\t\treturn !testEmptyLine(d);\n\t\t\t\t});\n\t\t\t}\n\n\t\t\tif (needsHeaderRow())\n\t\t\t\tfillHeaderFields();\n\n\t\t\treturn applyHeaderAndDynamicTypingAndTransformation();\n\t\t}\n\n\t\tfunction needsHeaderRow()\n\t\t{\n\t\t\treturn _config.header && _fields.length === 0;\n\t\t}\n\n\t\tfunction fillHeaderFields()\n\t\t{\n\t\t\tif (!_results)\n\t\t\t\treturn;\n\n\t\t\tfunction addHeader(header, i)\n\t\t\t{\n\t\t\t\tif (isFunction(_config.transformHeader))\n\t\t\t\t\theader = _config.transformHeader(header, i);\n\n\t\t\t\t_fields.push(header);\n\t\t\t}\n\n\t\t\tif (Array.isArray(_results.data[0]))\n\t\t\t{\n\t\t\t\tfor (var i = 0; needsHeaderRow() && i < _results.data.length; i++)\n\t\t\t\t\t_results.data[i].forEach(addHeader);\n\n\t\t\t\t_results.data.splice(0, 1);\n\t\t\t}\n\t\t\t// if _results.data[0] is not an array, we are in a step where _results.data is the row.\n\t\t\telse\n\t\t\t\t_results.data.forEach(addHeader);\n\t\t}\n\n\t\tfunction shouldApplyDynamicTyping(field) {\n\t\t\t// Cache function values to avoid calling it for each row\n\t\t\tif (_config.dynamicTypingFunction && _config.dynamicTyping[field] === undefined) {\n\t\t\t\t_config.dynamicTyping[field] = _config.dynamicTypingFunction(field);\n\t\t\t}\n\t\t\treturn (_config.dynamicTyping[field] || _config.dynamicTyping) === true;\n\t\t}\n\n\t\tfunction parseDynamic(field, value)\n\t\t{\n\t\t\tif (shouldApplyDynamicTyping(field))\n\t\t\t{\n\t\t\t\tif (value === 'true' || value === 'TRUE')\n\t\t\t\t\treturn true;\n\t\t\t\telse if (value === 'false' || value === 'FALSE')\n\t\t\t\t\treturn false;\n\t\t\t\telse if (testFloat(value))\n\t\t\t\t\treturn parseFloat(value);\n\t\t\t\telse if (ISO_DATE.test(value))\n\t\t\t\t\treturn new Date(value);\n\t\t\t\telse\n\t\t\t\t\treturn (value === '' ? null : value);\n\t\t\t}\n\t\t\treturn value;\n\t\t}\n\n\t\tfunction applyHeaderAndDynamicTypingAndTransformation()\n\t\t{\n\t\t\tif (!_results || (!_config.header && !_config.dynamicTyping && !_config.transform))\n\t\t\t\treturn _results;\n\n\t\t\tfunction processRow(rowSource, i)\n\t\t\t{\n\t\t\t\tvar row = _config.header ? {} : [];\n\n\t\t\t\tvar j;\n\t\t\t\tfor (j = 0; j < rowSource.length; j++)\n\t\t\t\t{\n\t\t\t\t\tvar field = j;\n\t\t\t\t\tvar value = rowSource[j];\n\n\t\t\t\t\tif (_config.header)\n\t\t\t\t\t\tfield = j >= _fields.length ? '__parsed_extra' : _fields[j];\n\n\t\t\t\t\tif (_config.transform)\n\t\t\t\t\t\tvalue = _config.transform(value,field);\n\n\t\t\t\t\tvalue = parseDynamic(field, value);\n\n\t\t\t\t\tif (field === '__parsed_extra')\n\t\t\t\t\t{\n\t\t\t\t\t\trow[field] = row[field] || [];\n\t\t\t\t\t\trow[field].push(value);\n\t\t\t\t\t}\n\t\t\t\t\telse\n\t\t\t\t\t\trow[field] = value;\n\t\t\t\t}\n\n\n\t\t\t\tif (_config.header)\n\t\t\t\t{\n\t\t\t\t\tif (j > _fields.length)\n\t\t\t\t\t\taddError('FieldMismatch', 'TooManyFields', 'Too many fields: expected ' + _fields.length + ' fields but parsed ' + j, _rowCounter + i);\n\t\t\t\t\telse if (j < _fields.length)\n\t\t\t\t\t\taddError('FieldMismatch', 'TooFewFields', 'Too few fields: expected ' + _fields.length + ' fields but parsed ' + j, _rowCounter + i);\n\t\t\t\t}\n\n\t\t\t\treturn row;\n\t\t\t}\n\n\t\t\tvar incrementBy = 1;\n\t\t\tif (!_results.data.length || Array.isArray(_results.data[0]))\n\t\t\t{\n\t\t\t\t_results.data = _results.data.map(processRow);\n\t\t\t\tincrementBy = _results.data.length;\n\t\t\t}\n\t\t\telse\n\t\t\t\t_results.data = processRow(_results.data, 0);\n\n\n\t\t\tif (_config.header && _results.meta)\n\t\t\t\t_results.meta.fields = _fields;\n\n\t\t\t_rowCounter += incrementBy;\n\t\t\treturn _results;\n\t\t}\n\n\t\tfunction guessDelimiter(input, newline, skipEmptyLines, comments, delimitersToGuess) {\n\t\t\tvar bestDelim, bestDelta, fieldCountPrevRow, maxFieldCount;\n\n\t\t\tdelimitersToGuess = delimitersToGuess || [',', '\\t', '|', ';', Papa.RECORD_SEP, Papa.UNIT_SEP];\n\n\t\t\tfor (var i = 0; i < delimitersToGuess.length; i++) {\n\t\t\t\tvar delim = delimitersToGuess[i];\n\t\t\t\tvar delta = 0, avgFieldCount = 0, emptyLinesCount = 0;\n\t\t\t\tfieldCountPrevRow = undefined;\n\n\t\t\t\tvar preview = new Parser({\n\t\t\t\t\tcomments: comments,\n\t\t\t\t\tdelimiter: delim,\n\t\t\t\t\tnewline: newline,\n\t\t\t\t\tpreview: 10\n\t\t\t\t}).parse(input);\n\n\t\t\t\tfor (var j = 0; j < preview.data.length; j++) {\n\t\t\t\t\tif (skipEmptyLines && testEmptyLine(preview.data[j])) {\n\t\t\t\t\t\temptyLinesCount++;\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t\tvar fieldCount = preview.data[j].length;\n\t\t\t\t\tavgFieldCount += fieldCount;\n\n\t\t\t\t\tif (typeof fieldCountPrevRow === 'undefined') {\n\t\t\t\t\t\tfieldCountPrevRow = fieldCount;\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t\telse if (fieldCount > 0) {\n\t\t\t\t\t\tdelta += Math.abs(fieldCount - fieldCountPrevRow);\n\t\t\t\t\t\tfieldCountPrevRow = fieldCount;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (preview.data.length > 0)\n\t\t\t\t\tavgFieldCount /= (preview.data.length - emptyLinesCount);\n\n\t\t\t\tif ((typeof bestDelta === 'undefined' || delta <= bestDelta)\n\t\t\t\t\t&& (typeof maxFieldCount === 'undefined' || avgFieldCount > maxFieldCount) && avgFieldCount > 1.99) {\n\t\t\t\t\tbestDelta = delta;\n\t\t\t\t\tbestDelim = delim;\n\t\t\t\t\tmaxFieldCount = avgFieldCount;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t_config.delimiter = bestDelim;\n\n\t\t\treturn {\n\t\t\t\tsuccessful: !!bestDelim,\n\t\t\t\tbestDelimiter: bestDelim\n\t\t\t};\n\t\t}\n\n\t\tfunction addError(type, code, msg, row)\n\t\t{\n\t\t\tvar error = {\n\t\t\t\ttype: type,\n\t\t\t\tcode: code,\n\t\t\t\tmessage: msg\n\t\t\t};\n\t\t\tif(row !== undefined) {\n\t\t\t\terror.row = row;\n\t\t\t}\n\t\t\t_results.errors.push(error);\n\t\t}\n\t}\n\n\t/** https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Regular_Expressions */\n\tfunction escapeRegExp(string)\n\t{\n\t\treturn string.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'); // $& means the whole matched string\n\t}\n\n\t/** The core parser implements speedy and correct CSV parsing */\n\tfunction Parser(config)\n\t{\n\t\t// Unpack the config object\n\t\tconfig = config || {};\n\t\tvar delim = config.delimiter;\n\t\tvar newline = config.newline;\n\t\tvar comments = config.comments;\n\t\tvar step = config.step;\n\t\tvar preview = config.preview;\n\t\tvar fastMode = config.fastMode;\n\t\tvar quoteChar;\n\t\tvar renamedHeaders = null;\n\t\tvar headerParsed = false;\n\n\t\tif (config.quoteChar === undefined || config.quoteChar === null) {\n\t\t\tquoteChar = '\"';\n\t\t} else {\n\t\t\tquoteChar = config.quoteChar;\n\t\t}\n\t\tvar escapeChar = quoteChar;\n\t\tif (config.escapeChar !== undefined) {\n\t\t\tescapeChar = config.escapeChar;\n\t\t}\n\n\t\t// Delimiter must be valid\n\t\tif (typeof delim !== 'string'\n\t\t\t|| Papa.BAD_DELIMITERS.indexOf(delim) > -1)\n\t\t\tdelim = ',';\n\n\t\t// Comment character must be valid\n\t\tif (comments === delim)\n\t\t\tthrow new Error('Comment character same as delimiter');\n\t\telse if (comments === true)\n\t\t\tcomments = '#';\n\t\telse if (typeof comments !== 'string'\n\t\t\t|| Papa.BAD_DELIMITERS.indexOf(comments) > -1)\n\t\t\tcomments = false;\n\n\t\t// Newline must be valid: \\r, \\n, or \\r\\n\n\t\tif (newline !== '\\n' && newline !== '\\r' && newline !== '\\r\\n')\n\t\t\tnewline = '\\n';\n\n\t\t// We're gonna need these at the Parser scope\n\t\tvar cursor = 0;\n\t\tvar aborted = false;\n\n\t\tthis.parse = function(input, baseIndex, ignoreLastRow)\n\t\t{\n\t\t\t// For some reason, in Chrome, this speeds things up (!?)\n\t\t\tif (typeof input !== 'string')\n\t\t\t\tthrow new Error('Input must be a string');\n\n\t\t\t// We don't need to compute some of these every time parse() is called,\n\t\t\t// but having them in a more local scope seems to perform better\n\t\t\tvar inputLen = input.length,\n\t\t\t\tdelimLen = delim.length,\n\t\t\t\tnewlineLen = newline.length,\n\t\t\t\tcommentsLen = comments.length;\n\t\t\tvar stepIsFunction = isFunction(step);\n\n\t\t\t// Establish starting state\n\t\t\tcursor = 0;\n\t\t\tvar data = [], errors = [], row = [], lastCursor = 0;\n\n\t\t\tif (!input)\n\t\t\t\treturn returnable();\n\n\t\t\tif (fastMode || (fastMode !== false && input.indexOf(quoteChar) === -1))\n\t\t\t{\n\t\t\t\tvar rows = input.split(newline);\n\t\t\t\tfor (var i = 0; i < rows.length; i++)\n\t\t\t\t{\n\t\t\t\t\trow = rows[i];\n\t\t\t\t\tcursor += row.length;\n\n\t\t\t\t\tif (i !== rows.length - 1)\n\t\t\t\t\t\tcursor += newline.length;\n\t\t\t\t\telse if (ignoreLastRow)\n\t\t\t\t\t\treturn returnable();\n\t\t\t\t\tif (comments && row.substring(0, commentsLen) === comments)\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\tif (stepIsFunction)\n\t\t\t\t\t{\n\t\t\t\t\t\tdata = [];\n\t\t\t\t\t\tpushRow(row.split(delim));\n\t\t\t\t\t\tdoStep();\n\t\t\t\t\t\tif (aborted)\n\t\t\t\t\t\t\treturn returnable();\n\t\t\t\t\t}\n\t\t\t\t\telse\n\t\t\t\t\t\tpushRow(row.split(delim));\n\t\t\t\t\tif (preview && i >= preview)\n\t\t\t\t\t{\n\t\t\t\t\t\tdata = data.slice(0, preview);\n\t\t\t\t\t\treturn returnable(true);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn returnable();\n\t\t\t}\n\n\t\t\tvar nextDelim = input.indexOf(delim, cursor);\n\t\t\tvar nextNewline = input.indexOf(newline, cursor);\n\t\t\tvar quoteCharRegex = new RegExp(escapeRegExp(escapeChar) + escapeRegExp(quoteChar), 'g');\n\t\t\tvar quoteSearch = input.indexOf(quoteChar, cursor);\n\n\t\t\t// Parser loop\n\t\t\tfor (;;)\n\t\t\t{\n\t\t\t\t// Field has opening quote\n\t\t\t\tif (input[cursor] === quoteChar)\n\t\t\t\t{\n\t\t\t\t\t// Start our search for the closing quote where the cursor is\n\t\t\t\t\tquoteSearch = cursor;\n\n\t\t\t\t\t// Skip the opening quote\n\t\t\t\t\tcursor++;\n\n\t\t\t\t\tfor (;;)\n\t\t\t\t\t{\n\t\t\t\t\t\t// Find closing quote\n\t\t\t\t\t\tquoteSearch = input.indexOf(quoteChar, quoteSearch + 1);\n\n\t\t\t\t\t\t//No other quotes are found - no other delimiters\n\t\t\t\t\t\tif (quoteSearch === -1)\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tif (!ignoreLastRow) {\n\t\t\t\t\t\t\t\t// No closing quote... what a pity\n\t\t\t\t\t\t\t\terrors.push({\n\t\t\t\t\t\t\t\t\ttype: 'Quotes',\n\t\t\t\t\t\t\t\t\tcode: 'MissingQuotes',\n\t\t\t\t\t\t\t\t\tmessage: 'Quoted field unterminated',\n\t\t\t\t\t\t\t\t\trow: data.length,\t// row has yet to be inserted\n\t\t\t\t\t\t\t\t\tindex: cursor\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\treturn finish();\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// Closing quote at EOF\n\t\t\t\t\t\tif (quoteSearch === inputLen - 1)\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tvar value = input.substring(cursor, quoteSearch).replace(quoteCharRegex, quoteChar);\n\t\t\t\t\t\t\treturn finish(value);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// If this quote is escaped, it's part of the data; skip it\n\t\t\t\t\t\t// If the quote character is the escape character, then check if the next character is the escape character\n\t\t\t\t\t\tif (quoteChar === escapeChar &&  input[quoteSearch + 1] === escapeChar)\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tquoteSearch++;\n\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// If the quote character is not the escape character, then check if the previous character was the escape character\n\t\t\t\t\t\tif (quoteChar !== escapeChar && quoteSearch !== 0 && input[quoteSearch - 1] === escapeChar)\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif(nextDelim !== -1 && nextDelim < (quoteSearch + 1)) {\n\t\t\t\t\t\t\tnextDelim = input.indexOf(delim, (quoteSearch + 1));\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif(nextNewline !== -1 && nextNewline < (quoteSearch + 1)) {\n\t\t\t\t\t\t\tnextNewline = input.indexOf(newline, (quoteSearch + 1));\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// Check up to nextDelim or nextNewline, whichever is closest\n\t\t\t\t\t\tvar checkUpTo = nextNewline === -1 ? nextDelim : Math.min(nextDelim, nextNewline);\n\t\t\t\t\t\tvar spacesBetweenQuoteAndDelimiter = extraSpaces(checkUpTo);\n\n\t\t\t\t\t\t// Closing quote followed by delimiter or 'unnecessary spaces + delimiter'\n\t\t\t\t\t\tif (input.substr(quoteSearch + 1 + spacesBetweenQuoteAndDelimiter, delimLen) === delim)\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\trow.push(input.substring(cursor, quoteSearch).replace(quoteCharRegex, quoteChar));\n\t\t\t\t\t\t\tcursor = quoteSearch + 1 + spacesBetweenQuoteAndDelimiter + delimLen;\n\n\t\t\t\t\t\t\t// If char after following delimiter is not quoteChar, we find next quote char position\n\t\t\t\t\t\t\tif (input[quoteSearch + 1 + spacesBetweenQuoteAndDelimiter + delimLen] !== quoteChar)\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tquoteSearch = input.indexOf(quoteChar, cursor);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tnextDelim = input.indexOf(delim, cursor);\n\t\t\t\t\t\t\tnextNewline = input.indexOf(newline, cursor);\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tvar spacesBetweenQuoteAndNewLine = extraSpaces(nextNewline);\n\n\t\t\t\t\t\t// Closing quote followed by newline or 'unnecessary spaces + newLine'\n\t\t\t\t\t\tif (input.substring(quoteSearch + 1 + spacesBetweenQuoteAndNewLine, quoteSearch + 1 + spacesBetweenQuoteAndNewLine + newlineLen) === newline)\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\trow.push(input.substring(cursor, quoteSearch).replace(quoteCharRegex, quoteChar));\n\t\t\t\t\t\t\tsaveRow(quoteSearch + 1 + spacesBetweenQuoteAndNewLine + newlineLen);\n\t\t\t\t\t\t\tnextDelim = input.indexOf(delim, cursor);\t// because we may have skipped the nextDelim in the quoted field\n\t\t\t\t\t\t\tquoteSearch = input.indexOf(quoteChar, cursor);\t// we search for first quote in next line\n\n\t\t\t\t\t\t\tif (stepIsFunction)\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tdoStep();\n\t\t\t\t\t\t\t\tif (aborted)\n\t\t\t\t\t\t\t\t\treturn returnable();\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tif (preview && data.length >= preview)\n\t\t\t\t\t\t\t\treturn returnable(true);\n\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\n\n\t\t\t\t\t\t// Checks for valid closing quotes are complete (escaped quotes or quote followed by EOF/delimiter/newline) -- assume these quotes are part of an invalid text string\n\t\t\t\t\t\terrors.push({\n\t\t\t\t\t\t\ttype: 'Quotes',\n\t\t\t\t\t\t\tcode: 'InvalidQuotes',\n\t\t\t\t\t\t\tmessage: 'Trailing quote on quoted field is malformed',\n\t\t\t\t\t\t\trow: data.length,\t// row has yet to be inserted\n\t\t\t\t\t\t\tindex: cursor\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\tquoteSearch++;\n\t\t\t\t\t\tcontinue;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\t// Comment found at start of new line\n\t\t\t\tif (comments && row.length === 0 && input.substring(cursor, cursor + commentsLen) === comments)\n\t\t\t\t{\n\t\t\t\t\tif (nextNewline === -1)\t// Comment ends at EOF\n\t\t\t\t\t\treturn returnable();\n\t\t\t\t\tcursor = nextNewline + newlineLen;\n\t\t\t\t\tnextNewline = input.indexOf(newline, cursor);\n\t\t\t\t\tnextDelim = input.indexOf(delim, cursor);\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\t// Next delimiter comes before next newline, so we've reached end of field\n\t\t\t\tif (nextDelim !== -1 && (nextDelim < nextNewline || nextNewline === -1))\n\t\t\t\t{\n\t\t\t\t\trow.push(input.substring(cursor, nextDelim));\n\t\t\t\t\tcursor = nextDelim + delimLen;\n\t\t\t\t\t// we look for next delimiter char\n\t\t\t\t\tnextDelim = input.indexOf(delim, cursor);\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\t// End of row\n\t\t\t\tif (nextNewline !== -1)\n\t\t\t\t{\n\t\t\t\t\trow.push(input.substring(cursor, nextNewline));\n\t\t\t\t\tsaveRow(nextNewline + newlineLen);\n\n\t\t\t\t\tif (stepIsFunction)\n\t\t\t\t\t{\n\t\t\t\t\t\tdoStep();\n\t\t\t\t\t\tif (aborted)\n\t\t\t\t\t\t\treturn returnable();\n\t\t\t\t\t}\n\n\t\t\t\t\tif (preview && data.length >= preview)\n\t\t\t\t\t\treturn returnable(true);\n\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tbreak;\n\t\t\t}\n\n\t\t\treturn finish();\n\n\n\t\t\tfunction pushRow(row)\n\t\t\t{\n\t\t\t\tdata.push(row);\n\t\t\t\tlastCursor = cursor;\n\t\t\t}\n\n\t\t\t/**\n             * checks if there are extra spaces after closing quote and given index without any text\n             * if Yes, returns the number of spaces\n             */\n\t\t\tfunction extraSpaces(index) {\n\t\t\t\tvar spaceLength = 0;\n\t\t\t\tif (index !== -1) {\n\t\t\t\t\tvar textBetweenClosingQuoteAndIndex = input.substring(quoteSearch + 1, index);\n\t\t\t\t\tif (textBetweenClosingQuoteAndIndex && textBetweenClosingQuoteAndIndex.trim() === '') {\n\t\t\t\t\t\tspaceLength = textBetweenClosingQuoteAndIndex.length;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn spaceLength;\n\t\t\t}\n\n\t\t\t/**\n\t\t\t * Appends the remaining input from cursor to the end into\n\t\t\t * row, saves the row, calls step, and returns the results.\n\t\t\t */\n\t\t\tfunction finish(value)\n\t\t\t{\n\t\t\t\tif (ignoreLastRow)\n\t\t\t\t\treturn returnable();\n\t\t\t\tif (typeof value === 'undefined')\n\t\t\t\t\tvalue = input.substring(cursor);\n\t\t\t\trow.push(value);\n\t\t\t\tcursor = inputLen;\t// important in case parsing is paused\n\t\t\t\tpushRow(row);\n\t\t\t\tif (stepIsFunction)\n\t\t\t\t\tdoStep();\n\t\t\t\treturn returnable();\n\t\t\t}\n\n\t\t\t/**\n\t\t\t * Appends the current row to the results. It sets the cursor\n\t\t\t * to newCursor and finds the nextNewline. The caller should\n\t\t\t * take care to execute user's step function and check for\n\t\t\t * preview and end parsing if necessary.\n\t\t\t */\n\t\t\tfunction saveRow(newCursor)\n\t\t\t{\n\t\t\t\tcursor = newCursor;\n\t\t\t\tpushRow(row);\n\t\t\t\trow = [];\n\t\t\t\tnextNewline = input.indexOf(newline, cursor);\n\t\t\t}\n\n\t\t\t/** Returns an object with the results, errors, and meta. */\n\t\t\tfunction returnable(stopped)\n\t\t\t{\n\t\t\t\tif (config.header && !baseIndex && data.length && !headerParsed)\n\t\t\t\t{\n\t\t\t\t\tconst result = data[0];\n\t\t\t\t\tconst headerCount = {}; // To track the count of each base header\n\t\t\t\t\tconst usedHeaders = new Set(result); // To track used headers and avoid duplicates\n\t\t\t\t\tlet duplicateHeaders = false;\n\n\t\t\t\t\tfor (let i = 0; i < result.length; i++) {\n\t\t\t\t\t\tlet header = result[i];\n\t\t\t\t\t\tif (isFunction(config.transformHeader))\n\t\t\t\t\t\t\theader = config.transformHeader(header, i);\n\n\t\t\t\t\t\tif (!headerCount[header]) {\n\t\t\t\t\t\t\theaderCount[header] = 1;\n\t\t\t\t\t\t\tresult[i] = header;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tlet newHeader;\n\t\t\t\t\t\t\tlet suffixCount = headerCount[header];\n\n\t\t\t\t\t\t\t// Find a unique new header\n\t\t\t\t\t\t\tdo {\n\t\t\t\t\t\t\t\tnewHeader = `${header}_${suffixCount}`;\n\t\t\t\t\t\t\t\tsuffixCount++;\n\t\t\t\t\t\t\t} while (usedHeaders.has(newHeader));\n\n\t\t\t\t\t\t\tusedHeaders.add(newHeader); // Mark this new Header as used\n\t\t\t\t\t\t\tresult[i] = newHeader;\n\t\t\t\t\t\t\theaderCount[header]++;\n\t\t\t\t\t\t\tduplicateHeaders = true;\n\t\t\t\t\t\t\tif (renamedHeaders === null) {\n\t\t\t\t\t\t\t\trenamedHeaders = {};\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\trenamedHeaders[newHeader] = header;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tusedHeaders.add(header); // Ensure the original header is marked as used\n\t\t\t\t\t}\n\t\t\t\t\tif (duplicateHeaders) {\n\t\t\t\t\t\tconsole.warn('Duplicate headers found and renamed.');\n\t\t\t\t\t}\n\t\t\t\t\theaderParsed = true;\n\t\t\t\t}\n\t\t\t\treturn {\n\t\t\t\t\tdata: data,\n\t\t\t\t\terrors: errors,\n\t\t\t\t\tmeta: {\n\t\t\t\t\t\tdelimiter: delim,\n\t\t\t\t\t\tlinebreak: newline,\n\t\t\t\t\t\taborted: aborted,\n\t\t\t\t\t\ttruncated: !!stopped,\n\t\t\t\t\t\tcursor: lastCursor + (baseIndex || 0),\n\t\t\t\t\t\trenamedHeaders: renamedHeaders\n\t\t\t\t\t}\n\t\t\t\t};\n\t\t\t}\n\n\t\t\t/** Executes the user's step function and resets data & errors. */\n\t\t\tfunction doStep()\n\t\t\t{\n\t\t\t\tstep(returnable());\n\t\t\t\tdata = [];\n\t\t\t\terrors = [];\n\t\t\t}\n\t\t};\n\n\t\t/** Sets the abort flag */\n\t\tthis.abort = function()\n\t\t{\n\t\t\taborted = true;\n\t\t};\n\n\t\t/** Gets the cursor position */\n\t\tthis.getCharIndex = function()\n\t\t{\n\t\t\treturn cursor;\n\t\t};\n\t}\n\n\n\tfunction newWorker()\n\t{\n\t\tif (!Papa.WORKERS_SUPPORTED)\n\t\t\treturn false;\n\n\t\tvar workerUrl = getWorkerBlob();\n\t\tvar w = new global.Worker(workerUrl);\n\t\tw.onmessage = mainThreadReceivedMessage;\n\t\tw.id = workerIdCounter++;\n\t\tworkers[w.id] = w;\n\t\treturn w;\n\t}\n\n\t/** Callback when main thread receives a message */\n\tfunction mainThreadReceivedMessage(e)\n\t{\n\t\tvar msg = e.data;\n\t\tvar worker = workers[msg.workerId];\n\t\tvar aborted = false;\n\n\t\tif (msg.error)\n\t\t\tworker.userError(msg.error, msg.file);\n\t\telse if (msg.results && msg.results.data)\n\t\t{\n\t\t\tvar abort = function() {\n\t\t\t\taborted = true;\n\t\t\t\tcompleteWorker(msg.workerId, { data: [], errors: [], meta: { aborted: true } });\n\t\t\t};\n\n\t\t\tvar handle = {\n\t\t\t\tabort: abort,\n\t\t\t\tpause: notImplemented,\n\t\t\t\tresume: notImplemented\n\t\t\t};\n\n\t\t\tif (isFunction(worker.userStep))\n\t\t\t{\n\t\t\t\tfor (var i = 0; i < msg.results.data.length; i++)\n\t\t\t\t{\n\t\t\t\t\tworker.userStep({\n\t\t\t\t\t\tdata: msg.results.data[i],\n\t\t\t\t\t\terrors: msg.results.errors,\n\t\t\t\t\t\tmeta: msg.results.meta\n\t\t\t\t\t}, handle);\n\t\t\t\t\tif (aborted)\n\t\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t\tdelete msg.results;\t// free memory ASAP\n\t\t\t}\n\t\t\telse if (isFunction(worker.userChunk))\n\t\t\t{\n\t\t\t\tworker.userChunk(msg.results, handle, msg.file);\n\t\t\t\tdelete msg.results;\n\t\t\t}\n\t\t}\n\n\t\tif (msg.finished && !aborted)\n\t\t\tcompleteWorker(msg.workerId, msg.results);\n\t}\n\n\tfunction completeWorker(workerId, results) {\n\t\tvar worker = workers[workerId];\n\t\tif (isFunction(worker.userComplete))\n\t\t\tworker.userComplete(results);\n\t\tworker.terminate();\n\t\tdelete workers[workerId];\n\t}\n\n\tfunction notImplemented() {\n\t\tthrow new Error('Not implemented.');\n\t}\n\n\t/** Callback when worker thread receives a message */\n\tfunction workerThreadReceivedMessage(e)\n\t{\n\t\tvar msg = e.data;\n\n\t\tif (typeof Papa.WORKER_ID === 'undefined' && msg)\n\t\t\tPapa.WORKER_ID = msg.workerId;\n\n\t\tif (typeof msg.input === 'string')\n\t\t{\n\t\t\tglobal.postMessage({\n\t\t\t\tworkerId: Papa.WORKER_ID,\n\t\t\t\tresults: Papa.parse(msg.input, msg.config),\n\t\t\t\tfinished: true\n\t\t\t});\n\t\t}\n\t\telse if ((global.File && msg.input instanceof File) || msg.input instanceof Object)\t// thank you, Safari (see issue #106)\n\t\t{\n\t\t\tvar results = Papa.parse(msg.input, msg.config);\n\t\t\tif (results)\n\t\t\t\tglobal.postMessage({\n\t\t\t\t\tworkerId: Papa.WORKER_ID,\n\t\t\t\t\tresults: results,\n\t\t\t\t\tfinished: true\n\t\t\t\t});\n\t\t}\n\t}\n\n\t/** Makes a deep copy of an array or object (mostly) */\n\tfunction copy(obj)\n\t{\n\t\tif (typeof obj !== 'object' || obj === null)\n\t\t\treturn obj;\n\t\tvar cpy = Array.isArray(obj) ? [] : {};\n\t\tfor (var key in obj)\n\t\t\tcpy[key] = copy(obj[key]);\n\t\treturn cpy;\n\t}\n\n\tfunction bindFunction(f, self)\n\t{\n\t\treturn function() { f.apply(self, arguments); };\n\t}\n\tfunction isFunction(func)\n\t{\n\t\treturn typeof func === 'function';\n\t}\n\n\treturn Papa;\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/papaparse/papaparse.js\n");

/***/ })

};
;