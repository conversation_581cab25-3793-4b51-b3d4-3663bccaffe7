"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-pdf";
exports.ids = ["vendor-chunks/react-pdf"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/Document.js":
/*!*****************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/Document.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var make_event_props__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! make-event-props */ \"(ssr)/./node_modules/make-event-props/dist/esm/index.js\");\n/* harmony import */ var make_cancellable_promise__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! make-cancellable-promise */ \"(ssr)/./node_modules/make-cancellable-promise/dist/esm/index.js\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tiny_invariant__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! tiny-invariant */ \"(ssr)/./node_modules/tiny-invariant/dist/esm/tiny-invariant.js\");\n/* harmony import */ var warning__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! warning */ \"(ssr)/./node_modules/warning/warning.js\");\n/* harmony import */ var dequal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! dequal */ \"(ssr)/./node_modules/dequal/dist/index.mjs\");\n/* harmony import */ var pdfjs_dist__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! pdfjs-dist */ \"(ssr)/./node_modules/pdfjs-dist/build/pdf.mjs\");\n/* harmony import */ var _DocumentContext_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./DocumentContext.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/DocumentContext.js\");\n/* harmony import */ var _Message_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./Message.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/Message.js\");\n/* harmony import */ var _LinkService_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./LinkService.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/LinkService.js\");\n/* harmony import */ var _PasswordResponses_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./PasswordResponses.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/PasswordResponses.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./shared/utils.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/utils.js\");\n/* harmony import */ var _shared_hooks_useResolver_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./shared/hooks/useResolver.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/hooks/useResolver.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ var __awaiter = undefined && undefined.__awaiter || function(thisArg, _arguments, P, generator) {\n    function adopt(value) {\n        return value instanceof P ? value : new P(function(resolve) {\n            resolve(value);\n        });\n    }\n    return new (P || (P = Promise))(function(resolve, reject) {\n        function fulfilled(value) {\n            try {\n                step(generator.next(value));\n            } catch (e) {\n                reject(e);\n            }\n        }\n        function rejected(value) {\n            try {\n                step(generator[\"throw\"](value));\n            } catch (e) {\n                reject(e);\n            }\n        }\n        function step(result) {\n            result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n        }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __rest = undefined && undefined.__rest || function(s, e) {\n    var t = {};\n    for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for(var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++){\n        if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n    }\n    return t;\n};\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst { PDFDataRangeTransport } = pdfjs_dist__WEBPACK_IMPORTED_MODULE_6__;\nconst defaultOnPassword = (callback, reason)=>{\n    switch(reason){\n        case _PasswordResponses_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"].NEED_PASSWORD:\n            {\n                const password = prompt('Enter the password to open this PDF file.');\n                callback(password);\n                break;\n            }\n        case _PasswordResponses_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"].INCORRECT_PASSWORD:\n            {\n                const password = prompt('Invalid password. Please try again.');\n                callback(password);\n                break;\n            }\n        default:\n    }\n};\nfunction isParameterObject(file) {\n    return typeof file === 'object' && file !== null && ('data' in file || 'range' in file || 'url' in file);\n}\n/**\n * Loads a document passed using `file` prop.\n */ const Document = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(function Document(_a, ref) {\n    var { children, className, error = 'Failed to load PDF file.', externalLinkRel, externalLinkTarget, file, inputRef, imageResourcesPath, loading = 'Loading PDF…', noData = 'No PDF file specified.', onItemClick, onLoadError: onLoadErrorProps, onLoadProgress, onLoadSuccess: onLoadSuccessProps, onPassword = defaultOnPassword, onSourceError: onSourceErrorProps, onSourceSuccess: onSourceSuccessProps, options, renderMode, rotate } = _a, otherProps = __rest(_a, [\n        \"children\",\n        \"className\",\n        \"error\",\n        \"externalLinkRel\",\n        \"externalLinkTarget\",\n        \"file\",\n        \"inputRef\",\n        \"imageResourcesPath\",\n        \"loading\",\n        \"noData\",\n        \"onItemClick\",\n        \"onLoadError\",\n        \"onLoadProgress\",\n        \"onLoadSuccess\",\n        \"onPassword\",\n        \"onSourceError\",\n        \"onSourceSuccess\",\n        \"options\",\n        \"renderMode\",\n        \"rotate\"\n    ]);\n    const [sourceState, sourceDispatch] = (0,_shared_hooks_useResolver_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"])();\n    const { value: source, error: sourceError } = sourceState;\n    const [pdfState, pdfDispatch] = (0,_shared_hooks_useResolver_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"])();\n    const { value: pdf, error: pdfError } = pdfState;\n    const linkService = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(new _LinkService_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"]());\n    const pages = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const prevFile = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(undefined);\n    const prevOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(undefined);\n    if (file && file !== prevFile.current && isParameterObject(file)) {\n        warning__WEBPACK_IMPORTED_MODULE_4__(!(0,dequal__WEBPACK_IMPORTED_MODULE_5__.dequal)(file, prevFile.current), `File prop passed to <Document /> changed, but it's equal to previous one. This might result in unnecessary reloads. Consider memoizing the value passed to \"file\" prop.`);\n        prevFile.current = file;\n    }\n    // Detect non-memoized changes in options prop\n    if (options && options !== prevOptions.current) {\n        warning__WEBPACK_IMPORTED_MODULE_4__(!(0,dequal__WEBPACK_IMPORTED_MODULE_5__.dequal)(options, prevOptions.current), `Options prop passed to <Document /> changed, but it's equal to previous one. This might result in unnecessary reloads. Consider memoizing the value passed to \"options\" prop.`);\n        prevOptions.current = options;\n    }\n    const viewer = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        // Handling jumping to internal links target\n        scrollPageIntoView: {\n            \"Document.Document.useRef[viewer]\": (args)=>{\n                const { dest, pageNumber, pageIndex = pageNumber - 1 } = args;\n                // First, check if custom handling of onItemClick was provided\n                if (onItemClick) {\n                    onItemClick({\n                        dest,\n                        pageIndex,\n                        pageNumber\n                    });\n                    return;\n                }\n                // If not, try to look for target page within the <Document>.\n                const page = pages.current[pageIndex];\n                if (page) {\n                    // Scroll to the page automatically\n                    page.scrollIntoView();\n                    return;\n                }\n                warning__WEBPACK_IMPORTED_MODULE_4__(false, `An internal link leading to page ${pageNumber} was clicked, but neither <Document> was provided with onItemClick nor it was able to find the page within itself. Either provide onItemClick to <Document> and handle navigating by yourself or ensure that all pages are rendered within <Document>.`);\n            }\n        }[\"Document.Document.useRef[viewer]\"]\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, {\n        \"Document.Document.useImperativeHandle\": ()=>({\n                linkService,\n                pages,\n                viewer\n            })\n    }[\"Document.Document.useImperativeHandle\"], []);\n    /**\n     * Called when a document source is resolved correctly\n     */ function onSourceSuccess() {\n        if (onSourceSuccessProps) {\n            onSourceSuccessProps();\n        }\n    }\n    /**\n     * Called when a document source failed to be resolved correctly\n     */ function onSourceError() {\n        if (!sourceError) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        warning__WEBPACK_IMPORTED_MODULE_4__(false, sourceError.toString());\n        if (onSourceErrorProps) {\n            onSourceErrorProps(sourceError);\n        }\n    }\n    function resetSource() {\n        sourceDispatch({\n            type: 'RESET'\n        });\n    }\n    // biome-ignore lint/correctness/useExhaustiveDependencies: See https://github.com/biomejs/biome/issues/3080\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(resetSource, [\n        file,\n        sourceDispatch\n    ]);\n    const findDocumentSource = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Document.Document.useCallback[findDocumentSource]\": ()=>__awaiter(this, void 0, void 0, {\n                \"Document.Document.useCallback[findDocumentSource]\": function*() {\n                    if (!file) {\n                        return null;\n                    }\n                    // File is a string\n                    if (typeof file === 'string') {\n                        if ((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_10__.isDataURI)(file)) {\n                            const fileByteString = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_10__.dataURItoByteString)(file);\n                            return {\n                                data: fileByteString\n                            };\n                        }\n                        (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_10__.displayCORSWarning)();\n                        return {\n                            url: file\n                        };\n                    }\n                    // File is PDFDataRangeTransport\n                    if (file instanceof PDFDataRangeTransport) {\n                        return {\n                            range: file\n                        };\n                    }\n                    // File is an ArrayBuffer\n                    if ((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_10__.isArrayBuffer)(file)) {\n                        return {\n                            data: file\n                        };\n                    }\n                    /**\n         * The cases below are browser-only.\n         * If you're running on a non-browser environment, these cases will be of no use.\n         */ if (_shared_utils_js__WEBPACK_IMPORTED_MODULE_10__.isBrowser) {\n                        // File is a Blob\n                        if ((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_10__.isBlob)(file)) {\n                            const data = yield (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_10__.loadFromFile)(file);\n                            return {\n                                data\n                            };\n                        }\n                    }\n                    // At this point, file must be an object\n                    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(typeof file === 'object', 'Invalid parameter in file, need either Uint8Array, string or a parameter object');\n                    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(isParameterObject(file), 'Invalid parameter object: need either .data, .range or .url');\n                    // File .url is a string\n                    if ('url' in file && typeof file.url === 'string') {\n                        if ((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_10__.isDataURI)(file.url)) {\n                            const { url } = file, otherParams = __rest(file, [\n                                \"url\"\n                            ]);\n                            const fileByteString = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_10__.dataURItoByteString)(url);\n                            return Object.assign({\n                                data: fileByteString\n                            }, otherParams);\n                        }\n                        (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_10__.displayCORSWarning)();\n                    }\n                    return file;\n                }\n            }[\"Document.Document.useCallback[findDocumentSource]\"])\n    }[\"Document.Document.useCallback[findDocumentSource]\"], [\n        file\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Document.Document.useEffect\": ()=>{\n            const cancellable = (0,make_cancellable_promise__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(findDocumentSource());\n            cancellable.promise.then({\n                \"Document.Document.useEffect\": (nextSource)=>{\n                    sourceDispatch({\n                        type: 'RESOLVE',\n                        value: nextSource\n                    });\n                }\n            }[\"Document.Document.useEffect\"]).catch({\n                \"Document.Document.useEffect\": (error)=>{\n                    sourceDispatch({\n                        type: 'REJECT',\n                        error\n                    });\n                }\n            }[\"Document.Document.useEffect\"]);\n            return ({\n                \"Document.Document.useEffect\": ()=>{\n                    (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_10__.cancelRunningTask)(cancellable);\n                }\n            })[\"Document.Document.useEffect\"];\n        }\n    }[\"Document.Document.useEffect\"], [\n        findDocumentSource,\n        sourceDispatch\n    ]);\n    // biome-ignore lint/correctness/useExhaustiveDependencies: Ommitted callbacks so they are not called every time they change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Document.Document.useEffect\": ()=>{\n            if (typeof source === 'undefined') {\n                return;\n            }\n            if (source === false) {\n                onSourceError();\n                return;\n            }\n            onSourceSuccess();\n        }\n    }[\"Document.Document.useEffect\"], [\n        source\n    ]);\n    /**\n     * Called when a document is read successfully\n     */ function onLoadSuccess() {\n        if (!pdf) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        if (onLoadSuccessProps) {\n            onLoadSuccessProps(pdf);\n        }\n        pages.current = new Array(pdf.numPages);\n        linkService.current.setDocument(pdf);\n    }\n    /**\n     * Called when a document failed to read successfully\n     */ function onLoadError() {\n        if (!pdfError) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        warning__WEBPACK_IMPORTED_MODULE_4__(false, pdfError.toString());\n        if (onLoadErrorProps) {\n            onLoadErrorProps(pdfError);\n        }\n    }\n    // biome-ignore lint/correctness/useExhaustiveDependencies: useEffect intentionally triggered on source change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function resetDocument() {\n        pdfDispatch({\n            type: 'RESET'\n        });\n    }, [\n        pdfDispatch,\n        source\n    ]);\n    // biome-ignore lint/correctness/useExhaustiveDependencies: Ommitted callbacks so they are not called every time they change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function loadDocument() {\n        if (!source) {\n            return;\n        }\n        const documentInitParams = options ? Object.assign(Object.assign({}, source), options) : source;\n        const destroyable = pdfjs_dist__WEBPACK_IMPORTED_MODULE_6__.getDocument(documentInitParams);\n        if (onLoadProgress) {\n            destroyable.onProgress = onLoadProgress;\n        }\n        if (onPassword) {\n            destroyable.onPassword = onPassword;\n        }\n        const loadingTask = destroyable;\n        const loadingPromise = loadingTask.promise.then({\n            \"Document.Document.useEffect.loadDocument.loadingPromise\": (nextPdf)=>{\n                pdfDispatch({\n                    type: 'RESOLVE',\n                    value: nextPdf\n                });\n            }\n        }[\"Document.Document.useEffect.loadDocument.loadingPromise\"]).catch({\n            \"Document.Document.useEffect.loadDocument.loadingPromise\": (error)=>{\n                if (loadingTask.destroyed) {\n                    return;\n                }\n                pdfDispatch({\n                    type: 'REJECT',\n                    error\n                });\n            }\n        }[\"Document.Document.useEffect.loadDocument.loadingPromise\"]);\n        return ({\n            \"Document.Document.useEffect.loadDocument\": ()=>{\n                loadingPromise.finally({\n                    \"Document.Document.useEffect.loadDocument\": ()=>loadingTask.destroy()\n                }[\"Document.Document.useEffect.loadDocument\"]);\n            }\n        })[\"Document.Document.useEffect.loadDocument\"];\n    }, [\n        options,\n        pdfDispatch,\n        source\n    ]);\n    // biome-ignore lint/correctness/useExhaustiveDependencies: Ommitted callbacks so they are not called every time they change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Document.Document.useEffect\": ()=>{\n            if (typeof pdf === 'undefined') {\n                return;\n            }\n            if (pdf === false) {\n                onLoadError();\n                return;\n            }\n            onLoadSuccess();\n        }\n    }[\"Document.Document.useEffect\"], [\n        pdf\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function setupLinkService() {\n        linkService.current.setViewer(viewer.current);\n        linkService.current.setExternalLinkRel(externalLinkRel);\n        linkService.current.setExternalLinkTarget(externalLinkTarget);\n    }, [\n        externalLinkRel,\n        externalLinkTarget\n    ]);\n    const registerPage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Document.Document.useCallback[registerPage]\": (pageIndex, ref)=>{\n            pages.current[pageIndex] = ref;\n        }\n    }[\"Document.Document.useCallback[registerPage]\"], []);\n    const unregisterPage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Document.Document.useCallback[unregisterPage]\": (pageIndex)=>{\n            delete pages.current[pageIndex];\n        }\n    }[\"Document.Document.useCallback[unregisterPage]\"], []);\n    const childContext = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"Document.Document.useMemo[childContext]\": ()=>({\n                imageResourcesPath,\n                linkService: linkService.current,\n                onItemClick,\n                pdf,\n                registerPage,\n                renderMode,\n                rotate,\n                unregisterPage\n            })\n    }[\"Document.Document.useMemo[childContext]\"], [\n        imageResourcesPath,\n        onItemClick,\n        pdf,\n        registerPage,\n        renderMode,\n        rotate,\n        unregisterPage\n    ]);\n    const eventProps = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"Document.Document.useMemo[eventProps]\": ()=>(0,make_event_props__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(otherProps, {\n                \"Document.Document.useMemo[eventProps]\": ()=>pdf\n            }[\"Document.Document.useMemo[eventProps]\"])\n    }[\"Document.Document.useMemo[eventProps]\"], // biome-ignore lint/correctness/useExhaustiveDependencies: FIXME\n    [\n        otherProps,\n        pdf\n    ]);\n    function renderChildren() {\n        return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_DocumentContext_js__WEBPACK_IMPORTED_MODULE_13__[\"default\"].Provider, {\n            value: childContext,\n            children: children\n        });\n    }\n    function renderContent() {\n        if (!file) {\n            return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_Message_js__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                type: \"no-data\",\n                children: typeof noData === 'function' ? noData() : noData\n            });\n        }\n        if (pdf === undefined || pdf === null) {\n            return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_Message_js__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                type: \"loading\",\n                children: typeof loading === 'function' ? loading() : loading\n            });\n        }\n        if (pdf === false) {\n            return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_Message_js__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                type: \"error\",\n                children: typeof error === 'function' ? error() : error\n            });\n        }\n        return renderChildren();\n    }\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", Object.assign({\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__[\"default\"])('react-pdf__Document', className),\n        // Assertion is needed for React 18 compatibility\n        ref: inputRef,\n        style: {\n            ['--scale-factor']: '1'\n        }\n    }, eventProps, {\n        children: renderContent()\n    }));\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Document);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/Document.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/DocumentContext.js":
/*!************************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/DocumentContext.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nconst documentContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(null);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (documentContext);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcGRmL2Rpc3QvZXNtL0RvY3VtZW50Q29udGV4dC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs2REFDc0M7QUFDdEMsTUFBTUMsZ0NBQWtCRCxvREFBYUEsQ0FBQztBQUN0QyxpRUFBZUMsZUFBZUEsRUFBQyIsInNvdXJjZXMiOlsiRDpcXHNvZnRcXFNlYVByb2plY3RcXGdpdGh1Yl9vcGVuX3Byb2plY3Rfc3VjY2Vzc1xcc3VuYVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xccmVhY3QtcGRmXFxkaXN0XFxlc21cXERvY3VtZW50Q29udGV4dC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5pbXBvcnQgeyBjcmVhdGVDb250ZXh0IH0gZnJvbSAncmVhY3QnO1xuY29uc3QgZG9jdW1lbnRDb250ZXh0ID0gY3JlYXRlQ29udGV4dChudWxsKTtcbmV4cG9ydCBkZWZhdWx0IGRvY3VtZW50Q29udGV4dDtcbiJdLCJuYW1lcyI6WyJjcmVhdGVDb250ZXh0IiwiZG9jdW1lbnRDb250ZXh0Il0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/DocumentContext.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/LinkService.js":
/*!********************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/LinkService.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LinkService)\n/* harmony export */ });\n/* harmony import */ var tiny_invariant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tiny-invariant */ \"(ssr)/./node_modules/tiny-invariant/dist/esm/tiny-invariant.js\");\n/* Copyright 2015 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nconst DEFAULT_LINK_REL = 'noopener noreferrer nofollow';\nclass LinkService {\n    constructor() {\n        this.externalLinkEnabled = true;\n        this.externalLinkRel = undefined;\n        this.externalLinkTarget = undefined;\n        this.isInPresentationMode = false;\n        this.pdfDocument = undefined;\n        this.pdfViewer = undefined;\n    }\n    setDocument(pdfDocument) {\n        this.pdfDocument = pdfDocument;\n    }\n    setViewer(pdfViewer) {\n        this.pdfViewer = pdfViewer;\n    }\n    setExternalLinkRel(externalLinkRel) {\n        this.externalLinkRel = externalLinkRel;\n    }\n    setExternalLinkTarget(externalLinkTarget) {\n        this.externalLinkTarget = externalLinkTarget;\n    }\n    setHistory() {\n        // Intentionally empty\n    }\n    get pagesCount() {\n        return this.pdfDocument ? this.pdfDocument.numPages : 0;\n    }\n    get page() {\n        (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this.pdfViewer, 'PDF viewer is not initialized.');\n        return this.pdfViewer.currentPageNumber || 0;\n    }\n    set page(value) {\n        (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this.pdfViewer, 'PDF viewer is not initialized.');\n        this.pdfViewer.currentPageNumber = value;\n    }\n    get rotation() {\n        return 0;\n    }\n    set rotation(_value) {\n        // Intentionally empty\n    }\n    goToDestination(dest) {\n        return new Promise((resolve) => {\n            (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this.pdfDocument, 'PDF document not loaded.');\n            (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(dest, 'Destination is not specified.');\n            if (typeof dest === 'string') {\n                this.pdfDocument.getDestination(dest).then(resolve);\n            }\n            else if (Array.isArray(dest)) {\n                resolve(dest);\n            }\n            else {\n                dest.then(resolve);\n            }\n        }).then((explicitDest) => {\n            (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(Array.isArray(explicitDest), `\"${explicitDest}\" is not a valid destination array.`);\n            const destRef = explicitDest[0];\n            new Promise((resolve) => {\n                (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this.pdfDocument, 'PDF document not loaded.');\n                if (destRef instanceof Object) {\n                    this.pdfDocument\n                        .getPageIndex(destRef)\n                        .then((pageIndex) => {\n                        resolve(pageIndex);\n                    })\n                        .catch(() => {\n                        (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(false, `\"${destRef}\" is not a valid page reference.`);\n                    });\n                }\n                else if (typeof destRef === 'number') {\n                    resolve(destRef);\n                }\n                else {\n                    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(false, `\"${destRef}\" is not a valid destination reference.`);\n                }\n            }).then((pageIndex) => {\n                const pageNumber = pageIndex + 1;\n                (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this.pdfViewer, 'PDF viewer is not initialized.');\n                (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(pageNumber >= 1 && pageNumber <= this.pagesCount, `\"${pageNumber}\" is not a valid page number.`);\n                this.pdfViewer.scrollPageIntoView({\n                    dest: explicitDest,\n                    pageIndex,\n                    pageNumber,\n                });\n            });\n        });\n    }\n    navigateTo(dest) {\n        this.goToDestination(dest);\n    }\n    goToPage(pageNumber) {\n        const pageIndex = pageNumber - 1;\n        (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this.pdfViewer, 'PDF viewer is not initialized.');\n        (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(pageNumber >= 1 && pageNumber <= this.pagesCount, `\"${pageNumber}\" is not a valid page number.`);\n        this.pdfViewer.scrollPageIntoView({\n            pageIndex,\n            pageNumber,\n        });\n    }\n    addLinkAttributes(link, url, newWindow) {\n        link.href = url;\n        link.rel = this.externalLinkRel || DEFAULT_LINK_REL;\n        link.target = newWindow ? '_blank' : this.externalLinkTarget || '';\n    }\n    getDestinationHash() {\n        return '#';\n    }\n    getAnchorUrl() {\n        return '#';\n    }\n    setHash() {\n        // Intentionally empty\n    }\n    executeNamedAction() {\n        // Intentionally empty\n    }\n    cachePageRef() {\n        // Intentionally empty\n    }\n    isPageVisible() {\n        return true;\n    }\n    isPageCached() {\n        return true;\n    }\n    executeSetOCGState() {\n        // Intentionally empty\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/LinkService.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/Message.js":
/*!****************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/Message.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Message)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\nfunction Message({ children, type }) {\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", { className: `react-pdf__message react-pdf__message--${type}`, children: children });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcGRmL2Rpc3QvZXNtL01lc3NhZ2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBZ0Q7QUFDakMsbUJBQW1CLGdCQUFnQjtBQUNsRCxXQUFXLHNEQUFJLFVBQVUscURBQXFELEtBQUssdUJBQXVCO0FBQzFHIiwic291cmNlcyI6WyJEOlxcc29mdFxcU2VhUHJvamVjdFxcZ2l0aHViX29wZW5fcHJvamVjdF9zdWNjZXNzXFxzdW5hXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxyZWFjdC1wZGZcXGRpc3RcXGVzbVxcTWVzc2FnZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBqc3ggYXMgX2pzeCB9IGZyb20gXCJyZWFjdC9qc3gtcnVudGltZVwiO1xuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTWVzc2FnZSh7IGNoaWxkcmVuLCB0eXBlIH0pIHtcbiAgICByZXR1cm4gX2pzeChcImRpdlwiLCB7IGNsYXNzTmFtZTogYHJlYWN0LXBkZl9fbWVzc2FnZSByZWFjdC1wZGZfX21lc3NhZ2UtLSR7dHlwZX1gLCBjaGlsZHJlbjogY2hpbGRyZW4gfSk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/Message.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/Page.js":
/*!*************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/Page.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Page)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var make_cancellable_promise__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! make-cancellable-promise */ \"(ssr)/./node_modules/make-cancellable-promise/dist/esm/index.js\");\n/* harmony import */ var make_event_props__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! make-event-props */ \"(ssr)/./node_modules/make-event-props/dist/esm/index.js\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var merge_refs__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! merge-refs */ \"(ssr)/./node_modules/merge-refs/dist/esm/index.js\");\n/* harmony import */ var tiny_invariant__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! tiny-invariant */ \"(ssr)/./node_modules/tiny-invariant/dist/esm/tiny-invariant.js\");\n/* harmony import */ var warning__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! warning */ \"(ssr)/./node_modules/warning/warning.js\");\n/* harmony import */ var _PageContext_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./PageContext.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/PageContext.js\");\n/* harmony import */ var _Message_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./Message.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/Message.js\");\n/* harmony import */ var _Page_Canvas_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./Page/Canvas.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/Page/Canvas.js\");\n/* harmony import */ var _Page_TextLayer_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./Page/TextLayer.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/Page/TextLayer.js\");\n/* harmony import */ var _Page_AnnotationLayer_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./Page/AnnotationLayer.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/Page/AnnotationLayer.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./shared/utils.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/utils.js\");\n/* harmony import */ var _shared_hooks_useDocumentContext_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./shared/hooks/useDocumentContext.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/hooks/useDocumentContext.js\");\n/* harmony import */ var _shared_hooks_useResolver_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./shared/hooks/useResolver.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/hooks/useResolver.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ var __rest = undefined && undefined.__rest || function(s, e) {\n    var t = {};\n    for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for(var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++){\n        if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n    }\n    return t;\n};\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst defaultScale = 1;\n/**\n * Displays a page.\n *\n * Should be placed inside `<Document />`. Alternatively, it can have `pdf` prop passed, which can be obtained from `<Document />`'s `onLoadSuccess` callback function, however some advanced functions like linking between pages inside a document may not be working correctly.\n */ function Page(props) {\n    const documentContext = (0,_shared_hooks_useDocumentContext_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    const mergedProps = Object.assign(Object.assign({}, documentContext), props);\n    const { _className = 'react-pdf__Page', _enableRegisterUnregisterPage = true, canvasBackground, canvasRef, children, className, customRenderer: CustomRenderer, customTextRenderer, devicePixelRatio, error = 'Failed to load the page.', height, inputRef, loading = 'Loading page…', noData = 'No page specified.', onGetAnnotationsError: onGetAnnotationsErrorProps, onGetAnnotationsSuccess: onGetAnnotationsSuccessProps, onGetStructTreeError: onGetStructTreeErrorProps, onGetStructTreeSuccess: onGetStructTreeSuccessProps, onGetTextError: onGetTextErrorProps, onGetTextSuccess: onGetTextSuccessProps, onLoadError: onLoadErrorProps, onLoadSuccess: onLoadSuccessProps, onRenderAnnotationLayerError: onRenderAnnotationLayerErrorProps, onRenderAnnotationLayerSuccess: onRenderAnnotationLayerSuccessProps, onRenderError: onRenderErrorProps, onRenderSuccess: onRenderSuccessProps, onRenderTextLayerError: onRenderTextLayerErrorProps, onRenderTextLayerSuccess: onRenderTextLayerSuccessProps, pageIndex: pageIndexProps, pageNumber: pageNumberProps, pdf, registerPage, renderAnnotationLayer: renderAnnotationLayerProps = true, renderForms = false, renderMode = 'canvas', renderTextLayer: renderTextLayerProps = true, rotate: rotateProps, scale: scaleProps = defaultScale, unregisterPage, width } = mergedProps, otherProps = __rest(mergedProps, [\n        \"_className\",\n        \"_enableRegisterUnregisterPage\",\n        \"canvasBackground\",\n        \"canvasRef\",\n        \"children\",\n        \"className\",\n        \"customRenderer\",\n        \"customTextRenderer\",\n        \"devicePixelRatio\",\n        \"error\",\n        \"height\",\n        \"inputRef\",\n        \"loading\",\n        \"noData\",\n        \"onGetAnnotationsError\",\n        \"onGetAnnotationsSuccess\",\n        \"onGetStructTreeError\",\n        \"onGetStructTreeSuccess\",\n        \"onGetTextError\",\n        \"onGetTextSuccess\",\n        \"onLoadError\",\n        \"onLoadSuccess\",\n        \"onRenderAnnotationLayerError\",\n        \"onRenderAnnotationLayerSuccess\",\n        \"onRenderError\",\n        \"onRenderSuccess\",\n        \"onRenderTextLayerError\",\n        \"onRenderTextLayerSuccess\",\n        \"pageIndex\",\n        \"pageNumber\",\n        \"pdf\",\n        \"registerPage\",\n        \"renderAnnotationLayer\",\n        \"renderForms\",\n        \"renderMode\",\n        \"renderTextLayer\",\n        \"rotate\",\n        \"scale\",\n        \"unregisterPage\",\n        \"width\"\n    ]);\n    const [pageState, pageDispatch] = (0,_shared_hooks_useResolver_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])();\n    const { value: page, error: pageError } = pageState;\n    const pageElement = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(pdf, 'Attempted to load a page, but no document was specified. Wrap <Page /> in a <Document /> or pass explicit `pdf` prop.');\n    const pageIndex = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_7__.isProvided)(pageNumberProps) ? pageNumberProps - 1 : pageIndexProps !== null && pageIndexProps !== void 0 ? pageIndexProps : null;\n    const pageNumber = pageNumberProps !== null && pageNumberProps !== void 0 ? pageNumberProps : (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_7__.isProvided)(pageIndexProps) ? pageIndexProps + 1 : null;\n    const rotate = rotateProps !== null && rotateProps !== void 0 ? rotateProps : page ? page.rotate : null;\n    const scale = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"Page.useMemo[scale]\": ()=>{\n            if (!page) {\n                return null;\n            }\n            // Be default, we'll render page at 100% * scale width.\n            let pageScale = 1;\n            // Passing scale explicitly null would cause the page not to render\n            const scaleWithDefault = scaleProps !== null && scaleProps !== void 0 ? scaleProps : defaultScale;\n            // If width/height is defined, calculate the scale of the page so it could be of desired width.\n            if (width || height) {\n                const viewport = page.getViewport({\n                    scale: 1,\n                    rotation: rotate\n                });\n                if (width) {\n                    pageScale = width / viewport.width;\n                } else if (height) {\n                    pageScale = height / viewport.height;\n                }\n            }\n            return scaleWithDefault * pageScale;\n        }\n    }[\"Page.useMemo[scale]\"], [\n        height,\n        page,\n        rotate,\n        scaleProps,\n        width\n    ]);\n    // biome-ignore lint/correctness/useExhaustiveDependencies: useEffect intentionally triggered on pdf change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function hook() {\n        return ({\n            \"Page.useEffect.hook\": ()=>{\n                if (!(0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_7__.isProvided)(pageIndex)) {\n                    // Impossible, but TypeScript doesn't know that\n                    return;\n                }\n                if (_enableRegisterUnregisterPage && unregisterPage) {\n                    unregisterPage(pageIndex);\n                }\n            }\n        })[\"Page.useEffect.hook\"];\n    }, [\n        _enableRegisterUnregisterPage,\n        pdf,\n        pageIndex,\n        unregisterPage\n    ]);\n    /**\n     * Called when a page is loaded successfully\n     */ function onLoadSuccess() {\n        if (onLoadSuccessProps) {\n            if (!page || !scale) {\n                // Impossible, but TypeScript doesn't know that\n                return;\n            }\n            onLoadSuccessProps((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_7__.makePageCallback)(page, scale));\n        }\n        if (_enableRegisterUnregisterPage && registerPage) {\n            if (!(0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_7__.isProvided)(pageIndex) || !pageElement.current) {\n                // Impossible, but TypeScript doesn't know that\n                return;\n            }\n            registerPage(pageIndex, pageElement.current);\n        }\n    }\n    /**\n     * Called when a page failed to load\n     */ function onLoadError() {\n        if (!pageError) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        warning__WEBPACK_IMPORTED_MODULE_4__(false, pageError.toString());\n        if (onLoadErrorProps) {\n            onLoadErrorProps(pageError);\n        }\n    }\n    // biome-ignore lint/correctness/useExhaustiveDependencies: useEffect intentionally triggered on pdf and pageIndex change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function resetPage() {\n        pageDispatch({\n            type: 'RESET'\n        });\n    }, [\n        pageDispatch,\n        pdf,\n        pageIndex\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function loadPage() {\n        if (!pdf || !pageNumber) {\n            return;\n        }\n        const cancellable = (0,make_cancellable_promise__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(pdf.getPage(pageNumber));\n        const runningTask = cancellable;\n        cancellable.promise.then({\n            \"Page.useEffect.loadPage\": (nextPage)=>{\n                pageDispatch({\n                    type: 'RESOLVE',\n                    value: nextPage\n                });\n            }\n        }[\"Page.useEffect.loadPage\"]).catch({\n            \"Page.useEffect.loadPage\": (error)=>{\n                pageDispatch({\n                    type: 'REJECT',\n                    error\n                });\n            }\n        }[\"Page.useEffect.loadPage\"]);\n        return ({\n            \"Page.useEffect.loadPage\": ()=>(0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_7__.cancelRunningTask)(runningTask)\n        })[\"Page.useEffect.loadPage\"];\n    }, [\n        pageDispatch,\n        pdf,\n        pageNumber\n    ]);\n    // biome-ignore lint/correctness/useExhaustiveDependencies: Ommitted callbacks so they are not called every time they change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Page.useEffect\": ()=>{\n            if (page === undefined) {\n                return;\n            }\n            if (page === false) {\n                onLoadError();\n                return;\n            }\n            onLoadSuccess();\n        }\n    }[\"Page.useEffect\"], [\n        page,\n        scale\n    ]);\n    const childContext = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"Page.useMemo[childContext]\": ()=>// Technically there cannot be page without pageIndex, pageNumber, rotate and scale, but TypeScript doesn't know that\n            page && (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_7__.isProvided)(pageIndex) && pageNumber && (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_7__.isProvided)(rotate) && (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_7__.isProvided)(scale) ? {\n                _className,\n                canvasBackground,\n                customTextRenderer,\n                devicePixelRatio,\n                onGetAnnotationsError: onGetAnnotationsErrorProps,\n                onGetAnnotationsSuccess: onGetAnnotationsSuccessProps,\n                onGetStructTreeError: onGetStructTreeErrorProps,\n                onGetStructTreeSuccess: onGetStructTreeSuccessProps,\n                onGetTextError: onGetTextErrorProps,\n                onGetTextSuccess: onGetTextSuccessProps,\n                onRenderAnnotationLayerError: onRenderAnnotationLayerErrorProps,\n                onRenderAnnotationLayerSuccess: onRenderAnnotationLayerSuccessProps,\n                onRenderError: onRenderErrorProps,\n                onRenderSuccess: onRenderSuccessProps,\n                onRenderTextLayerError: onRenderTextLayerErrorProps,\n                onRenderTextLayerSuccess: onRenderTextLayerSuccessProps,\n                page,\n                pageIndex,\n                pageNumber,\n                renderForms,\n                renderTextLayer: renderTextLayerProps,\n                rotate,\n                scale\n            } : null\n    }[\"Page.useMemo[childContext]\"], [\n        _className,\n        canvasBackground,\n        customTextRenderer,\n        devicePixelRatio,\n        onGetAnnotationsErrorProps,\n        onGetAnnotationsSuccessProps,\n        onGetStructTreeErrorProps,\n        onGetStructTreeSuccessProps,\n        onGetTextErrorProps,\n        onGetTextSuccessProps,\n        onRenderAnnotationLayerErrorProps,\n        onRenderAnnotationLayerSuccessProps,\n        onRenderErrorProps,\n        onRenderSuccessProps,\n        onRenderTextLayerErrorProps,\n        onRenderTextLayerSuccessProps,\n        page,\n        pageIndex,\n        pageNumber,\n        renderForms,\n        renderTextLayerProps,\n        rotate,\n        scale\n    ]);\n    const eventProps = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"Page.useMemo[eventProps]\": ()=>(0,make_event_props__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(otherProps, {\n                \"Page.useMemo[eventProps]\": ()=>page ? scale ? (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_7__.makePageCallback)(page, scale) : undefined : page\n            }[\"Page.useMemo[eventProps]\"])\n    }[\"Page.useMemo[eventProps]\"], // biome-ignore lint/correctness/useExhaustiveDependencies: FIXME\n    [\n        otherProps,\n        page,\n        scale\n    ]);\n    const pageKey = `${pageIndex}@${scale}/${rotate}`;\n    function renderMainLayer() {\n        switch(renderMode){\n            case 'custom':\n                {\n                    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(CustomRenderer, `renderMode was set to \"custom\", but no customRenderer was passed.`);\n                    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(CustomRenderer, {}, `${pageKey}_custom`);\n                }\n            case 'none':\n                return null;\n            case 'canvas':\n            default:\n                return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_Page_Canvas_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    canvasRef: canvasRef\n                }, `${pageKey}_canvas`);\n        }\n    }\n    function renderTextLayer() {\n        if (!renderTextLayerProps) {\n            return null;\n        }\n        return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_Page_TextLayer_js__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, `${pageKey}_text`);\n    }\n    function renderAnnotationLayer() {\n        if (!renderAnnotationLayerProps) {\n            return null;\n        }\n        return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_Page_AnnotationLayer_js__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, `${pageKey}_annotations`);\n    }\n    function renderChildren() {\n        return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_PageContext_js__WEBPACK_IMPORTED_MODULE_13__[\"default\"].Provider, {\n            value: childContext,\n            children: [\n                renderMainLayer(),\n                renderTextLayer(),\n                renderAnnotationLayer(),\n                children\n            ]\n        });\n    }\n    function renderContent() {\n        if (!pageNumber) {\n            return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_Message_js__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                type: \"no-data\",\n                children: typeof noData === 'function' ? noData() : noData\n            });\n        }\n        if (pdf === null || page === undefined || page === null) {\n            return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_Message_js__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                type: \"loading\",\n                children: typeof loading === 'function' ? loading() : loading\n            });\n        }\n        if (pdf === false || page === false) {\n            return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_Message_js__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                type: \"error\",\n                children: typeof error === 'function' ? error() : error\n            });\n        }\n        return renderChildren();\n    }\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", Object.assign({\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_className, className),\n        \"data-page-number\": pageNumber,\n        // Assertion is needed for React 18 compatibility\n        ref: (0,merge_refs__WEBPACK_IMPORTED_MODULE_15__[\"default\"])(inputRef, pageElement),\n        style: {\n            ['--scale-factor']: `${scale}`,\n            backgroundColor: canvasBackground || 'white',\n            position: 'relative',\n            minWidth: 'min-content',\n            minHeight: 'min-content'\n        }\n    }, eventProps, {\n        children: renderContent()\n    }));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/Page.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/Page/AnnotationLayer.css":
/*!******************************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/Page/AnnotationLayer.css ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"acc6f0c1f165\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcGRmL2Rpc3QvZXNtL1BhZ2UvQW5ub3RhdGlvbkxheWVyLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLEtBQVUsRUFBRSxFQUF1QiIsInNvdXJjZXMiOlsiRDpcXHNvZnRcXFNlYVByb2plY3RcXGdpdGh1Yl9vcGVuX3Byb2plY3Rfc3VjY2Vzc1xcc3VuYVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xccmVhY3QtcGRmXFxkaXN0XFxlc21cXFBhZ2VcXEFubm90YXRpb25MYXllci5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJhY2M2ZjBjMWYxNjVcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/Page/AnnotationLayer.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/Page/AnnotationLayer.js":
/*!*****************************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/Page/AnnotationLayer.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AnnotationLayer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var make_cancellable_promise__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! make-cancellable-promise */ \"(ssr)/./node_modules/make-cancellable-promise/dist/esm/index.js\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tiny_invariant__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! tiny-invariant */ \"(ssr)/./node_modules/tiny-invariant/dist/esm/tiny-invariant.js\");\n/* harmony import */ var warning__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! warning */ \"(ssr)/./node_modules/warning/warning.js\");\n/* harmony import */ var pdfjs_dist__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! pdfjs-dist */ \"(ssr)/./node_modules/pdfjs-dist/build/pdf.mjs\");\n/* harmony import */ var _shared_hooks_useDocumentContext_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../shared/hooks/useDocumentContext.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/hooks/useDocumentContext.js\");\n/* harmony import */ var _shared_hooks_usePageContext_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../shared/hooks/usePageContext.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/hooks/usePageContext.js\");\n/* harmony import */ var _shared_hooks_useResolver_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../shared/hooks/useResolver.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/hooks/useResolver.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../shared/utils.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/utils.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\nfunction AnnotationLayer() {\n    const documentContext = (0,_shared_hooks_useDocumentContext_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])();\n    const pageContext = (0,_shared_hooks_usePageContext_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])();\n    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(pageContext, 'Unable to find Page context.');\n    const mergedProps = Object.assign(Object.assign({}, documentContext), pageContext);\n    const { imageResourcesPath, linkService, onGetAnnotationsError: onGetAnnotationsErrorProps, onGetAnnotationsSuccess: onGetAnnotationsSuccessProps, onRenderAnnotationLayerError: onRenderAnnotationLayerErrorProps, onRenderAnnotationLayerSuccess: onRenderAnnotationLayerSuccessProps, page, pdf, renderForms, rotate, scale = 1 } = mergedProps;\n    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(pdf, 'Attempted to load page annotations, but no document was specified. Wrap <Page /> in a <Document /> or pass explicit `pdf` prop.');\n    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(page, 'Attempted to load page annotations, but no page was specified.');\n    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(linkService, 'Attempted to load page annotations, but no linkService was specified.');\n    const [annotationsState, annotationsDispatch] = (0,_shared_hooks_useResolver_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"])();\n    const { value: annotations, error: annotationsError } = annotationsState;\n    const layerElement = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    warning__WEBPACK_IMPORTED_MODULE_4__(Number.parseInt(window.getComputedStyle(document.body).getPropertyValue('--react-pdf-annotation-layer'), 10) === 1, 'AnnotationLayer styles not found. Read more: https://github.com/wojtekmaj/react-pdf#support-for-annotations');\n    function onLoadSuccess() {\n        if (!annotations) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        if (onGetAnnotationsSuccessProps) {\n            onGetAnnotationsSuccessProps(annotations);\n        }\n    }\n    function onLoadError() {\n        if (!annotationsError) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        warning__WEBPACK_IMPORTED_MODULE_4__(false, annotationsError.toString());\n        if (onGetAnnotationsErrorProps) {\n            onGetAnnotationsErrorProps(annotationsError);\n        }\n    }\n    // biome-ignore lint/correctness/useExhaustiveDependencies: useEffect intentionally triggered on page change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function resetAnnotations() {\n        annotationsDispatch({\n            type: 'RESET'\n        });\n    }, [\n        annotationsDispatch,\n        page\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function loadAnnotations() {\n        if (!page) {\n            return;\n        }\n        const cancellable = (0,make_cancellable_promise__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(page.getAnnotations());\n        const runningTask = cancellable;\n        cancellable.promise.then({\n            \"AnnotationLayer.useEffect.loadAnnotations\": (nextAnnotations)=>{\n                annotationsDispatch({\n                    type: 'RESOLVE',\n                    value: nextAnnotations\n                });\n            }\n        }[\"AnnotationLayer.useEffect.loadAnnotations\"]).catch({\n            \"AnnotationLayer.useEffect.loadAnnotations\": (error)=>{\n                annotationsDispatch({\n                    type: 'REJECT',\n                    error\n                });\n            }\n        }[\"AnnotationLayer.useEffect.loadAnnotations\"]);\n        return ({\n            \"AnnotationLayer.useEffect.loadAnnotations\": ()=>{\n                (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_10__.cancelRunningTask)(runningTask);\n            }\n        })[\"AnnotationLayer.useEffect.loadAnnotations\"];\n    }, [\n        annotationsDispatch,\n        page\n    ]);\n    // biome-ignore lint/correctness/useExhaustiveDependencies: Ommitted callbacks so they are not called every time they change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AnnotationLayer.useEffect\": ()=>{\n            if (annotations === undefined) {\n                return;\n            }\n            if (annotations === false) {\n                onLoadError();\n                return;\n            }\n            onLoadSuccess();\n        }\n    }[\"AnnotationLayer.useEffect\"], [\n        annotations\n    ]);\n    function onRenderSuccess() {\n        if (onRenderAnnotationLayerSuccessProps) {\n            onRenderAnnotationLayerSuccessProps();\n        }\n    }\n    function onRenderError(error) {\n        warning__WEBPACK_IMPORTED_MODULE_4__(false, `${error}`);\n        if (onRenderAnnotationLayerErrorProps) {\n            onRenderAnnotationLayerErrorProps(error);\n        }\n    }\n    const viewport = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"AnnotationLayer.useMemo[viewport]\": ()=>page.getViewport({\n                scale,\n                rotation: rotate\n            })\n    }[\"AnnotationLayer.useMemo[viewport]\"], [\n        page,\n        rotate,\n        scale\n    ]);\n    // biome-ignore lint/correctness/useExhaustiveDependencies: Ommitted callbacks so they are not called every time they change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function renderAnnotationLayer() {\n        if (!pdf || !page || !linkService || !annotations) {\n            return;\n        }\n        const { current: layer } = layerElement;\n        if (!layer) {\n            return;\n        }\n        const clonedViewport = viewport.clone({\n            dontFlip: true\n        });\n        const annotationLayerParameters = {\n            accessibilityManager: null,\n            annotationCanvasMap: null,\n            annotationEditorUIManager: null,\n            div: layer,\n            l10n: null,\n            page,\n            structTreeLayer: null,\n            viewport: clonedViewport\n        };\n        const renderParameters = {\n            annotations,\n            annotationStorage: pdf.annotationStorage,\n            div: layer,\n            imageResourcesPath,\n            linkService,\n            page,\n            renderForms,\n            viewport: clonedViewport\n        };\n        layer.innerHTML = '';\n        try {\n            new pdfjs_dist__WEBPACK_IMPORTED_MODULE_5__.AnnotationLayer(annotationLayerParameters).render(renderParameters);\n            // Intentional immediate callback\n            onRenderSuccess();\n        } catch (error) {\n            onRenderError(error);\n        }\n        return ({\n            \"AnnotationLayer.useEffect.renderAnnotationLayer\": ()=>{\n            // TODO: Cancel running task?\n            }\n        })[\"AnnotationLayer.useEffect.renderAnnotationLayer\"];\n    }, [\n        annotations,\n        imageResourcesPath,\n        linkService,\n        page,\n        pdf,\n        renderForms,\n        viewport\n    ]);\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__[\"default\"])('react-pdf__Page__annotations', 'annotationLayer'),\n        ref: layerElement\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/Page/AnnotationLayer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/Page/Canvas.js":
/*!********************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/Page/Canvas.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Canvas)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var merge_refs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! merge-refs */ \"(ssr)/./node_modules/merge-refs/dist/esm/index.js\");\n/* harmony import */ var tiny_invariant__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! tiny-invariant */ \"(ssr)/./node_modules/tiny-invariant/dist/esm/tiny-invariant.js\");\n/* harmony import */ var warning__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! warning */ \"(ssr)/./node_modules/warning/warning.js\");\n/* harmony import */ var pdfjs_dist__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! pdfjs-dist */ \"(ssr)/./node_modules/pdfjs-dist/build/pdf.mjs\");\n/* harmony import */ var _StructTree_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../StructTree.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/StructTree.js\");\n/* harmony import */ var _shared_hooks_usePageContext_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../shared/hooks/usePageContext.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/hooks/usePageContext.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../shared/utils.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/utils.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nconst ANNOTATION_MODE = pdfjs_dist__WEBPACK_IMPORTED_MODULE_4__.AnnotationMode;\nfunction Canvas(props) {\n    const pageContext = (0,_shared_hooks_usePageContext_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(pageContext, 'Unable to find Page context.');\n    const mergedProps = Object.assign(Object.assign({}, pageContext), props);\n    const { _className, canvasBackground, devicePixelRatio = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_6__.getDevicePixelRatio)(), onRenderError: onRenderErrorProps, onRenderSuccess: onRenderSuccessProps, page, renderForms, renderTextLayer, rotate, scale } = mergedProps;\n    const { canvasRef } = props;\n    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(page, 'Attempted to render page canvas, but no page was specified.');\n    const canvasElement = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    /**\n     * Called when a page is rendered successfully.\n     */ function onRenderSuccess() {\n        if (!page) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        if (onRenderSuccessProps) {\n            onRenderSuccessProps((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_6__.makePageCallback)(page, scale));\n        }\n    }\n    /**\n     * Called when a page fails to render.\n     */ function onRenderError(error) {\n        if ((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_6__.isCancelException)(error)) {\n            return;\n        }\n        warning__WEBPACK_IMPORTED_MODULE_3__(false, error.toString());\n        if (onRenderErrorProps) {\n            onRenderErrorProps(error);\n        }\n    }\n    const renderViewport = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"Canvas.useMemo[renderViewport]\": ()=>page.getViewport({\n                scale: scale * devicePixelRatio,\n                rotation: rotate\n            })\n    }[\"Canvas.useMemo[renderViewport]\"], [\n        devicePixelRatio,\n        page,\n        rotate,\n        scale\n    ]);\n    const viewport = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"Canvas.useMemo[viewport]\": ()=>page.getViewport({\n                scale,\n                rotation: rotate\n            })\n    }[\"Canvas.useMemo[viewport]\"], [\n        page,\n        rotate,\n        scale\n    ]);\n    // biome-ignore lint/correctness/useExhaustiveDependencies: Ommitted callbacks so they are not called every time they change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function drawPageOnCanvas() {\n        if (!page) {\n            return;\n        }\n        // Ensures the canvas will be re-rendered from scratch. Otherwise all form data will stay.\n        page.cleanup();\n        const { current: canvas } = canvasElement;\n        if (!canvas) {\n            return;\n        }\n        canvas.width = renderViewport.width;\n        canvas.height = renderViewport.height;\n        canvas.style.width = `${Math.floor(viewport.width)}px`;\n        canvas.style.height = `${Math.floor(viewport.height)}px`;\n        canvas.style.visibility = 'hidden';\n        const renderContext = {\n            annotationMode: renderForms ? ANNOTATION_MODE.ENABLE_FORMS : ANNOTATION_MODE.ENABLE,\n            canvasContext: canvas.getContext('2d', {\n                alpha: false\n            }),\n            viewport: renderViewport\n        };\n        if (canvasBackground) {\n            renderContext.background = canvasBackground;\n        }\n        const cancellable = page.render(renderContext);\n        const runningTask = cancellable;\n        cancellable.promise.then({\n            \"Canvas.useEffect.drawPageOnCanvas\": ()=>{\n                canvas.style.visibility = '';\n                onRenderSuccess();\n            }\n        }[\"Canvas.useEffect.drawPageOnCanvas\"]).catch(onRenderError);\n        return ({\n            \"Canvas.useEffect.drawPageOnCanvas\": ()=>(0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_6__.cancelRunningTask)(runningTask)\n        })[\"Canvas.useEffect.drawPageOnCanvas\"];\n    }, [\n        canvasBackground,\n        page,\n        renderForms,\n        renderViewport,\n        viewport\n    ]);\n    const cleanup = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Canvas.useCallback[cleanup]\": ()=>{\n            const { current: canvas } = canvasElement;\n            /**\n         * Zeroing the width and height cause most browsers to release graphics\n         * resources immediately, which can greatly reduce memory consumption.\n         */ if (canvas) {\n                canvas.width = 0;\n                canvas.height = 0;\n            }\n        }\n    }[\"Canvas.useCallback[cleanup]\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Canvas.useEffect\": ()=>cleanup\n    }[\"Canvas.useEffect\"], [\n        cleanup\n    ]);\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"canvas\", {\n        className: `${_className}__canvas`,\n        dir: \"ltr\",\n        ref: (0,merge_refs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(canvasRef, canvasElement),\n        style: {\n            display: 'block',\n            userSelect: 'none'\n        },\n        children: renderTextLayer ? (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_StructTree_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}) : null\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/Page/Canvas.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/Page/TextLayer.css":
/*!************************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/Page/TextLayer.css ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"65eac2ffe29d\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcGRmL2Rpc3QvZXNtL1BhZ2UvVGV4dExheWVyLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLEtBQVUsRUFBRSxFQUF1QiIsInNvdXJjZXMiOlsiRDpcXHNvZnRcXFNlYVByb2plY3RcXGdpdGh1Yl9vcGVuX3Byb2plY3Rfc3VjY2Vzc1xcc3VuYVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xccmVhY3QtcGRmXFxkaXN0XFxlc21cXFBhZ2VcXFRleHRMYXllci5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI2NWVhYzJmZmUyOWRcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/Page/TextLayer.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/Page/TextLayer.js":
/*!***********************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/Page/TextLayer.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TextLayer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var make_cancellable_promise__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! make-cancellable-promise */ \"(ssr)/./node_modules/make-cancellable-promise/dist/esm/index.js\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tiny_invariant__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! tiny-invariant */ \"(ssr)/./node_modules/tiny-invariant/dist/esm/tiny-invariant.js\");\n/* harmony import */ var warning__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! warning */ \"(ssr)/./node_modules/warning/warning.js\");\n/* harmony import */ var pdfjs_dist__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! pdfjs-dist */ \"(ssr)/./node_modules/pdfjs-dist/build/pdf.mjs\");\n/* harmony import */ var _shared_hooks_usePageContext_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../shared/hooks/usePageContext.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/hooks/usePageContext.js\");\n/* harmony import */ var _shared_hooks_useResolver_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../shared/hooks/useResolver.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/hooks/useResolver.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../shared/utils.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/utils.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\nfunction isTextItem(item) {\n    return 'str' in item;\n}\nfunction TextLayer() {\n    const pageContext = (0,_shared_hooks_usePageContext_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])();\n    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(pageContext, 'Unable to find Page context.');\n    const { customTextRenderer, onGetTextError, onGetTextSuccess, onRenderTextLayerError, onRenderTextLayerSuccess, page, pageIndex, pageNumber, rotate, scale } = pageContext;\n    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(page, 'Attempted to load page text content, but no page was specified.');\n    const [textContentState, textContentDispatch] = (0,_shared_hooks_useResolver_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])();\n    const { value: textContent, error: textContentError } = textContentState;\n    const layerElement = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    warning__WEBPACK_IMPORTED_MODULE_4__(Number.parseInt(window.getComputedStyle(document.body).getPropertyValue('--react-pdf-text-layer'), 10) === 1, 'TextLayer styles not found. Read more: https://github.com/wojtekmaj/react-pdf#support-for-text-layer');\n    /**\n     * Called when a page text content is read successfully\n     */ function onLoadSuccess() {\n        if (!textContent) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        if (onGetTextSuccess) {\n            onGetTextSuccess(textContent);\n        }\n    }\n    /**\n     * Called when a page text content failed to read successfully\n     */ function onLoadError() {\n        if (!textContentError) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        warning__WEBPACK_IMPORTED_MODULE_4__(false, textContentError.toString());\n        if (onGetTextError) {\n            onGetTextError(textContentError);\n        }\n    }\n    // biome-ignore lint/correctness/useExhaustiveDependencies: useEffect intentionally triggered on page change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function resetTextContent() {\n        textContentDispatch({\n            type: 'RESET'\n        });\n    }, [\n        page,\n        textContentDispatch\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function loadTextContent() {\n        if (!page) {\n            return;\n        }\n        const cancellable = (0,make_cancellable_promise__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(page.getTextContent());\n        const runningTask = cancellable;\n        cancellable.promise.then({\n            \"TextLayer.useEffect.loadTextContent\": (nextTextContent)=>{\n                textContentDispatch({\n                    type: 'RESOLVE',\n                    value: nextTextContent\n                });\n            }\n        }[\"TextLayer.useEffect.loadTextContent\"]).catch({\n            \"TextLayer.useEffect.loadTextContent\": (error)=>{\n                textContentDispatch({\n                    type: 'REJECT',\n                    error\n                });\n            }\n        }[\"TextLayer.useEffect.loadTextContent\"]);\n        return ({\n            \"TextLayer.useEffect.loadTextContent\": ()=>(0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_9__.cancelRunningTask)(runningTask)\n        })[\"TextLayer.useEffect.loadTextContent\"];\n    }, [\n        page,\n        textContentDispatch\n    ]);\n    // biome-ignore lint/correctness/useExhaustiveDependencies: Ommitted callbacks so they are not called every time they change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TextLayer.useEffect\": ()=>{\n            if (textContent === undefined) {\n                return;\n            }\n            if (textContent === false) {\n                onLoadError();\n                return;\n            }\n            onLoadSuccess();\n        }\n    }[\"TextLayer.useEffect\"], [\n        textContent\n    ]);\n    /**\n     * Called when a text layer is rendered successfully\n     */ const onRenderSuccess = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TextLayer.useCallback[onRenderSuccess]\": ()=>{\n            if (onRenderTextLayerSuccess) {\n                onRenderTextLayerSuccess();\n            }\n        }\n    }[\"TextLayer.useCallback[onRenderSuccess]\"], [\n        onRenderTextLayerSuccess\n    ]);\n    /**\n     * Called when a text layer failed to render successfully\n     */ const onRenderError = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TextLayer.useCallback[onRenderError]\": (error)=>{\n            warning__WEBPACK_IMPORTED_MODULE_4__(false, error.toString());\n            if (onRenderTextLayerError) {\n                onRenderTextLayerError(error);\n            }\n        }\n    }[\"TextLayer.useCallback[onRenderError]\"], [\n        onRenderTextLayerError\n    ]);\n    function onMouseDown() {\n        const layer = layerElement.current;\n        if (!layer) {\n            return;\n        }\n        layer.classList.add('selecting');\n    }\n    function onMouseUp() {\n        const layer = layerElement.current;\n        if (!layer) {\n            return;\n        }\n        layer.classList.remove('selecting');\n    }\n    const viewport = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"TextLayer.useMemo[viewport]\": ()=>page.getViewport({\n                scale,\n                rotation: rotate\n            })\n    }[\"TextLayer.useMemo[viewport]\"], [\n        page,\n        rotate,\n        scale\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(function renderTextLayer() {\n        if (!page || !textContent) {\n            return;\n        }\n        const { current: layer } = layerElement;\n        if (!layer) {\n            return;\n        }\n        layer.innerHTML = '';\n        const textContentSource = page.streamTextContent({\n            includeMarkedContent: true\n        });\n        const parameters = {\n            container: layer,\n            textContentSource,\n            viewport\n        };\n        const cancellable = new pdfjs_dist__WEBPACK_IMPORTED_MODULE_5__.TextLayer(parameters);\n        const runningTask = cancellable;\n        cancellable.render().then({\n            \"TextLayer.useLayoutEffect.renderTextLayer\": ()=>{\n                const end = document.createElement('div');\n                end.className = 'endOfContent';\n                layer.append(end);\n                const layerChildren = layer.querySelectorAll('[role=\"presentation\"]');\n                if (customTextRenderer) {\n                    let index = 0;\n                    textContent.items.forEach({\n                        \"TextLayer.useLayoutEffect.renderTextLayer\": (item, itemIndex)=>{\n                            if (!isTextItem(item)) {\n                                return;\n                            }\n                            const child = layerChildren[index];\n                            if (!child) {\n                                return;\n                            }\n                            const content = customTextRenderer(Object.assign({\n                                pageIndex,\n                                pageNumber,\n                                itemIndex\n                            }, item));\n                            child.innerHTML = content;\n                            index += item.str && item.hasEOL ? 2 : 1;\n                        }\n                    }[\"TextLayer.useLayoutEffect.renderTextLayer\"]);\n                }\n                // Intentional immediate callback\n                onRenderSuccess();\n            }\n        }[\"TextLayer.useLayoutEffect.renderTextLayer\"]).catch(onRenderError);\n        return ({\n            \"TextLayer.useLayoutEffect.renderTextLayer\": ()=>(0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_9__.cancelRunningTask)(runningTask)\n        })[\"TextLayer.useLayoutEffect.renderTextLayer\"];\n    }, [\n        customTextRenderer,\n        onRenderError,\n        onRenderSuccess,\n        page,\n        pageIndex,\n        pageNumber,\n        textContent,\n        viewport\n    ]);\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__[\"default\"])('react-pdf__Page__textContent', 'textLayer'),\n        onMouseUp: onMouseUp,\n        onMouseDown: onMouseDown,\n        ref: layerElement\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/Page/TextLayer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/PageContext.js":
/*!********************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/PageContext.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nconst pageContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(null);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (pageContext);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcGRmL2Rpc3QvZXNtL1BhZ2VDb250ZXh0LmpzIiwibWFwcGluZ3MiOiI7Ozs7OzZEQUNzQztBQUN0QyxNQUFNQyw0QkFBY0Qsb0RBQWFBLENBQUM7QUFDbEMsaUVBQWVDLFdBQVdBLEVBQUMiLCJzb3VyY2VzIjpbIkQ6XFxzb2Z0XFxTZWFQcm9qZWN0XFxnaXRodWJfb3Blbl9wcm9qZWN0X3N1Y2Nlc3NcXHN1bmFcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXHJlYWN0LXBkZlxcZGlzdFxcZXNtXFxQYWdlQ29udGV4dC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5pbXBvcnQgeyBjcmVhdGVDb250ZXh0IH0gZnJvbSAncmVhY3QnO1xuY29uc3QgcGFnZUNvbnRleHQgPSBjcmVhdGVDb250ZXh0KG51bGwpO1xuZXhwb3J0IGRlZmF1bHQgcGFnZUNvbnRleHQ7XG4iXSwibmFtZXMiOlsiY3JlYXRlQ29udGV4dCIsInBhZ2VDb250ZXh0Il0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/PageContext.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/PasswordResponses.js":
/*!**************************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/PasswordResponses.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// As defined in https://github.com/mozilla/pdf.js/blob/d9fac3459609a807be6506fb3441b5da4b154d14/src/shared/util.js#L371-L374\nconst PasswordResponses = {\n    NEED_PASSWORD: 1,\n    INCORRECT_PASSWORD: 2,\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PasswordResponses);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcGRmL2Rpc3QvZXNtL1Bhc3N3b3JkUmVzcG9uc2VzLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUVBQWUsaUJBQWlCLEVBQUMiLCJzb3VyY2VzIjpbIkQ6XFxzb2Z0XFxTZWFQcm9qZWN0XFxnaXRodWJfb3Blbl9wcm9qZWN0X3N1Y2Nlc3NcXHN1bmFcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXHJlYWN0LXBkZlxcZGlzdFxcZXNtXFxQYXNzd29yZFJlc3BvbnNlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBBcyBkZWZpbmVkIGluIGh0dHBzOi8vZ2l0aHViLmNvbS9tb3ppbGxhL3BkZi5qcy9ibG9iL2Q5ZmFjMzQ1OTYwOWE4MDdiZTY1MDZmYjM0NDFiNWRhNGIxNTRkMTQvc3JjL3NoYXJlZC91dGlsLmpzI0wzNzEtTDM3NFxuY29uc3QgUGFzc3dvcmRSZXNwb25zZXMgPSB7XG4gICAgTkVFRF9QQVNTV09SRDogMSxcbiAgICBJTkNPUlJFQ1RfUEFTU1dPUkQ6IDIsXG59O1xuZXhwb3J0IGRlZmF1bHQgUGFzc3dvcmRSZXNwb25zZXM7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/PasswordResponses.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/StructTree.js":
/*!*******************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/StructTree.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StructTree)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var make_cancellable_promise__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! make-cancellable-promise */ \"(ssr)/./node_modules/make-cancellable-promise/dist/esm/index.js\");\n/* harmony import */ var tiny_invariant__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! tiny-invariant */ \"(ssr)/./node_modules/tiny-invariant/dist/esm/tiny-invariant.js\");\n/* harmony import */ var warning__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! warning */ \"(ssr)/./node_modules/warning/warning.js\");\n/* harmony import */ var _StructTreeItem_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./StructTreeItem.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/StructTreeItem.js\");\n/* harmony import */ var _shared_hooks_usePageContext_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./shared/hooks/usePageContext.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/hooks/usePageContext.js\");\n/* harmony import */ var _shared_hooks_useResolver_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./shared/hooks/useResolver.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/hooks/useResolver.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./shared/utils.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/utils.js\");\n\n\n\n\n\n\n\n\n\nfunction StructTree() {\n    const pageContext = (0,_shared_hooks_usePageContext_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])();\n    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(pageContext, 'Unable to find Page context.');\n    const { onGetStructTreeError: onGetStructTreeErrorProps, onGetStructTreeSuccess: onGetStructTreeSuccessProps, } = pageContext;\n    const [structTreeState, structTreeDispatch] = (0,_shared_hooks_useResolver_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    const { value: structTree, error: structTreeError } = structTreeState;\n    const { customTextRenderer, page } = pageContext;\n    function onLoadSuccess() {\n        if (!structTree) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        if (onGetStructTreeSuccessProps) {\n            onGetStructTreeSuccessProps(structTree);\n        }\n    }\n    function onLoadError() {\n        if (!structTreeError) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        warning__WEBPACK_IMPORTED_MODULE_3__(false, structTreeError.toString());\n        if (onGetStructTreeErrorProps) {\n            onGetStructTreeErrorProps(structTreeError);\n        }\n    }\n    // biome-ignore lint/correctness/useExhaustiveDependencies: useEffect intentionally triggered on page change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function resetStructTree() {\n        structTreeDispatch({ type: 'RESET' });\n    }, [structTreeDispatch, page]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function loadStructTree() {\n        if (customTextRenderer) {\n            // TODO: Document why this is necessary\n            return;\n        }\n        if (!page) {\n            return;\n        }\n        const cancellable = (0,make_cancellable_promise__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(page.getStructTree());\n        const runningTask = cancellable;\n        cancellable.promise\n            .then((nextStructTree) => {\n            structTreeDispatch({ type: 'RESOLVE', value: nextStructTree });\n        })\n            .catch((error) => {\n            structTreeDispatch({ type: 'REJECT', error });\n        });\n        return () => (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_7__.cancelRunningTask)(runningTask);\n    }, [customTextRenderer, page, structTreeDispatch]);\n    // biome-ignore lint/correctness/useExhaustiveDependencies: Ommitted callbacks so they are not called every time they change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {\n        if (structTree === undefined) {\n            return;\n        }\n        if (structTree === false) {\n            onLoadError();\n            return;\n        }\n        onLoadSuccess();\n    }, [structTree]);\n    if (!structTree) {\n        return null;\n    }\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_StructTreeItem_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"], { className: \"react-pdf__Page__structTree structTree\", node: structTree });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/StructTree.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/StructTreeItem.js":
/*!***********************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/StructTreeItem.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StructTreeItem)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _shared_structTreeUtils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./shared/structTreeUtils.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/structTreeUtils.js\");\n\n\n\nfunction StructTreeItem({ className, node, }) {\n    const attributes = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(() => (0,_shared_structTreeUtils_js__WEBPACK_IMPORTED_MODULE_2__.getAttributes)(node), [node]);\n    const children = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(() => {\n        if (!(0,_shared_structTreeUtils_js__WEBPACK_IMPORTED_MODULE_2__.isStructTreeNode)(node)) {\n            return null;\n        }\n        if ((0,_shared_structTreeUtils_js__WEBPACK_IMPORTED_MODULE_2__.isStructTreeNodeWithOnlyContentChild)(node)) {\n            return null;\n        }\n        return node.children.map((child, index) => {\n            return (\n            // biome-ignore lint/suspicious/noArrayIndexKey: index is stable here\n            (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(StructTreeItem, { node: child }, index));\n        });\n    }, [node]);\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", Object.assign({ className: className }, attributes, { children: children })));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcGRmL2Rpc3QvZXNtL1N0cnVjdFRyZWVJdGVtLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBZ0Q7QUFDaEI7QUFDcUY7QUFDdEcsMEJBQTBCLGtCQUFrQjtBQUMzRCx1QkFBdUIsOENBQU8sT0FBTyx5RUFBYTtBQUNsRCxxQkFBcUIsOENBQU87QUFDNUIsYUFBYSw0RUFBZ0I7QUFDN0I7QUFDQTtBQUNBLFlBQVksZ0dBQW9DO0FBQ2hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLHNEQUFJLG1CQUFtQixhQUFhO0FBQ2hELFNBQVM7QUFDVCxLQUFLO0FBQ0wsWUFBWSxzREFBSSx5QkFBeUIsc0JBQXNCLGdCQUFnQixvQkFBb0I7QUFDbkciLCJzb3VyY2VzIjpbIkQ6XFxzb2Z0XFxTZWFQcm9qZWN0XFxnaXRodWJfb3Blbl9wcm9qZWN0X3N1Y2Nlc3NcXHN1bmFcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXHJlYWN0LXBkZlxcZGlzdFxcZXNtXFxTdHJ1Y3RUcmVlSXRlbS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBqc3ggYXMgX2pzeCB9IGZyb20gXCJyZWFjdC9qc3gtcnVudGltZVwiO1xuaW1wb3J0IHsgdXNlTWVtbyB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IGdldEF0dHJpYnV0ZXMsIGlzU3RydWN0VHJlZU5vZGUsIGlzU3RydWN0VHJlZU5vZGVXaXRoT25seUNvbnRlbnRDaGlsZCwgfSBmcm9tICcuL3NoYXJlZC9zdHJ1Y3RUcmVlVXRpbHMuanMnO1xuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gU3RydWN0VHJlZUl0ZW0oeyBjbGFzc05hbWUsIG5vZGUsIH0pIHtcbiAgICBjb25zdCBhdHRyaWJ1dGVzID0gdXNlTWVtbygoKSA9PiBnZXRBdHRyaWJ1dGVzKG5vZGUpLCBbbm9kZV0pO1xuICAgIGNvbnN0IGNoaWxkcmVuID0gdXNlTWVtbygoKSA9PiB7XG4gICAgICAgIGlmICghaXNTdHJ1Y3RUcmVlTm9kZShub2RlKSkge1xuICAgICAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKGlzU3RydWN0VHJlZU5vZGVXaXRoT25seUNvbnRlbnRDaGlsZChub2RlKSkge1xuICAgICAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIG5vZGUuY2hpbGRyZW4ubWFwKChjaGlsZCwgaW5kZXgpID0+IHtcbiAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAvLyBiaW9tZS1pZ25vcmUgbGludC9zdXNwaWNpb3VzL25vQXJyYXlJbmRleEtleTogaW5kZXggaXMgc3RhYmxlIGhlcmVcbiAgICAgICAgICAgIF9qc3goU3RydWN0VHJlZUl0ZW0sIHsgbm9kZTogY2hpbGQgfSwgaW5kZXgpKTtcbiAgICAgICAgfSk7XG4gICAgfSwgW25vZGVdKTtcbiAgICByZXR1cm4gKF9qc3goXCJzcGFuXCIsIE9iamVjdC5hc3NpZ24oeyBjbGFzc05hbWU6IGNsYXNzTmFtZSB9LCBhdHRyaWJ1dGVzLCB7IGNoaWxkcmVuOiBjaGlsZHJlbiB9KSkpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/StructTreeItem.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/shared/constants.js":
/*!*************************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/shared/constants.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HEADING_PATTERN: () => (/* binding */ HEADING_PATTERN),\n/* harmony export */   PDF_ROLE_TO_HTML_ROLE: () => (/* binding */ PDF_ROLE_TO_HTML_ROLE)\n/* harmony export */ });\n// From pdfjs-dist/lib/web/struct_tree_layer_builder.js\nconst PDF_ROLE_TO_HTML_ROLE = {\n    // Document level structure types\n    Document: null, // There's a \"document\" role, but it doesn't make sense here.\n    DocumentFragment: null,\n    // Grouping level structure types\n    Part: 'group',\n    Sect: 'group', // XXX: There's a \"section\" role, but it's abstract.\n    Div: 'group',\n    Aside: 'note',\n    NonStruct: 'none',\n    // Block level structure types\n    P: null,\n    // H<n>,\n    H: 'heading',\n    Title: null,\n    FENote: 'note',\n    // Sub-block level structure type\n    Sub: 'group',\n    // General inline level structure types\n    Lbl: null,\n    Span: null,\n    Em: null,\n    Strong: null,\n    Link: 'link',\n    Annot: 'note',\n    Form: 'form',\n    // Ruby and Warichu structure types\n    Ruby: null,\n    RB: null,\n    RT: null,\n    RP: null,\n    Warichu: null,\n    WT: null,\n    WP: null,\n    // List standard structure types\n    L: 'list',\n    LI: 'listitem',\n    LBody: null,\n    // Table standard structure types\n    Table: 'table',\n    TR: 'row',\n    TH: 'columnheader',\n    TD: 'cell',\n    THead: 'columnheader',\n    TBody: null,\n    TFoot: null,\n    // Standard structure type Caption\n    Caption: null,\n    // Standard structure type Figure\n    Figure: 'figure',\n    // Standard structure type Formula\n    Formula: null,\n    // standard structure type Artifact\n    Artifact: null,\n};\nconst HEADING_PATTERN = /^H(\\d+)$/;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/shared/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/shared/hooks/useDocumentContext.js":
/*!****************************************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/shared/hooks/useDocumentContext.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useDocumentContext)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _DocumentContext_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../DocumentContext.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/DocumentContext.js\");\n\n\nfunction useDocumentContext() {\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_DocumentContext_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcGRmL2Rpc3QvZXNtL3NoYXJlZC9ob29rcy91c2VEb2N1bWVudENvbnRleHQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQW1DO0FBQ29CO0FBQ3hDO0FBQ2YsV0FBVyxpREFBVSxDQUFDLDJEQUFlO0FBQ3JDIiwic291cmNlcyI6WyJEOlxcc29mdFxcU2VhUHJvamVjdFxcZ2l0aHViX29wZW5fcHJvamVjdF9zdWNjZXNzXFxzdW5hXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxyZWFjdC1wZGZcXGRpc3RcXGVzbVxcc2hhcmVkXFxob29rc1xcdXNlRG9jdW1lbnRDb250ZXh0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZUNvbnRleHQgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgRG9jdW1lbnRDb250ZXh0IGZyb20gJy4uLy4uL0RvY3VtZW50Q29udGV4dC5qcyc7XG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiB1c2VEb2N1bWVudENvbnRleHQoKSB7XG4gICAgcmV0dXJuIHVzZUNvbnRleHQoRG9jdW1lbnRDb250ZXh0KTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/shared/hooks/useDocumentContext.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/shared/hooks/usePageContext.js":
/*!************************************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/shared/hooks/usePageContext.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ usePageContext)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _PageContext_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../PageContext.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/PageContext.js\");\n\n\nfunction usePageContext() {\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_PageContext_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcGRmL2Rpc3QvZXNtL3NoYXJlZC9ob29rcy91c2VQYWdlQ29udGV4dC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBbUM7QUFDWTtBQUNoQztBQUNmLFdBQVcsaURBQVUsQ0FBQyx1REFBVztBQUNqQyIsInNvdXJjZXMiOlsiRDpcXHNvZnRcXFNlYVByb2plY3RcXGdpdGh1Yl9vcGVuX3Byb2plY3Rfc3VjY2Vzc1xcc3VuYVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xccmVhY3QtcGRmXFxkaXN0XFxlc21cXHNoYXJlZFxcaG9va3NcXHVzZVBhZ2VDb250ZXh0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZUNvbnRleHQgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgUGFnZUNvbnRleHQgZnJvbSAnLi4vLi4vUGFnZUNvbnRleHQuanMnO1xuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gdXNlUGFnZUNvbnRleHQoKSB7XG4gICAgcmV0dXJuIHVzZUNvbnRleHQoUGFnZUNvbnRleHQpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/shared/hooks/usePageContext.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/shared/hooks/useResolver.js":
/*!*********************************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/shared/hooks/useResolver.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useResolver)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n\nfunction reducer(state, action) {\n    switch (action.type) {\n        case 'RESOLVE':\n            return { value: action.value, error: undefined };\n        case 'REJECT':\n            return { value: false, error: action.error };\n        case 'RESET':\n            return { value: undefined, error: undefined };\n        default:\n            return state;\n    }\n}\nfunction useResolver() {\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useReducer)((reducer), { value: undefined, error: undefined });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcGRmL2Rpc3QvZXNtL3NoYXJlZC9ob29rcy91c2VSZXNvbHZlci5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFtQztBQUNuQztBQUNBO0FBQ0E7QUFDQSxxQkFBcUI7QUFDckI7QUFDQSxxQkFBcUI7QUFDckI7QUFDQSxxQkFBcUI7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDZTtBQUNmLFdBQVcsaURBQVUsY0FBYyxvQ0FBb0M7QUFDdkUiLCJzb3VyY2VzIjpbIkQ6XFxzb2Z0XFxTZWFQcm9qZWN0XFxnaXRodWJfb3Blbl9wcm9qZWN0X3N1Y2Nlc3NcXHN1bmFcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXHJlYWN0LXBkZlxcZGlzdFxcZXNtXFxzaGFyZWRcXGhvb2tzXFx1c2VSZXNvbHZlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VSZWR1Y2VyIH0gZnJvbSAncmVhY3QnO1xuZnVuY3Rpb24gcmVkdWNlcihzdGF0ZSwgYWN0aW9uKSB7XG4gICAgc3dpdGNoIChhY3Rpb24udHlwZSkge1xuICAgICAgICBjYXNlICdSRVNPTFZFJzpcbiAgICAgICAgICAgIHJldHVybiB7IHZhbHVlOiBhY3Rpb24udmFsdWUsIGVycm9yOiB1bmRlZmluZWQgfTtcbiAgICAgICAgY2FzZSAnUkVKRUNUJzpcbiAgICAgICAgICAgIHJldHVybiB7IHZhbHVlOiBmYWxzZSwgZXJyb3I6IGFjdGlvbi5lcnJvciB9O1xuICAgICAgICBjYXNlICdSRVNFVCc6XG4gICAgICAgICAgICByZXR1cm4geyB2YWx1ZTogdW5kZWZpbmVkLCBlcnJvcjogdW5kZWZpbmVkIH07XG4gICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgICByZXR1cm4gc3RhdGU7XG4gICAgfVxufVxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gdXNlUmVzb2x2ZXIoKSB7XG4gICAgcmV0dXJuIHVzZVJlZHVjZXIoKHJlZHVjZXIpLCB7IHZhbHVlOiB1bmRlZmluZWQsIGVycm9yOiB1bmRlZmluZWQgfSk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/shared/hooks/useResolver.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/shared/structTreeUtils.js":
/*!*******************************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/shared/structTreeUtils.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAttributes: () => (/* binding */ getAttributes),\n/* harmony export */   getBaseAttributes: () => (/* binding */ getBaseAttributes),\n/* harmony export */   getRoleAttributes: () => (/* binding */ getRoleAttributes),\n/* harmony export */   isPdfRole: () => (/* binding */ isPdfRole),\n/* harmony export */   isStructTreeNode: () => (/* binding */ isStructTreeNode),\n/* harmony export */   isStructTreeNodeWithOnlyContentChild: () => (/* binding */ isStructTreeNodeWithOnlyContentChild)\n/* harmony export */ });\n/* harmony import */ var _constants_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constants.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/constants.js\");\n\nfunction isPdfRole(role) {\n    return role in _constants_js__WEBPACK_IMPORTED_MODULE_0__.PDF_ROLE_TO_HTML_ROLE;\n}\nfunction isStructTreeNode(node) {\n    return 'children' in node;\n}\nfunction isStructTreeNodeWithOnlyContentChild(node) {\n    if (!isStructTreeNode(node)) {\n        return false;\n    }\n    return node.children.length === 1 && 0 in node.children && 'id' in node.children[0];\n}\nfunction getRoleAttributes(node) {\n    const attributes = {};\n    if (isStructTreeNode(node)) {\n        const { role } = node;\n        const matches = role.match(_constants_js__WEBPACK_IMPORTED_MODULE_0__.HEADING_PATTERN);\n        if (matches) {\n            attributes.role = 'heading';\n            attributes['aria-level'] = Number(matches[1]);\n        }\n        else if (isPdfRole(role)) {\n            const htmlRole = _constants_js__WEBPACK_IMPORTED_MODULE_0__.PDF_ROLE_TO_HTML_ROLE[role];\n            if (htmlRole) {\n                attributes.role = htmlRole;\n            }\n        }\n    }\n    return attributes;\n}\nfunction getBaseAttributes(node) {\n    const attributes = {};\n    if (isStructTreeNode(node)) {\n        if (node.alt !== undefined) {\n            attributes['aria-label'] = node.alt;\n        }\n        if (node.lang !== undefined) {\n            attributes.lang = node.lang;\n        }\n        if (isStructTreeNodeWithOnlyContentChild(node)) {\n            const [child] = node.children;\n            if (child) {\n                const childAttributes = getBaseAttributes(child);\n                return Object.assign(Object.assign({}, attributes), childAttributes);\n            }\n        }\n    }\n    else {\n        if ('id' in node) {\n            attributes['aria-owns'] = node.id;\n        }\n    }\n    return attributes;\n}\nfunction getAttributes(node) {\n    if (!node) {\n        return null;\n    }\n    return Object.assign(Object.assign({}, getRoleAttributes(node)), getBaseAttributes(node));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/shared/structTreeUtils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/shared/utils.js":
/*!*********************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/shared/utils.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cancelRunningTask: () => (/* binding */ cancelRunningTask),\n/* harmony export */   dataURItoByteString: () => (/* binding */ dataURItoByteString),\n/* harmony export */   displayCORSWarning: () => (/* binding */ displayCORSWarning),\n/* harmony export */   displayWorkerWarning: () => (/* binding */ displayWorkerWarning),\n/* harmony export */   getDevicePixelRatio: () => (/* binding */ getDevicePixelRatio),\n/* harmony export */   isArrayBuffer: () => (/* binding */ isArrayBuffer),\n/* harmony export */   isBlob: () => (/* binding */ isBlob),\n/* harmony export */   isBrowser: () => (/* binding */ isBrowser),\n/* harmony export */   isCancelException: () => (/* binding */ isCancelException),\n/* harmony export */   isDataURI: () => (/* binding */ isDataURI),\n/* harmony export */   isDefined: () => (/* binding */ isDefined),\n/* harmony export */   isLocalFileSystem: () => (/* binding */ isLocalFileSystem),\n/* harmony export */   isProvided: () => (/* binding */ isProvided),\n/* harmony export */   isString: () => (/* binding */ isString),\n/* harmony export */   loadFromFile: () => (/* binding */ loadFromFile),\n/* harmony export */   makePageCallback: () => (/* binding */ makePageCallback)\n/* harmony export */ });\n/* harmony import */ var tiny_invariant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tiny-invariant */ \"(ssr)/./node_modules/tiny-invariant/dist/esm/tiny-invariant.js\");\n/* harmony import */ var warning__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! warning */ \"(ssr)/./node_modules/warning/warning.js\");\n\n\n/**\n * Checks if we're running in a browser environment.\n */\nconst isBrowser = typeof window !== 'undefined';\n/**\n * Checks whether we're running from a local file system.\n */\nconst isLocalFileSystem = isBrowser && window.location.protocol === 'file:';\n/**\n * Checks whether a variable is defined.\n *\n * @param {*} variable Variable to check\n */\nfunction isDefined(variable) {\n    return typeof variable !== 'undefined';\n}\n/**\n * Checks whether a variable is defined and not null.\n *\n * @param {*} variable Variable to check\n */\nfunction isProvided(variable) {\n    return isDefined(variable) && variable !== null;\n}\n/**\n * Checks whether a variable provided is a string.\n *\n * @param {*} variable Variable to check\n */\nfunction isString(variable) {\n    return typeof variable === 'string';\n}\n/**\n * Checks whether a variable provided is an ArrayBuffer.\n *\n * @param {*} variable Variable to check\n */\nfunction isArrayBuffer(variable) {\n    return variable instanceof ArrayBuffer;\n}\n/**\n * Checks whether a variable provided is a Blob.\n *\n * @param {*} variable Variable to check\n */\nfunction isBlob(variable) {\n    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(isBrowser, 'isBlob can only be used in a browser environment');\n    return variable instanceof Blob;\n}\n/**\n * Checks whether a variable provided is a data URI.\n *\n * @param {*} variable String to check\n */\nfunction isDataURI(variable) {\n    return isString(variable) && /^data:/.test(variable);\n}\nfunction dataURItoByteString(dataURI) {\n    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(isDataURI(dataURI), 'Invalid data URI.');\n    const [headersString = '', dataString = ''] = dataURI.split(',');\n    const headers = headersString.split(';');\n    if (headers.indexOf('base64') !== -1) {\n        return atob(dataString);\n    }\n    return unescape(dataString);\n}\nfunction getDevicePixelRatio() {\n    return (isBrowser && window.devicePixelRatio) || 1;\n}\nconst allowFileAccessFromFilesTip = 'On Chromium based browsers, you can use --allow-file-access-from-files flag for debugging purposes.';\nfunction displayCORSWarning() {\n    warning__WEBPACK_IMPORTED_MODULE_1__(!isLocalFileSystem, `Loading PDF as base64 strings/URLs may not work on protocols other than HTTP/HTTPS. ${allowFileAccessFromFilesTip}`);\n}\nfunction displayWorkerWarning() {\n    warning__WEBPACK_IMPORTED_MODULE_1__(!isLocalFileSystem, `Loading PDF.js worker may not work on protocols other than HTTP/HTTPS. ${allowFileAccessFromFilesTip}`);\n}\nfunction cancelRunningTask(runningTask) {\n    if (runningTask === null || runningTask === void 0 ? void 0 : runningTask.cancel)\n        runningTask.cancel();\n}\nfunction makePageCallback(page, scale) {\n    Object.defineProperty(page, 'width', {\n        get() {\n            return this.view[2] * scale;\n        },\n        configurable: true,\n    });\n    Object.defineProperty(page, 'height', {\n        get() {\n            return this.view[3] * scale;\n        },\n        configurable: true,\n    });\n    Object.defineProperty(page, 'originalWidth', {\n        get() {\n            return this.view[2];\n        },\n        configurable: true,\n    });\n    Object.defineProperty(page, 'originalHeight', {\n        get() {\n            return this.view[3];\n        },\n        configurable: true,\n    });\n    return page;\n}\nfunction isCancelException(error) {\n    return error.name === 'RenderingCancelledException';\n}\nfunction loadFromFile(file) {\n    return new Promise((resolve, reject) => {\n        const reader = new FileReader();\n        reader.onload = () => {\n            if (!reader.result) {\n                return reject(new Error('Error while reading a file.'));\n            }\n            resolve(reader.result);\n        };\n        reader.onerror = (event) => {\n            if (!event.target) {\n                return reject(new Error('Error while reading a file.'));\n            }\n            const { error } = event.target;\n            if (!error) {\n                return reject(new Error('Error while reading a file.'));\n            }\n            switch (error.code) {\n                case error.NOT_FOUND_ERR:\n                    return reject(new Error('Error while reading a file: File not found.'));\n                case error.SECURITY_ERR:\n                    return reject(new Error('Error while reading a file: Security error.'));\n                case error.ABORT_ERR:\n                    return reject(new Error('Error while reading a file: Aborted.'));\n                default:\n                    return reject(new Error('Error while reading a file.'));\n            }\n        };\n        reader.readAsArrayBuffer(file);\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/shared/utils.js\n");

/***/ })

};
;