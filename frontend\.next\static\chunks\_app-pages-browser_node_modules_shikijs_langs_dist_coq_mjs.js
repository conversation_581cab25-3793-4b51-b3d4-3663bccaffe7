"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_coq_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/coq.mjs":
/*!**************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/coq.mjs ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Coq\\\",\\\"fileTypes\\\":[\\\"v\\\"],\\\"name\\\":\\\"coq\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(From|Require|Import|Export|Local|Global|Include)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.import.coq\\\"},{\\\"match\\\":\\\"\\\\\\\\b((Open|Close|Delimit|Undelimit|Bind)\\\\\\\\s+Scope)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.import.coq\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.source.coq\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.theorem.coq\\\"}},\\\"match\\\":\\\"\\\\\\\\b(Theorem|Lemma|Remark|Fact|Corollary|Property|Proposition)\\\\\\\\s+(([\\\\\\\\p{L}_ ])([\\\\\\\\p{L}0-9_ '])*)\\\"},{\\\"match\\\":\\\"\\\\\\\\bGoal\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.source.coq\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.source.coq\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.source.coq\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.assumption.coq\\\"}},\\\"match\\\":\\\"\\\\\\\\b(Parameters?|Axioms?|Conjectures?|Variables?|Hypothesis|Hypotheses)(\\\\\\\\s+Inline)?\\\\\\\\b\\\\\\\\s*\\\\\\\\(?\\\\\\\\s*(([\\\\\\\\p{L}_ ])([\\\\\\\\p{L}0-9_ '])*)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.source.coq\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.assumption.coq\\\"}},\\\"match\\\":\\\"\\\\\\\\b(Context)\\\\\\\\b\\\\\\\\s*`?\\\\\\\\s*([({])?\\\\\\\\s*(([\\\\\\\\p{L}_ ])([\\\\\\\\p{L}0-9_ '])*)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.source.coq\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.source.coq\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.function.coq\\\"}},\\\"match\\\":\\\"(\\\\\\\\b(?:Program|Local)\\\\\\\\s+)?\\\\\\\\b(Definition|Fixpoint|CoFixpoint|Function|Example|Let(?:\\\\\\\\s+Fixpoint|\\\\\\\\s+CoFixpoint)?|Instance|Equations|Equations?)\\\\\\\\s+(([\\\\\\\\p{L}_ ])([\\\\\\\\p{L}0-9_ '])*)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.source.coq\\\"}},\\\"match\\\":\\\"\\\\\\\\b((Show\\\\\\\\s+)?Obligation\\\\\\\\s+Tactic|Obligations\\\\\\\\s+of|Obligation|Next\\\\\\\\s+Obligation(\\\\\\\\s+of)?|Solve\\\\\\\\s+Obligations(\\\\\\\\s+of)?|Solve\\\\\\\\s+All\\\\\\\\s+Obligations|Admit\\\\\\\\s+Obligations(\\\\\\\\s+of)?|Instance)\\\\\\\\b\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.source.coq\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.type.coq\\\"}},\\\"match\\\":\\\"\\\\\\\\b(CoInductive|Inductive|Variant|Record|Structure|Class)\\\\\\\\s+(>\\\\\\\\s*)?(([\\\\\\\\p{L}_ ])([\\\\\\\\p{L}0-9_ '])*)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.source.coq\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.ltac\\\"}},\\\"match\\\":\\\"\\\\\\\\b(Ltac)\\\\\\\\s+(([\\\\\\\\p{L}_ ])([\\\\\\\\p{L}0-9_ '])*)\\\"},{\\\"match\\\":\\\"\\\\\\\\b(Hint(\\\\\\\\s+Mode)?|Create\\\\\\\\s+HintDb|Constructors|Resolve|Rewrite|Ltac|Ltac2|Implicit(\\\\\\\\s+Types)?|Set|Unset|Remove\\\\\\\\s+Printing|Arguments|((Tactic|Reserved)\\\\\\\\s+)?Notation|Infix|Section|Module(\\\\\\\\s+Type)?|End|Check|Print(\\\\\\\\s+All)?|Eval|Compute|Search|Universe|Coercions|Generalizable(\\\\\\\\s+(All|Variable))?|Existing(\\\\\\\\s+(Class|Instance))?|Canonical|About|Locate|Collection|Typeclasses\\\\\\\\s+(Opaque|Transparent))\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.source.coq\\\"},{\\\"match\\\":\\\"\\\\\\\\b(Proof|Qed|Defined|Save|Abort(\\\\\\\\s+All)?|Undo(\\\\\\\\s+To)?|Restart|Focus|Unfocus|Unfocused|Show\\\\\\\\s+Proof|Show\\\\\\\\s+Existentials|Show|Unshelve)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.source.coq\\\"},{\\\"match\\\":\\\"\\\\\\\\b(Quit|Drop|Time|Redirect|Timeout|Fail)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.debug.coq\\\"},{\\\"match\\\":\\\"\\\\\\\\b(admit|Admitted)\\\\\\\\b\\\",\\\"name\\\":\\\"invalid.illegal.admit.coq\\\"},{\\\"match\\\":\\\"[:|=<>*+\\\\\\\\-{}≠∨∧↔¬→≤≥]\\\",\\\"name\\\":\\\"keyword.operator.coq\\\"},{\\\"match\\\":\\\"\\\\\\\\b(forall|exists|Type|Set|Prop|nat|bool|option|list|unit|sum|prod|comparison|Empty_set)\\\\\\\\b|[∀∃]\\\",\\\"name\\\":\\\"support.type.coq\\\"},{\\\"match\\\":\\\"\\\\\\\\b(try|repeat|rew|progress|fresh|solve|now|first|tryif|at|once|do|only)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.ltac\\\"},{\\\"match\\\":\\\"\\\\\\\\b(into|with|eqn|by|move|as|using)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.ltac\\\"},{\\\"match\\\":\\\"\\\\\\\\b(match|lazymatch|multimatch|fun|with|return|end|let|in|if|then|else|fix|for|where|and)\\\\\\\\b|λ\\\",\\\"name\\\":\\\"keyword.control.gallina\\\"},{\\\"match\\\":\\\"\\\\\\\\b(intro|intros|revert|induction|destruct|auto|eauto|tauto|eassumption|apply|eapply|assumption|constructor|econstructor|reflexivity|inversion|injection|assert|split|esplit|omega|fold|unfold|specialize|rewrite|erewrite|change|symmetry|refine|simpl|intuition|firstorder|generalize|idtac|exist|exists|eexists|elim|eelim|rename|subst|congruence|trivial|left|right|set|pose|discriminate|clear|clearbody|contradict|contradiction|exact|dependent|remember|case|easy|unshelve|pattern|transitivity|etransitivity|f_equal|exfalso|replace|abstract|cycle|swap|revgoals|shelve|unshelve)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.builtin.ltac\\\"},{\\\"applyEndPatternLast\\\":1,\\\"begin\\\":\\\"\\\\\\\\(\\\\\\\\*(?!#)\\\",\\\"end\\\":\\\"\\\\\\\\*\\\\\\\\)\\\",\\\"name\\\":\\\"comment.block.coq\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block_comment\\\"},{\\\"include\\\":\\\"#block_double_quoted_string\\\"}]},{\\\"match\\\":\\\"\\\\\\\\b((0([xX])\\\\\\\\h+)|([0-9]+(\\\\\\\\.[0-9]+)?))\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.gallina\\\"},{\\\"match\\\":\\\"\\\\\\\\b(True|False|tt|false|true|Some|None|nil|cons|pair|inl|inr|[OS]|Eq|Lt|Gt|id|ex|all|unique)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.constructor.gallina\\\"},{\\\"match\\\":\\\"\\\\\\\\b_\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.wildcard.coq\\\"},{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.coq\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.coq\\\"}},\\\"name\\\":\\\"string.quoted.double.coq\\\"}],\\\"repository\\\":{\\\"block_comment\\\":{\\\"applyEndPatternLast\\\":1,\\\"begin\\\":\\\"\\\\\\\\(\\\\\\\\*(?!#)\\\",\\\"end\\\":\\\"\\\\\\\\*\\\\\\\\)\\\",\\\"name\\\":\\\"comment.block.coq\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block_comment\\\"},{\\\"include\\\":\\\"#block_double_quoted_string\\\"}]},\\\"block_double_quoted_string\\\":{\\\"applyEndPatternLast\\\":1,\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.coq\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.coq\\\"}},\\\"name\\\":\\\"string.quoted.double.coq\\\"}},\\\"scopeName\\\":\\\"source.coq\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/coq.mjs\n"));

/***/ })

}]);