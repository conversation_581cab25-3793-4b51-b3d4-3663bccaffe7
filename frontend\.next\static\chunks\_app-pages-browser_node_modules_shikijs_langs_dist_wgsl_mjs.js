"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_wgsl_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/wgsl.mjs":
/*!***************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/wgsl.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"WGSL\\\",\\\"name\\\":\\\"wgsl\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#line_comments\\\"},{\\\"include\\\":\\\"#block_comments\\\"},{\\\"include\\\":\\\"#keywords\\\"},{\\\"include\\\":\\\"#attributes\\\"},{\\\"include\\\":\\\"#functions\\\"},{\\\"include\\\":\\\"#function_calls\\\"},{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#types\\\"},{\\\"include\\\":\\\"#variables\\\"},{\\\"include\\\":\\\"#punctuation\\\"}],\\\"repository\\\":{\\\"attributes\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.attribute.at\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.attribute.wgsl\\\"}},\\\"match\\\":\\\"(@)([A-Za-z_]+)\\\",\\\"name\\\":\\\"meta.attribute.wgsl\\\"}]},\\\"block_comments\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"/\\\\\\\\*\\\\\\\\*/\\\",\\\"name\\\":\\\"comment.block.wgsl\\\"},{\\\"begin\\\":\\\"/\\\\\\\\*\\\\\\\\*\\\",\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"name\\\":\\\"comment.block.documentation.wgsl\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block_comments\\\"}]},{\\\"begin\\\":\\\"/\\\\\\\\*(?!\\\\\\\\*)\\\",\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"name\\\":\\\"comment.block.wgsl\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block_comments\\\"}]}]},\\\"constants\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(-?\\\\\\\\b[0-9][0-9]*\\\\\\\\.[0-9][0-9]*)([eE][+-]?[0-9]+)?\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.float.wgsl\\\"},{\\\"match\\\":\\\"-?\\\\\\\\b0x\\\\\\\\h+\\\\\\\\b|\\\\\\\\b0\\\\\\\\b|-?\\\\\\\\b[1-9][0-9]*\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.decimal.wgsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?:0x\\\\\\\\h+u\\\\\\\\b|0u\\\\\\\\b|[1-9][0-9]*u\\\\\\\\b)\\\",\\\"name\\\":\\\"constant.numeric.decimal.wgsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(true|false)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.boolean.wgsl\\\"}]},\\\"function_calls\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"([A-Za-z0-9_]+)(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.wgsl\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.brackets.round.wgsl\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.brackets.round.wgsl\\\"}},\\\"name\\\":\\\"meta.function.call.wgsl\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#line_comments\\\"},{\\\"include\\\":\\\"#block_comments\\\"},{\\\"include\\\":\\\"#keywords\\\"},{\\\"include\\\":\\\"#attributes\\\"},{\\\"include\\\":\\\"#function_calls\\\"},{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#types\\\"},{\\\"include\\\":\\\"#variables\\\"},{\\\"include\\\":\\\"#punctuation\\\"}]}]},\\\"functions\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\b(fn)\\\\\\\\s+([A-Za-z0-9_]+)((\\\\\\\\()|(<))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.fn.wgsl\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.wgsl\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.brackets.round.wgsl\\\"}},\\\"end\\\":\\\"\\\\\\\\{\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.brackets.curly.wgsl\\\"}},\\\"name\\\":\\\"meta.function.definition.wgsl\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#line_comments\\\"},{\\\"include\\\":\\\"#block_comments\\\"},{\\\"include\\\":\\\"#keywords\\\"},{\\\"include\\\":\\\"#attributes\\\"},{\\\"include\\\":\\\"#function_calls\\\"},{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#types\\\"},{\\\"include\\\":\\\"#variables\\\"},{\\\"include\\\":\\\"#punctuation\\\"}]}]},\\\"keywords\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(bitcast|block|break|case|continue|continuing|default|discard|else|elseif|enable|fallthrough|for|function|if|loop|private|read|read_write|return|storage|switch|uniform|while|workgroup|write)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.wgsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(asm|const|do|enum|handle|mat|premerge|regardless|typedef|unless|using|vec|void)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.wgsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(let|var)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.wgsl storage.type.wgsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(type)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.declaration.type.wgsl storage.type.wgsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(enum)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.declaration.enum.wgsl storage.type.wgsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(struct)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.declaration.struct.wgsl storage.type.wgsl\\\"},{\\\"match\\\":\\\"\\\\\\\\bfn\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.fn.wgsl\\\"},{\\\"match\\\":\\\"([\\\\\\\\^|]|\\\\\\\\|\\\\\\\\||&&|<<|>>|!)(?!=)\\\",\\\"name\\\":\\\"keyword.operator.logical.wgsl\\\"},{\\\"match\\\":\\\"&(?![\\\\\\\\&=])\\\",\\\"name\\\":\\\"keyword.operator.borrow.and.wgsl\\\"},{\\\"match\\\":\\\"(\\\\\\\\+=|-=|\\\\\\\\*=|/=|%=|\\\\\\\\^=|&=|\\\\\\\\|=|<<=|>>=)\\\",\\\"name\\\":\\\"keyword.operator.assignment.wgsl\\\"},{\\\"match\\\":\\\"(?<![<>])=(?![=>])\\\",\\\"name\\\":\\\"keyword.operator.assignment.equal.wgsl\\\"},{\\\"match\\\":\\\"(=(=)?(?!>)|!=|<=|(?<!=)>=)\\\",\\\"name\\\":\\\"keyword.operator.comparison.wgsl\\\"},{\\\"match\\\":\\\"(([+%]|(\\\\\\\\*(?!\\\\\\\\w)))(?!=))|(-(?!>))|(/(?!/))\\\",\\\"name\\\":\\\"keyword.operator.math.wgsl\\\"},{\\\"match\\\":\\\"\\\\\\\\.(?!\\\\\\\\.)\\\",\\\"name\\\":\\\"keyword.operator.access.dot.wgsl\\\"},{\\\"match\\\":\\\"->\\\",\\\"name\\\":\\\"keyword.operator.arrow.skinny.wgsl\\\"}]},\\\"line_comments\\\":{\\\"match\\\":\\\"\\\\\\\\s*//.*\\\",\\\"name\\\":\\\"comment.line.double-slash.wgsl\\\"},\\\"punctuation\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.comma.wgsl\\\"},{\\\"match\\\":\\\"[{}]\\\",\\\"name\\\":\\\"punctuation.brackets.curly.wgsl\\\"},{\\\"match\\\":\\\"[()]\\\",\\\"name\\\":\\\"punctuation.brackets.round.wgsl\\\"},{\\\"match\\\":\\\";\\\",\\\"name\\\":\\\"punctuation.semi.wgsl\\\"},{\\\"match\\\":\\\"[\\\\\\\\[\\\\\\\\]]\\\",\\\"name\\\":\\\"punctuation.brackets.square.wgsl\\\"},{\\\"match\\\":\\\"(?<![=-])[<>]\\\",\\\"name\\\":\\\"punctuation.brackets.angle.wgsl\\\"}]},\\\"types\\\":{\\\"name\\\":\\\"storage.type.wgsl\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(bool|i32|u32|f32)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.wgsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(i64|u64|f64)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.wgsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(vec(?:2i|3i|4i|2u|3u|4u|2f|3f|4f|2h|3h|4h))\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.wgsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(mat(?:2x2f|2x3f|2x4f|3x2f|3x3f|3x4f|4x2f|4x3f|4x4f|2x2h|2x3h|2x4h|3x2h|3x3h|3x4h|4x2h|4x3h|4x4h))\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.wgsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(vec[2-4]|mat[2-4]x[2-4])\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.wgsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(atomic)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.wgsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(array)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.wgsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b([A-Z][A-Za-z0-9]*)\\\\\\\\b\\\",\\\"name\\\":\\\"entity.name.type.wgsl\\\"}]},\\\"variables\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(?<!(?<!\\\\\\\\.)\\\\\\\\.)(?:r#(?!(crate|[Ss]elf|super)))?[a-z0-9_]+\\\\\\\\b\\\",\\\"name\\\":\\\"variable.other.wgsl\\\"}]}},\\\"scopeName\\\":\\\"source.wgsl\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/wgsl.mjs\n"));

/***/ })

}]);