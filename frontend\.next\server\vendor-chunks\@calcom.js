"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@calcom";
exports.ids = ["vendor-chunks/@calcom"];
exports.modules = {

/***/ "(ssr)/./node_modules/@calcom/embed-react/dist/Cal.es.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/@calcom/embed-react/dist/Cal.es.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ R),\n/* harmony export */   getCalApi: () => (/* binding */ j)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\n\nconst b = \"https://app.cal.com/embed/embed.js\";\nfunction m(s = b) {\n  (function(r, e, l) {\n    let t = function(n, i) {\n      n.q.push(i);\n    }, o = r.document;\n    r.Cal = r.Cal || function() {\n      let n = r.Cal, i = arguments;\n      if (n.loaded || (n.ns = {}, n.q = n.q || [], o.head.appendChild(o.createElement(\"script\")).src = e, n.loaded = !0), i[0] === l) {\n        const u = function() {\n          t(u, arguments);\n        }, c = i[1];\n        u.q = u.q || [], typeof c == \"string\" ? (n.ns[c] = n.ns[c] || u, t(n.ns[c], i), t(n, [\"initNamespace\", c])) : t(n, i);\n        return;\n      }\n      t(n, i);\n    };\n  })(\n    window,\n    //! Replace it with \"https://cal.com/embed.js\" or the URL where you have embed.js installed\n    s,\n    \"init\"\n  );\n  /*!  Copying ends here. */\n  return window.Cal;\n}\nm.toString();\nfunction q(s) {\n  const [r, e] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    e(() => m(s));\n  }, []), r;\n}\nconst h = function(r) {\n  const {\n    calLink: e,\n    calOrigin: l,\n    namespace: t = \"\",\n    config: o,\n    initConfig: n = {},\n    embedJsUrl: i,\n    ...u\n  } = r;\n  if (!e)\n    throw new Error(\"calLink is required\");\n  const c = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(!1), a = q(i), f = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!a || c.current || !f.current)\n      return;\n    c.current = !0;\n    const d = f.current;\n    t ? (a(\"init\", t, {\n      ...n,\n      origin: l\n    }), a.ns[t](\"inline\", {\n      elementOrSelector: d,\n      calLink: e,\n      config: o\n    })) : (a(\"init\", {\n      ...n,\n      origin: l\n    }), a(\"inline\", {\n      elementOrSelector: d,\n      calLink: e,\n      config: o\n    }));\n  }, [a, e, o, t, l, n]), a ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"div\", {\n    ref: f,\n    ...u\n  }) : null;\n}, R = h;\nfunction j(s) {\n  const r = typeof s == \"string\" ? { embedJsUrl: s } : s ?? {}, { namespace: e = \"\", embedJsUrl: l } = r;\n  return new Promise(function t(o) {\n    const n = m(l);\n    n(\"init\", e);\n    const i = e ? n.ns[e] : n;\n    if (!i) {\n      setTimeout(() => {\n        t(o);\n      }, 50);\n      return;\n    }\n    o(i);\n  });\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@calcom/embed-react/dist/Cal.es.mjs\n");

/***/ })

};
;