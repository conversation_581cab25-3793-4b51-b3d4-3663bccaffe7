"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_sdbl_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/sdbl.mjs":
/*!***************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/sdbl.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"1C (Query)\\\",\\\"fileTypes\\\":[\\\"sdbl\\\",\\\"query\\\"],\\\"firstLineMatch\\\":\\\"(?i)Выбрать|Select(\\\\\\\\s+Разрешенные|\\\\\\\\s+Allowed)?(\\\\\\\\s+Различные|\\\\\\\\s+Distinct)?(\\\\\\\\s+Первые|\\\\\\\\s+Top)?.*\\\",\\\"name\\\":\\\"sdbl\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(^\\\\\\\\s*//.*$)\\\",\\\"name\\\":\\\"comment.line.double-slash.sdbl\\\"},{\\\"begin\\\":\\\"//\\\",\\\"end\\\":\\\"$\\\",\\\"name\\\":\\\"comment.line.double-slash.sdbl\\\"},{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"end\\\":\\\"\\\\\\\"(?!\\\\\\\")\\\",\\\"name\\\":\\\"string.quoted.double.sdbl\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\"\\\\\\\"\\\",\\\"name\\\":\\\"constant.character.escape.sdbl\\\"},{\\\"match\\\":\\\"(^\\\\\\\\s*//.*$)\\\",\\\"name\\\":\\\"comment.line.double-slash.sdbl\\\"}]},{\\\"match\\\":\\\"(?i)(?<=[^\\\\\\\\wа-яё.]|^)(Неопределено|Undefined|Истина|True|Ложь|False|NULL)(?=[^\\\\\\\\wа-яё.]|$)\\\",\\\"name\\\":\\\"constant.language.sdbl\\\"},{\\\"match\\\":\\\"(?<=[^\\\\\\\\wа-яё.]|^)(\\\\\\\\d+\\\\\\\\.?\\\\\\\\d*)(?=[^\\\\\\\\wа-яё.]|$)\\\",\\\"name\\\":\\\"constant.numeric.sdbl\\\"},{\\\"match\\\":\\\"(?i)(?<=[^\\\\\\\\wа-яё.]|^)(Выбор|Case|Когда|When|Тогда|Then|Иначе|Else|Конец|End)(?=[^\\\\\\\\wа-яё.]|$)\\\",\\\"name\\\":\\\"keyword.control.conditional.sdbl\\\"},{\\\"match\\\":\\\"(?i)(?<!КАК\\\\\\\\s|AS\\\\\\\\s)(?<=[^\\\\\\\\wа-яё.]|^)(НЕ|NOT|И|AND|ИЛИ|OR|В\\\\\\\\s+ИЕРАРХИИ|IN\\\\\\\\s+HIERARCHY|В|In|Между|Between|Есть(\\\\\\\\s+НЕ)?\\\\\\\\s+NULL|Is(\\\\\\\\s+NOT)?\\\\\\\\s+NULL|Ссылка|Refs|Подобно|Like)(?=[^\\\\\\\\wа-яё.]|$)\\\",\\\"name\\\":\\\"keyword.operator.logical.sdbl\\\"},{\\\"match\\\":\\\"<=|>=|[=<>]\\\",\\\"name\\\":\\\"keyword.operator.comparison.sdbl\\\"},{\\\"match\\\":\\\"([+\\\\\\\\-*/%])\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.sdbl\\\"},{\\\"match\\\":\\\"([,;])\\\",\\\"name\\\":\\\"keyword.operator.sdbl\\\"},{\\\"match\\\":\\\"(?i)(?<=[^\\\\\\\\wа-яё.]|^)(Выбрать|Select|Разрешенные|Allowed|Различные|Distinct|Первые|Top|Как|As|ПустаяТаблица|EmptyTable|Поместить|Into|Уничтожить|Drop|Из|From|((Левое|Left|Правое|Right|Полное|Full)\\\\\\\\s+(Внешнее\\\\\\\\s+|Outer\\\\\\\\s+)?Соединение|Join)|((Внутреннее|Inner)\\\\\\\\s+Соединение|Join)|Где|Where|(Сгруппировать\\\\\\\\s+По(\\\\\\\\s+Группирующим\\\\\\\\s+Наборам)?)|(Group\\\\\\\\s+By(\\\\\\\\s+Grouping\\\\\\\\s+Set)?)|Имеющие|Having|Объединить(\\\\\\\\s+Все)?|Union(\\\\\\\\s+All)?|(Упорядочить\\\\\\\\s+По)|(Order\\\\\\\\s+By)|Автоупорядочивание|Autoorder|Итоги|Totals|По(\\\\\\\\s+Общие)?|By(\\\\\\\\s+Overall)?|(Только\\\\\\\\s+)?Иерархия|(Only\\\\\\\\s+)?Hierarchy|Периодами|Periods|Индексировать|Index|Выразить|Cast|Возр|Asc|Убыв|Desc|Для\\\\\\\\s+Изменения|(For\\\\\\\\s+Update(\\\\\\\\s+Of)?)|Спецсимвол|Escape|СгруппированоПо|GroupedBy)(?=[^\\\\\\\\wа-яё.]|$)\\\",\\\"name\\\":\\\"keyword.control.sdbl\\\"},{\\\"match\\\":\\\"(?i)(?<=[^\\\\\\\\wа-яё.]|^)(Значение|Value|ДатаВремя|DateTime|Тип|Type)(?=\\\\\\\\()\\\",\\\"name\\\":\\\"support.function.sdbl\\\"},{\\\"match\\\":\\\"(?i)(?<=[^\\\\\\\\wа-яё.]|^)(Подстрока|Substring|НРег|Lower|ВРег|Upper|Лев|Left|Прав|Right|ДлинаСтроки|StringLength|СтрНайти|StrFind|СтрЗаменить|StrReplace|СокрЛП|TrimAll|СокрЛ|TrimL|СокрП|TrimR)(?=\\\\\\\\()\\\",\\\"name\\\":\\\"support.function.sdbl\\\"},{\\\"match\\\":\\\"(?i)(?<=[^\\\\\\\\wа-яё.]|^)(Год|Year|Квартал|Quarter|Месяц|Month|ДеньГода|DayOfYear|День|Day|Неделя|Week|ДеньНедели|Weekday|Час|Hour|Минута|Minute|Секунда|Second|НачалоПериода|BeginOfPeriod|КонецПериода|EndOfPeriod|ДобавитьКДате|DateAdd|РазностьДат|DateDiff|Полугодие|HalfYear|Декада|TenDays)(?=\\\\\\\\()\\\",\\\"name\\\":\\\"support.function.sdbl\\\"},{\\\"match\\\":\\\"(?i)(?<=[^\\\\\\\\wа-яё.]|^)(ACOS|COS|ASIN|SIN|ATAN|TAN|EXP|POW|LOG|LOG10|Цел|Int|Окр|Round|SQRT)(?=\\\\\\\\()\\\",\\\"name\\\":\\\"support.function.sdbl\\\"},{\\\"match\\\":\\\"(?i)(?<=[^\\\\\\\\wа-яё.]|^)(Сумма|Sum|Среднее|Avg|Минимум|Min|Максимум|Max|Количество|Count)(?=\\\\\\\\()\\\",\\\"name\\\":\\\"support.function.sdbl\\\"},{\\\"match\\\":\\\"(?i)(?<=[^\\\\\\\\wа-яё.]|^)(ЕстьNULL|IsNULL|Представление|Presentation|ПредставлениеСсылки|RefPresentation|ТипЗначения|ValueType|АвтономерЗаписи|RecordAutoNumber|РазмерХранимыхДанных|StoredDataSize|УникальныйИдентификатор|UUID)(?=\\\\\\\\()\\\",\\\"name\\\":\\\"support.function.sdbl\\\"},{\\\"match\\\":\\\"(?i)(?<=[^\\\\\\\\wа-яё.])(Число|Number|Строка|String|Дата|Date|Булево|Boolean)(?=[^\\\\\\\\wа-яё.]|$)\\\",\\\"name\\\":\\\"support.type.sdbl\\\"},{\\\"match\\\":\\\"(&[\\\\\\\\wа-яё]+)\\\",\\\"name\\\":\\\"variable.parameter.sdbl\\\"}],\\\"scopeName\\\":\\\"source.sdbl\\\",\\\"aliases\\\":[\\\"1c-query\\\"]}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/sdbl.mjs\n"));

/***/ })

}]);