"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/number-flow";
exports.ids = ["vendor-chunks/number-flow"];
exports.modules = {

/***/ "(ssr)/./node_modules/number-flow/dist/lite-CzDvSY_4.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/number-flow/dist/lite-CzDvSY_4.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   D: () => (/* binding */ D),\n/* harmony export */   N: () => (/* binding */ J),\n/* harmony export */   S: () => (/* binding */ G),\n/* harmony export */   c: () => (/* binding */ q),\n/* harmony export */   d: () => (/* binding */ st),\n/* harmony export */   f: () => (/* binding */ nt),\n/* harmony export */   p: () => (/* binding */ A),\n/* harmony export */   r: () => (/* binding */ at)\n/* harmony export */ });\n/* harmony import */ var esm_env__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! esm-env */ \"(ssr)/./node_modules/esm-env/index.js\");\n/* harmony import */ var _plugins_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./plugins.mjs */ \"(ssr)/./node_modules/number-flow/dist/plugins.mjs\");\n\n\nconst m = (n, t, e) => {\n  const i = document.createElement(n), [s, a] = Array.isArray(t) ? [void 0, t] : [t, e];\n  return s && Object.assign(i, s), a == null || a.forEach((r) => i.appendChild(r)), i;\n}, F = (n, t) => {\n  var e;\n  return t === \"left\" ? n.offsetLeft : (((e = n.offsetParent instanceof HTMLElement ? n.offsetParent : null) == null ? void 0 : e.offsetWidth) ?? 0) - n.offsetWidth - n.offsetLeft;\n}, H = (n) => n.offsetWidth > 0 && n.offsetHeight > 0, st = (n, t) => {\n  esm_env__WEBPACK_IMPORTED_MODULE_0__.BROWSER && customElements.get(n) !== t && customElements.define(n, t);\n};\nfunction X(n, t, { reverse: e = !1 } = {}) {\n  const i = n.length;\n  for (let s = e ? i - 1 : 0; e ? s >= 0 : s < i; e ? s-- : s++)\n    t(n[s], s);\n}\nfunction nt(n, t, e, i) {\n  const s = t.formatToParts(n);\n  e && s.unshift({ type: \"prefix\", value: e }), i && s.push({ type: \"suffix\", value: i });\n  const a = [], r = [], o = [], c = [], d = {}, p = (h) => `${h}:${d[h] = (d[h] ?? -1) + 1}`;\n  let x = \"\", g = !1, y = !1;\n  for (const h of s) {\n    x += h.value;\n    const l = h.type === \"minusSign\" || h.type === \"plusSign\" ? \"sign\" : h.type;\n    l === \"integer\" ? (g = !0, r.push(...h.value.split(\"\").map((C) => ({ type: l, value: parseInt(C) })))) : l === \"group\" ? r.push({ type: l, value: h.value }) : l === \"decimal\" ? (y = !0, o.push({ type: l, value: h.value, key: p(l) })) : l === \"fraction\" ? o.push(...h.value.split(\"\").map((C) => ({\n      type: l,\n      value: parseInt(C),\n      key: p(l),\n      pos: -1 - d[l]\n    }))) : (g || y ? c : a).push({\n      type: l,\n      value: h.value,\n      key: p(l)\n    });\n  }\n  const T = [];\n  for (let h = r.length - 1; h >= 0; h--) {\n    const l = r[h];\n    T.unshift(l.type === \"integer\" ? {\n      ...l,\n      key: p(l.type),\n      pos: d[l.type]\n    } : {\n      ...l,\n      key: p(l.type)\n    });\n  }\n  return {\n    pre: a,\n    integer: T,\n    fraction: o,\n    post: c,\n    valueAsString: x,\n    value: typeof n == \"string\" ? parseFloat(n) : n\n  };\n}\nconst I = String.raw, V = String.raw, O = esm_env__WEBPACK_IMPORTED_MODULE_0__.BROWSER && (() => {\n  try {\n    document.createElement(\"div\").animate({ opacity: 0 }, { easing: \"linear(0, 1)\" });\n  } catch {\n    return !1;\n  }\n  return !0;\n})(), z = esm_env__WEBPACK_IMPORTED_MODULE_0__.BROWSER && typeof CSS < \"u\" && CSS.supports && CSS.supports(\"line-height\", \"mod(1,1)\"), A = esm_env__WEBPACK_IMPORTED_MODULE_0__.BROWSER && typeof matchMedia < \"u\" ? matchMedia(\"(prefers-reduced-motion: reduce)\") : null, $ = \"--_number-flow-d-opacity\", U = \"--_number-flow-d-width\", S = \"--_number-flow-dx\", j = \"--_number-flow-d\", Y = (() => {\n  try {\n    return CSS.registerProperty({\n      name: $,\n      syntax: \"<number>\",\n      inherits: !1,\n      initialValue: \"0\"\n    }), CSS.registerProperty({\n      name: S,\n      syntax: \"<length>\",\n      inherits: !0,\n      initialValue: \"0px\"\n    }), CSS.registerProperty({\n      name: U,\n      syntax: \"<number>\",\n      inherits: !1,\n      initialValue: \"0\"\n    }), CSS.registerProperty({\n      name: j,\n      syntax: \"<number>\",\n      inherits: !0,\n      initialValue: \"0\"\n    }), !0;\n  } catch {\n    return !1;\n  }\n})(), P = \"var(--number-flow-char-height, 1em)\", f = \"var(--number-flow-mask-height, 0.25em)\", k = `calc(${f} / 2)`, E = \"var(--number-flow-mask-width, 0.5em)\", u = `calc(${E} / var(--scale-x))`, w = \"#000 0, transparent 71%\", M = V`:host{display:inline-block;direction:ltr;white-space:nowrap;isolation:isolate;line-height:${P} !important}.number,.number__inner{display:inline-block;transform-origin:left top}:host([data-will-change]) :is(.number,.number__inner,.section,.digit,.digit__num,.symbol){will-change:transform}.number{--scale-x:calc(1 + var(${U}) / var(--width));transform:translateX(var(${S})) scaleX(var(--scale-x));margin:0 calc(-1 * ${E});position:relative;-webkit-mask-image:linear-gradient(to right,transparent 0,#000 ${u},#000 calc(100% - ${u}),transparent ),linear-gradient(to bottom,transparent 0,#000 ${f},#000 calc(100% - ${f}),transparent 100% ),radial-gradient(at bottom right,${w}),radial-gradient(at bottom left,${w}),radial-gradient(at top left,${w}),radial-gradient(at top right,${w});-webkit-mask-size:100% calc(100% - ${f} * 2),calc(100% - ${u} * 2) 100%,${u} ${f},${u} ${f},${u} ${f},${u} ${f};-webkit-mask-position:center,center,top left,top right,bottom right,bottom left;-webkit-mask-repeat:no-repeat}.number__inner{padding:${k} ${E};transform:scaleX(calc(1 / var(--scale-x))) translateX(calc(-1 * var(${S})))}:host > :not(.number){z-index:5}.section,.symbol{display:inline-block;position:relative;isolation:isolate}.section::after{content:'\\200b';display:inline-block}.section--justify-left{transform-origin:center left}.section--justify-right{transform-origin:center right}.section > [inert],.symbol > [inert]{margin:0 !important;position:absolute !important;z-index:-1}.digit{display:inline-block;position:relative;--c:var(--current) + var(${j})}.digit__num,.number .section::after{padding:${k} 0}.digit__num{display:inline-block;--offset-raw:mod(var(--length) + var(--n) - mod(var(--c),var(--length)),var(--length));--offset:calc( var(--offset-raw) - var(--length) * round(down,var(--offset-raw) / (var(--length) / 2),1) );--y:clamp(-100%,var(--offset) * 100%,100%);transform:translateY(var(--y))}.digit__num[inert]{position:absolute;top:0;left:50%;transform:translateX(-50%) translateY(var(--y))}.digit:not(.is-spinning) .digit__num[inert]{display:none}.symbol__value{display:inline-block;mix-blend-mode:plus-lighter;white-space:pre}.section--justify-left .symbol > [inert]{left:0}.section--justify-right .symbol > [inert]{right:0}.animate-presence{opacity:calc(1 + var(${$}))}`, G = esm_env__WEBPACK_IMPORTED_MODULE_0__.BROWSER ? HTMLElement : class {\n}, K = V`:host{display:inline-block;direction:ltr;white-space:nowrap;line-height:${P} !important}span{display:inline-block}:host([data-will-change]) span{will-change:transform}.number,.digit{padding:${k} 0}.symbol{white-space:pre}`, Z = (n) => `<span class=\"${n.type === \"integer\" || n.type === \"fraction\" ? \"digit\" : \"symbol\"}\" part=\"${n.type === \"integer\" || n.type === \"fraction\" ? `digit ${n.type}-digit` : `symbol ${n.type}`}\">${n.value}</span>`, v = (n, t) => `<span part=\"${t}\">${n.reduce((e, i) => e + Z(i), \"\")}</span>`, at = (n) => (\n  // shadowroot=\"open\" non-standard attribute for old Chrome:\n  I`<template shadowroot=\"open\" shadowrootmode=\"open\"\n\t\t\t><style>\n\t\t\t\t${K}</style\n\t\t\t><span role=\"img\" aria-label=\"${n.valueAsString}\"\n\t\t\t\t>${v(n.pre, \"left\")}<span part=\"number\" class=\"number\"\n\t\t\t\t\t>${v(n.integer, \"integer\")}${v(n.fraction, \"fraction\")}</span\n\t\t\t\t>${v(n.post, \"right\")}</span\n\t\t\t></template\n\t\t><span\n\t\t\tstyle=\"font-kerning: none; display: inline-block; line-height: ${P} !important; padding: ${f} 0;\"\n\t\t\t>${n.valueAsString}</span\n\t\t>`\n), q = z && O && Y;\nlet b;\nclass J extends G {\n  constructor() {\n    super(), this.created = !1, this.batched = !1;\n    const { animated: t, ...e } = this.constructor.defaultProps;\n    this._animated = this.computedAnimated = t, Object.assign(this, e);\n  }\n  get animated() {\n    return this._animated;\n  }\n  set animated(t) {\n    var e;\n    this.animated !== t && (this._animated = t, (e = this.shadowRoot) == null || e.getAnimations().forEach((i) => i.finish()));\n  }\n  /**\n   * @internal\n   */\n  set data(t) {\n    var o;\n    if (t == null)\n      return;\n    const { pre: e, integer: i, fraction: s, post: a, value: r } = t;\n    if (this.created) {\n      const c = this._data;\n      this._data = t, this.computedTrend = typeof this.trend == \"function\" ? this.trend(c.value, r) : this.trend, this.computedAnimated = q && this._animated && (!this.respectMotionPreference || !(A != null && A.matches)) && // https://github.com/barvian/number-flow/issues/9\n      H(this), (o = this.plugins) == null || o.forEach((d) => {\n        var p;\n        return (p = d.onUpdate) == null ? void 0 : p.call(d, t, c, this);\n      }), this.batched || this.willUpdate(), this._pre.update(e), this._num.update({ integer: i, fraction: s }), this._post.update(a), this.batched || this.didUpdate();\n    } else {\n      this._data = t, this.attachShadow({ mode: \"open\" });\n      try {\n        this._internals ?? (this._internals = this.attachInternals()), this._internals.role = \"img\";\n      } catch {\n      }\n      if (typeof CSSStyleSheet < \"u\" && this.shadowRoot.adoptedStyleSheets)\n        b || (b = new CSSStyleSheet(), b.replaceSync(M)), this.shadowRoot.adoptedStyleSheets = [b];\n      else {\n        const c = document.createElement(\"style\");\n        c.textContent = M, this.shadowRoot.appendChild(c);\n      }\n      this._pre = new N(this, e, {\n        justify: \"right\",\n        part: \"left\"\n      }), this.shadowRoot.appendChild(this._pre.el), this._num = new Q(this, i, s), this.shadowRoot.appendChild(this._num.el), this._post = new N(this, a, {\n        justify: \"left\",\n        part: \"right\"\n      }), this.shadowRoot.appendChild(this._post.el), this.created = !0;\n    }\n    try {\n      this._internals.ariaLabel = t.valueAsString;\n    } catch {\n    }\n  }\n  /**\n   * @internal\n   */\n  willUpdate() {\n    this._pre.willUpdate(), this._num.willUpdate(), this._post.willUpdate();\n  }\n  /**\n   * @internal\n   */\n  didUpdate() {\n    if (!this.computedAnimated)\n      return;\n    this._abortAnimationsFinish ? this._abortAnimationsFinish.abort() : this.dispatchEvent(new Event(\"animationsstart\")), this._pre.didUpdate(), this._num.didUpdate(), this._post.didUpdate();\n    const t = new AbortController();\n    Promise.all(this.shadowRoot.getAnimations().map((e) => e.finished)).then(() => {\n      t.signal.aborted || (this.dispatchEvent(new Event(\"animationsfinish\")), this._abortAnimationsFinish = void 0);\n    }), this._abortAnimationsFinish = t;\n  }\n}\nJ.defaultProps = {\n  transformTiming: {\n    duration: 900,\n    // Make sure to keep this minified:\n    easing: \"linear(0,.005,.019,.039,.066,.096,.129,.165,.202,.24,.278,.316,.354,.39,.426,.461,.494,.526,.557,.586,.614,.64,.665,.689,.711,.731,.751,.769,.786,.802,.817,.831,.844,.856,.867,.877,.887,.896,.904,.912,.919,.925,.931,.937,.942,.947,.951,.955,.959,.962,.965,.968,.971,.973,.976,.978,.98,.981,.983,.984,.986,.987,.988,.989,.99,.991,.992,.992,.993,.994,.994,.995,.995,.996,.996,.9963,.9967,.9969,.9972,.9975,.9977,.9979,.9981,.9982,.9984,.9985,.9987,.9988,.9989,1)\"\n  },\n  spinTiming: void 0,\n  opacityTiming: { duration: 450, easing: \"ease-out\" },\n  animated: !0,\n  trend: (n, t) => Math.sign(t - n),\n  respectMotionPreference: !0,\n  plugins: void 0,\n  digits: void 0\n};\nclass Q {\n  constructor(t, e, i, { className: s, ...a } = {}) {\n    this.flow = t, this._integer = new L(t, e, {\n      justify: \"right\",\n      part: \"integer\"\n    }), this._fraction = new L(t, i, {\n      justify: \"left\",\n      part: \"fraction\"\n    }), this._inner = m(\"span\", {\n      className: \"number__inner\"\n    }, [this._integer.el, this._fraction.el]), this.el = m(\"span\", {\n      ...a,\n      part: \"number\",\n      className: `number ${s ?? \"\"}`\n    }, [this._inner]);\n  }\n  willUpdate() {\n    this._prevWidth = this.el.offsetWidth, this._prevLeft = this.el.getBoundingClientRect().left, this._integer.willUpdate(), this._fraction.willUpdate();\n  }\n  update({ integer: t, fraction: e }) {\n    this._integer.update(t), this._fraction.update(e);\n  }\n  didUpdate() {\n    const t = this.el.getBoundingClientRect();\n    this._integer.didUpdate(), this._fraction.didUpdate();\n    const e = this._prevLeft - t.left, i = this.el.offsetWidth, s = this._prevWidth - i;\n    this.el.style.setProperty(\"--width\", String(i)), this.el.animate({\n      [S]: [`${e}px`, \"0px\"],\n      [U]: [s, 0]\n    }, {\n      ...this.flow.transformTiming,\n      composite: \"accumulate\"\n    });\n  }\n}\nclass W {\n  constructor(t, e, { justify: i, className: s, ...a }, r) {\n    this.flow = t, this.children = /* @__PURE__ */ new Map(), this.onCharRemove = (c) => () => {\n      this.children.delete(c);\n    }, this.justify = i;\n    const o = e.map((c) => this.addChar(c).el);\n    this.el = m(\"span\", {\n      ...a,\n      className: `section section--justify-${i} ${s ?? \"\"}`\n    }, r ? r(o) : o);\n  }\n  addChar(t, { startDigitsAtZero: e = !1, ...i } = {}) {\n    const s = t.type === \"integer\" || t.type === \"fraction\" ? new D(this, t.type, e ? 0 : t.value, t.pos, {\n      ...i,\n      onRemove: this.onCharRemove(t.key)\n    }) : new tt(this, t.type, t.value, {\n      ...i,\n      onRemove: this.onCharRemove(t.key)\n    });\n    return this.children.set(t.key, s), s;\n  }\n  unpop(t) {\n    t.el.removeAttribute(\"inert\"), t.el.style.top = \"\", t.el.style[this.justify] = \"\";\n  }\n  pop(t) {\n    t.forEach((e) => {\n      e.el.style.top = `${e.el.offsetTop}px`, e.el.style[this.justify] = `${F(e.el, this.justify)}px`;\n    }), t.forEach((e) => {\n      e.el.setAttribute(\"inert\", \"\"), e.present = !1;\n    });\n  }\n  addNewAndUpdateExisting(t) {\n    const e = /* @__PURE__ */ new Map(), i = /* @__PURE__ */ new Map(), s = this.justify === \"left\", a = s ? \"prepend\" : \"append\";\n    if (X(t, (r) => {\n      let o;\n      this.children.has(r.key) ? (o = this.children.get(r.key), i.set(r, o), this.unpop(o), o.present = !0) : (o = this.addChar(r, { startDigitsAtZero: !0, animateIn: !0 }), e.set(r, o)), this.el[a](o.el);\n    }, { reverse: s }), this.flow.computedAnimated) {\n      const r = this.el.getBoundingClientRect();\n      e.forEach((o) => {\n        o.willUpdate(r);\n      });\n    }\n    e.forEach((r, o) => {\n      r.update(o.value);\n    }), i.forEach((r, o) => {\n      r.update(o.value);\n    });\n  }\n  willUpdate() {\n    const t = this.el.getBoundingClientRect();\n    this._prevOffset = t[this.justify], this.children.forEach((e) => e.willUpdate(t));\n  }\n  didUpdate() {\n    const t = this.el.getBoundingClientRect();\n    this.children.forEach((s) => s.didUpdate(t));\n    const e = t[this.justify], i = this._prevOffset - e;\n    i && this.children.size && this.el.animate({\n      transform: [`translateX(${i}px)`, \"none\"]\n    }, {\n      ...this.flow.transformTiming,\n      composite: \"accumulate\"\n    });\n  }\n}\nclass L extends W {\n  update(t) {\n    const e = /* @__PURE__ */ new Map();\n    this.children.forEach((i, s) => {\n      t.find((a) => a.key === s) || e.set(s, i), this.unpop(i);\n    }), this.addNewAndUpdateExisting(t), e.forEach((i) => {\n      i instanceof D && i.update(0);\n    }), this.pop(e);\n  }\n}\nclass N extends W {\n  update(t) {\n    const e = /* @__PURE__ */ new Map();\n    this.children.forEach((i, s) => {\n      t.find((a) => a.key === s) || e.set(s, i);\n    }), this.pop(e), this.addNewAndUpdateExisting(t);\n  }\n}\nclass R {\n  constructor(t, e, { onRemove: i, animateIn: s = !1 } = {}) {\n    this.flow = t, this.el = e, this._present = !0, this._remove = () => {\n      var a;\n      this.el.remove(), (a = this._onRemove) == null || a.call(this);\n    }, this.el.classList.add(\"animate-presence\"), this.flow.computedAnimated && s && this.el.animate({\n      [$]: [-0.9999, 0]\n    }, {\n      ...this.flow.opacityTiming,\n      composite: \"accumulate\"\n    }), this._onRemove = i;\n  }\n  get present() {\n    return this._present;\n  }\n  set present(t) {\n    if (this._present !== t) {\n      if (this._present = t, t ? this.el.removeAttribute(\"inert\") : this.el.setAttribute(\"inert\", \"\"), !this.flow.computedAnimated) {\n        t || this._remove();\n        return;\n      }\n      this.el.style.setProperty(\"--_number-flow-d-opacity\", t ? \"0\" : \"-.999\"), this.el.animate({\n        [$]: t ? [-0.9999, 0] : [0.999, 0]\n      }, {\n        ...this.flow.opacityTiming,\n        composite: \"accumulate\"\n      }), t ? this.flow.removeEventListener(\"animationsfinish\", this._remove) : this.flow.addEventListener(\"animationsfinish\", this._remove, {\n        once: !0\n      });\n    }\n  }\n}\nclass B extends R {\n  constructor(t, e, i, s) {\n    super(t.flow, i, s), this.section = t, this.value = e, this.el = i;\n  }\n}\nclass D extends B {\n  constructor(t, e, i, s, a) {\n    var d, p;\n    const r = (((p = (d = t.flow.digits) == null ? void 0 : d[s]) == null ? void 0 : p.max) ?? 9) + 1, o = Array.from({ length: r }).map((x, g) => {\n      const y = m(\"span\", { className: \"digit__num\" }, [\n        document.createTextNode(String(g))\n      ]);\n      return g !== i && y.setAttribute(\"inert\", \"\"), y.style.setProperty(\"--n\", String(g)), y;\n    }), c = m(\"span\", {\n      part: `digit ${e}-digit`,\n      className: \"digit\"\n    }, o);\n    c.style.setProperty(\"--current\", String(i)), c.style.setProperty(\"--length\", String(r)), super(t, i, c, a), this.pos = s, this._onAnimationsFinish = () => {\n      this.el.classList.remove(\"is-spinning\");\n    }, this._numbers = o, this.length = r;\n  }\n  willUpdate(t) {\n    const e = this.el.getBoundingClientRect();\n    this._prevValue = this.value;\n    const i = e[this.section.justify] - t[this.section.justify], s = e.width / 2;\n    this._prevCenter = this.section.justify === \"left\" ? i + s : i - s;\n  }\n  update(t) {\n    this.el.style.setProperty(\"--current\", String(t)), this._numbers.forEach((e, i) => i === t ? e.removeAttribute(\"inert\") : e.setAttribute(\"inert\", \"\")), this.value = t;\n  }\n  didUpdate(t) {\n    const e = this.el.getBoundingClientRect(), i = e[this.section.justify] - t[this.section.justify], s = e.width / 2, a = this.section.justify === \"left\" ? i + s : i - s, r = this._prevCenter - a;\n    r && this.el.animate({\n      transform: [`translateX(${r}px)`, \"none\"]\n    }, {\n      ...this.flow.transformTiming,\n      composite: \"accumulate\"\n    });\n    const o = this.getDelta();\n    o && (this.el.classList.add(\"is-spinning\"), this.el.animate({\n      [j]: [-o, 0]\n    }, {\n      ...this.flow.spinTiming ?? this.flow.transformTiming,\n      composite: \"accumulate\"\n    }), this.flow.addEventListener(\"animationsfinish\", this._onAnimationsFinish, { once: !0 }));\n  }\n  getDelta() {\n    var i;\n    if (this.flow.plugins)\n      for (const s of this.flow.plugins) {\n        const a = (i = s.getDelta) == null ? void 0 : i.call(s, this.value, this._prevValue, this);\n        if (a != null)\n          return a;\n      }\n    const t = this.value - this._prevValue, e = this.flow.computedTrend || Math.sign(t);\n    return e < 0 && this.value > this._prevValue ? this.value - this.length - this._prevValue : e > 0 && this.value < this._prevValue ? this.length - this._prevValue + this.value : t;\n  }\n}\nclass tt extends B {\n  constructor(t, e, i, s) {\n    const a = m(\"span\", {\n      className: \"symbol__value\",\n      textContent: i\n    });\n    super(t, i, m(\"span\", {\n      part: `symbol ${e}`,\n      className: \"symbol\"\n    }, [a]), s), this.type = e, this._children = /* @__PURE__ */ new Map(), this._onChildRemove = (r) => () => {\n      this._children.delete(r);\n    }, this._children.set(i, new R(this.flow, a, {\n      onRemove: this._onChildRemove(i)\n    }));\n  }\n  willUpdate(t) {\n    if (this.type === \"decimal\")\n      return;\n    const e = this.el.getBoundingClientRect();\n    this._prevOffset = e[this.section.justify] - t[this.section.justify];\n  }\n  update(t) {\n    if (this.value !== t) {\n      const e = this._children.get(this.value);\n      e && (e.present = !1);\n      const i = this._children.get(t);\n      if (i)\n        i.present = !0;\n      else {\n        const s = m(\"span\", {\n          className: \"symbol__value\",\n          textContent: t\n        });\n        this.el.appendChild(s), this._children.set(t, new R(this.flow, s, {\n          animateIn: !0,\n          onRemove: this._onChildRemove(t)\n        }));\n      }\n    }\n    this.value = t;\n  }\n  didUpdate(t) {\n    if (this.type === \"decimal\")\n      return;\n    const i = this.el.getBoundingClientRect()[this.section.justify] - t[this.section.justify], s = this._prevOffset - i;\n    s && this.el.animate({\n      transform: [`translateX(${s}px)`, \"none\"]\n    }, { ...this.flow.transformTiming, composite: \"accumulate\" });\n  }\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/number-flow/dist/lite-CzDvSY_4.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/number-flow/dist/lite.mjs":
/*!************************************************!*\
  !*** ./node_modules/number-flow/dist/lite.mjs ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Digit: () => (/* reexport safe */ _lite_CzDvSY_4_mjs__WEBPACK_IMPORTED_MODULE_0__.D),\n/* harmony export */   canAnimate: () => (/* reexport safe */ _lite_CzDvSY_4_mjs__WEBPACK_IMPORTED_MODULE_0__.c),\n/* harmony export */   continuous: () => (/* reexport safe */ _plugins_mjs__WEBPACK_IMPORTED_MODULE_1__.continuous),\n/* harmony export */   \"default\": () => (/* reexport safe */ _lite_CzDvSY_4_mjs__WEBPACK_IMPORTED_MODULE_0__.N),\n/* harmony export */   define: () => (/* reexport safe */ _lite_CzDvSY_4_mjs__WEBPACK_IMPORTED_MODULE_0__.d),\n/* harmony export */   formatToData: () => (/* reexport safe */ _lite_CzDvSY_4_mjs__WEBPACK_IMPORTED_MODULE_0__.f),\n/* harmony export */   prefersReducedMotion: () => (/* reexport safe */ _lite_CzDvSY_4_mjs__WEBPACK_IMPORTED_MODULE_0__.p),\n/* harmony export */   renderInnerHTML: () => (/* reexport safe */ _lite_CzDvSY_4_mjs__WEBPACK_IMPORTED_MODULE_0__.r)\n/* harmony export */ });\n/* harmony import */ var _lite_CzDvSY_4_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lite-CzDvSY_4.mjs */ \"(ssr)/./node_modules/number-flow/dist/lite-CzDvSY_4.mjs\");\n/* harmony import */ var _plugins_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./plugins.mjs */ \"(ssr)/./node_modules/number-flow/dist/plugins.mjs\");\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbnVtYmVyLWZsb3cvZGlzdC9saXRlLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBQTZGO0FBQzdDO0FBVTlDIiwic291cmNlcyI6WyJEOlxcc29mdFxcU2VhUHJvamVjdFxcZ2l0aHViX29wZW5fcHJvamVjdF9zdWNjZXNzXFxzdW5hXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxudW1iZXItZmxvd1xcZGlzdFxcbGl0ZS5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgRCBhcyByLCBjIGFzIG8sIE4gYXMgbiwgZCBhcyBzLCBmIGFzIHQsIHAgYXMgZiwgciBhcyBkIH0gZnJvbSBcIi4vbGl0ZS1DekR2U1lfNC5tanNcIjtcbmltcG9ydCB7IGNvbnRpbnVvdXMgYXMgYyB9IGZyb20gXCIuL3BsdWdpbnMubWpzXCI7XG5leHBvcnQge1xuICByIGFzIERpZ2l0LFxuICBvIGFzIGNhbkFuaW1hdGUsXG4gIGMgYXMgY29udGludW91cyxcbiAgbiBhcyBkZWZhdWx0LFxuICBzIGFzIGRlZmluZSxcbiAgdCBhcyBmb3JtYXRUb0RhdGEsXG4gIGYgYXMgcHJlZmVyc1JlZHVjZWRNb3Rpb24sXG4gIGQgYXMgcmVuZGVySW5uZXJIVE1MXG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/number-flow/dist/lite.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/number-flow/dist/plugins.mjs":
/*!***************************************************!*\
  !*** ./node_modules/number-flow/dist/plugins.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   continuous: () => (/* binding */ l)\n/* harmony export */ });\nconst f = (e, n) => e == null ? n : n == null ? e : Math.max(e, n), i = /* @__PURE__ */ new WeakMap(), l = {\n  onUpdate(e, n, o) {\n    if (i.set(o, void 0), !o.computedTrend)\n      return;\n    const s = n.integer.concat(n.fraction).filter((t) => t.type === \"integer\" || t.type === \"fraction\"), r = e.integer.concat(e.fraction).filter((t) => t.type === \"integer\" || t.type === \"fraction\"), u = s.find((t) => !r.find((c) => c.pos === t.pos && c.value === t.value)), a = r.find((t) => !s.find((c) => t.pos === c.pos && t.value === c.value));\n    i.set(o, f(u == null ? void 0 : u.pos, a == null ? void 0 : a.pos));\n  },\n  getDelta(e, n, o) {\n    const s = e - n, r = i.get(o.flow);\n    if (!s && r != null && r >= o.pos)\n      return o.length * o.flow.computedTrend;\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbnVtYmVyLWZsb3cvZGlzdC9wbHVnaW5zLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUdFIiwic291cmNlcyI6WyJEOlxcc29mdFxcU2VhUHJvamVjdFxcZ2l0aHViX29wZW5fcHJvamVjdF9zdWNjZXNzXFxzdW5hXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxudW1iZXItZmxvd1xcZGlzdFxccGx1Z2lucy5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgZiA9IChlLCBuKSA9PiBlID09IG51bGwgPyBuIDogbiA9PSBudWxsID8gZSA6IE1hdGgubWF4KGUsIG4pLCBpID0gLyogQF9fUFVSRV9fICovIG5ldyBXZWFrTWFwKCksIGwgPSB7XG4gIG9uVXBkYXRlKGUsIG4sIG8pIHtcbiAgICBpZiAoaS5zZXQobywgdm9pZCAwKSwgIW8uY29tcHV0ZWRUcmVuZClcbiAgICAgIHJldHVybjtcbiAgICBjb25zdCBzID0gbi5pbnRlZ2VyLmNvbmNhdChuLmZyYWN0aW9uKS5maWx0ZXIoKHQpID0+IHQudHlwZSA9PT0gXCJpbnRlZ2VyXCIgfHwgdC50eXBlID09PSBcImZyYWN0aW9uXCIpLCByID0gZS5pbnRlZ2VyLmNvbmNhdChlLmZyYWN0aW9uKS5maWx0ZXIoKHQpID0+IHQudHlwZSA9PT0gXCJpbnRlZ2VyXCIgfHwgdC50eXBlID09PSBcImZyYWN0aW9uXCIpLCB1ID0gcy5maW5kKCh0KSA9PiAhci5maW5kKChjKSA9PiBjLnBvcyA9PT0gdC5wb3MgJiYgYy52YWx1ZSA9PT0gdC52YWx1ZSkpLCBhID0gci5maW5kKCh0KSA9PiAhcy5maW5kKChjKSA9PiB0LnBvcyA9PT0gYy5wb3MgJiYgdC52YWx1ZSA9PT0gYy52YWx1ZSkpO1xuICAgIGkuc2V0KG8sIGYodSA9PSBudWxsID8gdm9pZCAwIDogdS5wb3MsIGEgPT0gbnVsbCA/IHZvaWQgMCA6IGEucG9zKSk7XG4gIH0sXG4gIGdldERlbHRhKGUsIG4sIG8pIHtcbiAgICBjb25zdCBzID0gZSAtIG4sIHIgPSBpLmdldChvLmZsb3cpO1xuICAgIGlmICghcyAmJiByICE9IG51bGwgJiYgciA+PSBvLnBvcylcbiAgICAgIHJldHVybiBvLmxlbmd0aCAqIG8uZmxvdy5jb21wdXRlZFRyZW5kO1xuICB9XG59O1xuZXhwb3J0IHtcbiAgbCBhcyBjb250aW51b3VzXG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/number-flow/dist/plugins.mjs\n");

/***/ })

};
;