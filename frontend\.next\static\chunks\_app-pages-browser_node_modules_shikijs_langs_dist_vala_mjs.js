"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_vala_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/vala.mjs":
/*!***************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/vala.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Vala\\\",\\\"fileTypes\\\":[\\\"vala\\\",\\\"vapi\\\",\\\"gs\\\"],\\\"name\\\":\\\"vala\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#code\\\"}],\\\"repository\\\":{\\\"code\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#strings\\\"},{\\\"include\\\":\\\"#keywords\\\"},{\\\"include\\\":\\\"#types\\\"},{\\\"include\\\":\\\"#functions\\\"},{\\\"include\\\":\\\"#variables\\\"}]},\\\"comments\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.vala\\\"}},\\\"match\\\":\\\"/\\\\\\\\*\\\\\\\\*/\\\",\\\"name\\\":\\\"comment.block.empty.vala\\\"},{\\\"include\\\":\\\"text.html.javadoc\\\"},{\\\"include\\\":\\\"#comments-inline\\\"}]},\\\"comments-inline\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"/\\\\\\\\*\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.vala\\\"}},\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"name\\\":\\\"comment.block.vala\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"comment.line.double-slash.vala\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.comment.vala\\\"}},\\\"match\\\":\\\"\\\\\\\\s*((//).*$\\\\\\\\n?)\\\"}]},\\\"constants\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b((0([xX])\\\\\\\\h*)|(([0-9]+\\\\\\\\.?[0-9]*)|(\\\\\\\\.[0-9]+))(([eE])([+-])?[0-9]+)?)([LlFfUuDd]|UL|ul)?\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.vala\\\"},{\\\"match\\\":\\\"\\\\\\\\b([A-Z][A-Z0-9_]+)\\\\\\\\b\\\",\\\"name\\\":\\\"variable.other.constant.vala\\\"}]},\\\"functions\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(\\\\\\\\w+)(?=\\\\\\\\s*(<[\\\\\\\\s\\\\\\\\w.]+>\\\\\\\\s*)?\\\\\\\\()\\\",\\\"name\\\":\\\"entity.name.function.vala\\\"}]},\\\"keywords\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=^|[^@\\\\\\\\w.])(as|do|if|in|is|not|or|and|for|get|new|out|ref|set|try|var|base|case|else|enum|lock|null|this|true|void|weak|async|break|catch|class|const|false|owned|throw|using|while|with|yield|delete|extern|inline|params|public|return|sealed|signal|sizeof|static|struct|switch|throws|typeof|unlock|default|dynamic|ensures|finally|foreach|private|unowned|virtual|abstract|continue|delegate|internal|override|requires|volatile|construct|interface|namespace|protected|errordomain)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.vala\\\"},{\\\"match\\\":\\\"(?<=^|[^@\\\\\\\\w.])(bool|double|float|unichar|unichar2|char|uchar|int|uint|long|ulong|short|ushort|size_t|ssize_t|string|string16|string32|void|signal|int8|int16|int32|int64|uint8|uint16|uint32|uint64|va_list|time_t)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.vala\\\"},{\\\"match\\\":\\\"(#(?:if|elif|else|endif))\\\",\\\"name\\\":\\\"keyword.vala\\\"}]},\\\"strings\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\"\\\\\\\"\\\\\\\"\\\",\\\"end\\\":\\\"\\\\\\\"\\\\\\\"\\\\\\\"\\\",\\\"name\\\":\\\"string.quoted.triple.vala\\\"},{\\\"begin\\\":\\\"@\\\\\\\"\\\",\\\"end\\\":\\\"\\\\\\\"\\\",\\\"name\\\":\\\"string.quoted.interpolated.vala\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"constant.character.escape.vala\\\"},{\\\"match\\\":\\\"\\\\\\\\$\\\\\\\\w+\\\",\\\"name\\\":\\\"constant.character.escape.vala\\\"},{\\\"match\\\":\\\"\\\\\\\\$\\\\\\\\(([^)(]|\\\\\\\\(([^)(]|\\\\\\\\([^)]*\\\\\\\\))*\\\\\\\\))*\\\\\\\\)\\\",\\\"name\\\":\\\"constant.character.escape.vala\\\"}]},{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"end\\\":\\\"\\\\\\\"\\\",\\\"name\\\":\\\"string.quoted.double.vala\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"constant.character.escape.vala\\\"}]},{\\\"begin\\\":\\\"'\\\",\\\"end\\\":\\\"'\\\",\\\"name\\\":\\\"string.quoted.single.vala\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"constant.character.escape.vala\\\"}]},{\\\"match\\\":\\\"/((\\\\\\\\\\\\\\\\/)|([^/]))*/(?=\\\\\\\\s*[,;).\\\\\\\\n])\\\",\\\"name\\\":\\\"string.regexp.vala\\\"}]},\\\"types\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=^|[^@\\\\\\\\w.])(bool|double|float|unichar|unichar2|char|uchar|int|uint|long|ulong|short|ushort|size_t|ssize_t|string|string16|string32|void|signal|int8|int16|int32|int64|uint8|uint16|uint32|uint64|va_list|time_t)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.primitive.vala\\\"},{\\\"match\\\":\\\"\\\\\\\\b([A-Z]+\\\\\\\\w*)\\\\\\\\b\\\",\\\"name\\\":\\\"entity.name.type.vala\\\"}]},\\\"variables\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b([_a-z]+\\\\\\\\w*)\\\\\\\\b\\\",\\\"name\\\":\\\"variable.other.vala\\\"}]}},\\\"scopeName\\\":\\\"source.vala\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/vala.mjs\n"));

/***/ })

}]);