"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_nix_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/nix.mjs":
/*!**************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/nix.mjs ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Nix\\\",\\\"fileTypes\\\":[\\\"nix\\\"],\\\"name\\\":\\\"nix\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}],\\\"repository\\\":{\\\"attribute-bind\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#attribute-name\\\"},{\\\"include\\\":\\\"#attribute-bind-from-equals\\\"}]},\\\"attribute-bind-from-equals\\\":{\\\"begin\\\":\\\"=\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.bind.nix\\\"}},\\\"end\\\":\\\";\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.terminator.bind.nix\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},\\\"attribute-inherit\\\":{\\\"begin\\\":\\\"\\\\\\\\binherit\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.other.inherit.nix\\\"}},\\\"end\\\":\\\";\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.terminator.inherit.nix\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.function.arguments.nix\\\"}},\\\"end\\\":\\\"(?=;)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.function.arguments.nix\\\"}},\\\"end\\\":\\\"(?=;)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#bad-reserved\\\"},{\\\"include\\\":\\\"#attribute-name-single\\\"},{\\\"include\\\":\\\"#others\\\"}]},{\\\"include\\\":\\\"#expression\\\"}]},{\\\"begin\\\":\\\"(?=[a-zA-Z_])\\\",\\\"end\\\":\\\"(?=;)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#bad-reserved\\\"},{\\\"include\\\":\\\"#attribute-name-single\\\"},{\\\"include\\\":\\\"#others\\\"}]},{\\\"include\\\":\\\"#others\\\"}]},\\\"attribute-name\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b[a-zA-Z_][a-zA-Z0-9_'-]*\\\",\\\"name\\\":\\\"entity.other.attribute-name.multipart.nix\\\"},{\\\"match\\\":\\\"\\\\\\\\.\\\"},{\\\"include\\\":\\\"#string-quoted\\\"},{\\\"include\\\":\\\"#interpolation\\\"}]},\\\"attribute-name-single\\\":{\\\"match\\\":\\\"\\\\\\\\b[a-zA-Z_][a-zA-Z0-9_'-]*\\\",\\\"name\\\":\\\"entity.other.attribute-name.single.nix\\\"},\\\"attrset-contents\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#attribute-inherit\\\"},{\\\"include\\\":\\\"#bad-reserved\\\"},{\\\"include\\\":\\\"#attribute-bind\\\"},{\\\"include\\\":\\\"#others\\\"}]},\\\"attrset-definition\\\":{\\\"begin\\\":\\\"(?=\\\\\\\\{)\\\",\\\"end\\\":\\\"(?=([\\\\\\\\])};,]|\\\\\\\\b(else|then)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.attrset.nix\\\"}},\\\"end\\\":\\\"(})\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.attrset.nix\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#attrset-contents\\\"}]},{\\\"begin\\\":\\\"(?<=})\\\",\\\"end\\\":\\\"(?=([\\\\\\\\])};,]|\\\\\\\\b(else|then)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression-cont\\\"}]}]},\\\"attrset-definition-brace-opened\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=})\\\",\\\"end\\\":\\\"(?=([\\\\\\\\])};,]|\\\\\\\\b(else|then)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression-cont\\\"}]},{\\\"begin\\\":\\\"(?=.?)\\\",\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.attrset.nix\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#attrset-contents\\\"}]}]},\\\"attrset-for-sure\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?=\\\\\\\\brec\\\\\\\\b)\\\",\\\"end\\\":\\\"(?=([\\\\\\\\])};,]|\\\\\\\\b(else|then)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\brec\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.other.nix\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\{)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#others\\\"}]},{\\\"include\\\":\\\"#attrset-definition\\\"},{\\\"include\\\":\\\"#others\\\"}]},{\\\"begin\\\":\\\"(?=\\\\\\\\{\\\\\\\\s*(}|[^,?]*([=;])))\\\",\\\"end\\\":\\\"(?=([\\\\\\\\])};,]|\\\\\\\\b(else|then)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#attrset-definition\\\"},{\\\"include\\\":\\\"#others\\\"}]}]},\\\"attrset-or-function\\\":{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.attrset-or-function.nix\\\"}},\\\"end\\\":\\\"(?=([\\\\\\\\])};]|\\\\\\\\b(else|then)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?=(\\\\\\\\s*}|\\\\\\\"|\\\\\\\\binherit\\\\\\\\b|\\\\\\\\$\\\\\\\\{|\\\\\\\\b[a-zA-Z_][a-zA-Z0-9_'-]*(\\\\\\\\s*\\\\\\\\.|\\\\\\\\s*=[^=])))\\\",\\\"end\\\":\\\"(?=([\\\\\\\\])};,]|\\\\\\\\b(else|then)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#attrset-definition-brace-opened\\\"}]},{\\\"begin\\\":\\\"(?=(\\\\\\\\.\\\\\\\\.\\\\\\\\.|\\\\\\\\b[a-zA-Z_][a-zA-Z0-9_'-]*\\\\\\\\s*[,?]))\\\",\\\"end\\\":\\\"(?=([\\\\\\\\])};,]|\\\\\\\\b(else|then)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-definition-brace-opened\\\"}]},{\\\"include\\\":\\\"#bad-reserved\\\"},{\\\"begin\\\":\\\"\\\\\\\\b[a-zA-Z_][a-zA-Z0-9_'-]*\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"variable.parameter.function.maybe.nix\\\"}},\\\"end\\\":\\\"(?=([\\\\\\\\])};]|\\\\\\\\b(else|then)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?=\\\\\\\\.)\\\",\\\"end\\\":\\\"(?=([\\\\\\\\])};,]|\\\\\\\\b(else|then)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#attrset-definition-brace-opened\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\s*(,)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.nix\\\"}},\\\"end\\\":\\\"(?=([\\\\\\\\])};,]|\\\\\\\\b(else|then)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-definition-brace-opened\\\"}]},{\\\"begin\\\":\\\"(?==)\\\",\\\"end\\\":\\\"(?=([\\\\\\\\])};,]|\\\\\\\\b(else|then)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#attribute-bind-from-equals\\\"},{\\\"include\\\":\\\"#attrset-definition-brace-opened\\\"}]},{\\\"begin\\\":\\\"(?=\\\\\\\\?)\\\",\\\"end\\\":\\\"(?=([\\\\\\\\])};,]|\\\\\\\\b(else|then)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-parameter-default\\\"},{\\\"begin\\\":\\\",\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.nix\\\"}},\\\"end\\\":\\\"(?=([\\\\\\\\])};,]|\\\\\\\\b(else|then)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-definition-brace-opened\\\"}]}]},{\\\"include\\\":\\\"#others\\\"}]},{\\\"include\\\":\\\"#others\\\"}]},\\\"bad-reserved\\\":{\\\"match\\\":\\\"(?<![\\\\\\\\w'-])(if|then|else|assert|with|let|in|rec|inherit)(?![\\\\\\\\w'-])\\\",\\\"name\\\":\\\"invalid.illegal.reserved.nix\\\"},\\\"comment\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"/\\\\\\\\*([^*]|\\\\\\\\*[^/])*\\\",\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"name\\\":\\\"comment.block.nix\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment-remark\\\"}]},{\\\"begin\\\":\\\"#\\\",\\\"end\\\":\\\"$\\\",\\\"name\\\":\\\"comment.line.number-sign.nix\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment-remark\\\"}]}]},\\\"comment-remark\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"markup.bold.comment.nix\\\"}},\\\"match\\\":\\\"(TODO|FIXME|BUG|!!!):?\\\"},\\\"constants\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\b(builtins|true|false|null)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"constant.language.nix\\\"}},\\\"end\\\":\\\"(?=([\\\\\\\\])};,]|\\\\\\\\b(else|then)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression-cont\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\b(scopedImport|import|isNull|abort|throw|baseNameOf|dirOf|removeAttrs|map|toString|derivationStrict|derivation)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.function.nix\\\"}},\\\"end\\\":\\\"(?=([\\\\\\\\])};,]|\\\\\\\\b(else|then)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression-cont\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\b[0-9]+\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"constant.numeric.nix\\\"}},\\\"end\\\":\\\"(?=([\\\\\\\\])};,]|\\\\\\\\b(else|then)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression-cont\\\"}]}]},\\\"expression\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#parens-and-cont\\\"},{\\\"include\\\":\\\"#list-and-cont\\\"},{\\\"include\\\":\\\"#string\\\"},{\\\"include\\\":\\\"#interpolation\\\"},{\\\"include\\\":\\\"#with-assert\\\"},{\\\"include\\\":\\\"#function-for-sure\\\"},{\\\"include\\\":\\\"#attrset-for-sure\\\"},{\\\"include\\\":\\\"#attrset-or-function\\\"},{\\\"include\\\":\\\"#let\\\"},{\\\"include\\\":\\\"#if\\\"},{\\\"include\\\":\\\"#operator-unary\\\"},{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#bad-reserved\\\"},{\\\"include\\\":\\\"#parameter-name-and-cont\\\"},{\\\"include\\\":\\\"#others\\\"}]},\\\"expression-cont\\\":{\\\"begin\\\":\\\"(?=.?)\\\",\\\"end\\\":\\\"(?=([\\\\\\\\])};,]|\\\\\\\\b(else|then)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#parens\\\"},{\\\"include\\\":\\\"#list\\\"},{\\\"include\\\":\\\"#string\\\"},{\\\"include\\\":\\\"#interpolation\\\"},{\\\"include\\\":\\\"#function-for-sure\\\"},{\\\"include\\\":\\\"#attrset-for-sure\\\"},{\\\"include\\\":\\\"#attrset-or-function\\\"},{\\\"match\\\":\\\"(\\\\\\\\bor\\\\\\\\b|\\\\\\\\.|\\\\\\\\|>|<\\\\\\\\||==|!=|!|<=|<|>=|>|&&|\\\\\\\\|\\\\\\\\||->|//|\\\\\\\\?|\\\\\\\\+\\\\\\\\+|[-*]|/(?=([^*]|$))|\\\\\\\\+)\\\",\\\"name\\\":\\\"keyword.operator.nix\\\"},{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#bad-reserved\\\"},{\\\"include\\\":\\\"#parameter-name\\\"},{\\\"include\\\":\\\"#others\\\"}]},\\\"function-body\\\":{\\\"begin\\\":\\\"(@\\\\\\\\s*([a-zA-Z_][a-zA-Z0-9_'-]*)\\\\\\\\s*)?(:)\\\",\\\"end\\\":\\\"(?=([\\\\\\\\])};,]|\\\\\\\\b(else|then)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},\\\"function-body-from-colon\\\":{\\\"begin\\\":\\\"(:)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.function.nix\\\"}},\\\"end\\\":\\\"(?=([\\\\\\\\])};,]|\\\\\\\\b(else|then)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},\\\"function-contents\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#bad-reserved\\\"},{\\\"include\\\":\\\"#function-parameter\\\"},{\\\"include\\\":\\\"#others\\\"}]},\\\"function-definition\\\":{\\\"begin\\\":\\\"(?=.?)\\\",\\\"end\\\":\\\"(?=([\\\\\\\\])};,]|\\\\\\\\b(else|then)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-body-from-colon\\\"},{\\\"begin\\\":\\\"(?=.?)\\\",\\\"end\\\":\\\"(?=:)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(\\\\\\\\b[a-zA-Z_][a-zA-Z0-9_'-]*)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"variable.parameter.function.4.nix\\\"}},\\\"end\\\":\\\"(?=:)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"@\\\",\\\"end\\\":\\\"(?=:)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-header-until-colon-no-arg\\\"},{\\\"include\\\":\\\"#others\\\"}]},{\\\"include\\\":\\\"#others\\\"}]},{\\\"begin\\\":\\\"(?=\\\\\\\\{)\\\",\\\"end\\\":\\\"(?=:)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-header-until-colon-with-arg\\\"}]}]},{\\\"include\\\":\\\"#others\\\"}]},\\\"function-definition-brace-opened\\\":{\\\"begin\\\":\\\"(?=.?)\\\",\\\"end\\\":\\\"(?=([\\\\\\\\])};,]|\\\\\\\\b(else|then)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-body-from-colon\\\"},{\\\"begin\\\":\\\"(?=.?)\\\",\\\"end\\\":\\\"(?=:)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-header-close-brace-with-arg\\\"},{\\\"begin\\\":\\\"(?=.?)\\\",\\\"end\\\":\\\"(?=})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-contents\\\"}]}]},{\\\"include\\\":\\\"#others\\\"}]},\\\"function-for-sure\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?=(\\\\\\\\b[a-zA-Z_][a-zA-Z0-9_'-]*\\\\\\\\s*[:@]|\\\\\\\\{[^}]*}\\\\\\\\s*:|\\\\\\\\{[^#}\\\\\\\"'/=]*[,?]))\\\",\\\"end\\\":\\\"(?=([\\\\\\\\])};,]|\\\\\\\\b(else|then)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-definition\\\"}]}]},\\\"function-header-close-brace-no-arg\\\":{\\\"begin\\\":\\\"}\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.entity.function.nix\\\"}},\\\"end\\\":\\\"(?=:)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#others\\\"}]},\\\"function-header-close-brace-with-arg\\\":{\\\"begin\\\":\\\"}\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.entity.function.nix\\\"}},\\\"end\\\":\\\"(?=:)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-header-terminal-arg\\\"},{\\\"include\\\":\\\"#others\\\"}]},\\\"function-header-open-brace\\\":{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.entity.function.2.nix\\\"}},\\\"end\\\":\\\"(?=})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-contents\\\"}]},\\\"function-header-terminal-arg\\\":{\\\"begin\\\":\\\"(?=@)\\\",\\\"end\\\":\\\"(?=:)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"@\\\",\\\"end\\\":\\\"(?=:)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(\\\\\\\\b[a-zA-Z_][a-zA-Z0-9_'-]*)\\\",\\\"end\\\":\\\"(?=:)\\\",\\\"name\\\":\\\"variable.parameter.function.3.nix\\\"},{\\\"include\\\":\\\"#others\\\"}]},{\\\"include\\\":\\\"#others\\\"}]},\\\"function-header-until-colon-no-arg\\\":{\\\"begin\\\":\\\"(?=\\\\\\\\{)\\\",\\\"end\\\":\\\"(?=:)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-header-open-brace\\\"},{\\\"include\\\":\\\"#function-header-close-brace-no-arg\\\"}]},\\\"function-header-until-colon-with-arg\\\":{\\\"begin\\\":\\\"(?=\\\\\\\\{)\\\",\\\"end\\\":\\\"(?=:)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-header-open-brace\\\"},{\\\"include\\\":\\\"#function-header-close-brace-with-arg\\\"}]},\\\"function-parameter\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(\\\\\\\\.\\\\\\\\.\\\\\\\\.)\\\",\\\"end\\\":\\\"(,|(?=}))\\\",\\\"name\\\":\\\"keyword.operator.nix\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#others\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\b[a-zA-Z_][a-zA-Z0-9_'-]*\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"variable.parameter.function.1.nix\\\"}},\\\"end\\\":\\\"(,|(?=}))\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.nix\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#whitespace\\\"},{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#function-parameter-default\\\"},{\\\"include\\\":\\\"#expression\\\"}]},{\\\"include\\\":\\\"#others\\\"}]},\\\"function-parameter-default\\\":{\\\"begin\\\":\\\"\\\\\\\\?\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.nix\\\"}},\\\"end\\\":\\\"(?=[,}])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},\\\"if\\\":{\\\"begin\\\":\\\"(?=\\\\\\\\bif\\\\\\\\b)\\\",\\\"end\\\":\\\"(?=([\\\\\\\\])};,]|\\\\\\\\b(else|then)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\bif\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.other.nix\\\"}},\\\"end\\\":\\\"\\\\\\\\bth(?=en\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.other.nix\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},{\\\"begin\\\":\\\"(?<=th)en\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.other.nix\\\"}},\\\"end\\\":\\\"\\\\\\\\bel(?=se\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.other.nix\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},{\\\"begin\\\":\\\"(?<=el)se\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.other.nix\\\"}},\\\"end\\\":\\\"(?=([\\\\\\\\])};,]|\\\\\\\\b(else|then)\\\\\\\\b))\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.other.nix\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]}]},\\\"illegal\\\":{\\\"match\\\":\\\".\\\",\\\"name\\\":\\\"invalid.illegal\\\"},\\\"interpolation\\\":{\\\"begin\\\":\\\"\\\\\\\\$\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.embedded.begin.nix\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.embedded.end.nix\\\"}},\\\"name\\\":\\\"meta.embedded\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},\\\"let\\\":{\\\"begin\\\":\\\"(?=\\\\\\\\blet\\\\\\\\b)\\\",\\\"end\\\":\\\"(?=([\\\\\\\\])};,]|\\\\\\\\b(else|then)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\blet\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.other.nix\\\"}},\\\"end\\\":\\\"(?=([\\\\\\\\])};,]|\\\\\\\\b(in|else|then)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?=\\\\\\\\{)\\\",\\\"end\\\":\\\"(?=([\\\\\\\\])};,]|\\\\\\\\b(else|then)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"end\\\":\\\"}\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#attrset-contents\\\"}]},{\\\"begin\\\":\\\"(^|(?<=}))\\\",\\\"end\\\":\\\"(?=([\\\\\\\\])};,]|\\\\\\\\b(else|then)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression-cont\\\"}]},{\\\"include\\\":\\\"#others\\\"}]},{\\\"include\\\":\\\"#attrset-contents\\\"},{\\\"include\\\":\\\"#others\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\bin\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.other.nix\\\"}},\\\"end\\\":\\\"(?=([\\\\\\\\])};,]|\\\\\\\\b(else|then)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]}]},\\\"list\\\":{\\\"begin\\\":\\\"\\\\\\\\[\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.list.nix\\\"}},\\\"end\\\":\\\"]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.list.nix\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},\\\"list-and-cont\\\":{\\\"begin\\\":\\\"(?=\\\\\\\\[)\\\",\\\"end\\\":\\\"(?=([\\\\\\\\])};,]|\\\\\\\\b(else|then)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#list\\\"},{\\\"include\\\":\\\"#expression-cont\\\"}]},\\\"operator-unary\\\":{\\\"match\\\":\\\"([!-])\\\",\\\"name\\\":\\\"keyword.operator.unary.nix\\\"},\\\"others\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#whitespace\\\"},{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#illegal\\\"}]},\\\"parameter-name\\\":{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"variable.parameter.name.nix\\\"}},\\\"match\\\":\\\"\\\\\\\\b[a-zA-Z_][a-zA-Z0-9_'-]*\\\"},\\\"parameter-name-and-cont\\\":{\\\"begin\\\":\\\"\\\\\\\\b[a-zA-Z_][a-zA-Z0-9_'-]*\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"variable.parameter.name.nix\\\"}},\\\"end\\\":\\\"(?=([\\\\\\\\])};,]|\\\\\\\\b(else|then)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression-cont\\\"}]},\\\"parens\\\":{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.expression.nix\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.expression.nix\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},\\\"parens-and-cont\\\":{\\\"begin\\\":\\\"(?=\\\\\\\\()\\\",\\\"end\\\":\\\"(?=([\\\\\\\\])};,]|\\\\\\\\b(else|then)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#parens\\\"},{\\\"include\\\":\\\"#expression-cont\\\"}]},\\\"string\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?='')\\\",\\\"end\\\":\\\"(?=([\\\\\\\\])};,]|\\\\\\\\b(else|then)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"''\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.other.start.nix\\\"}},\\\"end\\\":\\\"''(?![$']|\\\\\\\\\\\\\\\\.)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.other.end.nix\\\"}},\\\"name\\\":\\\"string.quoted.other.nix\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"''([$']|\\\\\\\\\\\\\\\\.)\\\",\\\"name\\\":\\\"constant.character.escape.nix\\\"},{\\\"include\\\":\\\"#interpolation\\\"}]},{\\\"include\\\":\\\"#expression-cont\\\"}]},{\\\"begin\\\":\\\"(?=\\\\\\\")\\\",\\\"end\\\":\\\"(?=([\\\\\\\\])};,]|\\\\\\\\b(else|then)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string-quoted\\\"},{\\\"include\\\":\\\"#expression-cont\\\"}]},{\\\"begin\\\":\\\"(~?[a-zA-Z0-9._\\\\\\\\-+]*(/[a-zA-Z0-9._\\\\\\\\-+]+)+)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"string.unquoted.path.nix\\\"}},\\\"end\\\":\\\"(?=([\\\\\\\\])};,]|\\\\\\\\b(else|then)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression-cont\\\"}]},{\\\"begin\\\":\\\"(<[a-zA-Z0-9._\\\\\\\\-+]+(/[a-zA-Z0-9._\\\\\\\\-+]+)*>)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"string.unquoted.spath.nix\\\"}},\\\"end\\\":\\\"(?=([\\\\\\\\])};,]|\\\\\\\\b(else|then)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression-cont\\\"}]},{\\\"begin\\\":\\\"([a-zA-Z][a-zA-Z0-9+\\\\\\\\-.]*:[a-zA-Z0-9%/?:@\\\\\\\\&=+$,\\\\\\\\-_.!~*']+)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"string.unquoted.url.nix\\\"}},\\\"end\\\":\\\"(?=([\\\\\\\\])};,]|\\\\\\\\b(else|then)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression-cont\\\"}]}]},\\\"string-quoted\\\":{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.double.start.nix\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.double.end.nix\\\"}},\\\"name\\\":\\\"string.quoted.double.nix\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"constant.character.escape.nix\\\"},{\\\"include\\\":\\\"#interpolation\\\"}]},\\\"whitespace\\\":{\\\"match\\\":\\\"\\\\\\\\s+\\\"},\\\"with-assert\\\":{\\\"begin\\\":\\\"(?<![\\\\\\\\w'-])(with|assert)(?![\\\\\\\\w'-])\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.other.nix\\\"}},\\\"end\\\":\\\";\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]}},\\\"scopeName\\\":\\\"source.nix\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ac2hpa2lqcy9sYW5ncy9kaXN0L25peC5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLHdDQUF3QyxnRkFBZ0YsNEJBQTRCLGtCQUFrQixvQkFBb0IsZUFBZSxnQ0FBZ0MsRUFBRSw0Q0FBNEMsRUFBRSxpQ0FBaUMsbUNBQW1DLE9BQU8sd0NBQXdDLFlBQVksb0JBQW9CLE9BQU8sOENBQThDLGdCQUFnQiw0QkFBNEIsRUFBRSx3QkFBd0IsbURBQW1ELE9BQU8sd0NBQXdDLFlBQVksb0JBQW9CLE9BQU8saURBQWlELGdCQUFnQix1Q0FBdUMsT0FBTyx5REFBeUQsZUFBZSxtQkFBbUIsdUNBQXVDLE9BQU8seURBQXlELGVBQWUsbUJBQW1CLDhCQUE4QixFQUFFLHVDQUF1QyxFQUFFLHdCQUF3QixFQUFFLEVBQUUsNEJBQTRCLEVBQUUsRUFBRSwwQ0FBMEMsbUJBQW1CLDhCQUE4QixFQUFFLHVDQUF1QyxFQUFFLHdCQUF3QixFQUFFLEVBQUUsd0JBQXdCLEVBQUUscUJBQXFCLGVBQWUsbUdBQW1HLEVBQUUsb0JBQW9CLEVBQUUsK0JBQStCLEVBQUUsK0JBQStCLEVBQUUsNEJBQTRCLGdHQUFnRyx1QkFBdUIsZUFBZSxtQ0FBbUMsRUFBRSw4QkFBOEIsRUFBRSxnQ0FBZ0MsRUFBRSx3QkFBd0IsRUFBRSx5QkFBeUIsb0JBQW9CLDJCQUEyQiw0Q0FBNEMsa0JBQWtCLHVCQUF1QixPQUFPLGlEQUFpRCxhQUFhLHFCQUFxQixPQUFPLGlEQUFpRCxnQkFBZ0Isa0NBQWtDLEVBQUUsRUFBRSxpQkFBaUIsMkJBQTJCLDRDQUE0QyxpQ0FBaUMsRUFBRSxFQUFFLHNDQUFzQyxlQUFlLGlCQUFpQiwyQkFBMkIsNENBQTRDLGlDQUFpQyxFQUFFLEVBQUUsZ0NBQWdDLG9CQUFvQixPQUFPLGlEQUFpRCxnQkFBZ0Isa0NBQWtDLEVBQUUsRUFBRSx1QkFBdUIsZUFBZSx1REFBdUQsNENBQTRDLCtDQUErQyxPQUFPLGdDQUFnQyxtQkFBbUIsbUJBQW1CLHdCQUF3QixFQUFFLEVBQUUsb0NBQW9DLEVBQUUsd0JBQXdCLEVBQUUsRUFBRSxvQkFBb0IsUUFBUSxXQUFXLDhCQUE4Qiw0Q0FBNEMsb0NBQW9DLEVBQUUsd0JBQXdCLEVBQUUsRUFBRSwwQkFBMEIsaUJBQWlCLHNCQUFzQixPQUFPLDZEQUE2RCx3QkFBd0IsMkNBQTJDLHVCQUF1QixrQ0FBa0MsbUZBQW1GLDRDQUE0QyxpREFBaUQsRUFBRSxFQUFFLG1HQUFtRyw0Q0FBNEMsa0RBQWtELEVBQUUsRUFBRSw4QkFBOEIsRUFBRSwrREFBK0QsT0FBTyxvREFBb0Qsd0JBQXdCLDJDQUEyQywrQ0FBK0MsNENBQTRDLGlEQUFpRCxFQUFFLEVBQUUsMkNBQTJDLE9BQU8sbUNBQW1DLHdCQUF3Qiw0Q0FBNEMsa0RBQWtELEVBQUUsRUFBRSwyQ0FBMkMsNENBQTRDLDRDQUE0QyxFQUFFLGlEQUFpRCxFQUFFLEVBQUUsK0NBQStDLDRDQUE0Qyw0Q0FBNEMsRUFBRSxtQ0FBbUMsT0FBTyxtQ0FBbUMsd0JBQXdCLDRDQUE0QyxrREFBa0QsRUFBRSxFQUFFLEVBQUUsd0JBQXdCLEVBQUUsRUFBRSx3QkFBd0IsRUFBRSxtQkFBbUIsaUlBQWlJLGNBQWMsZUFBZSx1R0FBdUcsZ0NBQWdDLEVBQUUsRUFBRSx1RkFBdUYsZ0NBQWdDLEVBQUUsRUFBRSxxQkFBcUIsY0FBYyxPQUFPLHNDQUFzQyxzQ0FBc0MsZ0JBQWdCLGVBQWUsc0VBQXNFLE9BQU8sb0NBQW9DLHdCQUF3Qiw0Q0FBNEMsaUNBQWlDLEVBQUUsRUFBRSwwSkFBMEosT0FBTyxtQ0FBbUMsd0JBQXdCLDRDQUE0QyxpQ0FBaUMsRUFBRSxFQUFFLGtEQUFrRCxPQUFPLG1DQUFtQyx3QkFBd0IsNENBQTRDLGlDQUFpQyxFQUFFLEVBQUUsaUJBQWlCLGVBQWUsaUNBQWlDLEVBQUUsK0JBQStCLEVBQUUsd0JBQXdCLEVBQUUsK0JBQStCLEVBQUUsNkJBQTZCLEVBQUUsbUNBQW1DLEVBQUUsa0NBQWtDLEVBQUUscUNBQXFDLEVBQUUscUJBQXFCLEVBQUUsb0JBQW9CLEVBQUUsZ0NBQWdDLEVBQUUsMkJBQTJCLEVBQUUsOEJBQThCLEVBQUUseUNBQXlDLEVBQUUsd0JBQXdCLEVBQUUsc0JBQXNCLDRDQUE0Qyw0Q0FBNEMsd0JBQXdCLEVBQUUsc0JBQXNCLEVBQUUsd0JBQXdCLEVBQUUsK0JBQStCLEVBQUUsbUNBQW1DLEVBQUUsa0NBQWtDLEVBQUUscUNBQXFDLEVBQUUsbUtBQW1LLEVBQUUsMkJBQTJCLEVBQUUsOEJBQThCLEVBQUUsZ0NBQWdDLEVBQUUsd0JBQXdCLEVBQUUsb0JBQW9CLG1GQUFtRiw0Q0FBNEMsNEJBQTRCLEVBQUUsK0JBQStCLHFDQUFxQyxPQUFPLGtEQUFrRCx3QkFBd0IsNENBQTRDLDRCQUE0QixFQUFFLHdCQUF3QixlQUFlLDhCQUE4QixFQUFFLG9DQUFvQyxFQUFFLHdCQUF3QixFQUFFLDBCQUEwQiw0Q0FBNEMsNENBQTRDLDBDQUEwQyxFQUFFLHNEQUFzRCxpRUFBaUUsT0FBTyxnREFBZ0Qsa0NBQWtDLGlEQUFpRCxvREFBb0QsRUFBRSx3QkFBd0IsRUFBRSxFQUFFLHdCQUF3QixFQUFFLEVBQUUsb0JBQW9CLHFDQUFxQyxzREFBc0QsRUFBRSxFQUFFLEVBQUUsd0JBQXdCLEVBQUUsdUNBQXVDLDRDQUE0Qyw0Q0FBNEMsMENBQTBDLEVBQUUsc0RBQXNELHNEQUFzRCxFQUFFLG1DQUFtQyxtQkFBbUIsbUNBQW1DLEVBQUUsRUFBRSxFQUFFLHdCQUF3QixFQUFFLHdCQUF3QixlQUFlLDZEQUE2RCxHQUFHLEdBQUcsYUFBYSxJQUFJLHlDQUF5Qyw0Q0FBNEMscUNBQXFDLEVBQUUsRUFBRSx5Q0FBeUMsYUFBYSxzQkFBc0IsT0FBTyx5REFBeUQsa0NBQWtDLHdCQUF3QixFQUFFLDJDQUEyQyxhQUFhLHNCQUFzQixPQUFPLHlEQUF5RCxrQ0FBa0MsOENBQThDLEVBQUUsd0JBQXdCLEVBQUUsaUNBQWlDLGlCQUFpQixzQkFBc0IsT0FBTywyREFBMkQsZUFBZSxtQkFBbUIsbUNBQW1DLEVBQUUsbUNBQW1DLHFEQUFxRCxpREFBaUQsK0dBQStHLEVBQUUsd0JBQXdCLEVBQUUsRUFBRSx3QkFBd0IsRUFBRSx5Q0FBeUMsb0JBQW9CLHFDQUFxQyw0Q0FBNEMsRUFBRSxvREFBb0QsRUFBRSwyQ0FBMkMsb0JBQW9CLHFDQUFxQyw0Q0FBNEMsRUFBRSxzREFBc0QsRUFBRSx5QkFBeUIsZUFBZSxpREFBaUQsc0RBQXNELHdCQUF3QixFQUFFLEVBQUUsK0RBQStELE9BQU8sZ0RBQWdELGtCQUFrQixzQkFBc0IsT0FBTyxtQ0FBbUMsZ0JBQWdCLDRCQUE0QixFQUFFLHlCQUF5QixFQUFFLDRDQUE0QyxFQUFFLDRCQUE0QixFQUFFLEVBQUUsd0JBQXdCLEVBQUUsaUNBQWlDLHVDQUF1QyxPQUFPLG1DQUFtQyxpQkFBaUIsb0JBQW9CLDRCQUE0QixFQUFFLFNBQVMsc0RBQXNELDRDQUE0Qyw4Q0FBOEMsT0FBTyxnQ0FBZ0MsaURBQWlELE9BQU8sZ0NBQWdDLGdCQUFnQiw0QkFBNEIsRUFBRSxFQUFFLGdEQUFnRCxPQUFPLGdDQUFnQyxpREFBaUQsT0FBTyxnQ0FBZ0MsZ0JBQWdCLDRCQUE0QixFQUFFLEVBQUUsZ0RBQWdELE9BQU8sZ0NBQWdDLHdCQUF3Qiw4Q0FBOEMsT0FBTyxnQ0FBZ0MsZ0JBQWdCLDRCQUE0QixFQUFFLEVBQUUsY0FBYyw2Q0FBNkMsb0JBQW9CLHNCQUFzQixzQkFBc0IsT0FBTyxxREFBcUQsWUFBWSxvQkFBb0IsT0FBTyxtREFBbUQsMkNBQTJDLDRCQUE0QixFQUFFLFVBQVUsdURBQXVELDRDQUE0QywrQ0FBK0MsT0FBTyxnQ0FBZ0Msd0JBQXdCLCtDQUErQyxvQkFBb0IsMkJBQTJCLDRDQUE0QyxpQkFBaUIsY0FBYyxrQkFBa0Isa0NBQWtDLEVBQUUsRUFBRSxvQkFBb0IsNEJBQTRCLDRDQUE0QyxpQ0FBaUMsRUFBRSxFQUFFLHdCQUF3QixFQUFFLEVBQUUsa0NBQWtDLEVBQUUsd0JBQXdCLEVBQUUsRUFBRSw4Q0FBOEMsT0FBTyxnQ0FBZ0Msd0JBQXdCLDRDQUE0Qyw0QkFBNEIsRUFBRSxFQUFFLFdBQVcsdUNBQXVDLE9BQU8sOENBQThDLGdDQUFnQyxPQUFPLDhDQUE4QyxnQkFBZ0IsNEJBQTRCLEVBQUUsb0JBQW9CLCtDQUErQyw0Q0FBNEMsc0JBQXNCLEVBQUUsaUNBQWlDLEVBQUUscUJBQXFCLDZEQUE2RCxhQUFhLGVBQWUsNEJBQTRCLEVBQUUseUJBQXlCLEVBQUUseUJBQXlCLEVBQUUscUJBQXFCLGNBQWMsT0FBTywwQ0FBMEMsNkNBQTZDLDhCQUE4QiwrREFBK0QsT0FBTywwQ0FBMEMsd0JBQXdCLDRDQUE0QyxpQ0FBaUMsRUFBRSxhQUFhLHVDQUF1QyxPQUFPLG9EQUFvRCxvQ0FBb0MsT0FBTyxvREFBb0QsZ0JBQWdCLDRCQUE0QixFQUFFLHNCQUFzQiwrQ0FBK0MsNENBQTRDLHdCQUF3QixFQUFFLGlDQUFpQyxFQUFFLGFBQWEsZUFBZSw0Q0FBNEMsNENBQTRDLG9DQUFvQyxPQUFPLDREQUE0RCxtREFBbUQsT0FBTywwREFBMEQscURBQXFELDRFQUE0RSxFQUFFLCtCQUErQixFQUFFLEVBQUUsaUNBQWlDLEVBQUUsRUFBRSw4Q0FBOEMsNENBQTRDLCtCQUErQixFQUFFLGlDQUFpQyxFQUFFLEVBQUUsa0ZBQWtGLE9BQU8sdUNBQXVDLHdCQUF3Qiw0Q0FBNEMsaUNBQWlDLEVBQUUsRUFBRSxrRkFBa0YsT0FBTyx3Q0FBd0Msd0JBQXdCLDRDQUE0QyxpQ0FBaUMsRUFBRSxFQUFFLHFHQUFxRyxPQUFPLHNDQUFzQyx3QkFBd0IsNENBQTRDLGlDQUFpQyxFQUFFLEVBQUUsb0JBQW9CLHNDQUFzQyxPQUFPLDZEQUE2RCxtQ0FBbUMsT0FBTywyREFBMkQsc0RBQXNELG1FQUFtRSxFQUFFLCtCQUErQixFQUFFLGlCQUFpQixxQkFBcUIsa0JBQWtCLDBFQUEwRSxPQUFPLGdDQUFnQyxZQUFZLGtCQUFrQiw0QkFBNEIsR0FBRyw4QkFBOEI7O0FBRTk3Z0IsaUVBQWU7QUFDZjtBQUNBLENBQUMiLCJzb3VyY2VzIjpbIkQ6XFxzb2Z0XFxTZWFQcm9qZWN0XFxnaXRodWJfb3Blbl9wcm9qZWN0X3N1Y2Nlc3NcXHN1bmFcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXEBzaGlraWpzXFxsYW5nc1xcZGlzdFxcbml4Lm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBsYW5nID0gT2JqZWN0LmZyZWV6ZShKU09OLnBhcnNlKFwie1xcXCJkaXNwbGF5TmFtZVxcXCI6XFxcIk5peFxcXCIsXFxcImZpbGVUeXBlc1xcXCI6W1xcXCJuaXhcXFwiXSxcXFwibmFtZVxcXCI6XFxcIm5peFxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2V4cHJlc3Npb25cXFwifV0sXFxcInJlcG9zaXRvcnlcXFwiOntcXFwiYXR0cmlidXRlLWJpbmRcXFwiOntcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjYXR0cmlidXRlLW5hbWVcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjYXR0cmlidXRlLWJpbmQtZnJvbS1lcXVhbHNcXFwifV19LFxcXCJhdHRyaWJ1dGUtYmluZC1mcm9tLWVxdWFsc1xcXCI6e1xcXCJiZWdpblxcXCI6XFxcIj1cXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQub3BlcmF0b3IuYmluZC5uaXhcXFwifX0sXFxcImVuZFxcXCI6XFxcIjtcXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi50ZXJtaW5hdG9yLmJpbmQubml4XFxcIn19LFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNleHByZXNzaW9uXFxcIn1dfSxcXFwiYXR0cmlidXRlLWluaGVyaXRcXFwiOntcXFwiYmVnaW5cXFwiOlxcXCJcXFxcXFxcXGJpbmhlcml0XFxcXFxcXFxiXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLm90aGVyLmluaGVyaXQubml4XFxcIn19LFxcXCJlbmRcXFwiOlxcXCI7XFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24udGVybWluYXRvci5pbmhlcml0Lm5peFxcXCJ9fSxcXFwicGF0dGVybnNcXFwiOlt7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXFwoXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5zZWN0aW9uLmZ1bmN0aW9uLmFyZ3VtZW50cy5uaXhcXFwifX0sXFxcImVuZFxcXCI6XFxcIig/PTspXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXFwpXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5zZWN0aW9uLmZ1bmN0aW9uLmFyZ3VtZW50cy5uaXhcXFwifX0sXFxcImVuZFxcXCI6XFxcIig/PTspXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjYmFkLXJlc2VydmVkXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2F0dHJpYnV0ZS1uYW1lLXNpbmdsZVxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNvdGhlcnNcXFwifV19LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNleHByZXNzaW9uXFxcIn1dfSx7XFxcImJlZ2luXFxcIjpcXFwiKD89W2EtekEtWl9dKVxcXCIsXFxcImVuZFxcXCI6XFxcIig/PTspXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjYmFkLXJlc2VydmVkXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2F0dHJpYnV0ZS1uYW1lLXNpbmdsZVxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNvdGhlcnNcXFwifV19LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNvdGhlcnNcXFwifV19LFxcXCJhdHRyaWJ1dGUtbmFtZVxcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwibWF0Y2hcXFwiOlxcXCJcXFxcXFxcXGJbYS16QS1aX11bYS16QS1aMC05XyctXSpcXFwiLFxcXCJuYW1lXFxcIjpcXFwiZW50aXR5Lm90aGVyLmF0dHJpYnV0ZS1uYW1lLm11bHRpcGFydC5uaXhcXFwifSx7XFxcIm1hdGNoXFxcIjpcXFwiXFxcXFxcXFwuXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3N0cmluZy1xdW90ZWRcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjaW50ZXJwb2xhdGlvblxcXCJ9XX0sXFxcImF0dHJpYnV0ZS1uYW1lLXNpbmdsZVxcXCI6e1xcXCJtYXRjaFxcXCI6XFxcIlxcXFxcXFxcYlthLXpBLVpfXVthLXpBLVowLTlfJy1dKlxcXCIsXFxcIm5hbWVcXFwiOlxcXCJlbnRpdHkub3RoZXIuYXR0cmlidXRlLW5hbWUuc2luZ2xlLm5peFxcXCJ9LFxcXCJhdHRyc2V0LWNvbnRlbnRzXFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2F0dHJpYnV0ZS1pbmhlcml0XFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2JhZC1yZXNlcnZlZFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNhdHRyaWJ1dGUtYmluZFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNvdGhlcnNcXFwifV19LFxcXCJhdHRyc2V0LWRlZmluaXRpb25cXFwiOntcXFwiYmVnaW5cXFwiOlxcXCIoPz1cXFxcXFxcXHspXFxcIixcXFwiZW5kXFxcIjpcXFwiKD89KFtcXFxcXFxcXF0pfTssXXxcXFxcXFxcXGIoZWxzZXx0aGVuKVxcXFxcXFxcYikpXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImJlZ2luXFxcIjpcXFwiKFxcXFxcXFxceylcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24uYXR0cnNldC5uaXhcXFwifX0sXFxcImVuZFxcXCI6XFxcIih9KVxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24uYXR0cnNldC5uaXhcXFwifX0sXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2F0dHJzZXQtY29udGVudHNcXFwifV19LHtcXFwiYmVnaW5cXFwiOlxcXCIoPzw9fSlcXFwiLFxcXCJlbmRcXFwiOlxcXCIoPz0oW1xcXFxcXFxcXSl9OyxdfFxcXFxcXFxcYihlbHNlfHRoZW4pXFxcXFxcXFxiKSlcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNleHByZXNzaW9uLWNvbnRcXFwifV19XX0sXFxcImF0dHJzZXQtZGVmaW5pdGlvbi1icmFjZS1vcGVuZWRcXFwiOntcXFwicGF0dGVybnNcXFwiOlt7XFxcImJlZ2luXFxcIjpcXFwiKD88PX0pXFxcIixcXFwiZW5kXFxcIjpcXFwiKD89KFtcXFxcXFxcXF0pfTssXXxcXFxcXFxcXGIoZWxzZXx0aGVuKVxcXFxcXFxcYikpXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjZXhwcmVzc2lvbi1jb250XFxcIn1dfSx7XFxcImJlZ2luXFxcIjpcXFwiKD89Lj8pXFxcIixcXFwiZW5kXFxcIjpcXFwifVxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24uYXR0cnNldC5uaXhcXFwifX0sXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2F0dHJzZXQtY29udGVudHNcXFwifV19XX0sXFxcImF0dHJzZXQtZm9yLXN1cmVcXFwiOntcXFwicGF0dGVybnNcXFwiOlt7XFxcImJlZ2luXFxcIjpcXFwiKD89XFxcXFxcXFxicmVjXFxcXFxcXFxiKVxcXCIsXFxcImVuZFxcXCI6XFxcIig/PShbXFxcXFxcXFxdKX07LF18XFxcXFxcXFxiKGVsc2V8dGhlbilcXFxcXFxcXGIpKVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxcYnJlY1xcXFxcXFxcYlxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5vdGhlci5uaXhcXFwifX0sXFxcImVuZFxcXCI6XFxcIig/PVxcXFxcXFxceylcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNvdGhlcnNcXFwifV19LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNhdHRyc2V0LWRlZmluaXRpb25cXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjb3RoZXJzXFxcIn1dfSx7XFxcImJlZ2luXFxcIjpcXFwiKD89XFxcXFxcXFx7XFxcXFxcXFxzKih9fFteLD9dKihbPTtdKSkpXFxcIixcXFwiZW5kXFxcIjpcXFwiKD89KFtcXFxcXFxcXF0pfTssXXxcXFxcXFxcXGIoZWxzZXx0aGVuKVxcXFxcXFxcYikpXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjYXR0cnNldC1kZWZpbml0aW9uXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI290aGVyc1xcXCJ9XX1dfSxcXFwiYXR0cnNldC1vci1mdW5jdGlvblxcXCI6e1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxce1xcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uZGVmaW5pdGlvbi5hdHRyc2V0LW9yLWZ1bmN0aW9uLm5peFxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiKD89KFtcXFxcXFxcXF0pfTtdfFxcXFxcXFxcYihlbHNlfHRoZW4pXFxcXFxcXFxiKSlcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiYmVnaW5cXFwiOlxcXCIoPz0oXFxcXFxcXFxzKn18XFxcXFxcXCJ8XFxcXFxcXFxiaW5oZXJpdFxcXFxcXFxcYnxcXFxcXFxcXCRcXFxcXFxcXHt8XFxcXFxcXFxiW2EtekEtWl9dW2EtekEtWjAtOV8nLV0qKFxcXFxcXFxccypcXFxcXFxcXC58XFxcXFxcXFxzKj1bXj1dKSkpXFxcIixcXFwiZW5kXFxcIjpcXFwiKD89KFtcXFxcXFxcXF0pfTssXXxcXFxcXFxcXGIoZWxzZXx0aGVuKVxcXFxcXFxcYikpXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjYXR0cnNldC1kZWZpbml0aW9uLWJyYWNlLW9wZW5lZFxcXCJ9XX0se1xcXCJiZWdpblxcXCI6XFxcIig/PShcXFxcXFxcXC5cXFxcXFxcXC5cXFxcXFxcXC58XFxcXFxcXFxiW2EtekEtWl9dW2EtekEtWjAtOV8nLV0qXFxcXFxcXFxzKlssP10pKVxcXCIsXFxcImVuZFxcXCI6XFxcIig/PShbXFxcXFxcXFxdKX07LF18XFxcXFxcXFxiKGVsc2V8dGhlbilcXFxcXFxcXGIpKVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2Z1bmN0aW9uLWRlZmluaXRpb24tYnJhY2Utb3BlbmVkXFxcIn1dfSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjYmFkLXJlc2VydmVkXFxcIn0se1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxcYlthLXpBLVpfXVthLXpBLVowLTlfJy1dKlxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwidmFyaWFibGUucGFyYW1ldGVyLmZ1bmN0aW9uLm1heWJlLm5peFxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiKD89KFtcXFxcXFxcXF0pfTtdfFxcXFxcXFxcYihlbHNlfHRoZW4pXFxcXFxcXFxiKSlcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiYmVnaW5cXFwiOlxcXCIoPz1cXFxcXFxcXC4pXFxcIixcXFwiZW5kXFxcIjpcXFwiKD89KFtcXFxcXFxcXF0pfTssXXxcXFxcXFxcXGIoZWxzZXx0aGVuKVxcXFxcXFxcYikpXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjYXR0cnNldC1kZWZpbml0aW9uLWJyYWNlLW9wZW5lZFxcXCJ9XX0se1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxccyooLClcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQub3BlcmF0b3Iubml4XFxcIn19LFxcXCJlbmRcXFwiOlxcXCIoPz0oW1xcXFxcXFxcXSl9OyxdfFxcXFxcXFxcYihlbHNlfHRoZW4pXFxcXFxcXFxiKSlcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNmdW5jdGlvbi1kZWZpbml0aW9uLWJyYWNlLW9wZW5lZFxcXCJ9XX0se1xcXCJiZWdpblxcXCI6XFxcIig/PT0pXFxcIixcXFwiZW5kXFxcIjpcXFwiKD89KFtcXFxcXFxcXF0pfTssXXxcXFxcXFxcXGIoZWxzZXx0aGVuKVxcXFxcXFxcYikpXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjYXR0cmlidXRlLWJpbmQtZnJvbS1lcXVhbHNcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjYXR0cnNldC1kZWZpbml0aW9uLWJyYWNlLW9wZW5lZFxcXCJ9XX0se1xcXCJiZWdpblxcXCI6XFxcIig/PVxcXFxcXFxcPylcXFwiLFxcXCJlbmRcXFwiOlxcXCIoPz0oW1xcXFxcXFxcXSl9OyxdfFxcXFxcXFxcYihlbHNlfHRoZW4pXFxcXFxcXFxiKSlcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNmdW5jdGlvbi1wYXJhbWV0ZXItZGVmYXVsdFxcXCJ9LHtcXFwiYmVnaW5cXFwiOlxcXCIsXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLm9wZXJhdG9yLm5peFxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiKD89KFtcXFxcXFxcXF0pfTssXXxcXFxcXFxcXGIoZWxzZXx0aGVuKVxcXFxcXFxcYikpXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjZnVuY3Rpb24tZGVmaW5pdGlvbi1icmFjZS1vcGVuZWRcXFwifV19XX0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI290aGVyc1xcXCJ9XX0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI290aGVyc1xcXCJ9XX0sXFxcImJhZC1yZXNlcnZlZFxcXCI6e1xcXCJtYXRjaFxcXCI6XFxcIig/PCFbXFxcXFxcXFx3Jy1dKShpZnx0aGVufGVsc2V8YXNzZXJ0fHdpdGh8bGV0fGlufHJlY3xpbmhlcml0KSg/IVtcXFxcXFxcXHcnLV0pXFxcIixcXFwibmFtZVxcXCI6XFxcImludmFsaWQuaWxsZWdhbC5yZXNlcnZlZC5uaXhcXFwifSxcXFwiY29tbWVudFxcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiYmVnaW5cXFwiOlxcXCIvXFxcXFxcXFwqKFteKl18XFxcXFxcXFwqW14vXSkqXFxcIixcXFwiZW5kXFxcIjpcXFwiXFxcXFxcXFwqL1xcXCIsXFxcIm5hbWVcXFwiOlxcXCJjb21tZW50LmJsb2NrLm5peFxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2NvbW1lbnQtcmVtYXJrXFxcIn1dfSx7XFxcImJlZ2luXFxcIjpcXFwiI1xcXCIsXFxcImVuZFxcXCI6XFxcIiRcXFwiLFxcXCJuYW1lXFxcIjpcXFwiY29tbWVudC5saW5lLm51bWJlci1zaWduLm5peFxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2NvbW1lbnQtcmVtYXJrXFxcIn1dfV19LFxcXCJjb21tZW50LXJlbWFya1xcXCI6e1xcXCJjYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJtYXJrdXAuYm9sZC5jb21tZW50Lm5peFxcXCJ9fSxcXFwibWF0Y2hcXFwiOlxcXCIoVE9ET3xGSVhNRXxCVUd8ISEhKTo/XFxcIn0sXFxcImNvbnN0YW50c1xcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiYmVnaW5cXFwiOlxcXCJcXFxcXFxcXGIoYnVpbHRpbnN8dHJ1ZXxmYWxzZXxudWxsKVxcXFxcXFxcYlxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwiY29uc3RhbnQubGFuZ3VhZ2Uubml4XFxcIn19LFxcXCJlbmRcXFwiOlxcXCIoPz0oW1xcXFxcXFxcXSl9OyxdfFxcXFxcXFxcYihlbHNlfHRoZW4pXFxcXFxcXFxiKSlcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNleHByZXNzaW9uLWNvbnRcXFwifV19LHtcXFwiYmVnaW5cXFwiOlxcXCJcXFxcXFxcXGIoc2NvcGVkSW1wb3J0fGltcG9ydHxpc051bGx8YWJvcnR8dGhyb3d8YmFzZU5hbWVPZnxkaXJPZnxyZW1vdmVBdHRyc3xtYXB8dG9TdHJpbmd8ZGVyaXZhdGlvblN0cmljdHxkZXJpdmF0aW9uKVxcXFxcXFxcYlxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwic3VwcG9ydC5mdW5jdGlvbi5uaXhcXFwifX0sXFxcImVuZFxcXCI6XFxcIig/PShbXFxcXFxcXFxdKX07LF18XFxcXFxcXFxiKGVsc2V8dGhlbilcXFxcXFxcXGIpKVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2V4cHJlc3Npb24tY29udFxcXCJ9XX0se1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxcYlswLTldK1xcXFxcXFxcYlxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwiY29uc3RhbnQubnVtZXJpYy5uaXhcXFwifX0sXFxcImVuZFxcXCI6XFxcIig/PShbXFxcXFxcXFxdKX07LF18XFxcXFxcXFxiKGVsc2V8dGhlbilcXFxcXFxcXGIpKVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2V4cHJlc3Npb24tY29udFxcXCJ9XX1dfSxcXFwiZXhwcmVzc2lvblxcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNwYXJlbnMtYW5kLWNvbnRcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjbGlzdC1hbmQtY29udFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNzdHJpbmdcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjaW50ZXJwb2xhdGlvblxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiN3aXRoLWFzc2VydFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNmdW5jdGlvbi1mb3Itc3VyZVxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNhdHRyc2V0LWZvci1zdXJlXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2F0dHJzZXQtb3ItZnVuY3Rpb25cXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjbGV0XFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2lmXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI29wZXJhdG9yLXVuYXJ5XFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2NvbnN0YW50c1xcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNiYWQtcmVzZXJ2ZWRcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjcGFyYW1ldGVyLW5hbWUtYW5kLWNvbnRcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjb3RoZXJzXFxcIn1dfSxcXFwiZXhwcmVzc2lvbi1jb250XFxcIjp7XFxcImJlZ2luXFxcIjpcXFwiKD89Lj8pXFxcIixcXFwiZW5kXFxcIjpcXFwiKD89KFtcXFxcXFxcXF0pfTssXXxcXFxcXFxcXGIoZWxzZXx0aGVuKVxcXFxcXFxcYikpXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjcGFyZW5zXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2xpc3RcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjc3RyaW5nXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2ludGVycG9sYXRpb25cXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjZnVuY3Rpb24tZm9yLXN1cmVcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjYXR0cnNldC1mb3Itc3VyZVxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNhdHRyc2V0LW9yLWZ1bmN0aW9uXFxcIn0se1xcXCJtYXRjaFxcXCI6XFxcIihcXFxcXFxcXGJvclxcXFxcXFxcYnxcXFxcXFxcXC58XFxcXFxcXFx8Pnw8XFxcXFxcXFx8fD09fCE9fCF8PD18PHw+PXw+fCYmfFxcXFxcXFxcfFxcXFxcXFxcfHwtPnwvL3xcXFxcXFxcXD98XFxcXFxcXFwrXFxcXFxcXFwrfFstKl18Lyg/PShbXipdfCQpKXxcXFxcXFxcXCspXFxcIixcXFwibmFtZVxcXCI6XFxcImtleXdvcmQub3BlcmF0b3Iubml4XFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2NvbnN0YW50c1xcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNiYWQtcmVzZXJ2ZWRcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjcGFyYW1ldGVyLW5hbWVcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjb3RoZXJzXFxcIn1dfSxcXFwiZnVuY3Rpb24tYm9keVxcXCI6e1xcXCJiZWdpblxcXCI6XFxcIihAXFxcXFxcXFxzKihbYS16QS1aX11bYS16QS1aMC05XyctXSopXFxcXFxcXFxzKik/KDopXFxcIixcXFwiZW5kXFxcIjpcXFwiKD89KFtcXFxcXFxcXF0pfTssXXxcXFxcXFxcXGIoZWxzZXx0aGVuKVxcXFxcXFxcYikpXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjZXhwcmVzc2lvblxcXCJ9XX0sXFxcImZ1bmN0aW9uLWJvZHktZnJvbS1jb2xvblxcXCI6e1xcXCJiZWdpblxcXCI6XFxcIig6KVxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uZGVmaW5pdGlvbi5mdW5jdGlvbi5uaXhcXFwifX0sXFxcImVuZFxcXCI6XFxcIig/PShbXFxcXFxcXFxdKX07LF18XFxcXFxcXFxiKGVsc2V8dGhlbilcXFxcXFxcXGIpKVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2V4cHJlc3Npb25cXFwifV19LFxcXCJmdW5jdGlvbi1jb250ZW50c1xcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNiYWQtcmVzZXJ2ZWRcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjZnVuY3Rpb24tcGFyYW1ldGVyXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI290aGVyc1xcXCJ9XX0sXFxcImZ1bmN0aW9uLWRlZmluaXRpb25cXFwiOntcXFwiYmVnaW5cXFwiOlxcXCIoPz0uPylcXFwiLFxcXCJlbmRcXFwiOlxcXCIoPz0oW1xcXFxcXFxcXSl9OyxdfFxcXFxcXFxcYihlbHNlfHRoZW4pXFxcXFxcXFxiKSlcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNmdW5jdGlvbi1ib2R5LWZyb20tY29sb25cXFwifSx7XFxcImJlZ2luXFxcIjpcXFwiKD89Lj8pXFxcIixcXFwiZW5kXFxcIjpcXFwiKD89OilcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiYmVnaW5cXFwiOlxcXCIoXFxcXFxcXFxiW2EtekEtWl9dW2EtekEtWjAtOV8nLV0qKVxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwidmFyaWFibGUucGFyYW1ldGVyLmZ1bmN0aW9uLjQubml4XFxcIn19LFxcXCJlbmRcXFwiOlxcXCIoPz06KVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJiZWdpblxcXCI6XFxcIkBcXFwiLFxcXCJlbmRcXFwiOlxcXCIoPz06KVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2Z1bmN0aW9uLWhlYWRlci11bnRpbC1jb2xvbi1uby1hcmdcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjb3RoZXJzXFxcIn1dfSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjb3RoZXJzXFxcIn1dfSx7XFxcImJlZ2luXFxcIjpcXFwiKD89XFxcXFxcXFx7KVxcXCIsXFxcImVuZFxcXCI6XFxcIig/PTopXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjZnVuY3Rpb24taGVhZGVyLXVudGlsLWNvbG9uLXdpdGgtYXJnXFxcIn1dfV19LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNvdGhlcnNcXFwifV19LFxcXCJmdW5jdGlvbi1kZWZpbml0aW9uLWJyYWNlLW9wZW5lZFxcXCI6e1xcXCJiZWdpblxcXCI6XFxcIig/PS4/KVxcXCIsXFxcImVuZFxcXCI6XFxcIig/PShbXFxcXFxcXFxdKX07LF18XFxcXFxcXFxiKGVsc2V8dGhlbilcXFxcXFxcXGIpKVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2Z1bmN0aW9uLWJvZHktZnJvbS1jb2xvblxcXCJ9LHtcXFwiYmVnaW5cXFwiOlxcXCIoPz0uPylcXFwiLFxcXCJlbmRcXFwiOlxcXCIoPz06KVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2Z1bmN0aW9uLWhlYWRlci1jbG9zZS1icmFjZS13aXRoLWFyZ1xcXCJ9LHtcXFwiYmVnaW5cXFwiOlxcXCIoPz0uPylcXFwiLFxcXCJlbmRcXFwiOlxcXCIoPz19KVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2Z1bmN0aW9uLWNvbnRlbnRzXFxcIn1dfV19LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNvdGhlcnNcXFwifV19LFxcXCJmdW5jdGlvbi1mb3Itc3VyZVxcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiYmVnaW5cXFwiOlxcXCIoPz0oXFxcXFxcXFxiW2EtekEtWl9dW2EtekEtWjAtOV8nLV0qXFxcXFxcXFxzKls6QF18XFxcXFxcXFx7W159XSp9XFxcXFxcXFxzKjp8XFxcXFxcXFx7W14jfVxcXFxcXFwiJy89XSpbLD9dKSlcXFwiLFxcXCJlbmRcXFwiOlxcXCIoPz0oW1xcXFxcXFxcXSl9OyxdfFxcXFxcXFxcYihlbHNlfHRoZW4pXFxcXFxcXFxiKSlcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNmdW5jdGlvbi1kZWZpbml0aW9uXFxcIn1dfV19LFxcXCJmdW5jdGlvbi1oZWFkZXItY2xvc2UtYnJhY2Utbm8tYXJnXFxcIjp7XFxcImJlZ2luXFxcIjpcXFwifVxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uZGVmaW5pdGlvbi5lbnRpdHkuZnVuY3Rpb24ubml4XFxcIn19LFxcXCJlbmRcXFwiOlxcXCIoPz06KVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI290aGVyc1xcXCJ9XX0sXFxcImZ1bmN0aW9uLWhlYWRlci1jbG9zZS1icmFjZS13aXRoLWFyZ1xcXCI6e1xcXCJiZWdpblxcXCI6XFxcIn1cXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24uZW50aXR5LmZ1bmN0aW9uLm5peFxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiKD89OilcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNmdW5jdGlvbi1oZWFkZXItdGVybWluYWwtYXJnXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI290aGVyc1xcXCJ9XX0sXFxcImZ1bmN0aW9uLWhlYWRlci1vcGVuLWJyYWNlXFxcIjp7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXFx7XFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLmVudGl0eS5mdW5jdGlvbi4yLm5peFxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiKD89fSlcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNmdW5jdGlvbi1jb250ZW50c1xcXCJ9XX0sXFxcImZ1bmN0aW9uLWhlYWRlci10ZXJtaW5hbC1hcmdcXFwiOntcXFwiYmVnaW5cXFwiOlxcXCIoPz1AKVxcXCIsXFxcImVuZFxcXCI6XFxcIig/PTopXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImJlZ2luXFxcIjpcXFwiQFxcXCIsXFxcImVuZFxcXCI6XFxcIig/PTopXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImJlZ2luXFxcIjpcXFwiKFxcXFxcXFxcYlthLXpBLVpfXVthLXpBLVowLTlfJy1dKilcXFwiLFxcXCJlbmRcXFwiOlxcXCIoPz06KVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJ2YXJpYWJsZS5wYXJhbWV0ZXIuZnVuY3Rpb24uMy5uaXhcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjb3RoZXJzXFxcIn1dfSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjb3RoZXJzXFxcIn1dfSxcXFwiZnVuY3Rpb24taGVhZGVyLXVudGlsLWNvbG9uLW5vLWFyZ1xcXCI6e1xcXCJiZWdpblxcXCI6XFxcIig/PVxcXFxcXFxceylcXFwiLFxcXCJlbmRcXFwiOlxcXCIoPz06KVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2Z1bmN0aW9uLWhlYWRlci1vcGVuLWJyYWNlXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2Z1bmN0aW9uLWhlYWRlci1jbG9zZS1icmFjZS1uby1hcmdcXFwifV19LFxcXCJmdW5jdGlvbi1oZWFkZXItdW50aWwtY29sb24td2l0aC1hcmdcXFwiOntcXFwiYmVnaW5cXFwiOlxcXCIoPz1cXFxcXFxcXHspXFxcIixcXFwiZW5kXFxcIjpcXFwiKD89OilcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNmdW5jdGlvbi1oZWFkZXItb3Blbi1icmFjZVxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNmdW5jdGlvbi1oZWFkZXItY2xvc2UtYnJhY2Utd2l0aC1hcmdcXFwifV19LFxcXCJmdW5jdGlvbi1wYXJhbWV0ZXJcXFwiOntcXFwicGF0dGVybnNcXFwiOlt7XFxcImJlZ2luXFxcIjpcXFwiKFxcXFxcXFxcLlxcXFxcXFxcLlxcXFxcXFxcLilcXFwiLFxcXCJlbmRcXFwiOlxcXCIoLHwoPz19KSlcXFwiLFxcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5vcGVyYXRvci5uaXhcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNvdGhlcnNcXFwifV19LHtcXFwiYmVnaW5cXFwiOlxcXCJcXFxcXFxcXGJbYS16QS1aX11bYS16QS1aMC05XyctXSpcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInZhcmlhYmxlLnBhcmFtZXRlci5mdW5jdGlvbi4xLm5peFxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiKCx8KD89fSkpXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5vcGVyYXRvci5uaXhcXFwifX0sXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3doaXRlc3BhY2VcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjY29tbWVudFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNmdW5jdGlvbi1wYXJhbWV0ZXItZGVmYXVsdFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNleHByZXNzaW9uXFxcIn1dfSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjb3RoZXJzXFxcIn1dfSxcXFwiZnVuY3Rpb24tcGFyYW1ldGVyLWRlZmF1bHRcXFwiOntcXFwiYmVnaW5cXFwiOlxcXCJcXFxcXFxcXD9cXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQub3BlcmF0b3Iubml4XFxcIn19LFxcXCJlbmRcXFwiOlxcXCIoPz1bLH1dKVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2V4cHJlc3Npb25cXFwifV19LFxcXCJpZlxcXCI6e1xcXCJiZWdpblxcXCI6XFxcIig/PVxcXFxcXFxcYmlmXFxcXFxcXFxiKVxcXCIsXFxcImVuZFxcXCI6XFxcIig/PShbXFxcXFxcXFxdKX07LF18XFxcXFxcXFxiKGVsc2V8dGhlbilcXFxcXFxcXGIpKVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxcYmlmXFxcXFxcXFxiXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLm90aGVyLm5peFxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiXFxcXFxcXFxidGgoPz1lblxcXFxcXFxcYilcXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLm90aGVyLm5peFxcXCJ9fSxcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjZXhwcmVzc2lvblxcXCJ9XX0se1xcXCJiZWdpblxcXCI6XFxcIig/PD10aCllblxcXFxcXFxcYlxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5vdGhlci5uaXhcXFwifX0sXFxcImVuZFxcXCI6XFxcIlxcXFxcXFxcYmVsKD89c2VcXFxcXFxcXGIpXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5vdGhlci5uaXhcXFwifX0sXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2V4cHJlc3Npb25cXFwifV19LHtcXFwiYmVnaW5cXFwiOlxcXCIoPzw9ZWwpc2VcXFxcXFxcXGJcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQub3RoZXIubml4XFxcIn19LFxcXCJlbmRcXFwiOlxcXCIoPz0oW1xcXFxcXFxcXSl9OyxdfFxcXFxcXFxcYihlbHNlfHRoZW4pXFxcXFxcXFxiKSlcXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLm90aGVyLm5peFxcXCJ9fSxcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjZXhwcmVzc2lvblxcXCJ9XX1dfSxcXFwiaWxsZWdhbFxcXCI6e1xcXCJtYXRjaFxcXCI6XFxcIi5cXFwiLFxcXCJuYW1lXFxcIjpcXFwiaW52YWxpZC5pbGxlZ2FsXFxcIn0sXFxcImludGVycG9sYXRpb25cXFwiOntcXFwiYmVnaW5cXFwiOlxcXCJcXFxcXFxcXCRcXFxcXFxcXHtcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLnNlY3Rpb24uZW1iZWRkZWQuYmVnaW4ubml4XFxcIn19LFxcXCJlbmRcXFwiOlxcXCJ9XFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uc2VjdGlvbi5lbWJlZGRlZC5lbmQubml4XFxcIn19LFxcXCJuYW1lXFxcIjpcXFwibWV0YS5lbWJlZGRlZFxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2V4cHJlc3Npb25cXFwifV19LFxcXCJsZXRcXFwiOntcXFwiYmVnaW5cXFwiOlxcXCIoPz1cXFxcXFxcXGJsZXRcXFxcXFxcXGIpXFxcIixcXFwiZW5kXFxcIjpcXFwiKD89KFtcXFxcXFxcXF0pfTssXXxcXFxcXFxcXGIoZWxzZXx0aGVuKVxcXFxcXFxcYikpXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXFxibGV0XFxcXFxcXFxiXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLm90aGVyLm5peFxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiKD89KFtcXFxcXFxcXF0pfTssXXxcXFxcXFxcXGIoaW58ZWxzZXx0aGVuKVxcXFxcXFxcYikpXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImJlZ2luXFxcIjpcXFwiKD89XFxcXFxcXFx7KVxcXCIsXFxcImVuZFxcXCI6XFxcIig/PShbXFxcXFxcXFxdKX07LF18XFxcXFxcXFxiKGVsc2V8dGhlbilcXFxcXFxcXGIpKVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxce1xcXCIsXFxcImVuZFxcXCI6XFxcIn1cXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNhdHRyc2V0LWNvbnRlbnRzXFxcIn1dfSx7XFxcImJlZ2luXFxcIjpcXFwiKF58KD88PX0pKVxcXCIsXFxcImVuZFxcXCI6XFxcIig/PShbXFxcXFxcXFxdKX07LF18XFxcXFxcXFxiKGVsc2V8dGhlbilcXFxcXFxcXGIpKVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2V4cHJlc3Npb24tY29udFxcXCJ9XX0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI290aGVyc1xcXCJ9XX0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2F0dHJzZXQtY29udGVudHNcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjb3RoZXJzXFxcIn1dfSx7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXFxiaW5cXFxcXFxcXGJcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQub3RoZXIubml4XFxcIn19LFxcXCJlbmRcXFwiOlxcXCIoPz0oW1xcXFxcXFxcXSl9OyxdfFxcXFxcXFxcYihlbHNlfHRoZW4pXFxcXFxcXFxiKSlcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNleHByZXNzaW9uXFxcIn1dfV19LFxcXCJsaXN0XFxcIjp7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXFxbXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLmxpc3Qubml4XFxcIn19LFxcXCJlbmRcXFwiOlxcXCJdXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uZGVmaW5pdGlvbi5saXN0Lm5peFxcXCJ9fSxcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjZXhwcmVzc2lvblxcXCJ9XX0sXFxcImxpc3QtYW5kLWNvbnRcXFwiOntcXFwiYmVnaW5cXFwiOlxcXCIoPz1cXFxcXFxcXFspXFxcIixcXFwiZW5kXFxcIjpcXFwiKD89KFtcXFxcXFxcXF0pfTssXXxcXFxcXFxcXGIoZWxzZXx0aGVuKVxcXFxcXFxcYikpXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjbGlzdFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNleHByZXNzaW9uLWNvbnRcXFwifV19LFxcXCJvcGVyYXRvci11bmFyeVxcXCI6e1xcXCJtYXRjaFxcXCI6XFxcIihbIS1dKVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLm9wZXJhdG9yLnVuYXJ5Lm5peFxcXCJ9LFxcXCJvdGhlcnNcXFwiOntcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjd2hpdGVzcGFjZVxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNjb21tZW50XFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2lsbGVnYWxcXFwifV19LFxcXCJwYXJhbWV0ZXItbmFtZVxcXCI6e1xcXCJjYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJ2YXJpYWJsZS5wYXJhbWV0ZXIubmFtZS5uaXhcXFwifX0sXFxcIm1hdGNoXFxcIjpcXFwiXFxcXFxcXFxiW2EtekEtWl9dW2EtekEtWjAtOV8nLV0qXFxcIn0sXFxcInBhcmFtZXRlci1uYW1lLWFuZC1jb250XFxcIjp7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXFxiW2EtekEtWl9dW2EtekEtWjAtOV8nLV0qXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJ2YXJpYWJsZS5wYXJhbWV0ZXIubmFtZS5uaXhcXFwifX0sXFxcImVuZFxcXCI6XFxcIig/PShbXFxcXFxcXFxdKX07LF18XFxcXFxcXFxiKGVsc2V8dGhlbilcXFxcXFxcXGIpKVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2V4cHJlc3Npb24tY29udFxcXCJ9XX0sXFxcInBhcmVuc1xcXCI6e1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxcKFxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uZGVmaW5pdGlvbi5leHByZXNzaW9uLm5peFxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiXFxcXFxcXFwpXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uZGVmaW5pdGlvbi5leHByZXNzaW9uLm5peFxcXCJ9fSxcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjZXhwcmVzc2lvblxcXCJ9XX0sXFxcInBhcmVucy1hbmQtY29udFxcXCI6e1xcXCJiZWdpblxcXCI6XFxcIig/PVxcXFxcXFxcKClcXFwiLFxcXCJlbmRcXFwiOlxcXCIoPz0oW1xcXFxcXFxcXSl9OyxdfFxcXFxcXFxcYihlbHNlfHRoZW4pXFxcXFxcXFxiKSlcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNwYXJlbnNcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjZXhwcmVzc2lvbi1jb250XFxcIn1dfSxcXFwic3RyaW5nXFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJiZWdpblxcXCI6XFxcIig/PScnKVxcXCIsXFxcImVuZFxcXCI6XFxcIig/PShbXFxcXFxcXFxdKX07LF18XFxcXFxcXFxiKGVsc2V8dGhlbilcXFxcXFxcXGIpKVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJiZWdpblxcXCI6XFxcIicnXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLnN0cmluZy5vdGhlci5zdGFydC5uaXhcXFwifX0sXFxcImVuZFxcXCI6XFxcIicnKD8hWyQnXXxcXFxcXFxcXFxcXFxcXFxcLilcXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLnN0cmluZy5vdGhlci5lbmQubml4XFxcIn19LFxcXCJuYW1lXFxcIjpcXFwic3RyaW5nLnF1b3RlZC5vdGhlci5uaXhcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwibWF0Y2hcXFwiOlxcXCInJyhbJCddfFxcXFxcXFxcXFxcXFxcXFwuKVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJjb25zdGFudC5jaGFyYWN0ZXIuZXNjYXBlLm5peFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNpbnRlcnBvbGF0aW9uXFxcIn1dfSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjZXhwcmVzc2lvbi1jb250XFxcIn1dfSx7XFxcImJlZ2luXFxcIjpcXFwiKD89XFxcXFxcXCIpXFxcIixcXFwiZW5kXFxcIjpcXFwiKD89KFtcXFxcXFxcXF0pfTssXXxcXFxcXFxcXGIoZWxzZXx0aGVuKVxcXFxcXFxcYikpXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjc3RyaW5nLXF1b3RlZFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNleHByZXNzaW9uLWNvbnRcXFwifV19LHtcXFwiYmVnaW5cXFwiOlxcXCIofj9bYS16QS1aMC05Ll9cXFxcXFxcXC0rXSooL1thLXpBLVowLTkuX1xcXFxcXFxcLStdKykrKVxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwic3RyaW5nLnVucXVvdGVkLnBhdGgubml4XFxcIn19LFxcXCJlbmRcXFwiOlxcXCIoPz0oW1xcXFxcXFxcXSl9OyxdfFxcXFxcXFxcYihlbHNlfHRoZW4pXFxcXFxcXFxiKSlcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNleHByZXNzaW9uLWNvbnRcXFwifV19LHtcXFwiYmVnaW5cXFwiOlxcXCIoPFthLXpBLVowLTkuX1xcXFxcXFxcLStdKygvW2EtekEtWjAtOS5fXFxcXFxcXFwtK10rKSo+KVxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwic3RyaW5nLnVucXVvdGVkLnNwYXRoLm5peFxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiKD89KFtcXFxcXFxcXF0pfTssXXxcXFxcXFxcXGIoZWxzZXx0aGVuKVxcXFxcXFxcYikpXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjZXhwcmVzc2lvbi1jb250XFxcIn1dfSx7XFxcImJlZ2luXFxcIjpcXFwiKFthLXpBLVpdW2EtekEtWjAtOStcXFxcXFxcXC0uXSo6W2EtekEtWjAtOSUvPzpAXFxcXFxcXFwmPSskLFxcXFxcXFxcLV8uIX4qJ10rKVxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwic3RyaW5nLnVucXVvdGVkLnVybC5uaXhcXFwifX0sXFxcImVuZFxcXCI6XFxcIig/PShbXFxcXFxcXFxdKX07LF18XFxcXFxcXFxiKGVsc2V8dGhlbilcXFxcXFxcXGIpKVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2V4cHJlc3Npb24tY29udFxcXCJ9XX1dfSxcXFwic3RyaW5nLXF1b3RlZFxcXCI6e1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFwiXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLnN0cmluZy5kb3VibGUuc3RhcnQubml4XFxcIn19LFxcXCJlbmRcXFwiOlxcXCJcXFxcXFxcIlxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24uc3RyaW5nLmRvdWJsZS5lbmQubml4XFxcIn19LFxcXCJuYW1lXFxcIjpcXFwic3RyaW5nLnF1b3RlZC5kb3VibGUubml4XFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcIm1hdGNoXFxcIjpcXFwiXFxcXFxcXFxcXFxcXFxcXC5cXFwiLFxcXCJuYW1lXFxcIjpcXFwiY29uc3RhbnQuY2hhcmFjdGVyLmVzY2FwZS5uaXhcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjaW50ZXJwb2xhdGlvblxcXCJ9XX0sXFxcIndoaXRlc3BhY2VcXFwiOntcXFwibWF0Y2hcXFwiOlxcXCJcXFxcXFxcXHMrXFxcIn0sXFxcIndpdGgtYXNzZXJ0XFxcIjp7XFxcImJlZ2luXFxcIjpcXFwiKD88IVtcXFxcXFxcXHcnLV0pKHdpdGh8YXNzZXJ0KSg/IVtcXFxcXFxcXHcnLV0pXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLm90aGVyLm5peFxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiO1xcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2V4cHJlc3Npb25cXFwifV19fSxcXFwic2NvcGVOYW1lXFxcIjpcXFwic291cmNlLm5peFxcXCJ9XCIpKVxuXG5leHBvcnQgZGVmYXVsdCBbXG5sYW5nXG5dXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/nix.mjs\n"));

/***/ })

}]);