"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/motion-dom";
exports.ids = ["vendor-chunks/motion-dom"];
exports.modules = {

/***/ "(ssr)/./node_modules/motion-dom/dist/es/animation/GroupAnimation.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/animation/GroupAnimation.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GroupAnimation: () => (/* binding */ GroupAnimation)\n/* harmony export */ });\n/* harmony import */ var _utils_supports_scroll_timeline_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/supports/scroll-timeline.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/utils/supports/scroll-timeline.mjs\");\n\n\nclass GroupAnimation {\n    constructor(animations) {\n        // Bound to accomodate common `return animation.stop` pattern\n        this.stop = () => this.runAll(\"stop\");\n        this.animations = animations.filter(Boolean);\n    }\n    get finished() {\n        return Promise.all(this.animations.map((animation) => animation.finished));\n    }\n    /**\n     * TODO: Filter out cancelled or stopped animations before returning\n     */\n    getAll(propName) {\n        return this.animations[0][propName];\n    }\n    setAll(propName, newValue) {\n        for (let i = 0; i < this.animations.length; i++) {\n            this.animations[i][propName] = newValue;\n        }\n    }\n    attachTimeline(timeline, fallback) {\n        const subscriptions = this.animations.map((animation) => {\n            if ((0,_utils_supports_scroll_timeline_mjs__WEBPACK_IMPORTED_MODULE_0__.supportsScrollTimeline)() && animation.attachTimeline) {\n                return animation.attachTimeline(timeline);\n            }\n            else if (typeof fallback === \"function\") {\n                return fallback(animation);\n            }\n        });\n        return () => {\n            subscriptions.forEach((cancel, i) => {\n                cancel && cancel();\n                this.animations[i].stop();\n            });\n        };\n    }\n    get time() {\n        return this.getAll(\"time\");\n    }\n    set time(time) {\n        this.setAll(\"time\", time);\n    }\n    get speed() {\n        return this.getAll(\"speed\");\n    }\n    set speed(speed) {\n        this.setAll(\"speed\", speed);\n    }\n    get startTime() {\n        return this.getAll(\"startTime\");\n    }\n    get duration() {\n        let max = 0;\n        for (let i = 0; i < this.animations.length; i++) {\n            max = Math.max(max, this.animations[i].duration);\n        }\n        return max;\n    }\n    runAll(methodName) {\n        this.animations.forEach((controls) => controls[methodName]());\n    }\n    flatten() {\n        this.runAll(\"flatten\");\n    }\n    play() {\n        this.runAll(\"play\");\n    }\n    pause() {\n        this.runAll(\"pause\");\n    }\n    cancel() {\n        this.runAll(\"cancel\");\n    }\n    complete() {\n        this.runAll(\"complete\");\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/animation/GroupAnimation.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/animation/GroupAnimationWithThen.mjs":
/*!******************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/animation/GroupAnimationWithThen.mjs ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GroupAnimationWithThen: () => (/* binding */ GroupAnimationWithThen)\n/* harmony export */ });\n/* harmony import */ var _GroupAnimation_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./GroupAnimation.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/GroupAnimation.mjs\");\n\n\nclass GroupAnimationWithThen extends _GroupAnimation_mjs__WEBPACK_IMPORTED_MODULE_0__.GroupAnimation {\n    then(onResolve, _onReject) {\n        return this.finished.finally(onResolve).then(() => { });\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2FuaW1hdGlvbi9Hcm91cEFuaW1hdGlvbldpdGhUaGVuLm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFzRDs7QUFFdEQscUNBQXFDLCtEQUFjO0FBQ25EO0FBQ0EsOERBQThEO0FBQzlEO0FBQ0E7O0FBRWtDIiwic291cmNlcyI6WyJEOlxcc29mdFxcU2VhUHJvamVjdFxcZ2l0aHViX29wZW5fcHJvamVjdF9zdWNjZXNzXFxzdW5hXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxtb3Rpb24tZG9tXFxkaXN0XFxlc1xcYW5pbWF0aW9uXFxHcm91cEFuaW1hdGlvbldpdGhUaGVuLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBHcm91cEFuaW1hdGlvbiB9IGZyb20gJy4vR3JvdXBBbmltYXRpb24ubWpzJztcblxuY2xhc3MgR3JvdXBBbmltYXRpb25XaXRoVGhlbiBleHRlbmRzIEdyb3VwQW5pbWF0aW9uIHtcbiAgICB0aGVuKG9uUmVzb2x2ZSwgX29uUmVqZWN0KSB7XG4gICAgICAgIHJldHVybiB0aGlzLmZpbmlzaGVkLmZpbmFsbHkob25SZXNvbHZlKS50aGVuKCgpID0+IHsgfSk7XG4gICAgfVxufVxuXG5leHBvcnQgeyBHcm91cEFuaW1hdGlvbldpdGhUaGVuIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/animation/GroupAnimationWithThen.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/animation/generators/utils/calc-duration.mjs":
/*!**************************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/animation/generators/utils/calc-duration.mjs ***!
  \**************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calcGeneratorDuration: () => (/* binding */ calcGeneratorDuration),\n/* harmony export */   maxGeneratorDuration: () => (/* binding */ maxGeneratorDuration)\n/* harmony export */ });\n/**\n * Implement a practical max duration for keyframe generation\n * to prevent infinite loops\n */\nconst maxGeneratorDuration = 20000;\nfunction calcGeneratorDuration(generator) {\n    let duration = 0;\n    const timeStep = 50;\n    let state = generator.next(duration);\n    while (!state.done && duration < maxGeneratorDuration) {\n        duration += timeStep;\n        state = generator.next(duration);\n    }\n    return duration >= maxGeneratorDuration ? Infinity : duration;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2FuaW1hdGlvbi9nZW5lcmF0b3JzL3V0aWxzL2NhbGMtZHVyYXRpb24ubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUV1RCIsInNvdXJjZXMiOlsiRDpcXHNvZnRcXFNlYVByb2plY3RcXGdpdGh1Yl9vcGVuX3Byb2plY3Rfc3VjY2Vzc1xcc3VuYVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcbW90aW9uLWRvbVxcZGlzdFxcZXNcXGFuaW1hdGlvblxcZ2VuZXJhdG9yc1xcdXRpbHNcXGNhbGMtZHVyYXRpb24ubWpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogSW1wbGVtZW50IGEgcHJhY3RpY2FsIG1heCBkdXJhdGlvbiBmb3Iga2V5ZnJhbWUgZ2VuZXJhdGlvblxuICogdG8gcHJldmVudCBpbmZpbml0ZSBsb29wc1xuICovXG5jb25zdCBtYXhHZW5lcmF0b3JEdXJhdGlvbiA9IDIwMDAwO1xuZnVuY3Rpb24gY2FsY0dlbmVyYXRvckR1cmF0aW9uKGdlbmVyYXRvcikge1xuICAgIGxldCBkdXJhdGlvbiA9IDA7XG4gICAgY29uc3QgdGltZVN0ZXAgPSA1MDtcbiAgICBsZXQgc3RhdGUgPSBnZW5lcmF0b3IubmV4dChkdXJhdGlvbik7XG4gICAgd2hpbGUgKCFzdGF0ZS5kb25lICYmIGR1cmF0aW9uIDwgbWF4R2VuZXJhdG9yRHVyYXRpb24pIHtcbiAgICAgICAgZHVyYXRpb24gKz0gdGltZVN0ZXA7XG4gICAgICAgIHN0YXRlID0gZ2VuZXJhdG9yLm5leHQoZHVyYXRpb24pO1xuICAgIH1cbiAgICByZXR1cm4gZHVyYXRpb24gPj0gbWF4R2VuZXJhdG9yRHVyYXRpb24gPyBJbmZpbml0eSA6IGR1cmF0aW9uO1xufVxuXG5leHBvcnQgeyBjYWxjR2VuZXJhdG9yRHVyYXRpb24sIG1heEdlbmVyYXRvckR1cmF0aW9uIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/animation/generators/utils/calc-duration.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/animation/generators/utils/create-generator-easing.mjs":
/*!************************************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/animation/generators/utils/create-generator-easing.mjs ***!
  \************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createGeneratorEasing: () => (/* binding */ createGeneratorEasing)\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! motion-utils */ \"(ssr)/./node_modules/motion-utils/dist/es/time-conversion.mjs\");\n/* harmony import */ var _calc_duration_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./calc-duration.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/generators/utils/calc-duration.mjs\");\n\n\n\n/**\n * Create a progress => progress easing function from a generator.\n */\nfunction createGeneratorEasing(options, scale = 100, createGenerator) {\n    const generator = createGenerator({ ...options, keyframes: [0, scale] });\n    const duration = Math.min((0,_calc_duration_mjs__WEBPACK_IMPORTED_MODULE_0__.calcGeneratorDuration)(generator), _calc_duration_mjs__WEBPACK_IMPORTED_MODULE_0__.maxGeneratorDuration);\n    return {\n        type: \"keyframes\",\n        ease: (progress) => {\n            return generator.next(duration * progress).value / scale;\n        },\n        duration: (0,motion_utils__WEBPACK_IMPORTED_MODULE_1__.millisecondsToSeconds)(duration),\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2FuaW1hdGlvbi9nZW5lcmF0b3JzL3V0aWxzL2NyZWF0ZS1nZW5lcmF0b3ItZWFzaW5nLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBcUQ7QUFDNkI7O0FBRWxGO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0NBQXdDLG1DQUFtQztBQUMzRSw4QkFBOEIseUVBQXFCLGFBQWEsb0VBQW9CO0FBQ3BGO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULGtCQUFrQixtRUFBcUI7QUFDdkM7QUFDQTs7QUFFaUMiLCJzb3VyY2VzIjpbIkQ6XFxzb2Z0XFxTZWFQcm9qZWN0XFxnaXRodWJfb3Blbl9wcm9qZWN0X3N1Y2Nlc3NcXHN1bmFcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXG1vdGlvbi1kb21cXGRpc3RcXGVzXFxhbmltYXRpb25cXGdlbmVyYXRvcnNcXHV0aWxzXFxjcmVhdGUtZ2VuZXJhdG9yLWVhc2luZy5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgbWlsbGlzZWNvbmRzVG9TZWNvbmRzIH0gZnJvbSAnbW90aW9uLXV0aWxzJztcbmltcG9ydCB7IGNhbGNHZW5lcmF0b3JEdXJhdGlvbiwgbWF4R2VuZXJhdG9yRHVyYXRpb24gfSBmcm9tICcuL2NhbGMtZHVyYXRpb24ubWpzJztcblxuLyoqXG4gKiBDcmVhdGUgYSBwcm9ncmVzcyA9PiBwcm9ncmVzcyBlYXNpbmcgZnVuY3Rpb24gZnJvbSBhIGdlbmVyYXRvci5cbiAqL1xuZnVuY3Rpb24gY3JlYXRlR2VuZXJhdG9yRWFzaW5nKG9wdGlvbnMsIHNjYWxlID0gMTAwLCBjcmVhdGVHZW5lcmF0b3IpIHtcbiAgICBjb25zdCBnZW5lcmF0b3IgPSBjcmVhdGVHZW5lcmF0b3IoeyAuLi5vcHRpb25zLCBrZXlmcmFtZXM6IFswLCBzY2FsZV0gfSk7XG4gICAgY29uc3QgZHVyYXRpb24gPSBNYXRoLm1pbihjYWxjR2VuZXJhdG9yRHVyYXRpb24oZ2VuZXJhdG9yKSwgbWF4R2VuZXJhdG9yRHVyYXRpb24pO1xuICAgIHJldHVybiB7XG4gICAgICAgIHR5cGU6IFwia2V5ZnJhbWVzXCIsXG4gICAgICAgIGVhc2U6IChwcm9ncmVzcykgPT4ge1xuICAgICAgICAgICAgcmV0dXJuIGdlbmVyYXRvci5uZXh0KGR1cmF0aW9uICogcHJvZ3Jlc3MpLnZhbHVlIC8gc2NhbGU7XG4gICAgICAgIH0sXG4gICAgICAgIGR1cmF0aW9uOiBtaWxsaXNlY29uZHNUb1NlY29uZHMoZHVyYXRpb24pLFxuICAgIH07XG59XG5cbmV4cG9ydCB7IGNyZWF0ZUdlbmVyYXRvckVhc2luZyB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/animation/generators/utils/create-generator-easing.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/animation/generators/utils/is-generator.mjs":
/*!*************************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/animation/generators/utils/is-generator.mjs ***!
  \*************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isGenerator: () => (/* binding */ isGenerator)\n/* harmony export */ });\nfunction isGenerator(type) {\n    return typeof type === \"function\" && \"applyToOptions\" in type;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2FuaW1hdGlvbi9nZW5lcmF0b3JzL3V0aWxzL2lzLWdlbmVyYXRvci5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTs7QUFFdUIiLCJzb3VyY2VzIjpbIkQ6XFxzb2Z0XFxTZWFQcm9qZWN0XFxnaXRodWJfb3Blbl9wcm9qZWN0X3N1Y2Nlc3NcXHN1bmFcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXG1vdGlvbi1kb21cXGRpc3RcXGVzXFxhbmltYXRpb25cXGdlbmVyYXRvcnNcXHV0aWxzXFxpcy1nZW5lcmF0b3IubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIGlzR2VuZXJhdG9yKHR5cGUpIHtcbiAgICByZXR1cm4gdHlwZW9mIHR5cGUgPT09IFwiZnVuY3Rpb25cIiAmJiBcImFwcGx5VG9PcHRpb25zXCIgaW4gdHlwZTtcbn1cblxuZXhwb3J0IHsgaXNHZW5lcmF0b3IgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/animation/generators/utils/is-generator.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/animation/utils/get-value-transition.mjs":
/*!**********************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/animation/utils/get-value-transition.mjs ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getValueTransition: () => (/* binding */ getValueTransition)\n/* harmony export */ });\nfunction getValueTransition(transition, key) {\n    return (transition?.[key] ??\n        transition?.[\"default\"] ??\n        transition);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2FuaW1hdGlvbi91dGlscy9nZXQtdmFsdWUtdHJhbnNpdGlvbi5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRThCIiwic291cmNlcyI6WyJEOlxcc29mdFxcU2VhUHJvamVjdFxcZ2l0aHViX29wZW5fcHJvamVjdF9zdWNjZXNzXFxzdW5hXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxtb3Rpb24tZG9tXFxkaXN0XFxlc1xcYW5pbWF0aW9uXFx1dGlsc1xcZ2V0LXZhbHVlLXRyYW5zaXRpb24ubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIGdldFZhbHVlVHJhbnNpdGlvbih0cmFuc2l0aW9uLCBrZXkpIHtcbiAgICByZXR1cm4gKHRyYW5zaXRpb24/LltrZXldID8/XG4gICAgICAgIHRyYW5zaXRpb24/LltcImRlZmF1bHRcIl0gPz9cbiAgICAgICAgdHJhbnNpdGlvbik7XG59XG5cbmV4cG9ydCB7IGdldFZhbHVlVHJhbnNpdGlvbiB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/animation/utils/get-value-transition.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/easing/cubic-bezier.mjs":
/*!*********************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/animation/waapi/easing/cubic-bezier.mjs ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cubicBezierAsString: () => (/* binding */ cubicBezierAsString)\n/* harmony export */ });\nconst cubicBezierAsString = ([a, b, c, d]) => `cubic-bezier(${a}, ${b}, ${c}, ${d})`;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2FuaW1hdGlvbi93YWFwaS9lYXNpbmcvY3ViaWMtYmV6aWVyLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsOERBQThELEVBQUUsSUFBSSxFQUFFLElBQUksRUFBRSxJQUFJLEVBQUU7O0FBRW5EIiwic291cmNlcyI6WyJEOlxcc29mdFxcU2VhUHJvamVjdFxcZ2l0aHViX29wZW5fcHJvamVjdF9zdWNjZXNzXFxzdW5hXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxtb3Rpb24tZG9tXFxkaXN0XFxlc1xcYW5pbWF0aW9uXFx3YWFwaVxcZWFzaW5nXFxjdWJpYy1iZXppZXIubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGN1YmljQmV6aWVyQXNTdHJpbmcgPSAoW2EsIGIsIGMsIGRdKSA9PiBgY3ViaWMtYmV6aWVyKCR7YX0sICR7Yn0sICR7Y30sICR7ZH0pYDtcblxuZXhwb3J0IHsgY3ViaWNCZXppZXJBc1N0cmluZyB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/easing/cubic-bezier.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/easing/is-supported.mjs":
/*!*********************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/animation/waapi/easing/is-supported.mjs ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isWaapiSupportedEasing: () => (/* binding */ isWaapiSupportedEasing)\n/* harmony export */ });\n/* harmony import */ var _utils_is_bezier_definition_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../utils/is-bezier-definition.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/utils/is-bezier-definition.mjs\");\n/* harmony import */ var _utils_supports_linear_easing_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../utils/supports/linear-easing.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/utils/supports/linear-easing.mjs\");\n/* harmony import */ var _supported_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./supported.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/easing/supported.mjs\");\n\n\n\n\nfunction isWaapiSupportedEasing(easing) {\n    return Boolean((typeof easing === \"function\" && (0,_utils_supports_linear_easing_mjs__WEBPACK_IMPORTED_MODULE_0__.supportsLinearEasing)()) ||\n        !easing ||\n        (typeof easing === \"string\" &&\n            (easing in _supported_mjs__WEBPACK_IMPORTED_MODULE_1__.supportedWaapiEasing || (0,_utils_supports_linear_easing_mjs__WEBPACK_IMPORTED_MODULE_0__.supportsLinearEasing)())) ||\n        (0,_utils_is_bezier_definition_mjs__WEBPACK_IMPORTED_MODULE_2__.isBezierDefinition)(easing) ||\n        (Array.isArray(easing) && easing.every(isWaapiSupportedEasing)));\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2FuaW1hdGlvbi93YWFwaS9lYXNpbmcvaXMtc3VwcG9ydGVkLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQTZFO0FBQ0k7QUFDMUI7O0FBRXZEO0FBQ0Esb0RBQW9ELHVGQUFvQjtBQUN4RTtBQUNBO0FBQ0EsdUJBQXVCLGdFQUFvQixJQUFJLHVGQUFvQjtBQUNuRSxRQUFRLG1GQUFrQjtBQUMxQjtBQUNBOztBQUVrQyIsInNvdXJjZXMiOlsiRDpcXHNvZnRcXFNlYVByb2plY3RcXGdpdGh1Yl9vcGVuX3Byb2plY3Rfc3VjY2Vzc1xcc3VuYVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcbW90aW9uLWRvbVxcZGlzdFxcZXNcXGFuaW1hdGlvblxcd2FhcGlcXGVhc2luZ1xcaXMtc3VwcG9ydGVkLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBpc0JlemllckRlZmluaXRpb24gfSBmcm9tICcuLi8uLi8uLi91dGlscy9pcy1iZXppZXItZGVmaW5pdGlvbi5tanMnO1xuaW1wb3J0IHsgc3VwcG9ydHNMaW5lYXJFYXNpbmcgfSBmcm9tICcuLi8uLi8uLi91dGlscy9zdXBwb3J0cy9saW5lYXItZWFzaW5nLm1qcyc7XG5pbXBvcnQgeyBzdXBwb3J0ZWRXYWFwaUVhc2luZyB9IGZyb20gJy4vc3VwcG9ydGVkLm1qcyc7XG5cbmZ1bmN0aW9uIGlzV2FhcGlTdXBwb3J0ZWRFYXNpbmcoZWFzaW5nKSB7XG4gICAgcmV0dXJuIEJvb2xlYW4oKHR5cGVvZiBlYXNpbmcgPT09IFwiZnVuY3Rpb25cIiAmJiBzdXBwb3J0c0xpbmVhckVhc2luZygpKSB8fFxuICAgICAgICAhZWFzaW5nIHx8XG4gICAgICAgICh0eXBlb2YgZWFzaW5nID09PSBcInN0cmluZ1wiICYmXG4gICAgICAgICAgICAoZWFzaW5nIGluIHN1cHBvcnRlZFdhYXBpRWFzaW5nIHx8IHN1cHBvcnRzTGluZWFyRWFzaW5nKCkpKSB8fFxuICAgICAgICBpc0JlemllckRlZmluaXRpb24oZWFzaW5nKSB8fFxuICAgICAgICAoQXJyYXkuaXNBcnJheShlYXNpbmcpICYmIGVhc2luZy5ldmVyeShpc1dhYXBpU3VwcG9ydGVkRWFzaW5nKSkpO1xufVxuXG5leHBvcnQgeyBpc1dhYXBpU3VwcG9ydGVkRWFzaW5nIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/easing/is-supported.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/easing/map-easing.mjs":
/*!*******************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/animation/waapi/easing/map-easing.mjs ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mapEasingToNativeEasing: () => (/* binding */ mapEasingToNativeEasing)\n/* harmony export */ });\n/* harmony import */ var _utils_is_bezier_definition_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../utils/is-bezier-definition.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/utils/is-bezier-definition.mjs\");\n/* harmony import */ var _utils_supports_linear_easing_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../utils/supports/linear-easing.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/utils/supports/linear-easing.mjs\");\n/* harmony import */ var _utils_linear_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/linear.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/utils/linear.mjs\");\n/* harmony import */ var _cubic_bezier_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./cubic-bezier.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/easing/cubic-bezier.mjs\");\n/* harmony import */ var _supported_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./supported.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/easing/supported.mjs\");\n\n\n\n\n\n\nfunction mapEasingToNativeEasing(easing, duration) {\n    if (!easing) {\n        return undefined;\n    }\n    else if (typeof easing === \"function\" && (0,_utils_supports_linear_easing_mjs__WEBPACK_IMPORTED_MODULE_0__.supportsLinearEasing)()) {\n        return (0,_utils_linear_mjs__WEBPACK_IMPORTED_MODULE_1__.generateLinearEasing)(easing, duration);\n    }\n    else if ((0,_utils_is_bezier_definition_mjs__WEBPACK_IMPORTED_MODULE_2__.isBezierDefinition)(easing)) {\n        return (0,_cubic_bezier_mjs__WEBPACK_IMPORTED_MODULE_3__.cubicBezierAsString)(easing);\n    }\n    else if (Array.isArray(easing)) {\n        return easing.map((segmentEasing) => mapEasingToNativeEasing(segmentEasing, duration) ||\n            _supported_mjs__WEBPACK_IMPORTED_MODULE_4__.supportedWaapiEasing.easeOut);\n    }\n    else {\n        return _supported_mjs__WEBPACK_IMPORTED_MODULE_4__.supportedWaapiEasing[easing];\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2FuaW1hdGlvbi93YWFwaS9lYXNpbmcvbWFwLWVhc2luZy5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQTZFO0FBQ0k7QUFDdEI7QUFDRjtBQUNGOztBQUV2RDtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZDQUE2Qyx1RkFBb0I7QUFDakUsZUFBZSx1RUFBb0I7QUFDbkM7QUFDQSxhQUFhLG1GQUFrQjtBQUMvQixlQUFlLHNFQUFtQjtBQUNsQztBQUNBO0FBQ0E7QUFDQSxZQUFZLGdFQUFvQjtBQUNoQztBQUNBO0FBQ0EsZUFBZSxnRUFBb0I7QUFDbkM7QUFDQTs7QUFFbUMiLCJzb3VyY2VzIjpbIkQ6XFxzb2Z0XFxTZWFQcm9qZWN0XFxnaXRodWJfb3Blbl9wcm9qZWN0X3N1Y2Nlc3NcXHN1bmFcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXG1vdGlvbi1kb21cXGRpc3RcXGVzXFxhbmltYXRpb25cXHdhYXBpXFxlYXNpbmdcXG1hcC1lYXNpbmcubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGlzQmV6aWVyRGVmaW5pdGlvbiB9IGZyb20gJy4uLy4uLy4uL3V0aWxzL2lzLWJlemllci1kZWZpbml0aW9uLm1qcyc7XG5pbXBvcnQgeyBzdXBwb3J0c0xpbmVhckVhc2luZyB9IGZyb20gJy4uLy4uLy4uL3V0aWxzL3N1cHBvcnRzL2xpbmVhci1lYXNpbmcubWpzJztcbmltcG9ydCB7IGdlbmVyYXRlTGluZWFyRWFzaW5nIH0gZnJvbSAnLi4vdXRpbHMvbGluZWFyLm1qcyc7XG5pbXBvcnQgeyBjdWJpY0JlemllckFzU3RyaW5nIH0gZnJvbSAnLi9jdWJpYy1iZXppZXIubWpzJztcbmltcG9ydCB7IHN1cHBvcnRlZFdhYXBpRWFzaW5nIH0gZnJvbSAnLi9zdXBwb3J0ZWQubWpzJztcblxuZnVuY3Rpb24gbWFwRWFzaW5nVG9OYXRpdmVFYXNpbmcoZWFzaW5nLCBkdXJhdGlvbikge1xuICAgIGlmICghZWFzaW5nKSB7XG4gICAgICAgIHJldHVybiB1bmRlZmluZWQ7XG4gICAgfVxuICAgIGVsc2UgaWYgKHR5cGVvZiBlYXNpbmcgPT09IFwiZnVuY3Rpb25cIiAmJiBzdXBwb3J0c0xpbmVhckVhc2luZygpKSB7XG4gICAgICAgIHJldHVybiBnZW5lcmF0ZUxpbmVhckVhc2luZyhlYXNpbmcsIGR1cmF0aW9uKTtcbiAgICB9XG4gICAgZWxzZSBpZiAoaXNCZXppZXJEZWZpbml0aW9uKGVhc2luZykpIHtcbiAgICAgICAgcmV0dXJuIGN1YmljQmV6aWVyQXNTdHJpbmcoZWFzaW5nKTtcbiAgICB9XG4gICAgZWxzZSBpZiAoQXJyYXkuaXNBcnJheShlYXNpbmcpKSB7XG4gICAgICAgIHJldHVybiBlYXNpbmcubWFwKChzZWdtZW50RWFzaW5nKSA9PiBtYXBFYXNpbmdUb05hdGl2ZUVhc2luZyhzZWdtZW50RWFzaW5nLCBkdXJhdGlvbikgfHxcbiAgICAgICAgICAgIHN1cHBvcnRlZFdhYXBpRWFzaW5nLmVhc2VPdXQpO1xuICAgIH1cbiAgICBlbHNlIHtcbiAgICAgICAgcmV0dXJuIHN1cHBvcnRlZFdhYXBpRWFzaW5nW2Vhc2luZ107XG4gICAgfVxufVxuXG5leHBvcnQgeyBtYXBFYXNpbmdUb05hdGl2ZUVhc2luZyB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/easing/map-easing.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/easing/supported.mjs":
/*!******************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/animation/waapi/easing/supported.mjs ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supportedWaapiEasing: () => (/* binding */ supportedWaapiEasing)\n/* harmony export */ });\n/* harmony import */ var _cubic_bezier_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./cubic-bezier.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/easing/cubic-bezier.mjs\");\n\n\nconst supportedWaapiEasing = {\n    linear: \"linear\",\n    ease: \"ease\",\n    easeIn: \"ease-in\",\n    easeOut: \"ease-out\",\n    easeInOut: \"ease-in-out\",\n    circIn: /*@__PURE__*/ (0,_cubic_bezier_mjs__WEBPACK_IMPORTED_MODULE_0__.cubicBezierAsString)([0, 0.65, 0.55, 1]),\n    circOut: /*@__PURE__*/ (0,_cubic_bezier_mjs__WEBPACK_IMPORTED_MODULE_0__.cubicBezierAsString)([0.55, 0, 1, 0.45]),\n    backIn: /*@__PURE__*/ (0,_cubic_bezier_mjs__WEBPACK_IMPORTED_MODULE_0__.cubicBezierAsString)([0.31, 0.01, 0.66, -0.59]),\n    backOut: /*@__PURE__*/ (0,_cubic_bezier_mjs__WEBPACK_IMPORTED_MODULE_0__.cubicBezierAsString)([0.33, 1.53, 0.69, 0.99]),\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2FuaW1hdGlvbi93YWFwaS9lYXNpbmcvc3VwcG9ydGVkLm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUF5RDs7QUFFekQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCLHNFQUFtQjtBQUM3QywyQkFBMkIsc0VBQW1CO0FBQzlDLDBCQUEwQixzRUFBbUI7QUFDN0MsMkJBQTJCLHNFQUFtQjtBQUM5Qzs7QUFFZ0MiLCJzb3VyY2VzIjpbIkQ6XFxzb2Z0XFxTZWFQcm9qZWN0XFxnaXRodWJfb3Blbl9wcm9qZWN0X3N1Y2Nlc3NcXHN1bmFcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXG1vdGlvbi1kb21cXGRpc3RcXGVzXFxhbmltYXRpb25cXHdhYXBpXFxlYXNpbmdcXHN1cHBvcnRlZC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3ViaWNCZXppZXJBc1N0cmluZyB9IGZyb20gJy4vY3ViaWMtYmV6aWVyLm1qcyc7XG5cbmNvbnN0IHN1cHBvcnRlZFdhYXBpRWFzaW5nID0ge1xuICAgIGxpbmVhcjogXCJsaW5lYXJcIixcbiAgICBlYXNlOiBcImVhc2VcIixcbiAgICBlYXNlSW46IFwiZWFzZS1pblwiLFxuICAgIGVhc2VPdXQ6IFwiZWFzZS1vdXRcIixcbiAgICBlYXNlSW5PdXQ6IFwiZWFzZS1pbi1vdXRcIixcbiAgICBjaXJjSW46IC8qQF9fUFVSRV9fKi8gY3ViaWNCZXppZXJBc1N0cmluZyhbMCwgMC42NSwgMC41NSwgMV0pLFxuICAgIGNpcmNPdXQ6IC8qQF9fUFVSRV9fKi8gY3ViaWNCZXppZXJBc1N0cmluZyhbMC41NSwgMCwgMSwgMC40NV0pLFxuICAgIGJhY2tJbjogLypAX19QVVJFX18qLyBjdWJpY0JlemllckFzU3RyaW5nKFswLjMxLCAwLjAxLCAwLjY2LCAtMC41OV0pLFxuICAgIGJhY2tPdXQ6IC8qQF9fUFVSRV9fKi8gY3ViaWNCZXppZXJBc1N0cmluZyhbMC4zMywgMS41MywgMC42OSwgMC45OV0pLFxufTtcblxuZXhwb3J0IHsgc3VwcG9ydGVkV2FhcGlFYXNpbmcgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/easing/supported.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/start-waapi-animation.mjs":
/*!***********************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/animation/waapi/start-waapi-animation.mjs ***!
  \***********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   startWaapiAnimation: () => (/* binding */ startWaapiAnimation)\n/* harmony export */ });\n/* harmony import */ var _stats_animation_count_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../stats/animation-count.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/stats/animation-count.mjs\");\n/* harmony import */ var _stats_buffer_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../stats/buffer.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/stats/buffer.mjs\");\n/* harmony import */ var _easing_map_easing_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./easing/map-easing.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/easing/map-easing.mjs\");\n\n\n\n\nfunction startWaapiAnimation(element, valueName, keyframes, { delay = 0, duration = 300, repeat = 0, repeatType = \"loop\", ease = \"easeInOut\", times, } = {}, pseudoElement = undefined) {\n    const keyframeOptions = {\n        [valueName]: keyframes,\n    };\n    if (times)\n        keyframeOptions.offset = times;\n    const easing = (0,_easing_map_easing_mjs__WEBPACK_IMPORTED_MODULE_0__.mapEasingToNativeEasing)(ease, duration);\n    /**\n     * If this is an easing array, apply to keyframes, not animation as a whole\n     */\n    if (Array.isArray(easing))\n        keyframeOptions.easing = easing;\n    if (_stats_buffer_mjs__WEBPACK_IMPORTED_MODULE_1__.statsBuffer.value) {\n        _stats_animation_count_mjs__WEBPACK_IMPORTED_MODULE_2__.activeAnimations.waapi++;\n    }\n    const animation = element.animate(keyframeOptions, {\n        delay,\n        duration,\n        easing: !Array.isArray(easing) ? easing : \"linear\",\n        fill: \"both\",\n        iterations: repeat + 1,\n        direction: repeatType === \"reverse\" ? \"alternate\" : \"normal\",\n        pseudoElement,\n    });\n    if (_stats_buffer_mjs__WEBPACK_IMPORTED_MODULE_1__.statsBuffer.value) {\n        animation.finished.finally(() => {\n            _stats_animation_count_mjs__WEBPACK_IMPORTED_MODULE_2__.activeAnimations.waapi--;\n        });\n    }\n    return animation;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2FuaW1hdGlvbi93YWFwaS9zdGFydC13YWFwaS1hbmltYXRpb24ubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBbUU7QUFDZDtBQUNhOztBQUVsRSw4REFBOEQseUZBQXlGLElBQUk7QUFDM0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQiwrRUFBdUI7QUFDMUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsMERBQVc7QUFDbkIsUUFBUSx3RUFBZ0I7QUFDeEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMLFFBQVEsMERBQVc7QUFDbkI7QUFDQSxZQUFZLHdFQUFnQjtBQUM1QixTQUFTO0FBQ1Q7QUFDQTtBQUNBOztBQUUrQiIsInNvdXJjZXMiOlsiRDpcXHNvZnRcXFNlYVByb2plY3RcXGdpdGh1Yl9vcGVuX3Byb2plY3Rfc3VjY2Vzc1xcc3VuYVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcbW90aW9uLWRvbVxcZGlzdFxcZXNcXGFuaW1hdGlvblxcd2FhcGlcXHN0YXJ0LXdhYXBpLWFuaW1hdGlvbi5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgYWN0aXZlQW5pbWF0aW9ucyB9IGZyb20gJy4uLy4uL3N0YXRzL2FuaW1hdGlvbi1jb3VudC5tanMnO1xuaW1wb3J0IHsgc3RhdHNCdWZmZXIgfSBmcm9tICcuLi8uLi9zdGF0cy9idWZmZXIubWpzJztcbmltcG9ydCB7IG1hcEVhc2luZ1RvTmF0aXZlRWFzaW5nIH0gZnJvbSAnLi9lYXNpbmcvbWFwLWVhc2luZy5tanMnO1xuXG5mdW5jdGlvbiBzdGFydFdhYXBpQW5pbWF0aW9uKGVsZW1lbnQsIHZhbHVlTmFtZSwga2V5ZnJhbWVzLCB7IGRlbGF5ID0gMCwgZHVyYXRpb24gPSAzMDAsIHJlcGVhdCA9IDAsIHJlcGVhdFR5cGUgPSBcImxvb3BcIiwgZWFzZSA9IFwiZWFzZUluT3V0XCIsIHRpbWVzLCB9ID0ge30sIHBzZXVkb0VsZW1lbnQgPSB1bmRlZmluZWQpIHtcbiAgICBjb25zdCBrZXlmcmFtZU9wdGlvbnMgPSB7XG4gICAgICAgIFt2YWx1ZU5hbWVdOiBrZXlmcmFtZXMsXG4gICAgfTtcbiAgICBpZiAodGltZXMpXG4gICAgICAgIGtleWZyYW1lT3B0aW9ucy5vZmZzZXQgPSB0aW1lcztcbiAgICBjb25zdCBlYXNpbmcgPSBtYXBFYXNpbmdUb05hdGl2ZUVhc2luZyhlYXNlLCBkdXJhdGlvbik7XG4gICAgLyoqXG4gICAgICogSWYgdGhpcyBpcyBhbiBlYXNpbmcgYXJyYXksIGFwcGx5IHRvIGtleWZyYW1lcywgbm90IGFuaW1hdGlvbiBhcyBhIHdob2xlXG4gICAgICovXG4gICAgaWYgKEFycmF5LmlzQXJyYXkoZWFzaW5nKSlcbiAgICAgICAga2V5ZnJhbWVPcHRpb25zLmVhc2luZyA9IGVhc2luZztcbiAgICBpZiAoc3RhdHNCdWZmZXIudmFsdWUpIHtcbiAgICAgICAgYWN0aXZlQW5pbWF0aW9ucy53YWFwaSsrO1xuICAgIH1cbiAgICBjb25zdCBhbmltYXRpb24gPSBlbGVtZW50LmFuaW1hdGUoa2V5ZnJhbWVPcHRpb25zLCB7XG4gICAgICAgIGRlbGF5LFxuICAgICAgICBkdXJhdGlvbixcbiAgICAgICAgZWFzaW5nOiAhQXJyYXkuaXNBcnJheShlYXNpbmcpID8gZWFzaW5nIDogXCJsaW5lYXJcIixcbiAgICAgICAgZmlsbDogXCJib3RoXCIsXG4gICAgICAgIGl0ZXJhdGlvbnM6IHJlcGVhdCArIDEsXG4gICAgICAgIGRpcmVjdGlvbjogcmVwZWF0VHlwZSA9PT0gXCJyZXZlcnNlXCIgPyBcImFsdGVybmF0ZVwiIDogXCJub3JtYWxcIixcbiAgICAgICAgcHNldWRvRWxlbWVudCxcbiAgICB9KTtcbiAgICBpZiAoc3RhdHNCdWZmZXIudmFsdWUpIHtcbiAgICAgICAgYW5pbWF0aW9uLmZpbmlzaGVkLmZpbmFsbHkoKCkgPT4ge1xuICAgICAgICAgICAgYWN0aXZlQW5pbWF0aW9ucy53YWFwaS0tO1xuICAgICAgICB9KTtcbiAgICB9XG4gICAgcmV0dXJuIGFuaW1hdGlvbjtcbn1cblxuZXhwb3J0IHsgc3RhcnRXYWFwaUFuaW1hdGlvbiB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/start-waapi-animation.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/utils/attach-timeline.mjs":
/*!***********************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/animation/waapi/utils/attach-timeline.mjs ***!
  \***********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   attachTimeline: () => (/* binding */ attachTimeline)\n/* harmony export */ });\nfunction attachTimeline(animation, timeline) {\n    animation.timeline = timeline;\n    animation.onfinish = null;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2FuaW1hdGlvbi93YWFwaS91dGlscy9hdHRhY2gtdGltZWxpbmUubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTs7QUFFMEIiLCJzb3VyY2VzIjpbIkQ6XFxzb2Z0XFxTZWFQcm9qZWN0XFxnaXRodWJfb3Blbl9wcm9qZWN0X3N1Y2Nlc3NcXHN1bmFcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXG1vdGlvbi1kb21cXGRpc3RcXGVzXFxhbmltYXRpb25cXHdhYXBpXFx1dGlsc1xcYXR0YWNoLXRpbWVsaW5lLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJmdW5jdGlvbiBhdHRhY2hUaW1lbGluZShhbmltYXRpb24sIHRpbWVsaW5lKSB7XG4gICAgYW5pbWF0aW9uLnRpbWVsaW5lID0gdGltZWxpbmU7XG4gICAgYW5pbWF0aW9uLm9uZmluaXNoID0gbnVsbDtcbn1cblxuZXhwb3J0IHsgYXR0YWNoVGltZWxpbmUgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/utils/attach-timeline.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/utils/linear.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/animation/waapi/utils/linear.mjs ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateLinearEasing: () => (/* binding */ generateLinearEasing)\n/* harmony export */ });\nconst generateLinearEasing = (easing, duration, // as milliseconds\nresolution = 10 // as milliseconds\n) => {\n    let points = \"\";\n    const numPoints = Math.max(Math.round(duration / resolution), 2);\n    for (let i = 0; i < numPoints; i++) {\n        points += easing(i / (numPoints - 1)) + \", \";\n    }\n    return `linear(${points.substring(0, points.length - 2)})`;\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2FuaW1hdGlvbi93YWFwaS91dGlscy9saW5lYXIubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLGVBQWU7QUFDbkM7QUFDQTtBQUNBLHFCQUFxQix1Q0FBdUM7QUFDNUQ7O0FBRWdDIiwic291cmNlcyI6WyJEOlxcc29mdFxcU2VhUHJvamVjdFxcZ2l0aHViX29wZW5fcHJvamVjdF9zdWNjZXNzXFxzdW5hXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxtb3Rpb24tZG9tXFxkaXN0XFxlc1xcYW5pbWF0aW9uXFx3YWFwaVxcdXRpbHNcXGxpbmVhci5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgZ2VuZXJhdGVMaW5lYXJFYXNpbmcgPSAoZWFzaW5nLCBkdXJhdGlvbiwgLy8gYXMgbWlsbGlzZWNvbmRzXG5yZXNvbHV0aW9uID0gMTAgLy8gYXMgbWlsbGlzZWNvbmRzXG4pID0+IHtcbiAgICBsZXQgcG9pbnRzID0gXCJcIjtcbiAgICBjb25zdCBudW1Qb2ludHMgPSBNYXRoLm1heChNYXRoLnJvdW5kKGR1cmF0aW9uIC8gcmVzb2x1dGlvbiksIDIpO1xuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgbnVtUG9pbnRzOyBpKyspIHtcbiAgICAgICAgcG9pbnRzICs9IGVhc2luZyhpIC8gKG51bVBvaW50cyAtIDEpKSArIFwiLCBcIjtcbiAgICB9XG4gICAgcmV0dXJuIGBsaW5lYXIoJHtwb2ludHMuc3Vic3RyaW5nKDAsIHBvaW50cy5sZW5ndGggLSAyKX0pYDtcbn07XG5cbmV4cG9ydCB7IGdlbmVyYXRlTGluZWFyRWFzaW5nIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/utils/linear.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/frameloop/batcher.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/frameloop/batcher.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createRenderBatcher: () => (/* binding */ createRenderBatcher)\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! motion-utils */ \"(ssr)/./node_modules/motion-utils/dist/es/global-config.mjs\");\n/* harmony import */ var _order_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./order.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/frameloop/order.mjs\");\n/* harmony import */ var _render_step_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./render-step.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/frameloop/render-step.mjs\");\n\n\n\n\nconst maxElapsed = 40;\nfunction createRenderBatcher(scheduleNextBatch, allowKeepAlive) {\n    let runNextFrame = false;\n    let useDefaultElapsed = true;\n    const state = {\n        delta: 0.0,\n        timestamp: 0.0,\n        isProcessing: false,\n    };\n    const flagRunNextFrame = () => (runNextFrame = true);\n    const steps = _order_mjs__WEBPACK_IMPORTED_MODULE_0__.stepsOrder.reduce((acc, key) => {\n        acc[key] = (0,_render_step_mjs__WEBPACK_IMPORTED_MODULE_1__.createRenderStep)(flagRunNextFrame, allowKeepAlive ? key : undefined);\n        return acc;\n    }, {});\n    const { read, resolveKeyframes, update, preRender, render, postRender } = steps;\n    const processBatch = () => {\n        const timestamp = motion_utils__WEBPACK_IMPORTED_MODULE_2__.MotionGlobalConfig.useManualTiming\n            ? state.timestamp\n            : performance.now();\n        runNextFrame = false;\n        if (!motion_utils__WEBPACK_IMPORTED_MODULE_2__.MotionGlobalConfig.useManualTiming) {\n            state.delta = useDefaultElapsed\n                ? 1000 / 60\n                : Math.max(Math.min(timestamp - state.timestamp, maxElapsed), 1);\n        }\n        state.timestamp = timestamp;\n        state.isProcessing = true;\n        // Unrolled render loop for better per-frame performance\n        read.process(state);\n        resolveKeyframes.process(state);\n        update.process(state);\n        preRender.process(state);\n        render.process(state);\n        postRender.process(state);\n        state.isProcessing = false;\n        if (runNextFrame && allowKeepAlive) {\n            useDefaultElapsed = false;\n            scheduleNextBatch(processBatch);\n        }\n    };\n    const wake = () => {\n        runNextFrame = true;\n        useDefaultElapsed = true;\n        if (!state.isProcessing) {\n            scheduleNextBatch(processBatch);\n        }\n    };\n    const schedule = _order_mjs__WEBPACK_IMPORTED_MODULE_0__.stepsOrder.reduce((acc, key) => {\n        const step = steps[key];\n        acc[key] = (process, keepAlive = false, immediate = false) => {\n            if (!runNextFrame)\n                wake();\n            return step.schedule(process, keepAlive, immediate);\n        };\n        return acc;\n    }, {});\n    const cancel = (process) => {\n        for (let i = 0; i < _order_mjs__WEBPACK_IMPORTED_MODULE_0__.stepsOrder.length; i++) {\n            steps[_order_mjs__WEBPACK_IMPORTED_MODULE_0__.stepsOrder[i]].cancel(process);\n        }\n    };\n    return { schedule, cancel, state, steps };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/frameloop/batcher.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/frameloop/frame.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/frameloop/frame.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cancelFrame: () => (/* binding */ cancelFrame),\n/* harmony export */   frame: () => (/* binding */ frame),\n/* harmony export */   frameData: () => (/* binding */ frameData),\n/* harmony export */   frameSteps: () => (/* binding */ frameSteps)\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! motion-utils */ \"(ssr)/./node_modules/motion-utils/dist/es/noop.mjs\");\n/* harmony import */ var _batcher_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./batcher.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/frameloop/batcher.mjs\");\n\n\n\nconst { schedule: frame, cancel: cancelFrame, state: frameData, steps: frameSteps, } = /* @__PURE__ */ (0,_batcher_mjs__WEBPACK_IMPORTED_MODULE_0__.createRenderBatcher)(typeof requestAnimationFrame !== \"undefined\" ? requestAnimationFrame : motion_utils__WEBPACK_IMPORTED_MODULE_1__.noop, true);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2ZyYW1lbG9vcC9mcmFtZS5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQW9DO0FBQ2dCOztBQUVwRCxRQUFRLDZFQUE2RSxrQkFBa0IsaUVBQW1CLHdFQUF3RSw4Q0FBSTs7QUFFakoiLCJzb3VyY2VzIjpbIkQ6XFxzb2Z0XFxTZWFQcm9qZWN0XFxnaXRodWJfb3Blbl9wcm9qZWN0X3N1Y2Nlc3NcXHN1bmFcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXG1vdGlvbi1kb21cXGRpc3RcXGVzXFxmcmFtZWxvb3BcXGZyYW1lLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBub29wIH0gZnJvbSAnbW90aW9uLXV0aWxzJztcbmltcG9ydCB7IGNyZWF0ZVJlbmRlckJhdGNoZXIgfSBmcm9tICcuL2JhdGNoZXIubWpzJztcblxuY29uc3QgeyBzY2hlZHVsZTogZnJhbWUsIGNhbmNlbDogY2FuY2VsRnJhbWUsIHN0YXRlOiBmcmFtZURhdGEsIHN0ZXBzOiBmcmFtZVN0ZXBzLCB9ID0gLyogQF9fUFVSRV9fICovIGNyZWF0ZVJlbmRlckJhdGNoZXIodHlwZW9mIHJlcXVlc3RBbmltYXRpb25GcmFtZSAhPT0gXCJ1bmRlZmluZWRcIiA/IHJlcXVlc3RBbmltYXRpb25GcmFtZSA6IG5vb3AsIHRydWUpO1xuXG5leHBvcnQgeyBjYW5jZWxGcmFtZSwgZnJhbWUsIGZyYW1lRGF0YSwgZnJhbWVTdGVwcyB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/frameloop/frame.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/frameloop/microtask.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/frameloop/microtask.mjs ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cancelMicrotask: () => (/* binding */ cancelMicrotask),\n/* harmony export */   microtask: () => (/* binding */ microtask)\n/* harmony export */ });\n/* harmony import */ var _batcher_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./batcher.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/frameloop/batcher.mjs\");\n\n\nconst { schedule: microtask, cancel: cancelMicrotask } = \n/* @__PURE__ */ (0,_batcher_mjs__WEBPACK_IMPORTED_MODULE_0__.createRenderBatcher)(queueMicrotask, false);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2ZyYW1lbG9vcC9taWNyb3Rhc2subWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFvRDs7QUFFcEQsUUFBUSwrQ0FBK0M7QUFDdkQsZ0JBQWdCLGlFQUFtQjs7QUFFRyIsInNvdXJjZXMiOlsiRDpcXHNvZnRcXFNlYVByb2plY3RcXGdpdGh1Yl9vcGVuX3Byb2plY3Rfc3VjY2Vzc1xcc3VuYVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcbW90aW9uLWRvbVxcZGlzdFxcZXNcXGZyYW1lbG9vcFxcbWljcm90YXNrLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVSZW5kZXJCYXRjaGVyIH0gZnJvbSAnLi9iYXRjaGVyLm1qcyc7XG5cbmNvbnN0IHsgc2NoZWR1bGU6IG1pY3JvdGFzaywgY2FuY2VsOiBjYW5jZWxNaWNyb3Rhc2sgfSA9IFxuLyogQF9fUFVSRV9fICovIGNyZWF0ZVJlbmRlckJhdGNoZXIocXVldWVNaWNyb3Rhc2ssIGZhbHNlKTtcblxuZXhwb3J0IHsgY2FuY2VsTWljcm90YXNrLCBtaWNyb3Rhc2sgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/frameloop/microtask.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/frameloop/order.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/frameloop/order.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   stepsOrder: () => (/* binding */ stepsOrder)\n/* harmony export */ });\nconst stepsOrder = [\n    \"read\", // Read\n    \"resolveKeyframes\", // Write/Read/Write/Read\n    \"update\", // Compute\n    \"preRender\", // Compute\n    \"render\", // Write\n    \"postRender\", // Compute\n];\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2ZyYW1lbG9vcC9vcmRlci5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRXNCIiwic291cmNlcyI6WyJEOlxcc29mdFxcU2VhUHJvamVjdFxcZ2l0aHViX29wZW5fcHJvamVjdF9zdWNjZXNzXFxzdW5hXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxtb3Rpb24tZG9tXFxkaXN0XFxlc1xcZnJhbWVsb29wXFxvcmRlci5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3Qgc3RlcHNPcmRlciA9IFtcbiAgICBcInJlYWRcIiwgLy8gUmVhZFxuICAgIFwicmVzb2x2ZUtleWZyYW1lc1wiLCAvLyBXcml0ZS9SZWFkL1dyaXRlL1JlYWRcbiAgICBcInVwZGF0ZVwiLCAvLyBDb21wdXRlXG4gICAgXCJwcmVSZW5kZXJcIiwgLy8gQ29tcHV0ZVxuICAgIFwicmVuZGVyXCIsIC8vIFdyaXRlXG4gICAgXCJwb3N0UmVuZGVyXCIsIC8vIENvbXB1dGVcbl07XG5cbmV4cG9ydCB7IHN0ZXBzT3JkZXIgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/frameloop/order.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/frameloop/render-step.mjs":
/*!*******************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/frameloop/render-step.mjs ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createRenderStep: () => (/* binding */ createRenderStep)\n/* harmony export */ });\n/* harmony import */ var _stats_buffer_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../stats/buffer.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/stats/buffer.mjs\");\n\n\nfunction createRenderStep(runNextFrame, stepName) {\n    /**\n     * We create and reuse two queues, one to queue jobs for the current frame\n     * and one for the next. We reuse to avoid triggering GC after x frames.\n     */\n    let thisFrame = new Set();\n    let nextFrame = new Set();\n    /**\n     * Track whether we're currently processing jobs in this step. This way\n     * we can decide whether to schedule new jobs for this frame or next.\n     */\n    let isProcessing = false;\n    let flushNextFrame = false;\n    /**\n     * A set of processes which were marked keepAlive when scheduled.\n     */\n    const toKeepAlive = new WeakSet();\n    let latestFrameData = {\n        delta: 0.0,\n        timestamp: 0.0,\n        isProcessing: false,\n    };\n    let numCalls = 0;\n    function triggerCallback(callback) {\n        if (toKeepAlive.has(callback)) {\n            step.schedule(callback);\n            runNextFrame();\n        }\n        numCalls++;\n        callback(latestFrameData);\n    }\n    const step = {\n        /**\n         * Schedule a process to run on the next frame.\n         */\n        schedule: (callback, keepAlive = false, immediate = false) => {\n            const addToCurrentFrame = immediate && isProcessing;\n            const queue = addToCurrentFrame ? thisFrame : nextFrame;\n            if (keepAlive)\n                toKeepAlive.add(callback);\n            if (!queue.has(callback))\n                queue.add(callback);\n            return callback;\n        },\n        /**\n         * Cancel the provided callback from running on the next frame.\n         */\n        cancel: (callback) => {\n            nextFrame.delete(callback);\n            toKeepAlive.delete(callback);\n        },\n        /**\n         * Execute all schedule callbacks.\n         */\n        process: (frameData) => {\n            latestFrameData = frameData;\n            /**\n             * If we're already processing we've probably been triggered by a flushSync\n             * inside an existing process. Instead of executing, mark flushNextFrame\n             * as true and ensure we flush the following frame at the end of this one.\n             */\n            if (isProcessing) {\n                flushNextFrame = true;\n                return;\n            }\n            isProcessing = true;\n            [thisFrame, nextFrame] = [nextFrame, thisFrame];\n            // Execute this frame\n            thisFrame.forEach(triggerCallback);\n            /**\n             * If we're recording stats then\n             */\n            if (stepName && _stats_buffer_mjs__WEBPACK_IMPORTED_MODULE_0__.statsBuffer.value) {\n                _stats_buffer_mjs__WEBPACK_IMPORTED_MODULE_0__.statsBuffer.value.frameloop[stepName].push(numCalls);\n            }\n            numCalls = 0;\n            // Clear the frame so no callbacks remain. This is to avoid\n            // memory leaks should this render step not run for a while.\n            thisFrame.clear();\n            isProcessing = false;\n            if (flushNextFrame) {\n                flushNextFrame = false;\n                step.process(frameData);\n            }\n        },\n    };\n    return step;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/frameloop/render-step.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/frameloop/sync-time.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/frameloop/sync-time.mjs ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   time: () => (/* binding */ time)\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! motion-utils */ \"(ssr)/./node_modules/motion-utils/dist/es/global-config.mjs\");\n/* harmony import */ var _frame_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./frame.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/frameloop/frame.mjs\");\n\n\n\nlet now;\nfunction clearTime() {\n    now = undefined;\n}\n/**\n * An eventloop-synchronous alternative to performance.now().\n *\n * Ensures that time measurements remain consistent within a synchronous context.\n * Usually calling performance.now() twice within the same synchronous context\n * will return different values which isn't useful for animations when we're usually\n * trying to sync animations to the same frame.\n */\nconst time = {\n    now: () => {\n        if (now === undefined) {\n            time.set(_frame_mjs__WEBPACK_IMPORTED_MODULE_0__.frameData.isProcessing || motion_utils__WEBPACK_IMPORTED_MODULE_1__.MotionGlobalConfig.useManualTiming\n                ? _frame_mjs__WEBPACK_IMPORTED_MODULE_0__.frameData.timestamp\n                : performance.now());\n        }\n        return now;\n    },\n    set: (newTime) => {\n        now = newTime;\n        queueMicrotask(clearTime);\n    },\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2ZyYW1lbG9vcC9zeW5jLXRpbWUubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFrRDtBQUNWOztBQUV4QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUIsaURBQVMsaUJBQWlCLDREQUFrQjtBQUNqRSxrQkFBa0IsaURBQVM7QUFDM0I7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDs7QUFFZ0IiLCJzb3VyY2VzIjpbIkQ6XFxzb2Z0XFxTZWFQcm9qZWN0XFxnaXRodWJfb3Blbl9wcm9qZWN0X3N1Y2Nlc3NcXHN1bmFcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXG1vdGlvbi1kb21cXGRpc3RcXGVzXFxmcmFtZWxvb3BcXHN5bmMtdGltZS5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTW90aW9uR2xvYmFsQ29uZmlnIH0gZnJvbSAnbW90aW9uLXV0aWxzJztcbmltcG9ydCB7IGZyYW1lRGF0YSB9IGZyb20gJy4vZnJhbWUubWpzJztcblxubGV0IG5vdztcbmZ1bmN0aW9uIGNsZWFyVGltZSgpIHtcbiAgICBub3cgPSB1bmRlZmluZWQ7XG59XG4vKipcbiAqIEFuIGV2ZW50bG9vcC1zeW5jaHJvbm91cyBhbHRlcm5hdGl2ZSB0byBwZXJmb3JtYW5jZS5ub3coKS5cbiAqXG4gKiBFbnN1cmVzIHRoYXQgdGltZSBtZWFzdXJlbWVudHMgcmVtYWluIGNvbnNpc3RlbnQgd2l0aGluIGEgc3luY2hyb25vdXMgY29udGV4dC5cbiAqIFVzdWFsbHkgY2FsbGluZyBwZXJmb3JtYW5jZS5ub3coKSB0d2ljZSB3aXRoaW4gdGhlIHNhbWUgc3luY2hyb25vdXMgY29udGV4dFxuICogd2lsbCByZXR1cm4gZGlmZmVyZW50IHZhbHVlcyB3aGljaCBpc24ndCB1c2VmdWwgZm9yIGFuaW1hdGlvbnMgd2hlbiB3ZSdyZSB1c3VhbGx5XG4gKiB0cnlpbmcgdG8gc3luYyBhbmltYXRpb25zIHRvIHRoZSBzYW1lIGZyYW1lLlxuICovXG5jb25zdCB0aW1lID0ge1xuICAgIG5vdzogKCkgPT4ge1xuICAgICAgICBpZiAobm93ID09PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgIHRpbWUuc2V0KGZyYW1lRGF0YS5pc1Byb2Nlc3NpbmcgfHwgTW90aW9uR2xvYmFsQ29uZmlnLnVzZU1hbnVhbFRpbWluZ1xuICAgICAgICAgICAgICAgID8gZnJhbWVEYXRhLnRpbWVzdGFtcFxuICAgICAgICAgICAgICAgIDogcGVyZm9ybWFuY2Uubm93KCkpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBub3c7XG4gICAgfSxcbiAgICBzZXQ6IChuZXdUaW1lKSA9PiB7XG4gICAgICAgIG5vdyA9IG5ld1RpbWU7XG4gICAgICAgIHF1ZXVlTWljcm90YXNrKGNsZWFyVGltZSk7XG4gICAgfSxcbn07XG5cbmV4cG9ydCB7IHRpbWUgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/frameloop/sync-time.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/gestures/drag/state/is-active.mjs":
/*!***************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/gestures/drag/state/is-active.mjs ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isDragActive: () => (/* binding */ isDragActive),\n/* harmony export */   isDragging: () => (/* binding */ isDragging)\n/* harmony export */ });\nconst isDragging = {\n    x: false,\n    y: false,\n};\nfunction isDragActive() {\n    return isDragging.x || isDragging.y;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2dlc3R1cmVzL2RyYWcvc3RhdGUvaXMtYWN0aXZlLm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVvQyIsInNvdXJjZXMiOlsiRDpcXHNvZnRcXFNlYVByb2plY3RcXGdpdGh1Yl9vcGVuX3Byb2plY3Rfc3VjY2Vzc1xcc3VuYVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcbW90aW9uLWRvbVxcZGlzdFxcZXNcXGdlc3R1cmVzXFxkcmFnXFxzdGF0ZVxcaXMtYWN0aXZlLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBpc0RyYWdnaW5nID0ge1xuICAgIHg6IGZhbHNlLFxuICAgIHk6IGZhbHNlLFxufTtcbmZ1bmN0aW9uIGlzRHJhZ0FjdGl2ZSgpIHtcbiAgICByZXR1cm4gaXNEcmFnZ2luZy54IHx8IGlzRHJhZ2dpbmcueTtcbn1cblxuZXhwb3J0IHsgaXNEcmFnQWN0aXZlLCBpc0RyYWdnaW5nIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/gestures/drag/state/is-active.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/gestures/drag/state/set-active.mjs":
/*!****************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/gestures/drag/state/set-active.mjs ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   setDragLock: () => (/* binding */ setDragLock)\n/* harmony export */ });\n/* harmony import */ var _is_active_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./is-active.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/gestures/drag/state/is-active.mjs\");\n\n\nfunction setDragLock(axis) {\n    if (axis === \"x\" || axis === \"y\") {\n        if (_is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging[axis]) {\n            return null;\n        }\n        else {\n            _is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging[axis] = true;\n            return () => {\n                _is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging[axis] = false;\n            };\n        }\n    }\n    else {\n        if (_is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging.x || _is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging.y) {\n            return null;\n        }\n        else {\n            _is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging.x = _is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging.y = true;\n            return () => {\n                _is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging.x = _is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging.y = false;\n            };\n        }\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2dlc3R1cmVzL2RyYWcvc3RhdGUvc2V0LWFjdGl2ZS5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBNkM7O0FBRTdDO0FBQ0E7QUFDQSxZQUFZLHNEQUFVO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBLFlBQVksc0RBQVU7QUFDdEI7QUFDQSxnQkFBZ0Isc0RBQVU7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLHNEQUFVLE1BQU0sc0RBQVU7QUFDdEM7QUFDQTtBQUNBO0FBQ0EsWUFBWSxzREFBVSxLQUFLLHNEQUFVO0FBQ3JDO0FBQ0EsZ0JBQWdCLHNEQUFVLEtBQUssc0RBQVU7QUFDekM7QUFDQTtBQUNBO0FBQ0E7O0FBRXVCIiwic291cmNlcyI6WyJEOlxcc29mdFxcU2VhUHJvamVjdFxcZ2l0aHViX29wZW5fcHJvamVjdF9zdWNjZXNzXFxzdW5hXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxtb3Rpb24tZG9tXFxkaXN0XFxlc1xcZ2VzdHVyZXNcXGRyYWdcXHN0YXRlXFxzZXQtYWN0aXZlLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBpc0RyYWdnaW5nIH0gZnJvbSAnLi9pcy1hY3RpdmUubWpzJztcblxuZnVuY3Rpb24gc2V0RHJhZ0xvY2soYXhpcykge1xuICAgIGlmIChheGlzID09PSBcInhcIiB8fCBheGlzID09PSBcInlcIikge1xuICAgICAgICBpZiAoaXNEcmFnZ2luZ1theGlzXSkge1xuICAgICAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICBpc0RyYWdnaW5nW2F4aXNdID0gdHJ1ZTtcbiAgICAgICAgICAgIHJldHVybiAoKSA9PiB7XG4gICAgICAgICAgICAgICAgaXNEcmFnZ2luZ1theGlzXSA9IGZhbHNlO1xuICAgICAgICAgICAgfTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBlbHNlIHtcbiAgICAgICAgaWYgKGlzRHJhZ2dpbmcueCB8fCBpc0RyYWdnaW5nLnkpIHtcbiAgICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgaXNEcmFnZ2luZy54ID0gaXNEcmFnZ2luZy55ID0gdHJ1ZTtcbiAgICAgICAgICAgIHJldHVybiAoKSA9PiB7XG4gICAgICAgICAgICAgICAgaXNEcmFnZ2luZy54ID0gaXNEcmFnZ2luZy55ID0gZmFsc2U7XG4gICAgICAgICAgICB9O1xuICAgICAgICB9XG4gICAgfVxufVxuXG5leHBvcnQgeyBzZXREcmFnTG9jayB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/gestures/drag/state/set-active.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/gestures/hover.mjs":
/*!************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/gestures/hover.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hover: () => (/* binding */ hover)\n/* harmony export */ });\n/* harmony import */ var _drag_state_is_active_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./drag/state/is-active.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/gestures/drag/state/is-active.mjs\");\n/* harmony import */ var _utils_setup_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/setup.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/gestures/utils/setup.mjs\");\n\n\n\nfunction isValidHover(event) {\n    return !(event.pointerType === \"touch\" || (0,_drag_state_is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragActive)());\n}\n/**\n * Create a hover gesture. hover() is different to .addEventListener(\"pointerenter\")\n * in that it has an easier syntax, filters out polyfilled touch events, interoperates\n * with drag gestures, and automatically removes the \"pointerennd\" event listener when the hover ends.\n *\n * @public\n */\nfunction hover(elementOrSelector, onHoverStart, options = {}) {\n    const [elements, eventOptions, cancel] = (0,_utils_setup_mjs__WEBPACK_IMPORTED_MODULE_1__.setupGesture)(elementOrSelector, options);\n    const onPointerEnter = (enterEvent) => {\n        if (!isValidHover(enterEvent))\n            return;\n        const { target } = enterEvent;\n        const onHoverEnd = onHoverStart(target, enterEvent);\n        if (typeof onHoverEnd !== \"function\" || !target)\n            return;\n        const onPointerLeave = (leaveEvent) => {\n            if (!isValidHover(leaveEvent))\n                return;\n            onHoverEnd(leaveEvent);\n            target.removeEventListener(\"pointerleave\", onPointerLeave);\n        };\n        target.addEventListener(\"pointerleave\", onPointerLeave, eventOptions);\n    };\n    elements.forEach((element) => {\n        element.addEventListener(\"pointerenter\", onPointerEnter, eventOptions);\n    });\n    return cancel;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/gestures/hover.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/gestures/press/index.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/gestures/press/index.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   press: () => (/* binding */ press)\n/* harmony export */ });\n/* harmony import */ var _drag_state_is_active_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../drag/state/is-active.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/gestures/drag/state/is-active.mjs\");\n/* harmony import */ var _utils_is_node_or_child_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/is-node-or-child.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/gestures/utils/is-node-or-child.mjs\");\n/* harmony import */ var _utils_is_primary_pointer_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/is-primary-pointer.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/gestures/utils/is-primary-pointer.mjs\");\n/* harmony import */ var _utils_setup_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/setup.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/gestures/utils/setup.mjs\");\n/* harmony import */ var _utils_is_keyboard_accessible_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./utils/is-keyboard-accessible.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/gestures/press/utils/is-keyboard-accessible.mjs\");\n/* harmony import */ var _utils_keyboard_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./utils/keyboard.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/gestures/press/utils/keyboard.mjs\");\n/* harmony import */ var _utils_state_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils/state.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/gestures/press/utils/state.mjs\");\n\n\n\n\n\n\n\n\n/**\n * Filter out events that are not primary pointer events, or are triggering\n * while a Motion gesture is active.\n */\nfunction isValidPressEvent(event) {\n    return (0,_utils_is_primary_pointer_mjs__WEBPACK_IMPORTED_MODULE_0__.isPrimaryPointer)(event) && !(0,_drag_state_is_active_mjs__WEBPACK_IMPORTED_MODULE_1__.isDragActive)();\n}\n/**\n * Create a press gesture.\n *\n * Press is different to `\"pointerdown\"`, `\"pointerup\"` in that it\n * automatically filters out secondary pointer events like right\n * click and multitouch.\n *\n * It also adds accessibility support for keyboards, where\n * an element with a press gesture will receive focus and\n *  trigger on Enter `\"keydown\"` and `\"keyup\"` events.\n *\n * This is different to a browser's `\"click\"` event, which does\n * respond to keyboards but only for the `\"click\"` itself, rather\n * than the press start and end/cancel. The element also needs\n * to be focusable for this to work, whereas a press gesture will\n * make an element focusable by default.\n *\n * @public\n */\nfunction press(targetOrSelector, onPressStart, options = {}) {\n    const [targets, eventOptions, cancelEvents] = (0,_utils_setup_mjs__WEBPACK_IMPORTED_MODULE_2__.setupGesture)(targetOrSelector, options);\n    const startPress = (startEvent) => {\n        const target = startEvent.currentTarget;\n        if (!isValidPressEvent(startEvent) || _utils_state_mjs__WEBPACK_IMPORTED_MODULE_3__.isPressing.has(target))\n            return;\n        _utils_state_mjs__WEBPACK_IMPORTED_MODULE_3__.isPressing.add(target);\n        const onPressEnd = onPressStart(target, startEvent);\n        const onPointerEnd = (endEvent, success) => {\n            window.removeEventListener(\"pointerup\", onPointerUp);\n            window.removeEventListener(\"pointercancel\", onPointerCancel);\n            if (!isValidPressEvent(endEvent) || !_utils_state_mjs__WEBPACK_IMPORTED_MODULE_3__.isPressing.has(target)) {\n                return;\n            }\n            _utils_state_mjs__WEBPACK_IMPORTED_MODULE_3__.isPressing.delete(target);\n            if (typeof onPressEnd === \"function\") {\n                onPressEnd(endEvent, { success });\n            }\n        };\n        const onPointerUp = (upEvent) => {\n            onPointerEnd(upEvent, target === window ||\n                target === document ||\n                options.useGlobalTarget ||\n                (0,_utils_is_node_or_child_mjs__WEBPACK_IMPORTED_MODULE_4__.isNodeOrChild)(target, upEvent.target));\n        };\n        const onPointerCancel = (cancelEvent) => {\n            onPointerEnd(cancelEvent, false);\n        };\n        window.addEventListener(\"pointerup\", onPointerUp, eventOptions);\n        window.addEventListener(\"pointercancel\", onPointerCancel, eventOptions);\n    };\n    targets.forEach((target) => {\n        const pointerDownTarget = options.useGlobalTarget ? window : target;\n        pointerDownTarget.addEventListener(\"pointerdown\", startPress, eventOptions);\n        if (target instanceof HTMLElement) {\n            target.addEventListener(\"focus\", (event) => (0,_utils_keyboard_mjs__WEBPACK_IMPORTED_MODULE_5__.enableKeyboardPress)(event, eventOptions));\n            if (!(0,_utils_is_keyboard_accessible_mjs__WEBPACK_IMPORTED_MODULE_6__.isElementKeyboardAccessible)(target) &&\n                !target.hasAttribute(\"tabindex\")) {\n                target.tabIndex = 0;\n            }\n        }\n    });\n    return cancelEvents;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/gestures/press/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/gestures/press/utils/is-keyboard-accessible.mjs":
/*!*****************************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/gestures/press/utils/is-keyboard-accessible.mjs ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isElementKeyboardAccessible: () => (/* binding */ isElementKeyboardAccessible)\n/* harmony export */ });\nconst focusableElements = new Set([\n    \"BUTTON\",\n    \"INPUT\",\n    \"SELECT\",\n    \"TEXTAREA\",\n    \"A\",\n]);\nfunction isElementKeyboardAccessible(element) {\n    return (focusableElements.has(element.tagName) ||\n        element.tabIndex !== -1);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2dlc3R1cmVzL3ByZXNzL3V0aWxzL2lzLWtleWJvYXJkLWFjY2Vzc2libGUubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUV1QyIsInNvdXJjZXMiOlsiRDpcXHNvZnRcXFNlYVByb2plY3RcXGdpdGh1Yl9vcGVuX3Byb2plY3Rfc3VjY2Vzc1xcc3VuYVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcbW90aW9uLWRvbVxcZGlzdFxcZXNcXGdlc3R1cmVzXFxwcmVzc1xcdXRpbHNcXGlzLWtleWJvYXJkLWFjY2Vzc2libGUubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGZvY3VzYWJsZUVsZW1lbnRzID0gbmV3IFNldChbXG4gICAgXCJCVVRUT05cIixcbiAgICBcIklOUFVUXCIsXG4gICAgXCJTRUxFQ1RcIixcbiAgICBcIlRFWFRBUkVBXCIsXG4gICAgXCJBXCIsXG5dKTtcbmZ1bmN0aW9uIGlzRWxlbWVudEtleWJvYXJkQWNjZXNzaWJsZShlbGVtZW50KSB7XG4gICAgcmV0dXJuIChmb2N1c2FibGVFbGVtZW50cy5oYXMoZWxlbWVudC50YWdOYW1lKSB8fFxuICAgICAgICBlbGVtZW50LnRhYkluZGV4ICE9PSAtMSk7XG59XG5cbmV4cG9ydCB7IGlzRWxlbWVudEtleWJvYXJkQWNjZXNzaWJsZSB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/gestures/press/utils/is-keyboard-accessible.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/gestures/press/utils/keyboard.mjs":
/*!***************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/gestures/press/utils/keyboard.mjs ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   enableKeyboardPress: () => (/* binding */ enableKeyboardPress)\n/* harmony export */ });\n/* harmony import */ var _state_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./state.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/gestures/press/utils/state.mjs\");\n\n\n/**\n * Filter out events that are not \"Enter\" keys.\n */\nfunction filterEvents(callback) {\n    return (event) => {\n        if (event.key !== \"Enter\")\n            return;\n        callback(event);\n    };\n}\nfunction firePointerEvent(target, type) {\n    target.dispatchEvent(new PointerEvent(\"pointer\" + type, { isPrimary: true, bubbles: true }));\n}\nconst enableKeyboardPress = (focusEvent, eventOptions) => {\n    const element = focusEvent.currentTarget;\n    if (!element)\n        return;\n    const handleKeydown = filterEvents(() => {\n        if (_state_mjs__WEBPACK_IMPORTED_MODULE_0__.isPressing.has(element))\n            return;\n        firePointerEvent(element, \"down\");\n        const handleKeyup = filterEvents(() => {\n            firePointerEvent(element, \"up\");\n        });\n        const handleBlur = () => firePointerEvent(element, \"cancel\");\n        element.addEventListener(\"keyup\", handleKeyup, eventOptions);\n        element.addEventListener(\"blur\", handleBlur, eventOptions);\n    });\n    element.addEventListener(\"keydown\", handleKeydown, eventOptions);\n    /**\n     * Add an event listener that fires on blur to remove the keydown events.\n     */\n    element.addEventListener(\"blur\", () => element.removeEventListener(\"keydown\", handleKeydown), eventOptions);\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/gestures/press/utils/keyboard.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/gestures/press/utils/state.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/gestures/press/utils/state.mjs ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isPressing: () => (/* binding */ isPressing)\n/* harmony export */ });\nconst isPressing = new WeakSet();\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2dlc3R1cmVzL3ByZXNzL3V0aWxzL3N0YXRlLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7O0FBRXNCIiwic291cmNlcyI6WyJEOlxcc29mdFxcU2VhUHJvamVjdFxcZ2l0aHViX29wZW5fcHJvamVjdF9zdWNjZXNzXFxzdW5hXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxtb3Rpb24tZG9tXFxkaXN0XFxlc1xcZ2VzdHVyZXNcXHByZXNzXFx1dGlsc1xcc3RhdGUubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGlzUHJlc3NpbmcgPSBuZXcgV2Vha1NldCgpO1xuXG5leHBvcnQgeyBpc1ByZXNzaW5nIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/gestures/press/utils/state.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/gestures/utils/is-node-or-child.mjs":
/*!*****************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/gestures/utils/is-node-or-child.mjs ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isNodeOrChild: () => (/* binding */ isNodeOrChild)\n/* harmony export */ });\n/**\n * Recursively traverse up the tree to check whether the provided child node\n * is the parent or a descendant of it.\n *\n * @param parent - Element to find\n * @param child - Element to test against parent\n */\nconst isNodeOrChild = (parent, child) => {\n    if (!child) {\n        return false;\n    }\n    else if (parent === child) {\n        return true;\n    }\n    else {\n        return isNodeOrChild(parent, child.parentElement);\n    }\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2dlc3R1cmVzL3V0aWxzL2lzLW5vZGUtb3ItY2hpbGQubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRXlCIiwic291cmNlcyI6WyJEOlxcc29mdFxcU2VhUHJvamVjdFxcZ2l0aHViX29wZW5fcHJvamVjdF9zdWNjZXNzXFxzdW5hXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxtb3Rpb24tZG9tXFxkaXN0XFxlc1xcZ2VzdHVyZXNcXHV0aWxzXFxpcy1ub2RlLW9yLWNoaWxkLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIFJlY3Vyc2l2ZWx5IHRyYXZlcnNlIHVwIHRoZSB0cmVlIHRvIGNoZWNrIHdoZXRoZXIgdGhlIHByb3ZpZGVkIGNoaWxkIG5vZGVcbiAqIGlzIHRoZSBwYXJlbnQgb3IgYSBkZXNjZW5kYW50IG9mIGl0LlxuICpcbiAqIEBwYXJhbSBwYXJlbnQgLSBFbGVtZW50IHRvIGZpbmRcbiAqIEBwYXJhbSBjaGlsZCAtIEVsZW1lbnQgdG8gdGVzdCBhZ2FpbnN0IHBhcmVudFxuICovXG5jb25zdCBpc05vZGVPckNoaWxkID0gKHBhcmVudCwgY2hpbGQpID0+IHtcbiAgICBpZiAoIWNoaWxkKSB7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gICAgZWxzZSBpZiAocGFyZW50ID09PSBjaGlsZCkge1xuICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9XG4gICAgZWxzZSB7XG4gICAgICAgIHJldHVybiBpc05vZGVPckNoaWxkKHBhcmVudCwgY2hpbGQucGFyZW50RWxlbWVudCk7XG4gICAgfVxufTtcblxuZXhwb3J0IHsgaXNOb2RlT3JDaGlsZCB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/gestures/utils/is-node-or-child.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/gestures/utils/is-primary-pointer.mjs":
/*!*******************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/gestures/utils/is-primary-pointer.mjs ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isPrimaryPointer: () => (/* binding */ isPrimaryPointer)\n/* harmony export */ });\nconst isPrimaryPointer = (event) => {\n    if (event.pointerType === \"mouse\") {\n        return typeof event.button !== \"number\" || event.button <= 0;\n    }\n    else {\n        /**\n         * isPrimary is true for all mice buttons, whereas every touch point\n         * is regarded as its own input. So subsequent concurrent touch points\n         * will be false.\n         *\n         * Specifically match against false here as incomplete versions of\n         * PointerEvents in very old browser might have it set as undefined.\n         */\n        return event.isPrimary !== false;\n    }\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2dlc3R1cmVzL3V0aWxzL2lzLXByaW1hcnktcG9pbnRlci5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUU0QiIsInNvdXJjZXMiOlsiRDpcXHNvZnRcXFNlYVByb2plY3RcXGdpdGh1Yl9vcGVuX3Byb2plY3Rfc3VjY2Vzc1xcc3VuYVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcbW90aW9uLWRvbVxcZGlzdFxcZXNcXGdlc3R1cmVzXFx1dGlsc1xcaXMtcHJpbWFyeS1wb2ludGVyLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBpc1ByaW1hcnlQb2ludGVyID0gKGV2ZW50KSA9PiB7XG4gICAgaWYgKGV2ZW50LnBvaW50ZXJUeXBlID09PSBcIm1vdXNlXCIpIHtcbiAgICAgICAgcmV0dXJuIHR5cGVvZiBldmVudC5idXR0b24gIT09IFwibnVtYmVyXCIgfHwgZXZlbnQuYnV0dG9uIDw9IDA7XG4gICAgfVxuICAgIGVsc2Uge1xuICAgICAgICAvKipcbiAgICAgICAgICogaXNQcmltYXJ5IGlzIHRydWUgZm9yIGFsbCBtaWNlIGJ1dHRvbnMsIHdoZXJlYXMgZXZlcnkgdG91Y2ggcG9pbnRcbiAgICAgICAgICogaXMgcmVnYXJkZWQgYXMgaXRzIG93biBpbnB1dC4gU28gc3Vic2VxdWVudCBjb25jdXJyZW50IHRvdWNoIHBvaW50c1xuICAgICAgICAgKiB3aWxsIGJlIGZhbHNlLlxuICAgICAgICAgKlxuICAgICAgICAgKiBTcGVjaWZpY2FsbHkgbWF0Y2ggYWdhaW5zdCBmYWxzZSBoZXJlIGFzIGluY29tcGxldGUgdmVyc2lvbnMgb2ZcbiAgICAgICAgICogUG9pbnRlckV2ZW50cyBpbiB2ZXJ5IG9sZCBicm93c2VyIG1pZ2h0IGhhdmUgaXQgc2V0IGFzIHVuZGVmaW5lZC5cbiAgICAgICAgICovXG4gICAgICAgIHJldHVybiBldmVudC5pc1ByaW1hcnkgIT09IGZhbHNlO1xuICAgIH1cbn07XG5cbmV4cG9ydCB7IGlzUHJpbWFyeVBvaW50ZXIgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/gestures/utils/is-primary-pointer.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/gestures/utils/setup.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/gestures/utils/setup.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   setupGesture: () => (/* binding */ setupGesture)\n/* harmony export */ });\n/* harmony import */ var _utils_resolve_elements_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../utils/resolve-elements.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/utils/resolve-elements.mjs\");\n\n\nfunction setupGesture(elementOrSelector, options) {\n    const elements = (0,_utils_resolve_elements_mjs__WEBPACK_IMPORTED_MODULE_0__.resolveElements)(elementOrSelector);\n    const gestureAbortController = new AbortController();\n    const eventOptions = {\n        passive: true,\n        ...options,\n        signal: gestureAbortController.signal,\n    };\n    const cancel = () => gestureAbortController.abort();\n    return [elements, eventOptions, cancel];\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2dlc3R1cmVzL3V0aWxzL3NldHVwLm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFtRTs7QUFFbkU7QUFDQSxxQkFBcUIsNEVBQWU7QUFDcEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUV3QiIsInNvdXJjZXMiOlsiRDpcXHNvZnRcXFNlYVByb2plY3RcXGdpdGh1Yl9vcGVuX3Byb2plY3Rfc3VjY2Vzc1xcc3VuYVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcbW90aW9uLWRvbVxcZGlzdFxcZXNcXGdlc3R1cmVzXFx1dGlsc1xcc2V0dXAubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHJlc29sdmVFbGVtZW50cyB9IGZyb20gJy4uLy4uL3V0aWxzL3Jlc29sdmUtZWxlbWVudHMubWpzJztcblxuZnVuY3Rpb24gc2V0dXBHZXN0dXJlKGVsZW1lbnRPclNlbGVjdG9yLCBvcHRpb25zKSB7XG4gICAgY29uc3QgZWxlbWVudHMgPSByZXNvbHZlRWxlbWVudHMoZWxlbWVudE9yU2VsZWN0b3IpO1xuICAgIGNvbnN0IGdlc3R1cmVBYm9ydENvbnRyb2xsZXIgPSBuZXcgQWJvcnRDb250cm9sbGVyKCk7XG4gICAgY29uc3QgZXZlbnRPcHRpb25zID0ge1xuICAgICAgICBwYXNzaXZlOiB0cnVlLFxuICAgICAgICAuLi5vcHRpb25zLFxuICAgICAgICBzaWduYWw6IGdlc3R1cmVBYm9ydENvbnRyb2xsZXIuc2lnbmFsLFxuICAgIH07XG4gICAgY29uc3QgY2FuY2VsID0gKCkgPT4gZ2VzdHVyZUFib3J0Q29udHJvbGxlci5hYm9ydCgpO1xuICAgIHJldHVybiBbZWxlbWVudHMsIGV2ZW50T3B0aW9ucywgY2FuY2VsXTtcbn1cblxuZXhwb3J0IHsgc2V0dXBHZXN0dXJlIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/gestures/utils/setup.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/stats/animation-count.mjs":
/*!*******************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/stats/animation-count.mjs ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   activeAnimations: () => (/* binding */ activeAnimations)\n/* harmony export */ });\nconst activeAnimations = {\n    layout: 0,\n    mainThread: 0,\n    waapi: 0,\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL3N0YXRzL2FuaW1hdGlvbi1jb3VudC5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRTRCIiwic291cmNlcyI6WyJEOlxcc29mdFxcU2VhUHJvamVjdFxcZ2l0aHViX29wZW5fcHJvamVjdF9zdWNjZXNzXFxzdW5hXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxtb3Rpb24tZG9tXFxkaXN0XFxlc1xcc3RhdHNcXGFuaW1hdGlvbi1jb3VudC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgYWN0aXZlQW5pbWF0aW9ucyA9IHtcbiAgICBsYXlvdXQ6IDAsXG4gICAgbWFpblRocmVhZDogMCxcbiAgICB3YWFwaTogMCxcbn07XG5cbmV4cG9ydCB7IGFjdGl2ZUFuaW1hdGlvbnMgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/stats/animation-count.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/stats/buffer.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/stats/buffer.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   statsBuffer: () => (/* binding */ statsBuffer)\n/* harmony export */ });\nconst statsBuffer = {\n    value: null,\n    addProjectionMetrics: null,\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL3N0YXRzL2J1ZmZlci5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBOztBQUV1QiIsInNvdXJjZXMiOlsiRDpcXHNvZnRcXFNlYVByb2plY3RcXGdpdGh1Yl9vcGVuX3Byb2plY3Rfc3VjY2Vzc1xcc3VuYVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcbW90aW9uLWRvbVxcZGlzdFxcZXNcXHN0YXRzXFxidWZmZXIubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IHN0YXRzQnVmZmVyID0ge1xuICAgIHZhbHVlOiBudWxsLFxuICAgIGFkZFByb2plY3Rpb25NZXRyaWNzOiBudWxsLFxufTtcblxuZXhwb3J0IHsgc3RhdHNCdWZmZXIgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/stats/buffer.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/utils/is-bezier-definition.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/utils/is-bezier-definition.mjs ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isBezierDefinition: () => (/* binding */ isBezierDefinition)\n/* harmony export */ });\nconst isBezierDefinition = (easing) => Array.isArray(easing) && typeof easing[0] === \"number\";\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL3V0aWxzL2lzLWJlemllci1kZWZpbml0aW9uLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7O0FBRThCIiwic291cmNlcyI6WyJEOlxcc29mdFxcU2VhUHJvamVjdFxcZ2l0aHViX29wZW5fcHJvamVjdF9zdWNjZXNzXFxzdW5hXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxtb3Rpb24tZG9tXFxkaXN0XFxlc1xcdXRpbHNcXGlzLWJlemllci1kZWZpbml0aW9uLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBpc0JlemllckRlZmluaXRpb24gPSAoZWFzaW5nKSA9PiBBcnJheS5pc0FycmF5KGVhc2luZykgJiYgdHlwZW9mIGVhc2luZ1swXSA9PT0gXCJudW1iZXJcIjtcblxuZXhwb3J0IHsgaXNCZXppZXJEZWZpbml0aW9uIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/utils/is-bezier-definition.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/utils/resolve-elements.mjs":
/*!********************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/utils/resolve-elements.mjs ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveElements: () => (/* binding */ resolveElements)\n/* harmony export */ });\nfunction resolveElements(elementOrSelector, scope, selectorCache) {\n    if (elementOrSelector instanceof EventTarget) {\n        return [elementOrSelector];\n    }\n    else if (typeof elementOrSelector === \"string\") {\n        let root = document;\n        if (scope) {\n            root = scope.current;\n        }\n        const elements = selectorCache?.[elementOrSelector] ??\n            root.querySelectorAll(elementOrSelector);\n        return elements ? Array.from(elements) : [];\n    }\n    return Array.from(elementOrSelector);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL3V0aWxzL3Jlc29sdmUtZWxlbWVudHMubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRTJCIiwic291cmNlcyI6WyJEOlxcc29mdFxcU2VhUHJvamVjdFxcZ2l0aHViX29wZW5fcHJvamVjdF9zdWNjZXNzXFxzdW5hXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxtb3Rpb24tZG9tXFxkaXN0XFxlc1xcdXRpbHNcXHJlc29sdmUtZWxlbWVudHMubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIHJlc29sdmVFbGVtZW50cyhlbGVtZW50T3JTZWxlY3Rvciwgc2NvcGUsIHNlbGVjdG9yQ2FjaGUpIHtcbiAgICBpZiAoZWxlbWVudE9yU2VsZWN0b3IgaW5zdGFuY2VvZiBFdmVudFRhcmdldCkge1xuICAgICAgICByZXR1cm4gW2VsZW1lbnRPclNlbGVjdG9yXTtcbiAgICB9XG4gICAgZWxzZSBpZiAodHlwZW9mIGVsZW1lbnRPclNlbGVjdG9yID09PSBcInN0cmluZ1wiKSB7XG4gICAgICAgIGxldCByb290ID0gZG9jdW1lbnQ7XG4gICAgICAgIGlmIChzY29wZSkge1xuICAgICAgICAgICAgcm9vdCA9IHNjb3BlLmN1cnJlbnQ7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgZWxlbWVudHMgPSBzZWxlY3RvckNhY2hlPy5bZWxlbWVudE9yU2VsZWN0b3JdID8/XG4gICAgICAgICAgICByb290LnF1ZXJ5U2VsZWN0b3JBbGwoZWxlbWVudE9yU2VsZWN0b3IpO1xuICAgICAgICByZXR1cm4gZWxlbWVudHMgPyBBcnJheS5mcm9tKGVsZW1lbnRzKSA6IFtdO1xuICAgIH1cbiAgICByZXR1cm4gQXJyYXkuZnJvbShlbGVtZW50T3JTZWxlY3Rvcik7XG59XG5cbmV4cG9ydCB7IHJlc29sdmVFbGVtZW50cyB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/utils/resolve-elements.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/utils/supports/flags.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/utils/supports/flags.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supportsFlags: () => (/* binding */ supportsFlags)\n/* harmony export */ });\n/**\n * Add the ability for test suites to manually set support flags\n * to better test more environments.\n */\nconst supportsFlags = {};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL3V0aWxzL3N1cHBvcnRzL2ZsYWdzLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFeUIiLCJzb3VyY2VzIjpbIkQ6XFxzb2Z0XFxTZWFQcm9qZWN0XFxnaXRodWJfb3Blbl9wcm9qZWN0X3N1Y2Nlc3NcXHN1bmFcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXG1vdGlvbi1kb21cXGRpc3RcXGVzXFx1dGlsc1xcc3VwcG9ydHNcXGZsYWdzLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEFkZCB0aGUgYWJpbGl0eSBmb3IgdGVzdCBzdWl0ZXMgdG8gbWFudWFsbHkgc2V0IHN1cHBvcnQgZmxhZ3NcbiAqIHRvIGJldHRlciB0ZXN0IG1vcmUgZW52aXJvbm1lbnRzLlxuICovXG5jb25zdCBzdXBwb3J0c0ZsYWdzID0ge307XG5cbmV4cG9ydCB7IHN1cHBvcnRzRmxhZ3MgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/utils/supports/flags.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/utils/supports/linear-easing.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/utils/supports/linear-easing.mjs ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supportsLinearEasing: () => (/* binding */ supportsLinearEasing)\n/* harmony export */ });\n/* harmony import */ var _memo_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./memo.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/utils/supports/memo.mjs\");\n\n\nconst supportsLinearEasing = /*@__PURE__*/ (0,_memo_mjs__WEBPACK_IMPORTED_MODULE_0__.memoSupports)(() => {\n    try {\n        document\n            .createElement(\"div\")\n            .animate({ opacity: 0 }, { easing: \"linear(0, 1)\" });\n    }\n    catch (e) {\n        return false;\n    }\n    return true;\n}, \"linearEasing\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL3V0aWxzL3N1cHBvcnRzL2xpbmVhci1lYXNpbmcubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTBDOztBQUUxQywyQ0FBMkMsdURBQVk7QUFDdkQ7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCLFlBQVksSUFBSSx3QkFBd0I7QUFDL0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7O0FBRStCIiwic291cmNlcyI6WyJEOlxcc29mdFxcU2VhUHJvamVjdFxcZ2l0aHViX29wZW5fcHJvamVjdF9zdWNjZXNzXFxzdW5hXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxtb3Rpb24tZG9tXFxkaXN0XFxlc1xcdXRpbHNcXHN1cHBvcnRzXFxsaW5lYXItZWFzaW5nLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBtZW1vU3VwcG9ydHMgfSBmcm9tICcuL21lbW8ubWpzJztcblxuY29uc3Qgc3VwcG9ydHNMaW5lYXJFYXNpbmcgPSAvKkBfX1BVUkVfXyovIG1lbW9TdXBwb3J0cygoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgICAgZG9jdW1lbnRcbiAgICAgICAgICAgIC5jcmVhdGVFbGVtZW50KFwiZGl2XCIpXG4gICAgICAgICAgICAuYW5pbWF0ZSh7IG9wYWNpdHk6IDAgfSwgeyBlYXNpbmc6IFwibGluZWFyKDAsIDEpXCIgfSk7XG4gICAgfVxuICAgIGNhdGNoIChlKSB7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gICAgcmV0dXJuIHRydWU7XG59LCBcImxpbmVhckVhc2luZ1wiKTtcblxuZXhwb3J0IHsgc3VwcG9ydHNMaW5lYXJFYXNpbmcgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/utils/supports/linear-easing.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/utils/supports/memo.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/utils/supports/memo.mjs ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   memoSupports: () => (/* binding */ memoSupports)\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-utils */ \"(ssr)/./node_modules/motion-utils/dist/es/memo.mjs\");\n/* harmony import */ var _flags_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./flags.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/utils/supports/flags.mjs\");\n\n\n\nfunction memoSupports(callback, supportsFlag) {\n    const memoized = (0,motion_utils__WEBPACK_IMPORTED_MODULE_0__.memo)(callback);\n    return () => _flags_mjs__WEBPACK_IMPORTED_MODULE_1__.supportsFlags[supportsFlag] ?? memoized();\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL3V0aWxzL3N1cHBvcnRzL21lbW8ubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFvQztBQUNROztBQUU1QztBQUNBLHFCQUFxQixrREFBSTtBQUN6QixpQkFBaUIscURBQWE7QUFDOUI7O0FBRXdCIiwic291cmNlcyI6WyJEOlxcc29mdFxcU2VhUHJvamVjdFxcZ2l0aHViX29wZW5fcHJvamVjdF9zdWNjZXNzXFxzdW5hXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxtb3Rpb24tZG9tXFxkaXN0XFxlc1xcdXRpbHNcXHN1cHBvcnRzXFxtZW1vLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBtZW1vIH0gZnJvbSAnbW90aW9uLXV0aWxzJztcbmltcG9ydCB7IHN1cHBvcnRzRmxhZ3MgfSBmcm9tICcuL2ZsYWdzLm1qcyc7XG5cbmZ1bmN0aW9uIG1lbW9TdXBwb3J0cyhjYWxsYmFjaywgc3VwcG9ydHNGbGFnKSB7XG4gICAgY29uc3QgbWVtb2l6ZWQgPSBtZW1vKGNhbGxiYWNrKTtcbiAgICByZXR1cm4gKCkgPT4gc3VwcG9ydHNGbGFnc1tzdXBwb3J0c0ZsYWddID8/IG1lbW9pemVkKCk7XG59XG5cbmV4cG9ydCB7IG1lbW9TdXBwb3J0cyB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/utils/supports/memo.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/utils/supports/scroll-timeline.mjs":
/*!****************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/utils/supports/scroll-timeline.mjs ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supportsScrollTimeline: () => (/* binding */ supportsScrollTimeline)\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-utils */ \"(ssr)/./node_modules/motion-utils/dist/es/memo.mjs\");\n\n\nconst supportsScrollTimeline = /* @__PURE__ */ (0,motion_utils__WEBPACK_IMPORTED_MODULE_0__.memo)(() => window.ScrollTimeline !== undefined);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL3V0aWxzL3N1cHBvcnRzL3Njcm9sbC10aW1lbGluZS5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBb0M7O0FBRXBDLCtDQUErQyxrREFBSTs7QUFFakIiLCJzb3VyY2VzIjpbIkQ6XFxzb2Z0XFxTZWFQcm9qZWN0XFxnaXRodWJfb3Blbl9wcm9qZWN0X3N1Y2Nlc3NcXHN1bmFcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXG1vdGlvbi1kb21cXGRpc3RcXGVzXFx1dGlsc1xcc3VwcG9ydHNcXHNjcm9sbC10aW1lbGluZS5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgbWVtbyB9IGZyb20gJ21vdGlvbi11dGlscyc7XG5cbmNvbnN0IHN1cHBvcnRzU2Nyb2xsVGltZWxpbmUgPSAvKiBAX19QVVJFX18gKi8gbWVtbygoKSA9PiB3aW5kb3cuU2Nyb2xsVGltZWxpbmUgIT09IHVuZGVmaW5lZCk7XG5cbmV4cG9ydCB7IHN1cHBvcnRzU2Nyb2xsVGltZWxpbmUgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/utils/supports/scroll-timeline.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/value/index.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/value/index.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MotionValue: () => (/* binding */ MotionValue),\n/* harmony export */   collectMotionValues: () => (/* binding */ collectMotionValues),\n/* harmony export */   motionValue: () => (/* binding */ motionValue)\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! motion-utils */ \"(ssr)/./node_modules/motion-utils/dist/es/warn-once.mjs\");\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! motion-utils */ \"(ssr)/./node_modules/motion-utils/dist/es/subscription-manager.mjs\");\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! motion-utils */ \"(ssr)/./node_modules/motion-utils/dist/es/velocity-per-second.mjs\");\n/* harmony import */ var _frameloop_sync_time_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../frameloop/sync-time.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/frameloop/sync-time.mjs\");\n/* harmony import */ var _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../frameloop/frame.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/frameloop/frame.mjs\");\n\n\n\n\n/**\n * Maximum time between the value of two frames, beyond which we\n * assume the velocity has since been 0.\n */\nconst MAX_VELOCITY_DELTA = 30;\nconst isFloat = (value) => {\n    return !isNaN(parseFloat(value));\n};\nconst collectMotionValues = {\n    current: undefined,\n};\n/**\n * `MotionValue` is used to track the state and velocity of motion values.\n *\n * @public\n */\nclass MotionValue {\n    /**\n     * @param init - The initiating value\n     * @param config - Optional configuration options\n     *\n     * -  `transformer`: A function to transform incoming values with.\n     */\n    constructor(init, options = {}) {\n        /**\n         * This will be replaced by the build step with the latest version number.\n         * When MotionValues are provided to motion components, warn if versions are mixed.\n         */\n        this.version = \"12.7.3\";\n        /**\n         * Tracks whether this value can output a velocity. Currently this is only true\n         * if the value is numerical, but we might be able to widen the scope here and support\n         * other value types.\n         *\n         * @internal\n         */\n        this.canTrackVelocity = null;\n        /**\n         * An object containing a SubscriptionManager for each active event.\n         */\n        this.events = {};\n        this.updateAndNotify = (v, render = true) => {\n            const currentTime = _frameloop_sync_time_mjs__WEBPACK_IMPORTED_MODULE_0__.time.now();\n            /**\n             * If we're updating the value during another frame or eventloop\n             * than the previous frame, then the we set the previous frame value\n             * to current.\n             */\n            if (this.updatedAt !== currentTime) {\n                this.setPrevFrameValue();\n            }\n            this.prev = this.current;\n            this.setCurrent(v);\n            // Update update subscribers\n            if (this.current !== this.prev && this.events.change) {\n                this.events.change.notify(this.current);\n            }\n            // Update render subscribers\n            if (render && this.events.renderRequest) {\n                this.events.renderRequest.notify(this.current);\n            }\n        };\n        this.hasAnimated = false;\n        this.setCurrent(init);\n        this.owner = options.owner;\n    }\n    setCurrent(current) {\n        this.current = current;\n        this.updatedAt = _frameloop_sync_time_mjs__WEBPACK_IMPORTED_MODULE_0__.time.now();\n        if (this.canTrackVelocity === null && current !== undefined) {\n            this.canTrackVelocity = isFloat(this.current);\n        }\n    }\n    setPrevFrameValue(prevFrameValue = this.current) {\n        this.prevFrameValue = prevFrameValue;\n        this.prevUpdatedAt = this.updatedAt;\n    }\n    /**\n     * Adds a function that will be notified when the `MotionValue` is updated.\n     *\n     * It returns a function that, when called, will cancel the subscription.\n     *\n     * When calling `onChange` inside a React component, it should be wrapped with the\n     * `useEffect` hook. As it returns an unsubscribe function, this should be returned\n     * from the `useEffect` function to ensure you don't add duplicate subscribers..\n     *\n     * ```jsx\n     * export const MyComponent = () => {\n     *   const x = useMotionValue(0)\n     *   const y = useMotionValue(0)\n     *   const opacity = useMotionValue(1)\n     *\n     *   useEffect(() => {\n     *     function updateOpacity() {\n     *       const maxXY = Math.max(x.get(), y.get())\n     *       const newOpacity = transform(maxXY, [0, 100], [1, 0])\n     *       opacity.set(newOpacity)\n     *     }\n     *\n     *     const unsubscribeX = x.on(\"change\", updateOpacity)\n     *     const unsubscribeY = y.on(\"change\", updateOpacity)\n     *\n     *     return () => {\n     *       unsubscribeX()\n     *       unsubscribeY()\n     *     }\n     *   }, [])\n     *\n     *   return <motion.div style={{ x }} />\n     * }\n     * ```\n     *\n     * @param subscriber - A function that receives the latest value.\n     * @returns A function that, when called, will cancel this subscription.\n     *\n     * @deprecated\n     */\n    onChange(subscription) {\n        if (true) {\n            (0,motion_utils__WEBPACK_IMPORTED_MODULE_1__.warnOnce)(false, `value.onChange(callback) is deprecated. Switch to value.on(\"change\", callback).`);\n        }\n        return this.on(\"change\", subscription);\n    }\n    on(eventName, callback) {\n        if (!this.events[eventName]) {\n            this.events[eventName] = new motion_utils__WEBPACK_IMPORTED_MODULE_2__.SubscriptionManager();\n        }\n        const unsubscribe = this.events[eventName].add(callback);\n        if (eventName === \"change\") {\n            return () => {\n                unsubscribe();\n                /**\n                 * If we have no more change listeners by the start\n                 * of the next frame, stop active animations.\n                 */\n                _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_3__.frame.read(() => {\n                    if (!this.events.change.getSize()) {\n                        this.stop();\n                    }\n                });\n            };\n        }\n        return unsubscribe;\n    }\n    clearListeners() {\n        for (const eventManagers in this.events) {\n            this.events[eventManagers].clear();\n        }\n    }\n    /**\n     * Attaches a passive effect to the `MotionValue`.\n     */\n    attach(passiveEffect, stopPassiveEffect) {\n        this.passiveEffect = passiveEffect;\n        this.stopPassiveEffect = stopPassiveEffect;\n    }\n    /**\n     * Sets the state of the `MotionValue`.\n     *\n     * @remarks\n     *\n     * ```jsx\n     * const x = useMotionValue(0)\n     * x.set(10)\n     * ```\n     *\n     * @param latest - Latest value to set.\n     * @param render - Whether to notify render subscribers. Defaults to `true`\n     *\n     * @public\n     */\n    set(v, render = true) {\n        if (!render || !this.passiveEffect) {\n            this.updateAndNotify(v, render);\n        }\n        else {\n            this.passiveEffect(v, this.updateAndNotify);\n        }\n    }\n    setWithVelocity(prev, current, delta) {\n        this.set(current);\n        this.prev = undefined;\n        this.prevFrameValue = prev;\n        this.prevUpdatedAt = this.updatedAt - delta;\n    }\n    /**\n     * Set the state of the `MotionValue`, stopping any active animations,\n     * effects, and resets velocity to `0`.\n     */\n    jump(v, endAnimation = true) {\n        this.updateAndNotify(v);\n        this.prev = v;\n        this.prevUpdatedAt = this.prevFrameValue = undefined;\n        endAnimation && this.stop();\n        if (this.stopPassiveEffect)\n            this.stopPassiveEffect();\n    }\n    /**\n     * Returns the latest state of `MotionValue`\n     *\n     * @returns - The latest state of `MotionValue`\n     *\n     * @public\n     */\n    get() {\n        if (collectMotionValues.current) {\n            collectMotionValues.current.push(this);\n        }\n        return this.current;\n    }\n    /**\n     * @public\n     */\n    getPrevious() {\n        return this.prev;\n    }\n    /**\n     * Returns the latest velocity of `MotionValue`\n     *\n     * @returns - The latest velocity of `MotionValue`. Returns `0` if the state is non-numerical.\n     *\n     * @public\n     */\n    getVelocity() {\n        const currentTime = _frameloop_sync_time_mjs__WEBPACK_IMPORTED_MODULE_0__.time.now();\n        if (!this.canTrackVelocity ||\n            this.prevFrameValue === undefined ||\n            currentTime - this.updatedAt > MAX_VELOCITY_DELTA) {\n            return 0;\n        }\n        const delta = Math.min(this.updatedAt - this.prevUpdatedAt, MAX_VELOCITY_DELTA);\n        // Casts because of parseFloat's poor typing\n        return (0,motion_utils__WEBPACK_IMPORTED_MODULE_4__.velocityPerSecond)(parseFloat(this.current) -\n            parseFloat(this.prevFrameValue), delta);\n    }\n    /**\n     * Registers a new animation to control this `MotionValue`. Only one\n     * animation can drive a `MotionValue` at one time.\n     *\n     * ```jsx\n     * value.start()\n     * ```\n     *\n     * @param animation - A function that starts the provided animation\n     */\n    start(startAnimation) {\n        this.stop();\n        return new Promise((resolve) => {\n            this.hasAnimated = true;\n            this.animation = startAnimation(resolve);\n            if (this.events.animationStart) {\n                this.events.animationStart.notify();\n            }\n        }).then(() => {\n            if (this.events.animationComplete) {\n                this.events.animationComplete.notify();\n            }\n            this.clearAnimation();\n        });\n    }\n    /**\n     * Stop the currently active animation.\n     *\n     * @public\n     */\n    stop() {\n        if (this.animation) {\n            this.animation.stop();\n            if (this.events.animationCancel) {\n                this.events.animationCancel.notify();\n            }\n        }\n        this.clearAnimation();\n    }\n    /**\n     * Returns `true` if this value is currently animating.\n     *\n     * @public\n     */\n    isAnimating() {\n        return !!this.animation;\n    }\n    clearAnimation() {\n        delete this.animation;\n    }\n    /**\n     * Destroy and clean up subscribers to this `MotionValue`.\n     *\n     * The `MotionValue` hooks like `useMotionValue` and `useTransform` automatically\n     * handle the lifecycle of the returned `MotionValue`, so this method is only necessary if you've manually\n     * created a `MotionValue` via the `motionValue` function.\n     *\n     * @public\n     */\n    destroy() {\n        this.clearListeners();\n        this.stop();\n        if (this.stopPassiveEffect) {\n            this.stopPassiveEffect();\n        }\n    }\n}\nfunction motionValue(init, options) {\n    return new MotionValue(init, options);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/value/index.mjs\n");

/***/ })

};
;