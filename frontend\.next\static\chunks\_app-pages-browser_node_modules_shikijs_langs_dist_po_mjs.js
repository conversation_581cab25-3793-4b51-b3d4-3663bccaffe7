"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_po_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/po.mjs":
/*!*************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/po.mjs ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Gettext PO\\\",\\\"fileTypes\\\":[\\\"po\\\",\\\"pot\\\",\\\"potx\\\"],\\\"name\\\":\\\"po\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"^(?:(?=(msg(?:id(_plural)?|ctxt))\\\\\\\\s*\\\\\\\"[^\\\\\\\"])|\\\\\\\\s*$)\\\",\\\"end\\\":\\\"\\\\\\\\z\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#body\\\"}]},{\\\"include\\\":\\\"#comments\\\"},{\\\"match\\\":\\\"^msg(id|str)\\\\\\\\s+\\\\\\\"\\\\\\\"\\\\\\\\s*$\\\\\\\\n?\\\",\\\"name\\\":\\\"comment.line.number-sign.po\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.language.po\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.key-value.po\\\"},\\\"3\\\":{\\\"name\\\":\\\"string.other.po\\\"}},\\\"match\\\":\\\"^\\\\\\\"(?:([^\\\\\\\\s:]+)(:)\\\\\\\\s+)?([^\\\\\\\"]*)\\\\\\\"\\\\\\\\s*$\\\\\\\\n?\\\",\\\"name\\\":\\\"meta.header.po\\\"}],\\\"repository\\\":{\\\"body\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^(msgid(_plural)?)\\\\\\\\s+\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.msgid.po\\\"}},\\\"end\\\":\\\"^(?!\\\\\\\")\\\",\\\"name\\\":\\\"meta.scope.msgid.po\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(\\\\\\\\G|^)\\\\\\\"\\\",\\\"end\\\":\\\"\\\\\\\"\\\",\\\"name\\\":\\\"string.quoted.double.po\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\[\\\\\\\\\\\\\\\\\\\\\\\"]\\\",\\\"name\\\":\\\"constant.character.escape.po\\\"}]}]},{\\\"begin\\\":\\\"^(msgstr)(?:(\\\\\\\\[)(\\\\\\\\d+)(]))?\\\\\\\\s+\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.msgstr.po\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.msgstr.po\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.numeric.po\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.control.msgstr.po\\\"}},\\\"end\\\":\\\"^(?!\\\\\\\")\\\",\\\"name\\\":\\\"meta.scope.msgstr.po\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(\\\\\\\\G|^)\\\\\\\"\\\",\\\"end\\\":\\\"\\\\\\\"\\\",\\\"name\\\":\\\"string.quoted.double.po\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\[\\\\\\\\\\\\\\\\\\\\\\\"]\\\",\\\"name\\\":\\\"constant.character.escape.po\\\"}]}]},{\\\"begin\\\":\\\"^(msgctxt)(?:(\\\\\\\\[)(\\\\\\\\d+)(]))?\\\\\\\\s+\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.msgctxt.po\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.msgctxt.po\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.numeric.po\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.control.msgctxt.po\\\"}},\\\"end\\\":\\\"^(?!\\\\\\\")\\\",\\\"name\\\":\\\"meta.scope.msgctxt.po\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(\\\\\\\\G|^)\\\\\\\"\\\",\\\"end\\\":\\\"\\\\\\\"\\\",\\\"name\\\":\\\"string.quoted.double.po\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\[\\\\\\\\\\\\\\\\\\\\\\\"]\\\",\\\"name\\\":\\\"constant.character.escape.po\\\"}]}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.po\\\"}},\\\"match\\\":\\\"^(#~).*$\\\\\\\\n?\\\",\\\"name\\\":\\\"comment.line.number-sign.obsolete.po\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"match\\\":\\\"^(?!\\\\\\\\s*$)[^#\\\\\\\"].*$\\\\\\\\n?\\\",\\\"name\\\":\\\"invalid.illegal.po\\\"}]},\\\"comments\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^(?=#)\\\",\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(#,)\\\\\\\\s+\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.po\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.line.number-sign.flag.po\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.type.flag.po\\\"}},\\\"match\\\":\\\"(?:\\\\\\\\G|,\\\\\\\\s*)(fuzzy|(?:no-)?(?:c|objc|sh|lisp|elisp|librep|scheme|smalltalk|java|csharp|awk|object-pascal|ycp|tcl|perl|perl-brace|php|gcc-internal|qt|boost)-format)\\\"}]},{\\\"begin\\\":\\\"#\\\\\\\\.\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.po\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.line.number-sign.extracted.po\\\"},{\\\"begin\\\":\\\"(#:)[ \\\\\\\\t]*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.po\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.line.number-sign.reference.po\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(\\\\\\\\S+:)([\\\\\\\\d;]*)\\\",\\\"name\\\":\\\"storage.type.class.po\\\"}]},{\\\"begin\\\":\\\"#\\\\\\\\|\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.po\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.line.number-sign.previous.po\\\"},{\\\"begin\\\":\\\"#\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.po\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.line.number-sign.po\\\"}]}]}},\\\"scopeName\\\":\\\"source.po\\\",\\\"aliases\\\":[\\\"pot\\\",\\\"potx\\\"]}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/po.mjs\n"));

/***/ })

}]);