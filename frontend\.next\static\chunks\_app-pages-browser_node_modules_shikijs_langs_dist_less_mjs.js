"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_less_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/less.mjs":
/*!***************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/less.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Less\\\",\\\"name\\\":\\\"less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment-block\\\"},{\\\"include\\\":\\\"#less-namespace-accessors\\\"},{\\\"include\\\":\\\"#less-extend\\\"},{\\\"include\\\":\\\"#at-rules\\\"},{\\\"include\\\":\\\"#less-variable-assignment\\\"},{\\\"include\\\":\\\"#property-list\\\"},{\\\"include\\\":\\\"#selector\\\"}],\\\"repository\\\":{\\\"angle-type\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.unit.less\\\"}},\\\"match\\\":\\\"(?i:[-+]?(?:\\\\\\\\d*\\\\\\\\.\\\\\\\\d+(?:[eE][-+]?\\\\\\\\d+)*|[-+]?\\\\\\\\d+)(deg|grad|rad|turn))\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.less\\\"},\\\"arbitrary-repetition\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arbitrary-repetition.less\\\"}},\\\"match\\\":\\\"\\\\\\\\s*(,)\\\"},\\\"at-charset\\\":{\\\"begin\\\":\\\"\\\\\\\\s*((@)charset\\\\\\\\b)\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.at-rule.charset.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.less\\\"}},\\\"end\\\":\\\"\\\\\\\\s*((?=;|$))\\\",\\\"name\\\":\\\"meta.at-rule.charset.less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#literal-string\\\"}]},\\\"at-container\\\":{\\\"begin\\\":\\\"(?=\\\\\\\\s*@container)\\\",\\\"end\\\":\\\"\\\\\\\\s*(})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.block.end.less\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"((@)container)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.at-rule.container.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.less\\\"},\\\"3\\\":{\\\"name\\\":\\\"support.constant.container.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\{)\\\",\\\"name\\\":\\\"meta.at-rule.container.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\s*(?=[^{;])\\\",\\\"end\\\":\\\"\\\\\\\\s*(?=[{;])\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(not|and|or)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.operator.comparison.less\\\"},{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.at-rule.container-query.less\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.type.property-name.less\\\"}},\\\"match\\\":\\\"\\\\\\\\b(aspect-ratio|block-size|height|inline-size|orientation|width)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.size-feature.less\\\"},{\\\"match\\\":\\\"(([<>])=?)|[=/]\\\",\\\"name\\\":\\\"keyword.operator.comparison.less\\\"},{\\\"match\\\":\\\":\\\",\\\"name\\\":\\\"punctuation.separator.key-value.less\\\"},{\\\"match\\\":\\\"portrait|landscape\\\",\\\"name\\\":\\\"support.constant.property-value.less\\\"},{\\\"include\\\":\\\"#numeric-values\\\"},{\\\"match\\\":\\\"/\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.less\\\"},{\\\"include\\\":\\\"#var-function\\\"},{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#less-variable-interpolation\\\"}]},{\\\"include\\\":\\\"#style-function\\\"},{\\\"match\\\":\\\"--|-?(?:[a-zA-Z_·À-ÖØ-öø-ͽͿ-῿‌‍‿⁀⁰-↏Ⰰ-⿯、-퟿豈-﷏ﷰ-�𐀀-\\\\\\\\x{EFFFF}]|\\\\\\\\\\\\\\\\(?:\\\\\\\\N|\\\\\\\\H|\\\\\\\\h{1,6}[\\\\\\\\sR]))(?:[-\\\\\\\\da-zA-Z_·À-ÖØ-öø-ͽͿ-῿‌‍‿⁀⁰-↏Ⰰ-⿯、-퟿豈-﷏ﷰ-�𐀀-\\\\\\\\x{EFFFF}]|\\\\\\\\\\\\\\\\(?:\\\\\\\\N|\\\\\\\\H|\\\\\\\\h{1,6}[\\\\\\\\sR]))*\\\",\\\"name\\\":\\\"variable.parameter.container-name.css\\\"},{\\\"include\\\":\\\"#arbitrary-repetition\\\"},{\\\"include\\\":\\\"#less-variables\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\s*(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.block.begin.less\\\"}},\\\"end\\\":\\\"(?=})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#rule-list-body\\\"},{\\\"include\\\":\\\"$self\\\"}]}]},\\\"at-counter-style\\\":{\\\"begin\\\":\\\"\\\\\\\\s*((@)counter-style\\\\\\\\b)\\\\\\\\s+(?:(?i:\\\\\\\\b(decimal|none)\\\\\\\\b)|(-?(?:[_a-zA-Z[^\\\\\\\\x00-\\\\\\\\x7F]]|\\\\\\\\\\\\\\\\(?:\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))(?:[-\\\\\\\\w[^\\\\\\\\x00-\\\\\\\\x7F]]|\\\\\\\\\\\\\\\\(?:\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))*))\\\\\\\\s*(?=\\\\\\\\{|$)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.at-rule.counter-style.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.less\\\"},\\\"3\\\":{\\\"name\\\":\\\"invalid.illegal.counter-style-name.less\\\"},\\\"4\\\":{\\\"name\\\":\\\"entity.other.counter-style-name.css\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.block.begin.less\\\"}},\\\"name\\\":\\\"meta.at-rule.counter-style.less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment-block\\\"},{\\\"include\\\":\\\"#rule-list\\\"}]},\\\"at-custom-media\\\":{\\\"begin\\\":\\\"(?=\\\\\\\\s*@custom-media\\\\\\\\b)\\\",\\\"end\\\":\\\"\\\\\\\\s*(?=;)\\\",\\\"name\\\":\\\"meta.at-rule.custom-media.less\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.property-list.less\\\"}},\\\"match\\\":\\\"\\\\\\\\s*;\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.at-rule.custom-media.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.less\\\"},\\\"3\\\":{\\\"name\\\":\\\"support.constant.custom-media.less\\\"}},\\\"match\\\":\\\"\\\\\\\\s*((@)custom-media)(?=.*?)\\\"},{\\\"include\\\":\\\"#media-query-list\\\"}]},\\\"at-font-face\\\":{\\\"begin\\\":\\\"\\\\\\\\s*((@)font-face)\\\\\\\\s*(?=\\\\\\\\{|$)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.at-rule.font-face.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.less\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.block.end.less\\\"}},\\\"name\\\":\\\"meta.at-rule.font-face.less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment-block\\\"},{\\\"include\\\":\\\"#rule-list\\\"}]},\\\"at-import\\\":{\\\"begin\\\":\\\"\\\\\\\\s*((@)import\\\\\\\\b)\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.at-rule.import.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.less\\\"}},\\\"end\\\":\\\";\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.terminator.rule.less\\\"}},\\\"name\\\":\\\"meta.at-rule.import.less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#url-function\\\"},{\\\"include\\\":\\\"#less-variables\\\"},{\\\"begin\\\":\\\"(?<=([\\\\\\\"'])|([\\\\\\\"']\\\\\\\\)))\\\\\\\\s*\\\",\\\"end\\\":\\\"\\\\\\\\s*(?=;)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#media-query\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.group.less\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"reference|inline|less|css|once|multiple|optional\\\",\\\"name\\\":\\\"constant.language.import-directive.less\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"}]},{\\\"include\\\":\\\"#literal-string\\\"}]},\\\"at-keyframes\\\":{\\\"begin\\\":\\\"\\\\\\\\s*((@)keyframes)(?=.*?\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.at-rule.keyframe.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.less\\\"},\\\"4\\\":{\\\"name\\\":\\\"support.constant.keyframe.less\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.block.end.less\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\s*(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.block.begin.less\\\"}},\\\"end\\\":\\\"(?=})\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.keyframe-selector.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.numeric.less\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.other.unit.less\\\"}},\\\"match\\\":\\\"\\\\\\\\s*(?:(from|to)|((?:\\\\\\\\.[0-9]+|[0-9]+(?:\\\\\\\\.[0-9]*)?)(%)))\\\\\\\\s*,?\\\\\\\\s*\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\s*(?=[^{;])\\\",\\\"end\\\":\\\"\\\\\\\\s*(?=\\\\\\\\{)\\\",\\\"name\\\":\\\"meta.at-rule.keyframe.less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#keyframe-name\\\"},{\\\"include\\\":\\\"#arbitrary-repetition\\\"}]}]},\\\"at-media\\\":{\\\"begin\\\":\\\"(?=\\\\\\\\s*@media\\\\\\\\b)\\\",\\\"end\\\":\\\"\\\\\\\\s*(})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.block.end.less\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\s*((@)media)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.at-rule.media.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.less\\\"},\\\"3\\\":{\\\"name\\\":\\\"support.constant.media.less\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(?=\\\\\\\\{)\\\",\\\"name\\\":\\\"meta.at-rule.media.less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#media-query-list\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\s*(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.block.begin.less\\\"}},\\\"end\\\":\\\"(?=})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#rule-list-body\\\"},{\\\"include\\\":\\\"$self\\\"}]}]},\\\"at-namespace\\\":{\\\"begin\\\":\\\"\\\\\\\\s*((@)namespace)\\\\\\\\s+\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.at-rule.namespace.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.less\\\"}},\\\"end\\\":\\\";\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.terminator.rule.less\\\"}},\\\"name\\\":\\\"meta.at-rule.namespace.less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#url-function\\\"},{\\\"include\\\":\\\"#literal-string\\\"},{\\\"match\\\":\\\"(-?(?:[_a-zA-Z[^\\\\\\\\x00-\\\\\\\\x7F]]|\\\\\\\\\\\\\\\\(?:\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))(?:[-\\\\\\\\w[^\\\\\\\\x00-\\\\\\\\x7F]]|\\\\\\\\\\\\\\\\(?:\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))*)\\\",\\\"name\\\":\\\"entity.name.constant.namespace-prefix.less\\\"}]},\\\"at-page\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.at-rule.page.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.less\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.entity.less\\\"},\\\"4\\\":{\\\"name\\\":\\\"entity.other.attribute-name.pseudo-class.less\\\"}},\\\"match\\\":\\\"\\\\\\\\s*((@)page)\\\\\\\\s*(?:(:)(first|left|right))?\\\\\\\\s*(?=\\\\\\\\{|$)\\\",\\\"name\\\":\\\"meta.at-rule.page.less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment-block\\\"},{\\\"include\\\":\\\"#rule-list\\\"}]},\\\"at-rules\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#at-charset\\\"},{\\\"include\\\":\\\"#at-container\\\"},{\\\"include\\\":\\\"#at-counter-style\\\"},{\\\"include\\\":\\\"#at-custom-media\\\"},{\\\"include\\\":\\\"#at-font-face\\\"},{\\\"include\\\":\\\"#at-media\\\"},{\\\"include\\\":\\\"#at-import\\\"},{\\\"include\\\":\\\"#at-keyframes\\\"},{\\\"include\\\":\\\"#at-namespace\\\"},{\\\"include\\\":\\\"#at-page\\\"},{\\\"include\\\":\\\"#at-supports\\\"},{\\\"include\\\":\\\"#at-viewport\\\"}]},\\\"at-supports\\\":{\\\"begin\\\":\\\"(?=\\\\\\\\s*@supports\\\\\\\\b)\\\",\\\"end\\\":\\\"(?=\\\\\\\\s*)(})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.block.end.less\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\s*((@)supports)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.at-rule.supports.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.less\\\"},\\\"3\\\":{\\\"name\\\":\\\"support.constant.supports.less\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(?=\\\\\\\\{)\\\",\\\"name\\\":\\\"meta.at-rule.supports.less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#at-supports-operators\\\"},{\\\"include\\\":\\\"#at-supports-parens\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\s*(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.property-list.begin.less\\\"}},\\\"end\\\":\\\"(?=})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#rule-list-body\\\"},{\\\"include\\\":\\\"$self\\\"}]}]},\\\"at-supports-operators\\\":{\\\"match\\\":\\\"\\\\\\\\b(?:and|or|not)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.operator.logic.less\\\"},\\\"at-supports-parens\\\":{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.group.less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#at-supports-operators\\\"},{\\\"include\\\":\\\"#at-supports-parens\\\"},{\\\"include\\\":\\\"#rule-list-body\\\"}]},\\\"attr-function\\\":{\\\"begin\\\":\\\"\\\\\\\\b(attr)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.filter.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#qualified-name\\\"},{\\\"include\\\":\\\"#literal-string\\\"},{\\\"begin\\\":\\\"(-?(?:[_a-zA-Z[^\\\\\\\\x00-\\\\\\\\x7F]]|\\\\\\\\\\\\\\\\(?:\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))(?:[-\\\\\\\\w[^\\\\\\\\x00-\\\\\\\\x7F]]|\\\\\\\\\\\\\\\\(?:\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))*)\\\",\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"name\\\":\\\"entity.other.attribute-name.less\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b((?i:em|ex|ch|rem)|(?i:v(?:[wh]|min|max))|(?i:cm|mm|q|in|pt|pc|px|fr)|(?i:deg|grad|rad|turn)|(?i:s|ms)|(?i:Hz|kHz)|(?i:dp(?:i|cm|px)))\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.unit.less\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#property-value-constants\\\"},{\\\"include\\\":\\\"#numeric-values\\\"}]},{\\\"include\\\":\\\"#color-values\\\"}]}]},\\\"builtin-functions\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#attr-function\\\"},{\\\"include\\\":\\\"#calc-function\\\"},{\\\"include\\\":\\\"#color-functions\\\"},{\\\"include\\\":\\\"#counter-functions\\\"},{\\\"include\\\":\\\"#cross-fade-function\\\"},{\\\"include\\\":\\\"#cubic-bezier-function\\\"},{\\\"include\\\":\\\"#filter-function\\\"},{\\\"include\\\":\\\"#fit-content-function\\\"},{\\\"include\\\":\\\"#format-function\\\"},{\\\"include\\\":\\\"#gradient-functions\\\"},{\\\"include\\\":\\\"#grid-repeat-function\\\"},{\\\"include\\\":\\\"#image-function\\\"},{\\\"include\\\":\\\"#less-functions\\\"},{\\\"include\\\":\\\"#local-function\\\"},{\\\"include\\\":\\\"#minmax-function\\\"},{\\\"include\\\":\\\"#regexp-function\\\"},{\\\"include\\\":\\\"#shape-functions\\\"},{\\\"include\\\":\\\"#steps-function\\\"},{\\\"include\\\":\\\"#symbols-function\\\"},{\\\"include\\\":\\\"#transform-functions\\\"},{\\\"include\\\":\\\"#url-function\\\"},{\\\"include\\\":\\\"#var-function\\\"}]},\\\"calc-function\\\":{\\\"begin\\\":\\\"\\\\\\\\b(calc)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.calc.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-strings\\\"},{\\\"include\\\":\\\"#var-function\\\"},{\\\"include\\\":\\\"#calc-function\\\"},{\\\"include\\\":\\\"#attr-function\\\"},{\\\"include\\\":\\\"#less-math\\\"},{\\\"include\\\":\\\"#relative-color\\\"}]}]},\\\"color-adjuster-operators\\\":{\\\"match\\\":\\\"[-+*](?=\\\\\\\\s+)\\\",\\\"name\\\":\\\"keyword.operator.less\\\"},\\\"color-functions\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\b(rgba?)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.color.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-strings\\\"},{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#var-function\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#value-separator\\\"},{\\\"include\\\":\\\"#percentage-type\\\"},{\\\"include\\\":\\\"#number-type\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(hsla|hsl|hwb|oklab|oklch|lab|lch)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.color.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#color-values\\\"},{\\\"include\\\":\\\"#less-strings\\\"},{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#var-function\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#angle-type\\\"},{\\\"include\\\":\\\"#percentage-type\\\"},{\\\"include\\\":\\\"#number-type\\\"},{\\\"include\\\":\\\"#calc-function\\\"},{\\\"include\\\":\\\"#value-separator\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(light-dark)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.color.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#color-values\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"}]}]},{\\\"include\\\":\\\"#less-color-functions\\\"}]},\\\"color-values\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#color-functions\\\"},{\\\"include\\\":\\\"#less-functions\\\"},{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#var-function\\\"},{\\\"match\\\":\\\"\\\\\\\\b(aqua|black|blue|fuchsia|gray|green|lime|maroon|navy|olive|orange|purple|red|silver|teal|white|yellow)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.color.w3c-standard-color-name.less\\\"},{\\\"match\\\":\\\"\\\\\\\\b(aliceblue|antiquewhite|aquamarine|azure|beige|bisque|blanchedalmond|blueviolet|brown|burlywood|cadetblue|chartreuse|chocolate|coral|cornflowerblue|cornsilk|crimson|cyan|darkblue|darkcyan|darkgoldenrod|darkgray|darkgreen|darkgrey|darkkhaki|darkmagenta|darkolivegreen|darkorange|darkorchid|darkred|darksalmon|darkseagreen|darkslateblue|darkslategray|darkslategrey|darkturquoise|darkviolet|deeppink|deepskyblue|dimgray|dimgrey|dodgerblue|firebrick|floralwhite|forestgreen|gainsboro|ghostwhite|gold|goldenrod|greenyellow|grey|honeydew|hotpink|indianred|indigo|ivory|khaki|lavender|lavenderblush|lawngreen|lemonchiffon|lightblue|lightcoral|lightcyan|lightgoldenrodyellow|lightgray|lightgreen|lightgrey|lightpink|lightsalmon|lightseagreen|lightskyblue|lightslategray|lightslategrey|lightsteelblue|lightyellow|limegreen|linen|magenta|mediumaquamarine|mediumblue|mediumorchid|mediumpurple|mediumseagreen|mediumslateblue|mediumspringgreen|mediumturquoise|mediumvioletred|midnightblue|mintcream|mistyrose|moccasin|navajowhite|oldlace|olivedrab|orangered|orchid|palegoldenrod|palegreen|paleturquoise|palevioletred|papayawhip|peachpuff|peru|pink|plum|powderblue|rebeccapurple|rosybrown|royalblue|saddlebrown|salmon|sandybrown|seagreen|seashell|sienna|skyblue|slateblue|slategray|slategrey|snow|springgreen|steelblue|tan|thistle|tomato|turquoise|violet|wheat|whitesmoke|yellowgreen)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.color.w3c-extended-color-keywords.less\\\"},{\\\"match\\\":\\\"\\\\\\\\b((?i)currentColor|transparent)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.color.w3c-special-color-keyword.less\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.constant.less\\\"}},\\\"match\\\":\\\"(#)(\\\\\\\\h{3}|\\\\\\\\h{4}|\\\\\\\\h{6}|\\\\\\\\h{8})\\\\\\\\b\\\",\\\"name\\\":\\\"constant.other.color.rgb-value.less\\\"},{\\\"include\\\":\\\"#relative-color\\\"}]},\\\"comma-delimiter\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.less\\\"}},\\\"match\\\":\\\"\\\\\\\\s*(,)\\\\\\\\s*\\\"},\\\"comment-block\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"/\\\\\\\\*\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.less\\\"}},\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.less\\\"}},\\\"name\\\":\\\"comment.block.less\\\"},{\\\"include\\\":\\\"#comment-line\\\"}]},\\\"comment-line\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.less\\\"}},\\\"match\\\":\\\"(//).*$\\\\\\\\n?\\\",\\\"name\\\":\\\"comment.line.double-slash.less\\\"},\\\"counter-functions\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\b(counter)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.filter.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-strings\\\"},{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#var-function\\\"},{\\\"match\\\":\\\"(?:--(?:[-\\\\\\\\w[^\\\\\\\\x00-\\\\\\\\x7F]]|\\\\\\\\\\\\\\\\(?:\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))+|-?(?:[_a-zA-Z[^\\\\\\\\x00-\\\\\\\\x7F]]|\\\\\\\\\\\\\\\\(?:\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))(?:[-\\\\\\\\w[^\\\\\\\\x00-\\\\\\\\x7F]]|\\\\\\\\\\\\\\\\(?:\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))*)\\\",\\\"name\\\":\\\"entity.other.counter-name.less\\\"},{\\\"begin\\\":\\\"(?=,)\\\",\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"match\\\":\\\"\\\\\\\\b((?i:arabic-indic|armenian|bengali|cambodian|circle|cjk-decimal|cjk-earthly-branch|cjk-heavenly-stem|decimal-leading-zero|decimal|devanagari|disclosure-closed|disclosure-open|disc|ethiopic-numeric|georgian|gujarati|gurmukhi|hebrew|hiragana-iroha|hiragana|japanese-formal|japanese-informal|kannada|katakana-iroha|katakana|khmer|korean-hangul-formal|korean-hanja-formal|korean-hanja-informal|lao|lower-alpha|lower-armenian|lower-greek|lower-latin|lower-roman|malayalam|mongolian|myanmar|oriya|persian|simp-chinese-formal|simp-chinese-informal|square|tamil|telugu|thai|tibetan|trad-chinese-formal|trad-chinese-informal|upper-alpha|upper-armenian|upper-latin|upper-roman)|none)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.counter-style.less\\\"}]}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(counters)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.filter.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(-?(?:[_a-zA-Z[^\\\\\\\\x00-\\\\\\\\x7F]]|\\\\\\\\\\\\\\\\(?:\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))(?:[-\\\\\\\\w[^\\\\\\\\x00-\\\\\\\\x7F]]|\\\\\\\\\\\\\\\\(?:\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))*)\\\",\\\"name\\\":\\\"entity.other.counter-name.less string.unquoted.less\\\"},{\\\"begin\\\":\\\"(?=,)\\\",\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-strings\\\"},{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#var-function\\\"},{\\\"include\\\":\\\"#literal-string\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"match\\\":\\\"\\\\\\\\b((?i:arabic-indic|armenian|bengali|cambodian|circle|cjk-decimal|cjk-earthly-branch|cjk-heavenly-stem|decimal-leading-zero|decimal|devanagari|disclosure-closed|disclosure-open|disc|ethiopic-numeric|georgian|gujarati|gurmukhi|hebrew|hiragana-iroha|hiragana|japanese-formal|japanese-informal|kannada|katakana-iroha|katakana|khmer|korean-hangul-formal|korean-hanja-formal|korean-hanja-informal|lao|lower-alpha|lower-armenian|lower-greek|lower-latin|lower-roman|malayalam|mongolian|myanmar|oriya|persian|simp-chinese-formal|simp-chinese-informal|square|tamil|telugu|thai|tibetan|trad-chinese-formal|trad-chinese-informal|upper-alpha|upper-armenian|upper-latin|upper-roman)|none)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.counter-style.less\\\"}]}]}]}]},\\\"cross-fade-function\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\b(cross-fade)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.image.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#percentage-type\\\"},{\\\"include\\\":\\\"#color-values\\\"},{\\\"include\\\":\\\"#image-type\\\"},{\\\"include\\\":\\\"#literal-string\\\"},{\\\"include\\\":\\\"#unquoted-string\\\"}]}]}]},\\\"cubic-bezier-function\\\":{\\\"begin\\\":\\\"\\\\\\\\b(cubic-bezier)(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.timing.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"contentName\\\":\\\"meta.group.less\\\",\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-functions\\\"},{\\\"include\\\":\\\"#calc-function\\\"},{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#var-function\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#number-type\\\"}]},\\\"custom-property-name\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.custom-property.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.type.custom-property.name.less\\\"}},\\\"match\\\":\\\"\\\\\\\\s*(--)((?:[-\\\\\\\\w[^\\\\\\\\x00-\\\\\\\\x7F]]|\\\\\\\\\\\\\\\\(?:\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))+)\\\",\\\"name\\\":\\\"support.type.custom-property.less\\\"},\\\"dimensions\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#angle-type\\\"},{\\\"include\\\":\\\"#frequency-type\\\"},{\\\"include\\\":\\\"#time-type\\\"},{\\\"include\\\":\\\"#percentage-type\\\"},{\\\"include\\\":\\\"#length-type\\\"}]},\\\"filter-function\\\":{\\\"begin\\\":\\\"\\\\\\\\b(filter)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.filter.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"name\\\":\\\"meta.group.less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#image-type\\\"},{\\\"include\\\":\\\"#literal-string\\\"},{\\\"include\\\":\\\"#filter-functions\\\"}]}]},\\\"filter-functions\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#less-functions\\\"},{\\\"begin\\\":\\\"\\\\\\\\b(blur)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.filter.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#length-type\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(brightness|contrast|grayscale|invert|opacity|saturate|sepia)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.filter.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#percentage-type\\\"},{\\\"include\\\":\\\"#number-type\\\"},{\\\"include\\\":\\\"#less-functions\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(drop-shadow)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.filter.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#length-type\\\"},{\\\"include\\\":\\\"#color-values\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(hue-rotate)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.filter.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#angle-type\\\"}]}]}]},\\\"fit-content-function\\\":{\\\"begin\\\":\\\"\\\\\\\\b(fit-content)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.grid.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#var-function\\\"},{\\\"include\\\":\\\"#calc-function\\\"},{\\\"include\\\":\\\"#percentage-type\\\"},{\\\"include\\\":\\\"#length-type\\\"}]}]},\\\"format-function\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\b(format)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.function.format.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#literal-string\\\"}]}]}]},\\\"frequency-type\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.unit.less\\\"}},\\\"match\\\":\\\"(?i:[-+]?(?:\\\\\\\\d*\\\\\\\\.\\\\\\\\d+(?:[eE][-+]?\\\\\\\\d+)*|[-+]?\\\\\\\\d+)(Hz|kHz))\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.less\\\"},\\\"global-property-values\\\":{\\\"match\\\":\\\"\\\\\\\\b(?:initial|inherit|unset|revert-layer|revert)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.less\\\"},\\\"gradient-functions\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\b((?:repeating-)?linear-gradient)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.gradient.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#var-function\\\"},{\\\"include\\\":\\\"#angle-type\\\"},{\\\"include\\\":\\\"#color-values\\\"},{\\\"include\\\":\\\"#percentage-type\\\"},{\\\"include\\\":\\\"#length-type\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"match\\\":\\\"\\\\\\\\bto\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.less\\\"},{\\\"match\\\":\\\"\\\\\\\\b(top|right|bottom|left)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.less\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b((?:repeating-)?radial-gradient)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.gradient.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#var-function\\\"},{\\\"include\\\":\\\"#color-values\\\"},{\\\"include\\\":\\\"#percentage-type\\\"},{\\\"include\\\":\\\"#length-type\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"match\\\":\\\"\\\\\\\\b(at|circle|ellipse)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.less\\\"},{\\\"match\\\":\\\"\\\\\\\\b(top|right|bottom|left|center|(farthest|closest)-(corner|side))\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.less\\\"}]}]}]},\\\"grid-repeat-function\\\":{\\\"begin\\\":\\\"\\\\\\\\b(repeat)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.grid.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#var-function\\\"},{\\\"include\\\":\\\"#length-type\\\"},{\\\"include\\\":\\\"#percentage-type\\\"},{\\\"include\\\":\\\"#minmax-function\\\"},{\\\"include\\\":\\\"#integer-type\\\"},{\\\"match\\\":\\\"\\\\\\\\b(auto-(fi(?:ll|t)))\\\\\\\\b\\\",\\\"name\\\":\\\"support.keyword.repetitions.less\\\"},{\\\"match\\\":\\\"\\\\\\\\b(((m(?:ax|in))-content)|auto)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.less\\\"}]}]},\\\"image-function\\\":{\\\"begin\\\":\\\"\\\\\\\\b(image)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.image.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#image-type\\\"},{\\\"include\\\":\\\"#literal-string\\\"},{\\\"include\\\":\\\"#color-values\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#unquoted-string\\\"}]}]},\\\"image-type\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#cross-fade-function\\\"},{\\\"include\\\":\\\"#gradient-functions\\\"},{\\\"include\\\":\\\"#image-function\\\"},{\\\"include\\\":\\\"#url-function\\\"}]},\\\"important\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.less\\\"}},\\\"match\\\":\\\"(!)\\\\\\\\s*important\\\",\\\"name\\\":\\\"keyword.other.important.less\\\"},\\\"integer-type\\\":{\\\"match\\\":\\\"[-+]?\\\\\\\\d+\\\",\\\"name\\\":\\\"constant.numeric.less\\\"},\\\"keyframe-name\\\":{\\\"begin\\\":\\\"\\\\\\\\s*(-?(?:[_a-z[^\\\\\\\\x00-\\\\\\\\x7F]]|(?:(:?\\\\\\\\\\\\\\\\[0-9a-f]{1,6}(\\\\\\\\r\\\\\\\\n|[\\\\\\\\s\\\\\\\\t\\\\\\\\r\\\\\\\\n\\\\\\\\f])?)|\\\\\\\\\\\\\\\\[^\\\\\\\\r\\\\\\\\n\\\\\\\\f0-9a-f]))(?:[_a-z0-9\\\\\\\\-[^\\\\\\\\x00-\\\\\\\\x7F]]|(?:(:?\\\\\\\\\\\\\\\\[0-9a-f]{1,6}(\\\\\\\\r\\\\\\\\n|[\\\\\\\\t\\\\\\\\r\\\\\\\\n\\\\\\\\f])?)|\\\\\\\\\\\\\\\\[^\\\\\\\\r\\\\\\\\n\\\\\\\\f0-9a-f]))*)?\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.constant.animation-name.less\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(?:(,)|(?=[{;]))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arbitrary-repetition.less\\\"}}},\\\"length-type\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.unit.less\\\"}},\\\"match\\\":\\\"[-+]?(?:\\\\\\\\d+\\\\\\\\.\\\\\\\\d+|\\\\\\\\.?\\\\\\\\d+)(?:[eE][-+]?\\\\\\\\d+)?(em|ex|ch|rem|vw|vh|vmin|vmax|cm|mm|[mq]|in|pt|pc|px|fr|dpi|dpcm|dppx|x)\\\",\\\"name\\\":\\\"constant.numeric.less\\\"},{\\\"match\\\":\\\"\\\\\\\\b[-+]?0\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.less\\\"}]},\\\"less-boolean-function\\\":{\\\"begin\\\":\\\"\\\\\\\\b(boolean)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.boolean.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-logical-comparisons\\\"}]}]},\\\"less-color-blend-functions\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\b(multiply|screen|overlay|(soft|hard)light|difference|exclusion|negation|average)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.color-blend.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#var-function\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#color-values\\\"}]}]}]},\\\"less-color-channel-functions\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\b(hue|saturation|lightness|hsv(hue|saturation|value)|red|green|blue|alpha|luma|luminance)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.color-definition.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#color-values\\\"}]}]}]},\\\"less-color-definition-functions\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\b(argb)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.color-definition.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#var-function\\\"},{\\\"include\\\":\\\"#color-values\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(hsva?)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.color.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#integer-type\\\"},{\\\"include\\\":\\\"#percentage-type\\\"},{\\\"include\\\":\\\"#number-type\\\"},{\\\"include\\\":\\\"#less-strings\\\"},{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#var-function\\\"},{\\\"include\\\":\\\"#calc-function\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"}]}]}]},\\\"less-color-functions\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#less-color-blend-functions\\\"},{\\\"include\\\":\\\"#less-color-channel-functions\\\"},{\\\"include\\\":\\\"#less-color-definition-functions\\\"},{\\\"include\\\":\\\"#less-color-operation-functions\\\"}]},\\\"less-color-operation-functions\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\b(fade|shade|tint)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.color-operation.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#color-values\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#percentage-type\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(spin)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.color-operation.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#color-values\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#number-type\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(((de)?saturate)|((light|dark)en)|(fade(in|out)))(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.color-operation.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#color-values\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#percentage-type\\\"},{\\\"match\\\":\\\"\\\\\\\\brelative\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.relative.less\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(contrast)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.color-operation.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#color-values\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#percentage-type\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(greyscale)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.color-operation.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#color-values\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(mix)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.color-operation.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#color-values\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#less-math\\\"},{\\\"include\\\":\\\"#percentage-type\\\"}]}]}]},\\\"less-extend\\\":{\\\"begin\\\":\\\"(:)(extend)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.entity.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.other.attribute-name.pseudo-class.extend.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\ball\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.all.less\\\"},{\\\"include\\\":\\\"#selectors\\\"}]}]},\\\"less-functions\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#less-boolean-function\\\"},{\\\"include\\\":\\\"#less-color-functions\\\"},{\\\"include\\\":\\\"#less-if-function\\\"},{\\\"include\\\":\\\"#less-list-functions\\\"},{\\\"include\\\":\\\"#less-math-functions\\\"},{\\\"include\\\":\\\"#less-misc-functions\\\"},{\\\"include\\\":\\\"#less-string-functions\\\"},{\\\"include\\\":\\\"#less-type-functions\\\"}]},\\\"less-if-function\\\":{\\\"begin\\\":\\\"\\\\\\\\b(if)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.if.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-mixin-guards\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#property-values\\\"}]}]},\\\"less-list-functions\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\b(length)(?=\\\\\\\\()\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.length.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#property-values\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(extract)(?=\\\\\\\\()\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.extract.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#property-values\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#integer-type\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(range)(?=\\\\\\\\()\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.range.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#property-values\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#integer-type\\\"}]}]}]},\\\"less-logical-comparisons\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.logical.less\\\"}},\\\"match\\\":\\\"\\\\\\\\s*(=|(([<>])=?))\\\\\\\\s*\\\"},{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.group.less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-logical-comparisons\\\"}]},{\\\"match\\\":\\\"\\\\\\\\btrue|false\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.less\\\"},{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.separator.less\\\"},{\\\"include\\\":\\\"#property-values\\\"},{\\\"include\\\":\\\"#selectors\\\"},{\\\"include\\\":\\\"#unquoted-string\\\"}]},\\\"less-math\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"[-+*/]\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.less\\\"},{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.group.less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-math\\\"}]},{\\\"include\\\":\\\"#numeric-values\\\"},{\\\"include\\\":\\\"#less-variables\\\"}]},\\\"less-math-functions\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\b(ceil|floor|percentage|round|sqrt|abs|a?(sin|cos|tan))(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.math.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#numeric-values\\\"}]}]},{\\\"captures\\\":{\\\"2\\\":{\\\"name\\\":\\\"support.function.math.less\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"match\\\":\\\"((pi)(\\\\\\\\()(\\\\\\\\)))\\\",\\\"name\\\":\\\"meta.function-call.less\\\"},{\\\"begin\\\":\\\"\\\\\\\\b(pow|m(od|in|ax))(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.math.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#numeric-values\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"}]}]}]},\\\"less-misc-functions\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\b(color)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.color.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#literal-string\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(image-(size|width|height))(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.image.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#literal-string\\\"},{\\\"include\\\":\\\"#unquoted-string\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(convert|unit)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.convert.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#numeric-values\\\"},{\\\"include\\\":\\\"#literal-string\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"match\\\":\\\"(([cm])?m|in|p([tcx])|m?s|g?rad|deg|turn|%|r?em|ex|ch)\\\",\\\"name\\\":\\\"keyword.other.unit.less\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(data-uri)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.data-uri.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#literal-string\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.less\\\"}},\\\"match\\\":\\\"\\\\\\\\s*(,)\\\"}]}]},{\\\"captures\\\":{\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"match\\\":\\\"\\\\\\\\b(default(\\\\\\\\()(\\\\\\\\)))\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.default.less\\\"},{\\\"begin\\\":\\\"\\\\\\\\b(get-unit)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.get-unit.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#dimensions\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(svg-gradient)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.svg-gradient.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#angle-type\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#color-values\\\"},{\\\"include\\\":\\\"#percentage-type\\\"},{\\\"include\\\":\\\"#length-type\\\"},{\\\"match\\\":\\\"\\\\\\\\bto\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.less\\\"},{\\\"match\\\":\\\"\\\\\\\\b(top|right|bottom|left|center)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.less\\\"},{\\\"match\\\":\\\"\\\\\\\\b(at|circle|ellipse)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.less\\\"}]}]}]},\\\"less-mixin-guards\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\s*(and|not|or)?\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.logical.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"name\\\":\\\"meta.group.less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-variable-comparison\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.group.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"match\\\":\\\"default((\\\\\\\\()(\\\\\\\\)))\\\",\\\"name\\\":\\\"support.function.default.less\\\"},{\\\"include\\\":\\\"#property-values\\\"},{\\\"include\\\":\\\"#less-logical-comparisons\\\"},{\\\"include\\\":\\\"$self\\\"}]}]}]},\\\"less-namespace-accessors\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?=\\\\\\\\s*when\\\\\\\\b)\\\",\\\"end\\\":\\\"\\\\\\\\s*(?:(,)|(?=[{;]))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.block.end.less\\\"}},\\\"name\\\":\\\"meta.conditional.guarded-namespace.less\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.conditional.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.less\\\"}},\\\"match\\\":\\\"\\\\\\\\s*(when)(?=.*?)\\\"},{\\\"include\\\":\\\"#less-mixin-guards\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"begin\\\":\\\"\\\\\\\\s*(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.property-list.begin.less\\\"}},\\\"end\\\":\\\"(?=})\\\",\\\"name\\\":\\\"meta.block.less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#rule-list-body\\\"}]},{\\\"include\\\":\\\"#selectors\\\"}]},{\\\"begin\\\":\\\"(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.terminator.rule.less\\\"}},\\\"name\\\":\\\"meta.group.less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-variable-assignment\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#property-values\\\"},{\\\"include\\\":\\\"#rule-list-body\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.terminator.rule.less\\\"}},\\\"match\\\":\\\"(;)|(?=[})])\\\"}]},\\\"less-string-functions\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\b(e(scape)?)(?=\\\\\\\\()\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.escape.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#literal-string\\\"},{\\\"include\\\":\\\"#unquoted-string\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\s*(%)(?=\\\\\\\\()\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.format.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#literal-string\\\"},{\\\"include\\\":\\\"#property-values\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(replace)(?=\\\\\\\\()\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.replace.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#literal-string\\\"},{\\\"include\\\":\\\"#property-values\\\"}]}]}]},\\\"less-strings\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(~)(['\\\\\\\"])\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.character.escape.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.less\\\"}},\\\"contentName\\\":\\\"markup.raw.inline.less\\\",\\\"end\\\":\\\"(['\\\\\\\"])|(\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.less\\\"}},\\\"name\\\":\\\"string.quoted.other.less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string-content\\\"}]}]},\\\"less-type-functions\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\b(is(number|string|color|keyword|url|pixel|em|percentage|ruleset))(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.type.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#property-values\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(isunit)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.type.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#property-values\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"match\\\":\\\"\\\\\\\\b((?i:em|ex|ch|rem)|(?i:v(?:[wh]|min|max))|(?i:cm|mm|q|in|pt|pc|px|fr)|(?i:deg|grad|rad|turn)|(?i:s|ms)|(?i:Hz|kHz)|(?i:dp(?:i|cm|px)))\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.unit.less\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(isdefined)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.type.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-variables\\\"}]}]}]},\\\"less-variable-assignment\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(@)(-?(?:[-\\\\\\\\w[^\\\\\\\\x00-\\\\\\\\x7F]]|\\\\\\\\\\\\\\\\(?:\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))(?:[-\\\\\\\\w[^\\\\\\\\x00-\\\\\\\\x7F]]|\\\\\\\\\\\\\\\\(?:\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))*)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"variable.other.readwrite.less\\\"},\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.variable.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.other.variable.less\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(;|(\\\\\\\\.{3})|(?=\\\\\\\\)))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.terminator.rule.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.spread.less\\\"}},\\\"name\\\":\\\"meta.property-value.less\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.key-value.less\\\"},\\\"4\\\":{\\\"name\\\":\\\"meta.property-value.less\\\"}},\\\"match\\\":\\\"(((\\\\\\\\+_?)?):)([\\\\\\\\s\\\\\\\\t]*)\\\"},{\\\"include\\\":\\\"#property-values\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#property-list\\\"},{\\\"include\\\":\\\"#unquoted-string\\\"}]}]},\\\"less-variable-comparison\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(@{1,2})(-?([_a-z[^\\\\\\\\x00-\\\\\\\\x7F]]|\\\\\\\\\\\\\\\\(?:\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))(?:[-\\\\\\\\w[^\\\\\\\\x00-\\\\\\\\x7F]]|\\\\\\\\\\\\\\\\(?:\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))*)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"variable.other.readwrite.less\\\"},\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.variable.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.other.variable.less\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(?=\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.terminator.rule.less\\\"}},\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.logical.less\\\"}},\\\"match\\\":\\\"\\\\\\\\s*(=|(([<>])=?))\\\\\\\\s*\\\"},{\\\"match\\\":\\\"\\\\\\\\btrue\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.less\\\"},{\\\"include\\\":\\\"#property-values\\\"},{\\\"include\\\":\\\"#selectors\\\"},{\\\"include\\\":\\\"#unquoted-string\\\"},{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.separator.less\\\"}]}]},\\\"less-variable-interpolation\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.variable.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.expression.less\\\"},\\\"3\\\":{\\\"name\\\":\\\"support.other.variable.less\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.expression.less\\\"}},\\\"match\\\":\\\"(@)(\\\\\\\\{)([-\\\\\\\\w]+)(})\\\",\\\"name\\\":\\\"variable.other.readwrite.less\\\"},\\\"less-variables\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.variable.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.other.variable.less\\\"}},\\\"match\\\":\\\"\\\\\\\\s*(@@?)([-\\\\\\\\w]+)\\\",\\\"name\\\":\\\"variable.other.readwrite.less\\\"},{\\\"include\\\":\\\"#less-variable-interpolation\\\"}]},\\\"literal-string\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"'\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.less\\\"}},\\\"end\\\":\\\"(')|(\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.less\\\"}},\\\"name\\\":\\\"string.quoted.single.less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string-content\\\"}]},{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.less\\\"}},\\\"end\\\":\\\"(\\\\\\\")|(\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.less\\\"}},\\\"name\\\":\\\"string.quoted.double.less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string-content\\\"}]},{\\\"include\\\":\\\"#less-strings\\\"}]},\\\"local-function\\\":{\\\"begin\\\":\\\"\\\\\\\\b(local)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.function.font-face.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#unquoted-string\\\"}]}]},\\\"media-query\\\":{\\\"begin\\\":\\\"\\\\\\\\s*(only|not)?\\\\\\\\s*(all|aural|braille|embossed|handheld|print|projection|screen|tty|tv)?\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.logic.media.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.constant.media.less\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(?:(,)|(?=[{;]))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arbitrary-repetition.less\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#custom-property-name\\\"},{\\\"begin\\\":\\\"\\\\\\\\s*(and)?\\\\\\\\s*(\\\\\\\\()\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.logic.media.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.group.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(--|-?(?:[a-zA-Z_·À-ÖØ-öø-ͽͿ-῿‌‍‿⁀⁰-↏Ⰰ-⿯、-퟿豈-﷏ﷰ-�𐀀-\\\\\\\\x{EFFFF}]|\\\\\\\\\\\\\\\\(?:\\\\\\\\N|\\\\\\\\H|\\\\\\\\h{1,6}[\\\\\\\\sR]))(?:[-\\\\\\\\da-zA-Z_·À-ÖØ-öø-ͽͿ-῿‌‍‿⁀⁰-↏Ⰰ-⿯、-퟿豈-﷏ﷰ-�𐀀-\\\\\\\\x{EFFFF}]|\\\\\\\\\\\\\\\\(?:\\\\\\\\N|\\\\\\\\H|\\\\\\\\h{1,6}[\\\\\\\\sR]))*)\\\\\\\\s*(?=[:)])\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.type.property-name.media.less\\\"}},\\\"end\\\":\\\"(((\\\\\\\\+_?)?):)|(?=\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.key-value.less\\\"}}},{\\\"match\\\":\\\"\\\\\\\\b(portrait|landscape|progressive|interlace)\\\",\\\"name\\\":\\\"support.constant.property-value.less\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.numeric.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.arithmetic.less\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.numeric.less\\\"}},\\\"match\\\":\\\"\\\\\\\\s*(\\\\\\\\d+)(/)(\\\\\\\\d+)\\\"},{\\\"include\\\":\\\"#less-math\\\"}]}]},\\\"media-query-list\\\":{\\\"begin\\\":\\\"\\\\\\\\s*(?=[^{;])\\\",\\\"end\\\":\\\"\\\\\\\\s*(?=[{;])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#media-query\\\"}]},\\\"minmax-function\\\":{\\\"begin\\\":\\\"\\\\\\\\b(minmax)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.grid.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#var-function\\\"},{\\\"include\\\":\\\"#length-type\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"match\\\":\\\"\\\\\\\\b(m(?:ax-content|in-content))\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.less\\\"}]}]},\\\"number-type\\\":{\\\"match\\\":\\\"[-+]?(?:\\\\\\\\d+\\\\\\\\.\\\\\\\\d+|\\\\\\\\.?\\\\\\\\d+)(?:[eE][-+]?\\\\\\\\d+)?\\\",\\\"name\\\":\\\"constant.numeric.less\\\"},\\\"numeric-values\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#dimensions\\\"},{\\\"include\\\":\\\"#percentage-type\\\"},{\\\"include\\\":\\\"#number-type\\\"}]},\\\"percentage-type\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.unit.less\\\"}},\\\"match\\\":\\\"[-+]?(?:\\\\\\\\d+\\\\\\\\.\\\\\\\\d+|\\\\\\\\.?\\\\\\\\d+)(?:[eE][-+]?\\\\\\\\d+)?(%)\\\",\\\"name\\\":\\\"constant.numeric.less\\\"},\\\"property-list\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?=(?=[^;]*)\\\\\\\\{)\\\",\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.block.end.less\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#rule-list\\\"}]}]},\\\"property-value-constants\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(flex-start|flex-end|start|end|space-between|space-around|space-evenly|stretch|baseline|safe|unsafe|legacy|anchor-center|first|last|self-start|self-end)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.less\\\"},{\\\"match\\\":\\\"\\\\\\\\b(text-before-edge|before-edge|middle|central|text-after-edge|after-edge|ideographic|alphabetic|hanging|mathematical|top|center|bottom)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.less\\\"},{\\\"include\\\":\\\"#global-property-values\\\"},{\\\"include\\\":\\\"#cubic-bezier-function\\\"},{\\\"include\\\":\\\"#steps-function\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?:replace|add|accumulate)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.less\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?:normal|alternate-reverse|alternate|reverse)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.less\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?:forwards|backwards|both)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.less\\\"},{\\\"match\\\":\\\"\\\\\\\\binfinite\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.less\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?:running|paused)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.less\\\"},{\\\"match\\\":\\\"\\\\\\\\be(?:ntry|xit)(?:-crossing|)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.less\\\"},{\\\"match\\\":\\\"\\\\\\\\b(linear|ease-in-out|ease-in|ease-out|ease|step-start|step-end)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.less\\\"},{\\\"match\\\":\\\"\\\\\\\\b(absolute|active|add|all-petite-caps|all-small-caps|all-scroll|all|alphabetic|alpha|alternate-reverse|alternate|always|annotation|antialiased|at|autohiding-scrollbar|auto|avoid-column|avoid-page|avoid-region|avoid|background-color|background-image|background-position|background-size|background-repeat|background|backwards|balance|baseline|below|bevel|bicubic|bidi-override|blink|block-line-height|block-start|block-end|block|blur|bolder|bold|border-top-left-radius|border-top-right-radius|border-bottom-left-radius|border-bottom-right-radius|border-end-end-radius|border-end-start-radius|border-start-end-radius|border-start-start-radius|border-block-start-color|border-block-start-style|border-block-start-width|border-block-start|border-block-end-color|border-block-end-style|border-block-end-width|border-block-end|border-block-color|border-block-style|border-block-width|border-block|border-inline-start-color|border-inline-start-style|border-inline-start-width|border-inline-start|border-inline-end-color|border-inline-end-style|border-inline-end-width|border-inline-end|border-inline-color|border-inline-style|border-inline-width|border-inline|border-top-color|border-top-style|border-top-width|border-top|border-right-color|border-right-style|border-right-width|border-right|border-bottom-color|border-bottom-style|border-bottom-width|border-bottom|border-left-color|border-left-style|border-left-width|border-left|border-image-outset|border-image-repeat|border-image-slice|border-image-source|border-image-width|border-image|border-color|border-style|border-width|border-radius|border-collapse|border-spacing|border|both|bottom|box-shadow|box|break-all|break-word|break-spaces|brightness|butt(on)?|capitalize|central|center|char(acter-variant)?|cjk-ideographic|clip|clone|close-quote|closest-corner|closest-side|col-resize|collapse|color-stop|color-burn|color-dodge|color|column-count|column-gap|column-reverse|column-rule-color|column-rule-width|column-rule|column-width|columns|column|common-ligatures|condensed|consider-shifts|contain|content-box|contents?|contextual|contrast|cover|crisp-edges|crispEdges|crop|crosshair|cross|darken|dashed|default|dense|device-width|diagonal-fractions|difference|disabled|discard|discretionary-ligatures|disregard-shifts|distribute-all-lines|distribute-letter|distribute-space|distribute|dotted|double|drop-shadow|[nsew]{1,4}-resize|ease-in-out|ease-in|ease-out|ease|element|ellipsis|embed|end|EndColorStr|evenodd|exclude-ruby|exclusion|expanded|extra-condensed|extra-expanded|farthest-corner|farthest-side|farthest|fill-box|fill-opacity|fill|filter|fit-content|fixed|flat|flex-basis|flex-end|flex-grow|flex-shrink|flex-start|flexbox|flex|flip|flood-color|font-size-adjust|font-size|font-stretch|font-weight|font|forwards|from-image|from|full-width|gap|geometricPrecision|glyphs|gradient|grayscale|grid-column-gap|grid-column|grid-row-gap|grid-row|grid-gap|grid-height|grid|groove|hand|hanging|hard-light|height|help|hidden|hide|historical-forms|historical-ligatures|horizontal-tb|horizontal|hue|ideographic|ideograph-alpha|ideograph-numeric|ideograph-parenthesis|ideograph-space|inactive|include-ruby|infinite|inherit|initial|inline-end|inline-size|inline-start|inline-table|inline-line-height|inline-flexbox|inline-flex|inline-box|inline-block|inline|inset|inside|inter-ideograph|inter-word|intersect|invert|isolate|isolation|italic|jis(04|78|83|90)|justify-all|justify|keep-all|larger|large|last|layout|left|letter-spacing|lighten|lighter|lighting-color|linear-gradient|linearRGB|linear|line-edge|line-height|line-through|line|lining-nums|list-item|local|loose|lowercase|lr-tb|ltr|luminosity|luminance|manual|manipulation|margin-bottom|margin-box|margin-left|margin-right|margin-top|margin|marker(-offset|s)?|match-parent|mathematical|max-(content|height|lines|size|width)|medium|middle|min-(content|height|width)|miter|mixed|move|multiply|newspaper|no-change|no-clip|no-close-quote|no-open-quote|no-common-ligatures|no-discretionary-ligatures|no-historical-ligatures|no-contextual|no-drop|no-repeat|none|nonzero|normal|not-allowed|nowrap|oblique|offset-after|offset-before|offset-end|offset-start|offset|oldstyle-nums|opacity|open-quote|optimize(Legibility|Precision|Quality|Speed)|order|ordinal|ornaments|outline-color|outline-offset|outline-width|outline|outset|outside|overline|over-edge|overlay|padding(-(?:bottom|box|left|right|top|box))?|page|paint(ed)?|paused|pan-(x|left|right|y|up|down)|perspective-origin|petite-caps|pixelated|pointer|pinch-zoom|pretty|pre(-(?:line|wrap))?|preserve-3d|preserve-breaks|preserve-spaces|preserve|progid:DXImageTransform\\\\\\\\.Microsoft\\\\\\\\.(Alpha|Blur|dropshadow|gradient|Shadow)|progress|proportional-nums|proportional-width|radial-gradient|recto|region|relative|repeating-linear-gradient|repeating-radial-gradient|repeat-x|repeat-y|repeat|replaced|reset-size|reverse|revert-layer|revert|ridge|right|round|row-gap|row-resize|row-reverse|row|rtl|ruby|running|saturate|saturation|screen|scrollbar|scroll-position|scroll|separate|sepia|scale-down|semi-condensed|semi-expanded|shape-image-threshold|shape-margin|shape-outside|show|sideways-lr|sideways-rl|sideways|simplified|size|slashed-zero|slice|small-caps|smaller|small|smooth|snap|solid|soft-light|space-around|space-between|space|span|sRGB|stable|stacked-fractions|stack|startColorStr|start|static|step-end|step-start|sticky|stop-color|stop-opacity|stretch|strict|stroke-box|stroke-dasharray|stroke-dashoffset|stroke-miterlimit|stroke-opacity|stroke-width|stroke|styleset|style|stylistic|subgrid|subpixel-antialiased|subtract|super|swash|table-caption|table-cell|table-column-group|table-footer-group|table-header-group|table-row-group|table-column|table-row|table|tabular-nums|tb-rl|text((-(?:bottom|(decoration|emphasis)-color|indent|(over|under)-edge|shadow|size(-adjust)?|top))|field)?|thick|thin|titling-caps|titling-case|top|touch|to|traditional|transform-origin|transform-style|transform|ultra-condensed|ultra-expanded|under-edge|underline|unicase|unset|uppercase|upright|use-glyph-orientation|use-script|verso|vertical(-(?:align|ideographic|lr|rl|text))?|view-box|viewport-fill-opacity|viewport-fill|visibility|visibleFill|visiblePainted|visibleStroke|visible|wait|wavy|weight|whitespace|width|word-spacing|wrap-reverse|wrap-reverse|wrap|xx?-(large|small)|z-index|zero|zoom-in|zoom-out|zoom|arabic-indic|armenian|bengali|cambodian|circle|cjk-decimal|cjk-earthly-branch|cjk-heavenly-stem|decimal-leading-zero|decimal|devanagari|disclosure-closed|disclosure-open|disc|ethiopic-numeric|georgian|gujarati|gurmukhi|hebrew|hiragana-iroha|hiragana|japanese-formal|japanese-informal|kannada|katakana-iroha|katakana|khmer|korean-hangul-formal|korean-hanja-formal|korean-hanja-informal|lao|lower-alpha|lower-armenian|lower-greek|lower-latin|lower-roman|malayalam|mongolian|myanmar|oriya|persian|simp-chinese-formal|simp-chinese-informal|square|tamil|telugu|thai|tibetan|trad-chinese-formal|trad-chinese-informal|upper-alpha|upper-armenian|upper-latin|upper-roman)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.less\\\"},{\\\"match\\\":\\\"\\\\\\\\b(sans-serif|serif|monospace|fantasy|cursive)\\\\\\\\b(?=\\\\\\\\s*[;,\\\\\\\\n}])\\\",\\\"name\\\":\\\"support.constant.font-name.less\\\"}]},\\\"property-values\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comment-block\\\"},{\\\"include\\\":\\\"#builtin-functions\\\"},{\\\"include\\\":\\\"#color-functions\\\"},{\\\"include\\\":\\\"#less-functions\\\"},{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#unicode-range\\\"},{\\\"include\\\":\\\"#numeric-values\\\"},{\\\"include\\\":\\\"#color-values\\\"},{\\\"include\\\":\\\"#property-value-constants\\\"},{\\\"include\\\":\\\"#less-math\\\"},{\\\"include\\\":\\\"#literal-string\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#important\\\"}]},\\\"pseudo-selectors\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(:)(dir)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.entity.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"entity.other.attribute-name.pseudo-class.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"ltr|rtl\\\",\\\"name\\\":\\\"variable.parameter.dir.less\\\"},{\\\"include\\\":\\\"#less-variables\\\"}]}]},{\\\"begin\\\":\\\"(:)(lang)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.entity.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"entity.other.attribute-name.pseudo-class.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#literal-string\\\"},{\\\"include\\\":\\\"#unquoted-string\\\"}]}]},{\\\"begin\\\":\\\"(:)(not)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.entity.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"entity.other.attribute-name.pseudo-class.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#selectors\\\"}]}]},{\\\"begin\\\":\\\"(:)(nth(-last)?-(child|of-type))(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.entity.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.other.attribute-name.pseudo-class.less\\\"}},\\\"contentName\\\":\\\"meta.function-call.less\\\",\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"entity.other.attribute-name.pseudo-class.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"name\\\":\\\"meta.group.less\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(even|odd)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.pseudo-class.less\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.arithmetic.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.unit.less\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.operator.arithmetic.less\\\"}},\\\"match\\\":\\\"(?:([-+])?\\\\\\\\d+{0,1}(n)(\\\\\\\\s*([-+])\\\\\\\\s*\\\\\\\\d+)?|[-+]?\\\\\\\\s*\\\\\\\\d+)\\\",\\\"name\\\":\\\"constant.numeric.less\\\"},{\\\"include\\\":\\\"#less-math\\\"},{\\\"include\\\":\\\"#less-strings\\\"},{\\\"include\\\":\\\"#less-variable-interpolation\\\"}]}]},{\\\"begin\\\":\\\"(:)(host-context|host|has|is|not|where)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.entity.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"entity.other.attribute-name.pseudo-class.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#selectors\\\"}]}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.entity.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.other.attribute-name.pseudo-class.less\\\"}},\\\"match\\\":\\\"(:)(active|any-link|autofill|blank|buffering|checked|current|default|defined|disabled|empty|enabled|first-child|first-of-type|first|focus-visible|focus-within|focus|fullscreen|future|host|hover|in-range|indeterminate|invalid|last-child|last-of-type|left|local-link|link|modal|muted|only-child|only-of-type|optional|out-of-range|past|paused|picture-in-picture|placeholder-shown|playing|popover-open|read-only|read-write|required|right|root|scope|seeking|stalled|target-within|target|user-invalid|user-valid|valid|visited|volume-locked)\\\\\\\\b\\\",\\\"name\\\":\\\"meta.function-call.less\\\"},{\\\"begin\\\":\\\"(::?)(highlight|part|state)(?=\\\\\\\\s*(\\\\\\\\())\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.entity.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"entity.other.attribute-name.pseudo-element.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"--|-?(?:[a-zA-Z_·À-ÖØ-öø-ͽͿ-῿‌‍‿⁀⁰-↏Ⰰ-⿯、-퟿豈-﷏ﷰ-�𐀀-\\\\\\\\x{EFFFF}]|\\\\\\\\\\\\\\\\(?:\\\\\\\\N|\\\\\\\\H|\\\\\\\\h{1,6}[\\\\\\\\sR]))(?:[-\\\\\\\\da-zA-Z_·À-ÖØ-öø-ͽͿ-῿‌‍‿⁀⁰-↏Ⰰ-⿯、-퟿豈-﷏ﷰ-�𐀀-\\\\\\\\x{EFFFF}]|\\\\\\\\\\\\\\\\(?:\\\\\\\\N|\\\\\\\\H|\\\\\\\\h{1,6}[\\\\\\\\sR]))*\\\",\\\"name\\\":\\\"variable.parameter.less\\\"},{\\\"include\\\":\\\"#less-variables\\\"}]}]},{\\\"begin\\\":\\\"(::?)slotted(?=\\\\\\\\s*(\\\\\\\\())\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.entity.less\\\"}},\\\"contentName\\\":\\\"meta.function-call.less\\\",\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"entity.other.attribute-name.pseudo-element.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"name\\\":\\\"meta.group.less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#selectors\\\"}]}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.entity.less\\\"}},\\\"match\\\":\\\"(::?)(after|backdrop|before|cue|file-selector-button|first-letter|first-line|grammar-error|marker|placeholder|selection|spelling-error|target-text|view-transition-group|view-transition-image-pair|view-transition-new|view-transition-old|view-transition)\\\\\\\\b\\\",\\\"name\\\":\\\"entity.other.attribute-name.pseudo-element.less\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.entity.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"meta.namespace.vendor-prefix.less\\\"}},\\\"match\\\":\\\"(::?)(-\\\\\\\\w+-)(--|-?(?:[a-zA-Z_·À-ÖØ-öø-ͽͿ-῿‌‍‿⁀⁰-↏Ⰰ-⿯、-퟿豈-﷏ﷰ-�𐀀-\\\\\\\\x{EFFFF}]|\\\\\\\\\\\\\\\\(?:\\\\\\\\N|\\\\\\\\H|\\\\\\\\h{1,6}[\\\\\\\\sR]))(?:[-\\\\\\\\da-zA-Z_·À-ÖØ-öø-ͽͿ-῿‌‍‿⁀⁰-↏Ⰰ-⿯、-퟿豈-﷏ﷰ-�𐀀-\\\\\\\\x{EFFFF}]|\\\\\\\\\\\\\\\\(?:\\\\\\\\N|\\\\\\\\H|\\\\\\\\h{1,6}[\\\\\\\\sR]))*)\\\\\\\\b\\\",\\\"name\\\":\\\"entity.other.attribute-name.pseudo-element.less\\\"}]},\\\"qualified-name\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.constant.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.namespace.wildcard.less\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.namespace.less\\\"}},\\\"match\\\":\\\"(?:(-?(?:[-\\\\\\\\w[^\\\\\\\\x00-\\\\\\\\x7F]]|\\\\\\\\\\\\\\\\(?:\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))(?:[_a-zA-Z[^\\\\\\\\x00-\\\\\\\\x7F]]|\\\\\\\\\\\\\\\\(?:\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))*)|(\\\\\\\\*))?(\\\\\\\\|)(?!=)\\\"},\\\"regexp-function\\\":{\\\"begin\\\":\\\"\\\\\\\\b(regexp)(?=\\\\\\\\()\\\",\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"support.function.regexp.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#literal-string\\\"}]}]},\\\"relative-color\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"from\\\",\\\"name\\\":\\\"keyword.other.less\\\"},{\\\"match\\\":\\\"\\\\\\\\b[hslawbc]\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.less\\\"}]},\\\"rule-list\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.block.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\s*})\\\",\\\"name\\\":\\\"meta.property-list.less\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.terminator.rule.less\\\"}},\\\"match\\\":\\\"\\\\\\\\s*(;)|(?=[})])\\\"},{\\\"include\\\":\\\"#rule-list-body\\\"},{\\\"include\\\":\\\"#less-extend\\\"}]}]},\\\"rule-list-body\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comment-block\\\"},{\\\"include\\\":\\\"#comment-line\\\"},{\\\"include\\\":\\\"#at-rules\\\"},{\\\"include\\\":\\\"#less-variable-assignment\\\"},{\\\"begin\\\":\\\"(?=[-\\\\\\\\w]*?@\\\\\\\\{.*}[-\\\\\\\\w]*?\\\\\\\\s*:[^;{(]*(?=[;})]))\\\",\\\"end\\\":\\\"(?=\\\\\\\\s*(;)|(?=[})]))\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?=[^\\\\\\\\s:])\\\",\\\"end\\\":\\\"(?=(((\\\\\\\\+_?)?):)[\\\\\\\\s\\\\\\\\t]*)\\\",\\\"name\\\":\\\"support.type.property-name.less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-variable-interpolation\\\"}]},{\\\"begin\\\":\\\"(((\\\\\\\\+_?)?):)(?=[\\\\\\\\s\\\\\\\\t]*)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.key-value.less\\\"}},\\\"contentName\\\":\\\"support.type.property-name.less\\\",\\\"end\\\":\\\"(?=\\\\\\\\s*(;)|(?=[})]))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#property-values\\\"}]}]},{\\\"begin\\\":\\\"(?=[-a-z])\\\",\\\"end\\\":\\\"$|(?![-a-z])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#custom-property-name\\\"},{\\\"begin\\\":\\\"(-[\\\\\\\\w-]+?-)((?:[a-zA-Z_·À-ÖØ-öø-ͽͿ-῿‌‍‿⁀⁰-↏Ⰰ-⿯、-퟿豈-﷏ﷰ-�𐀀-\\\\\\\\x{EFFFF}]|\\\\\\\\\\\\\\\\(?:\\\\\\\\N|\\\\\\\\H|\\\\\\\\h{1,6}[\\\\\\\\sR]))(?:[-\\\\\\\\da-zA-Z_·À-ÖØ-öø-ͽͿ-῿‌‍‿⁀⁰-↏Ⰰ-⿯、-퟿豈-﷏ﷰ-�𐀀-\\\\\\\\x{EFFFF}]|\\\\\\\\\\\\\\\\(?:\\\\\\\\N|\\\\\\\\H|\\\\\\\\h{1,6}[\\\\\\\\sR]))*)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.type.property-name.less\\\"},\\\"1\\\":{\\\"name\\\":\\\"meta.namespace.vendor-prefix.less\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(;)|(?=[})])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.terminator.rule.less\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"(((\\\\\\\\+_?)?):)(?=[\\\\\\\\s\\\\\\\\t]*)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.key-value.less\\\"}},\\\"contentName\\\":\\\"meta.property-value.less\\\",\\\"end\\\":\\\"(?=\\\\\\\\s*(;)|(?=[})]))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#property-values\\\"},{\\\"match\\\":\\\"[\\\\\\\\w-]+\\\",\\\"name\\\":\\\"support.constant.property-value.less\\\"}]}]},{\\\"include\\\":\\\"#filter-function\\\"},{\\\"begin\\\":\\\"\\\\\\\\b(border((-(bottom|top)-(left|right))|((-(start|end)){2}))?-radius|(border-image(?!-)))\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.type.property-name.less\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(;)|(?=[})])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.terminator.rule.less\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"(((\\\\\\\\+_?)?):)(?=[\\\\\\\\s\\\\\\\\t]*)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.key-value.less\\\"}},\\\"contentName\\\":\\\"meta.property-value.less\\\",\\\"end\\\":\\\"(?=\\\\\\\\s*(;)|(?=[})]))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#value-separator\\\"},{\\\"include\\\":\\\"#property-values\\\"}]}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.custom-property.prefix.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.type.custom-property.name.less\\\"}},\\\"match\\\":\\\"\\\\\\\\b(var-)(-?(?:[-\\\\\\\\w[^\\\\\\\\x00-\\\\\\\\x{9F}]]|\\\\\\\\\\\\\\\\(?:\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))(?:[_a-zA-Z[^\\\\\\\\x00-\\\\\\\\x{9F}]]|\\\\\\\\\\\\\\\\(?:\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))*)(?=\\\\\\\\s)\\\",\\\"name\\\":\\\"invalid.deprecated.custom-property.less\\\"},{\\\"begin\\\":\\\"\\\\\\\\bfont(-family)?(?!-)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.type.property-name.less\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(;)|(?=[})])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.terminator.rule.less\\\"}},\\\"name\\\":\\\"meta.property-name.less\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.key-value.less\\\"},\\\"4\\\":{\\\"name\\\":\\\"meta.property-value.less\\\"}},\\\"match\\\":\\\"(((\\\\\\\\+_?)?):)([\\\\\\\\s\\\\\\\\t]*)\\\"},{\\\"include\\\":\\\"#property-values\\\"},{\\\"match\\\":\\\"-?(?:[_a-zA-Z[^\\\\\\\\x00-\\\\\\\\x{9F}]]|\\\\\\\\\\\\\\\\(?:\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))(?:[-\\\\\\\\w[^\\\\\\\\x00-\\\\\\\\x{9F}]]|\\\\\\\\\\\\\\\\(?:\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))*(\\\\\\\\s+-?(?:[_a-zA-Z[^\\\\\\\\x00-\\\\\\\\x{9F}]]|\\\\\\\\\\\\\\\\(?:\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))(?:[-\\\\\\\\w[^\\\\\\\\x00-\\\\\\\\x{9F}]]|\\\\\\\\\\\\\\\\(?:\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))*)*\\\",\\\"name\\\":\\\"string.unquoted.less\\\"},{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.separator.less\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\banimation-timeline\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.type.property-name.less\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(;)|(?=[})])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.terminator.rule.less\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"(((\\\\\\\\+_?)?):)(?=[\\\\\\\\s\\\\\\\\t]*)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.key-value.less\\\"}},\\\"contentName\\\":\\\"meta.property-value.less\\\",\\\"end\\\":\\\"(?=\\\\\\\\s*(;)|(?=[})]))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment-block\\\"},{\\\"include\\\":\\\"#custom-property-name\\\"},{\\\"include\\\":\\\"#scroll-function\\\"},{\\\"include\\\":\\\"#view-function\\\"},{\\\"include\\\":\\\"#property-values\\\"},{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#arbitrary-repetition\\\"},{\\\"include\\\":\\\"#important\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\banimation(?:-name)?(?=(?:\\\\\\\\+_?)?:)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.type.property-name.less\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(;)|(?=[})])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.terminator.rule.less\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"(((\\\\\\\\+_?)?):)(?=[\\\\\\\\s\\\\\\\\t]*)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.key-value.less\\\"}},\\\"contentName\\\":\\\"meta.property-value.less\\\",\\\"end\\\":\\\"(?=\\\\\\\\s*(;)|(?=[})]))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment-block\\\"},{\\\"include\\\":\\\"#builtin-functions\\\"},{\\\"include\\\":\\\"#less-functions\\\"},{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#numeric-values\\\"},{\\\"include\\\":\\\"#property-value-constants\\\"},{\\\"match\\\":\\\"-?(?:[_a-zA-Z[^\\\\\\\\x00-\\\\\\\\x7F]]|(?:(:?\\\\\\\\\\\\\\\\[0-9a-f]{1,6}(\\\\\\\\r\\\\\\\\n|[\\\\\\\\s\\\\\\\\t\\\\\\\\r\\\\\\\\n\\\\\\\\f])?)|\\\\\\\\\\\\\\\\[^\\\\\\\\r\\\\\\\\n\\\\\\\\f0-9a-f]))(?:[-_a-zA-Z0-9[^\\\\\\\\x00-\\\\\\\\x7F]]|(?:(:?\\\\\\\\\\\\\\\\[0-9a-f]{1,6}(\\\\\\\\r\\\\\\\\n|[\\\\\\\\t\\\\\\\\r\\\\\\\\n\\\\\\\\f])?)|\\\\\\\\\\\\\\\\[^\\\\\\\\r\\\\\\\\n\\\\\\\\f0-9a-f]))*\\\",\\\"name\\\":\\\"variable.other.constant.animation-name.less string.unquoted.less\\\"},{\\\"include\\\":\\\"#less-math\\\"},{\\\"include\\\":\\\"#arbitrary-repetition\\\"},{\\\"include\\\":\\\"#important\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(transition(-(property|duration|delay|timing-function))?)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.type.property-name.less\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(;)|(?=[})])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.terminator.rule.less\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"(((\\\\\\\\+_?)?):)(?=[\\\\\\\\s\\\\\\\\t]*)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.key-value.less\\\"}},\\\"contentName\\\":\\\"meta.property-value.less\\\",\\\"end\\\":\\\"(?=\\\\\\\\s*(;)|(?=[})]))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#time-type\\\"},{\\\"include\\\":\\\"#property-values\\\"},{\\\"include\\\":\\\"#cubic-bezier-function\\\"},{\\\"include\\\":\\\"#steps-function\\\"},{\\\"include\\\":\\\"#arbitrary-repetition\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(?:backdrop-)?filter\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.type.property-name.less\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(;)|(?=[})])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.terminator.rule.less\\\"}},\\\"name\\\":\\\"meta.property-name.less\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.key-value.less\\\"},\\\"4\\\":{\\\"name\\\":\\\"meta.property-value.less\\\"}},\\\"match\\\":\\\"(((\\\\\\\\+_?)?):)([\\\\\\\\s\\\\\\\\t]*)\\\"},{\\\"match\\\":\\\"\\\\\\\\b(inherit|initial|unset|none)\\\\\\\\b\\\",\\\"name\\\":\\\"meta.property-value.less\\\"},{\\\"include\\\":\\\"#filter-functions\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\bwill-change\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.type.property-name.less\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(;)|(?=[})])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.terminator.rule.less\\\"}},\\\"name\\\":\\\"meta.property-name.less\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.key-value.less\\\"},\\\"4\\\":{\\\"name\\\":\\\"meta.property-value.less\\\"}},\\\"match\\\":\\\"(((\\\\\\\\+_?)?):)([\\\\\\\\s\\\\\\\\t]*)\\\"},{\\\"match\\\":\\\"unset|initial|inherit|will-change|auto|scroll-position|contents\\\",\\\"name\\\":\\\"invalid.illegal.property-value.less\\\"},{\\\"match\\\":\\\"-?(?:[-\\\\\\\\w[^\\\\\\\\x00-\\\\\\\\x{9F}]]|\\\\\\\\\\\\\\\\(?:\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))(?:[_a-zA-Z[^\\\\\\\\x00-\\\\\\\\x{9F}]]|\\\\\\\\\\\\\\\\(?:\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))*\\\",\\\"name\\\":\\\"support.constant.property-value.less\\\"},{\\\"include\\\":\\\"#arbitrary-repetition\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\bcounter-(increment|(re)?set)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.type.property-name.less\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(;)|(?=[})])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.terminator.rule.less\\\"}},\\\"name\\\":\\\"meta.property-name.less\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.key-value.less\\\"},\\\"4\\\":{\\\"name\\\":\\\"meta.property-value.less\\\"}},\\\"match\\\":\\\"(((\\\\\\\\+_?)?):)([\\\\\\\\s\\\\\\\\t]*)\\\"},{\\\"match\\\":\\\"-?(?:[-\\\\\\\\w[^\\\\\\\\x00-\\\\\\\\x{9F}]]|\\\\\\\\\\\\\\\\(?:\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))(?:[_a-zA-Z[^\\\\\\\\x00-\\\\\\\\x{9F}]]|\\\\\\\\\\\\\\\\(?:\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))*\\\",\\\"name\\\":\\\"entity.name.constant.counter-name.less\\\"},{\\\"include\\\":\\\"#integer-type\\\"},{\\\"match\\\":\\\"unset|initial|inherit|auto\\\",\\\"name\\\":\\\"invalid.illegal.property-value.less\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\bcontainer(?:-name)?(?=\\\\\\\\s*?:)\\\",\\\"end\\\":\\\"\\\\\\\\s*(;)|(?=[})])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.terminator.rule.less\\\"}},\\\"name\\\":\\\"support.type.property-name.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(((\\\\\\\\+_?)?):)(?=[\\\\\\\\s\\\\\\\\t]*)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.key-value.less\\\"}},\\\"contentName\\\":\\\"meta.property-value.less\\\",\\\"end\\\":\\\"(?=\\\\\\\\s*(;)|(?=[})]))\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\bdefault\\\\\\\\b\\\",\\\"name\\\":\\\"invalid.illegal.property-value.less\\\"},{\\\"include\\\":\\\"#global-property-values\\\"},{\\\"include\\\":\\\"#custom-property-name\\\"},{\\\"contentName\\\":\\\"variable.other.constant.container-name.less\\\",\\\"match\\\":\\\"--|-?(?:[a-zA-Z_·À-ÖØ-öø-ͽͿ-῿‌‍‿⁀⁰-↏Ⰰ-⿯、-퟿豈-﷏ﷰ-�𐀀-\\\\\\\\x{EFFFF}]|\\\\\\\\\\\\\\\\(?:\\\\\\\\N|\\\\\\\\H|\\\\\\\\h{1,6}[\\\\\\\\sR]))(?:[-\\\\\\\\da-zA-Z_·À-ÖØ-öø-ͽͿ-῿‌‍‿⁀⁰-↏Ⰰ-⿯、-퟿豈-﷏ﷰ-�𐀀-\\\\\\\\x{EFFFF}]|\\\\\\\\\\\\\\\\(?:\\\\\\\\N|\\\\\\\\H|\\\\\\\\h{1,6}[\\\\\\\\sR]))*\\\",\\\"name\\\":\\\"support.constant.property-value.less\\\"},{\\\"include\\\":\\\"#property-values\\\"}]}]},{\\\"match\\\":\\\"\\\\\\\\b(accent-height|align-content|align-items|align-self|alignment-baseline|all|animation-timing-function|animation-range-start|animation-range-end|animation-range|animation-play-state|animation-name|animation-iteration-count|animation-fill-mode|animation-duration|animation-direction|animation-delay|animation-composition|animation|appearance|ascent|aspect-ratio|azimuth|backface-visibility|background-size|background-repeat-y|background-repeat-x|background-repeat|background-position-y|background-position-x|background-position|background-origin|background-image|background-color|background-clip|background-blend-mode|background-attachment|background|baseline-shift|begin|bias|blend-mode|border-top-left-radius|border-top-right-radius|border-bottom-left-radius|border-bottom-right-radius|border-end-end-radius|border-end-start-radius|border-start-end-radius|border-start-start-radius|border-block-start-color|border-block-start-style|border-block-start-width|border-block-start|border-block-end-color|border-block-end-style|border-block-end-width|border-block-end|border-block-color|border-block-style|border-block-width|border-block|border-inline-start-color|border-inline-start-style|border-inline-start-width|border-inline-start|border-inline-end-color|border-inline-end-style|border-inline-end-width|border-inline-end|border-inline-color|border-inline-style|border-inline-width|border-inline|border-top-color|border-top-style|border-top-width|border-top|border-right-color|border-right-style|border-right-width|border-right|border-bottom-color|border-bottom-style|border-bottom-width|border-bottom|border-left-color|border-left-style|border-left-width|border-left|border-image-outset|border-image-repeat|border-image-slice|border-image-source|border-image-width|border-image|border-color|border-style|border-width|border-radius|border-collapse|border-spacing|border|bottom|box-(align|decoration-break|direction|flex|ordinal-group|orient|pack|shadow|sizing)|break-(after|before|inside)|caption-side|clear|clip-path|clip-rule|clip|color(-(interpolation(-filters)?|profile|rendering))?|columns|column-(break-before|count|fill|gap|(rule(-(color|style|width))?)|span|width)|container-name|container-type|container|contain-intrinsic-block-size|contain-intrinsic-inline-size|contain-intrinsic-height|contain-intrinsic-size|contain-intrinsic-width|contain|content|counter-(increment|reset)|cursor|[cdf][xy]|direction|display|divisor|dominant-baseline|dur|elevation|empty-cells|enable-background|end|fallback|fill(-(opacity|rule))?|filter|flex(-(align|basis|direction|flow|grow|item-align|line-pack|negative|order|pack|positive|preferred-size|shrink|wrap))?|float|flood-(color|opacity)|font-display|font-family|font-feature-settings|font-kerning|font-language-override|font-size(-adjust)?|font-smoothing|font-stretch|font-style|font-synthesis|font-variant(-(alternates|caps|east-asian|ligatures|numeric|position))?|font-weight|font|fr|((column|row)-)?gap|glyph-orientation-(horizontal|vertical)|grid-(area|gap)|grid-auto-(columns|flow|rows)|grid-(column|row)(-(end|gap|start))?|grid-template(-(areas|columns|rows))?|grid|height|hyphens|image-(orientation|rendering|resolution)|inset(-(block|inline))?(-(start|end))?|isolation|justify-content|justify-items|justify-self|kerning|left|letter-spacing|lighting-color|line-(box-contain|break|clamp|height)|list-style(-(image|position|type))?|(margin|padding)(-(bottom|left|right|top)|(-(block|inline)?(-(end|start))?))?|marker(-(end|mid|start))?|mask(-(clip||composite|image|origin|position|repeat|size|type))?|(m(?:ax|in))-(height|width)|mix-blend-mode|nbsp-mode|negative|object-(fit|position)|opacity|operator|order|orphans|outline(-(color|offset|style|width))?|overflow(-((inline|block)|scrolling|wrap|[xy]))?|overscroll-behavior(-(?:block|(inline|[xy])))?|pad(ding(-(bottom|left|right|top))?)?|page(-break-(after|before|inside))?|paint-order|pause(-(after|before))?|perspective(-origin(-([xy]))?)?|pitch(-range)?|place-content|place-self|pointer-events|position|prefix|quotes|range|resize|right|rotate|scale|scroll-behavior|shape-(image-threshold|margin|outside|rendering)|size|speak(-as)?|src|stop-(color|opacity)|stroke(-(dash(array|offset)|line(cap|join)|miterlimit|opacity|width))?|suffix|symbols|system|tab-size|table-layout|tap-highlight-color|text-align(-last)?|text-decoration(-(color|line|style))?|text-emphasis(-(color|position|style))?|text-(anchor|fill-color|height|indent|justify|orientation|overflow|rendering|size-adjust|shadow|transform|underline-position|wrap)|top|touch-action|transform(-origin(-([xy]))?)|transform(-style)?|transition(-(delay|duration|property|timing-function))?|translate|unicode-(bidi|range)|user-(drag|select)|vertical-align|visibility|white-space(-collapse)?|widows|width|will-change|word-(break|spacing|wrap)|writing-mode|z-index|zoom)\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.property-name.less\\\"},{\\\"match\\\":\\\"\\\\\\\\b(((contain-intrinsic|max|min)-)?(block|inline)?-size)\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.property-name.less\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\b((?:\\\\\\\\+_?)?:)([\\\\\\\\s\\\\\\\\t]*)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.key-value.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"meta.property-value.less\\\"}},\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.key-value.less\\\"},\\\"4\\\":{\\\"name\\\":\\\"meta.property-value.less\\\"}},\\\"contentName\\\":\\\"meta.property-value.less\\\",\\\"end\\\":\\\"\\\\\\\\s*(;)|(?=[})])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.terminator.rule.less\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#property-values\\\"}]},{\\\"include\\\":\\\"$self\\\"}]},\\\"scroll-function\\\":{\\\"begin\\\":\\\"\\\\\\\\b(scroll)(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.scroll.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"root|nearest|self\\\",\\\"name\\\":\\\"support.constant.scroller.less\\\"},{\\\"match\\\":\\\"block|inline|[xy]\\\",\\\"name\\\":\\\"support.constant.axis.less\\\"},{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#var-function\\\"}]},\\\"selector\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?=[>~+/.*#a-zA-Z\\\\\\\\[\\\\\\\\&]|(:{1,2}\\\\\\\\S)|@\\\\\\\\{)\\\",\\\"contentName\\\":\\\"meta.selector.less\\\",\\\"end\\\":\\\"(?=@(?!\\\\\\\\{)|[{;])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment-line\\\"},{\\\"include\\\":\\\"#selectors\\\"},{\\\"include\\\":\\\"#less-namespace-accessors\\\"},{\\\"include\\\":\\\"#less-variable-interpolation\\\"},{\\\"include\\\":\\\"#important\\\"}]}]},\\\"selectors\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b([a-z](?:[-_a-z0-9·]|\\\\\\\\\\\\\\\\\\\\\\\\.|[À-ÖØ-öø-˿̀-ͽͿ-῿‌-‍‿-⁀⁰-↏Ⰰ-⿯、-퟿豈-﷏ﷰ-�𐀀-\\\\\\\\x{EFFFF}])*-(?:[-_a-z0-9·]|\\\\\\\\\\\\\\\\\\\\\\\\.|[À-ÖØ-öø-˿̀-ͽͿ-῿‌-‍‿-⁀⁰-↏Ⰰ-⿯、-퟿豈-﷏ﷰ-�𐀀-\\\\\\\\x{EFFFF}])*)\\\\\\\\b\\\",\\\"name\\\":\\\"entity.name.tag.custom.less\\\"},{\\\"match\\\":\\\"\\\\\\\\b(a|abbr|acronym|address|applet|area|article|aside|audio|b|base|basefont|bdi|bdo|big|blockquote|body|br|button|canvas|caption|circle|cite|clipPath|code|col|colgroup|content|data|dataList|dd|defs|del|details|dfn|dialog|dir|div|dl|dt|element|ellipse|em|embed|eventsource|fieldset|figcaption|figure|filter|footer|foreignObject|form|frame|frameset|g|glyph|glyphRef|h1|h2|h3|h4|h5|h6|head|header|hgroup|hr|html|i|iframe|image|img|input|ins|isindex|kbd|keygen|label|legend|li|line|linearGradient|link|main|map|mark|marker|mask|menu|meta|meter|nav|noframes|noscript|object|ol|optgroup|option|output|p|param|path|pattern|picture|polygon|polyline|pre|progress|q|radialGradient|rect|rp|ruby|rt|rtc|s|samp|script|section|select|shadow|small|source|span|stop|strike|strong|style|sub|summary|sup|svg|switch|symbol|table|tbody|td|template|textarea|textPath|tfoot|th|thead|time|title|tr|track|tref|tspan|tt|u|ul|use|var|video|wbr|xmp)\\\\\\\\b\\\",\\\"name\\\":\\\"entity.name.tag.less\\\"},{\\\"begin\\\":\\\"(\\\\\\\\.)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.entity.less\\\"}},\\\"end\\\":\\\"(?![-\\\\\\\\w[^\\\\\\\\x00-\\\\\\\\x{9F}]]|\\\\\\\\\\\\\\\\(\\\\\\\\h{1,6} ?|\\\\\\\\H)|(@(?=\\\\\\\\{)))\\\",\\\"name\\\":\\\"entity.other.attribute-name.class.less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-variable-interpolation\\\"}]},{\\\"begin\\\":\\\"(#)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.entity.less\\\"}},\\\"end\\\":\\\"(?![-\\\\\\\\w[^\\\\\\\\x00-\\\\\\\\x{9F}]]|\\\\\\\\\\\\\\\\(\\\\\\\\h{1,6} ?|\\\\\\\\H)|(@(?=\\\\\\\\{)))\\\",\\\"name\\\":\\\"entity.other.attribute-name.id.less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-variable-interpolation\\\"}]},{\\\"begin\\\":\\\"(&)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.entity.less\\\"}},\\\"contentName\\\":\\\"entity.other.attribute-name.parent.less\\\",\\\"end\\\":\\\"(?![-\\\\\\\\w[^\\\\\\\\x00-\\\\\\\\x{9F}]]|\\\\\\\\\\\\\\\\(\\\\\\\\h{1,6} ?|\\\\\\\\H)|(@(?=\\\\\\\\{)))\\\",\\\"name\\\":\\\"entity.other.attribute-name.parent.less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-variable-interpolation\\\"},{\\\"include\\\":\\\"#selectors\\\"}]},{\\\"include\\\":\\\"#pseudo-selectors\\\"},{\\\"include\\\":\\\"#less-extend\\\"},{\\\"match\\\":\\\"(?!\\\\\\\\+_?:)(?:>{1,3}|[~+])(?![>~+;}])\\\",\\\"name\\\":\\\"punctuation.separator.combinator.less\\\"},{\\\"match\\\":\\\"((?:>{1,3}|[~+])){2,}\\\",\\\"name\\\":\\\"invalid.illegal.combinator.less\\\"},{\\\"match\\\":\\\"/deep/\\\",\\\"name\\\":\\\"invalid.illegal.combinator.less\\\"},{\\\"begin\\\":\\\"\\\\\\\\[\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.braces.begin.less\\\"}},\\\"end\\\":\\\"]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.braces.end.less\\\"}},\\\"name\\\":\\\"meta.attribute-selector.less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-variable-interpolation\\\"},{\\\"include\\\":\\\"#qualified-name\\\"},{\\\"match\\\":\\\"(-?(?:[_a-zA-Z[^\\\\\\\\x00-\\\\\\\\x7F]]|\\\\\\\\\\\\\\\\(?:\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))(?:[-\\\\\\\\w[^\\\\\\\\x00-\\\\\\\\x7F]]|\\\\\\\\\\\\\\\\(?:\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))*)\\\",\\\"name\\\":\\\"entity.other.attribute-name.less\\\"},{\\\"begin\\\":\\\"\\\\\\\\s*([~*|^$]?=)\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.attribute-selector.less\\\"}},\\\"end\\\":\\\"(?=([\\\\\\\\s\\\\\\\\]]))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-variable-interpolation\\\"},{\\\"match\\\":\\\"[^\\\\\\\\s\\\\\\\\]\\\\\\\\['\\\\\\\"]\\\",\\\"name\\\":\\\"string.unquoted.less\\\"},{\\\"include\\\":\\\"#literal-string\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.less\\\"}},\\\"match\\\":\\\"(?:\\\\\\\\s+([iI]))?\\\"},{\\\"match\\\":\\\"]\\\",\\\"name\\\":\\\"punctuation.definition.entity.less\\\"}]}]},{\\\"include\\\":\\\"#arbitrary-repetition\\\"},{\\\"match\\\":\\\"\\\\\\\\*\\\",\\\"name\\\":\\\"entity.name.tag.wildcard.less\\\"}]},\\\"shape-functions\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\b(rect)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.function.shape.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\bauto\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.less\\\"},{\\\"include\\\":\\\"#length-type\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(inset)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.function.shape.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\bround\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.less\\\"},{\\\"include\\\":\\\"#length-type\\\"},{\\\"include\\\":\\\"#percentage-type\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(circle|ellipse)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.function.shape.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\bat\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.less\\\"},{\\\"match\\\":\\\"\\\\\\\\b(top|right|bottom|left|center|closest-side|farthest-side)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.less\\\"},{\\\"include\\\":\\\"#length-type\\\"},{\\\"include\\\":\\\"#percentage-type\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(polygon)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.function.shape.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(nonzero|evenodd)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.less\\\"},{\\\"include\\\":\\\"#length-type\\\"},{\\\"include\\\":\\\"#percentage-type\\\"}]}]}]},\\\"steps-function\\\":{\\\"begin\\\":\\\"\\\\\\\\b(steps)(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.timing.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"contentName\\\":\\\"meta.group.less\\\",\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"jump-start|jump-end|jump-none|jump-both|start|end\\\",\\\"name\\\":\\\"support.constant.step-position.less\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#integer-type\\\"},{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#var-function\\\"},{\\\"include\\\":\\\"#calc-function\\\"}]},\\\"string-content\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#less-variable-interpolation\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n\\\",\\\"name\\\":\\\"constant.character.escape.newline.less\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\(\\\\\\\\h{1,6}|.)\\\",\\\"name\\\":\\\"constant.character.escape.less\\\"}]},\\\"style-function\\\":{\\\"begin\\\":\\\"\\\\\\\\b(style)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.function.style.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#rule-list-body\\\"}]}]},\\\"symbols-function\\\":{\\\"begin\\\":\\\"\\\\\\\\b(symbols)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.counter.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(cyclic|numeric|alphabetic|symbolic|fixed)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.symbol-type.less\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#literal-string\\\"},{\\\"include\\\":\\\"#image-type\\\"}]}]},\\\"time-type\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.unit.less\\\"}},\\\"match\\\":\\\"(?i:[-+]?(?:\\\\\\\\d*\\\\\\\\.\\\\\\\\d+(?:[eE][-+]?\\\\\\\\d+)*|[-+]?\\\\\\\\d+)(s|ms))\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.less\\\"},\\\"transform-functions\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\b((?:matrix|scale)(?:3d|))(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.function.transform.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#number-type\\\"},{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#var-function\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(translate(3d)?)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.function.transform.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#percentage-type\\\"},{\\\"include\\\":\\\"#length-type\\\"},{\\\"include\\\":\\\"#number-type\\\"},{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#var-function\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(translate[XY])(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.function.transform.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#percentage-type\\\"},{\\\"include\\\":\\\"#length-type\\\"},{\\\"include\\\":\\\"#number-type\\\"},{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#var-function\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(rotate[XYZ]?|skew[XY])(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.function.transform.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#angle-type\\\"},{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#calc-function\\\"},{\\\"include\\\":\\\"#var-function\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(skew)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.function.transform.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#angle-type\\\"},{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#calc-function\\\"},{\\\"include\\\":\\\"#var-function\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(translateZ|perspective)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.function.transform.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#length-type\\\"},{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#calc-function\\\"},{\\\"include\\\":\\\"#var-function\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(rotate3d)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.function.transform.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#angle-type\\\"},{\\\"include\\\":\\\"#number-type\\\"},{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#calc-function\\\"},{\\\"include\\\":\\\"#var-function\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(scale[XYZ])(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.function.transform.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#number-type\\\"},{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#calc-function\\\"},{\\\"include\\\":\\\"#var-function\\\"}]}]}]},\\\"unicode-range\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.constant.unicode-range.prefix.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.codepoint-range.less\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.section.range.less\\\"}},\\\"match\\\":\\\"(?i)(u\\\\\\\\+)([0-9a-f?]{1,6}(?:(-)[0-9a-f]{1,6})?)\\\",\\\"name\\\":\\\"support.unicode-range.less\\\"},\\\"unquoted-string\\\":{\\\"match\\\":\\\"[^\\\\\\\\s'\\\\\\\"]\\\",\\\"name\\\":\\\"string.unquoted.less\\\"},\\\"url-function\\\":{\\\"begin\\\":\\\"\\\\\\\\b(url)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.url.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#literal-string\\\"},{\\\"include\\\":\\\"#unquoted-string\\\"},{\\\"include\\\":\\\"#var-function\\\"}]}]},\\\"value-separator\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.less\\\"}},\\\"match\\\":\\\"\\\\\\\\s*(/)\\\\\\\\s*\\\"},\\\"var-function\\\":{\\\"begin\\\":\\\"\\\\\\\\b(var)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.var.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#custom-property-name\\\"},{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#property-values\\\"}]}]},\\\"view-function\\\":{\\\"begin\\\":\\\"\\\\\\\\b(view)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.view.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"block|inline|[xy]|auto\\\",\\\"name\\\":\\\"support.constant.property-value.less\\\"},{\\\"include\\\":\\\"#percentage-type\\\"},{\\\"include\\\":\\\"#length-type\\\"},{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#var-function\\\"},{\\\"include\\\":\\\"#calc-function\\\"},{\\\"include\\\":\\\"#arbitrary-repetition\\\"}]}]}},\\\"scopeName\\\":\\\"source.css.less\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/less.mjs\n"));

/***/ })

}]);