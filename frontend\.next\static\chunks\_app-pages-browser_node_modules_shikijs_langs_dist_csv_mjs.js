"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_csv_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/csv.mjs":
/*!**************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/csv.mjs ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"CSV\\\",\\\"fileTypes\\\":[\\\"csv\\\"],\\\"name\\\":\\\"csv\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"rainbow1\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.rainbow2\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.function.rainbow3\\\"},\\\"4\\\":{\\\"name\\\":\\\"comment.rainbow4\\\"},\\\"5\\\":{\\\"name\\\":\\\"string.rainbow5\\\"},\\\"6\\\":{\\\"name\\\":\\\"variable.parameter.rainbow6\\\"},\\\"7\\\":{\\\"name\\\":\\\"constant.numeric.rainbow7\\\"},\\\"8\\\":{\\\"name\\\":\\\"entity.name.type.rainbow8\\\"},\\\"9\\\":{\\\"name\\\":\\\"markup.bold.rainbow9\\\"},\\\"10\\\":{\\\"name\\\":\\\"invalid.rainbow10\\\"}},\\\"match\\\":\\\"( *\\\\\\\"(?:[^\\\\\\\"]*\\\\\\\"\\\\\\\")*[^\\\\\\\"]*\\\\\\\" *(?:,|$)|[^,]*(?:,|$))?( *\\\\\\\"(?:[^\\\\\\\"]*\\\\\\\"\\\\\\\")*[^\\\\\\\"]*\\\\\\\" *(?:,|$)|[^,]*(?:,|$))?( *\\\\\\\"(?:[^\\\\\\\"]*\\\\\\\"\\\\\\\")*[^\\\\\\\"]*\\\\\\\" *(?:,|$)|[^,]*(?:,|$))?( *\\\\\\\"(?:[^\\\\\\\"]*\\\\\\\"\\\\\\\")*[^\\\\\\\"]*\\\\\\\" *(?:,|$)|[^,]*(?:,|$))?( *\\\\\\\"(?:[^\\\\\\\"]*\\\\\\\"\\\\\\\")*[^\\\\\\\"]*\\\\\\\" *(?:,|$)|[^,]*(?:,|$))?( *\\\\\\\"(?:[^\\\\\\\"]*\\\\\\\"\\\\\\\")*[^\\\\\\\"]*\\\\\\\" *(?:,|$)|[^,]*(?:,|$))?( *\\\\\\\"(?:[^\\\\\\\"]*\\\\\\\"\\\\\\\")*[^\\\\\\\"]*\\\\\\\" *(?:,|$)|[^,]*(?:,|$))?( *\\\\\\\"(?:[^\\\\\\\"]*\\\\\\\"\\\\\\\")*[^\\\\\\\"]*\\\\\\\" *(?:,|$)|[^,]*(?:,|$))?( *\\\\\\\"(?:[^\\\\\\\"]*\\\\\\\"\\\\\\\")*[^\\\\\\\"]*\\\\\\\" *(?:,|$)|[^,]*(?:,|$))?( *\\\\\\\"(?:[^\\\\\\\"]*\\\\\\\"\\\\\\\")*[^\\\\\\\"]*\\\\\\\" *(?:,|$)|[^,]*(?:,|$))?\\\",\\\"name\\\":\\\"rainbowgroup\\\"}],\\\"scopeName\\\":\\\"text.csv\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/csv.mjs\n"));

/***/ })

}]);