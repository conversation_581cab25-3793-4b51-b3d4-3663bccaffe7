"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_typespec_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/typespec.mjs":
/*!*******************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/typespec.mjs ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"TypeSpec\\\",\\\"fileTypes\\\":[\\\"tsp\\\"],\\\"name\\\":\\\"typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#statement\\\"}],\\\"repository\\\":{\\\"alias-id\\\":{\\\"begin\\\":\\\"(=)\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.assignment.tsp\\\"}},\\\"end\\\":\\\"(?=[,;@)}]|\\\\\\\\bextern\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.alias-id.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},\\\"alias-statement\\\":{\\\"begin\\\":\\\"\\\\\\\\b(alias)\\\\\\\\b\\\\\\\\s+(\\\\\\\\b[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\b|`(?:[^`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*`)\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.tsp\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.type.tsp\\\"}},\\\"end\\\":\\\"(?=[,;@)}]|\\\\\\\\bextern\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.alias-statement.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#alias-id\\\"},{\\\"include\\\":\\\"#type-parameters\\\"}]},\\\"augment-decorator-statement\\\":{\\\"begin\\\":\\\"((@@)\\\\\\\\b[_$[:alpha:]](?:[_$[:alnum:]]|\\\\\\\\.[_$[:alpha:]])*\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.tag.tsp\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.tag.tsp\\\"}},\\\"end\\\":\\\"(?=([_$[:alpha:]`]))|(?=[,;@)}]|\\\\\\\\bextern\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.augment-decorator-statement.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#parenthesized-expression\\\"}]},\\\"block-comment\\\":{\\\"begin\\\":\\\"/\\\\\\\\*\\\",\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"name\\\":\\\"comment.block.tsp\\\"},\\\"boolean-literal\\\":{\\\"match\\\":\\\"\\\\\\\\b(true|false)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.tsp\\\"},\\\"callExpression\\\":{\\\"begin\\\":\\\"(\\\\\\\\b[_$[:alpha:]](?:[_$[:alnum:]]|\\\\\\\\.[_$[:alpha:]])*\\\\\\\\b)\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.tsp\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parenthesis.open.tsp\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.parenthesis.close.tsp\\\"}},\\\"name\\\":\\\"meta.callExpression.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#expression\\\"},{\\\"include\\\":\\\"#punctuation-comma\\\"}]},\\\"const-statement\\\":{\\\"begin\\\":\\\"\\\\\\\\b(const)\\\\\\\\b\\\\\\\\s+(\\\\\\\\b[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\b|`(?:[^`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*`)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.tsp\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.name.tsp\\\"}},\\\"end\\\":\\\"(?=[,;@)}]|\\\\\\\\bextern\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.const-statement.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type-annotation\\\"},{\\\"include\\\":\\\"#operator-assignment\\\"},{\\\"include\\\":\\\"#expression\\\"}]},\\\"decorator\\\":{\\\"begin\\\":\\\"((@)\\\\\\\\b[_$[:alpha:]](?:[_$[:alnum:]]|\\\\\\\\.[_$[:alpha:]])*\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.tag.tsp\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.tag.tsp\\\"}},\\\"end\\\":\\\"(?=([_$[:alpha:]`]))|(?=[,;@)}]|\\\\\\\\bextern\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.decorator.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#parenthesized-expression\\\"}]},\\\"decorator-declaration-statement\\\":{\\\"begin\\\":\\\"(?:(extern)\\\\\\\\s+)?\\\\\\\\b(dec)\\\\\\\\b\\\\\\\\s+(\\\\\\\\b[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\b|`(?:[^`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*`)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.tsp\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.tsp\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.function.tsp\\\"}},\\\"end\\\":\\\"(?=[,;@)}]|\\\\\\\\bextern\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.decorator-declaration-statement.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#operation-parameters\\\"}]},\\\"directive\\\":{\\\"begin\\\":\\\"\\\\\\\\s*(#\\\\\\\\b[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.directive.name.tsp\\\"}},\\\"end\\\":\\\"$|(?=[,;@)}]|\\\\\\\\bextern\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.directive.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string-literal\\\"},{\\\"include\\\":\\\"#identifier-expression\\\"}]},\\\"doc-comment\\\":{\\\"begin\\\":\\\"/\\\\\\\\*\\\\\\\\*\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"comment.block.tsp\\\"}},\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"comment.block.tsp\\\"}},\\\"name\\\":\\\"comment.block.tsp\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#doc-comment-block\\\"}]},\\\"doc-comment-block\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#doc-comment-param\\\"},{\\\"include\\\":\\\"#doc-comment-return-tag\\\"},{\\\"include\\\":\\\"#doc-comment-unknown-tag\\\"}]},\\\"doc-comment-param\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.tag.tspdoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.tag.tspdoc\\\"},\\\"3\\\":{\\\"name\\\":\\\"variable.name.tsp\\\"}},\\\"match\\\":\\\"((@)(?:param|template|prop))\\\\\\\\s+(\\\\\\\\b[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\b|`(?:[^`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*`)\\\\\\\\b\\\",\\\"name\\\":\\\"comment.block.tsp\\\"},\\\"doc-comment-return-tag\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.tag.tspdoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.tag.tspdoc\\\"}},\\\"match\\\":\\\"((@)returns)\\\\\\\\b\\\",\\\"name\\\":\\\"comment.block.tsp\\\"},\\\"doc-comment-unknown-tag\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.tag.tsp\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.tag.tsp\\\"}},\\\"match\\\":\\\"((@)(?:\\\\\\\\b[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\b|`(?:[^`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*`))\\\\\\\\b\\\",\\\"name\\\":\\\"comment.block.tsp\\\"},\\\"enum-body\\\":{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.curlybrace.open.tsp\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.curlybrace.close.tsp\\\"}},\\\"name\\\":\\\"meta.enum-body.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#enum-member\\\"},{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#directive\\\"},{\\\"include\\\":\\\"#decorator\\\"},{\\\"include\\\":\\\"#punctuation-comma\\\"}]},\\\"enum-member\\\":{\\\"begin\\\":\\\"(\\\\\\\\b[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\b|`(?:[^`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*`)\\\\\\\\s*(:?)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.name.tsp\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.type.annotation.tsp\\\"}},\\\"end\\\":\\\"(?=[,;@)}]|\\\\\\\\bextern\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.enum-member.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#type-annotation\\\"}]},\\\"enum-statement\\\":{\\\"begin\\\":\\\"\\\\\\\\b(enum)\\\\\\\\b\\\\\\\\s+(\\\\\\\\b[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\b|`(?:[^`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*`)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.tsp\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.type.tsp\\\"}},\\\"end\\\":\\\"(?<=})|(?=[,;@)}]|\\\\\\\\bextern\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.enum-statement.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#enum-body\\\"}]},\\\"escape-character\\\":{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"constant.character.escape.tsp\\\"},\\\"expression\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#directive\\\"},{\\\"include\\\":\\\"#parenthesized-expression\\\"},{\\\"include\\\":\\\"#valueof\\\"},{\\\"include\\\":\\\"#typeof\\\"},{\\\"include\\\":\\\"#type-arguments\\\"},{\\\"include\\\":\\\"#object-literal\\\"},{\\\"include\\\":\\\"#tuple-literal\\\"},{\\\"include\\\":\\\"#tuple-expression\\\"},{\\\"include\\\":\\\"#model-expression\\\"},{\\\"include\\\":\\\"#callExpression\\\"},{\\\"include\\\":\\\"#identifier-expression\\\"}]},\\\"function-declaration-statement\\\":{\\\"begin\\\":\\\"(?:(extern)\\\\\\\\s+)?\\\\\\\\b(fn)\\\\\\\\b\\\\\\\\s+(\\\\\\\\b[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\b|`(?:[^`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*`)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.tsp\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.tsp\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.function.tsp\\\"}},\\\"end\\\":\\\"(?=[,;@)}]|\\\\\\\\bextern\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.function-declaration-statement.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#operation-parameters\\\"},{\\\"include\\\":\\\"#type-annotation\\\"}]},\\\"identifier-expression\\\":{\\\"match\\\":\\\"\\\\\\\\b[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\b|`(?:[^`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*`\\\",\\\"name\\\":\\\"entity.name.type.tsp\\\"},\\\"import-statement\\\":{\\\"begin\\\":\\\"\\\\\\\\b(import)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.tsp\\\"}},\\\"end\\\":\\\"(?=[,;@)}]|\\\\\\\\bextern\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.import-statement.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#token\\\"}]},\\\"interface-body\\\":{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.curlybrace.open.tsp\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.curlybrace.close.tsp\\\"}},\\\"name\\\":\\\"meta.interface-body.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#directive\\\"},{\\\"include\\\":\\\"#decorator\\\"},{\\\"include\\\":\\\"#interface-member\\\"},{\\\"include\\\":\\\"#punctuation-semicolon\\\"}]},\\\"interface-heritage\\\":{\\\"begin\\\":\\\"\\\\\\\\b(extends)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.tsp\\\"}},\\\"end\\\":\\\"((?=\\\\\\\\{)|(?=[;@)}]|\\\\\\\\bextern\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b))\\\",\\\"name\\\":\\\"meta.interface-heritage.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"},{\\\"include\\\":\\\"#punctuation-comma\\\"}]},\\\"interface-member\\\":{\\\"begin\\\":\\\"(?:\\\\\\\\b(op)\\\\\\\\b\\\\\\\\s+)?(\\\\\\\\b[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\b|`(?:[^`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*`)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.tsp\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.tsp\\\"}},\\\"end\\\":\\\"(?=[,;@)}]|\\\\\\\\bextern\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.interface-member.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#operation-signature\\\"}]},\\\"interface-statement\\\":{\\\"begin\\\":\\\"\\\\\\\\b(interface)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.tsp\\\"}},\\\"end\\\":\\\"(?<=})|(?=[,;@)}]|\\\\\\\\bextern\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.interface-statement.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#type-parameters\\\"},{\\\"include\\\":\\\"#interface-heritage\\\"},{\\\"include\\\":\\\"#interface-body\\\"},{\\\"include\\\":\\\"#expression\\\"}]},\\\"line-comment\\\":{\\\"match\\\":\\\"//.*$\\\",\\\"name\\\":\\\"comment.line.double-slash.tsp\\\"},\\\"model-expression\\\":{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.curlybrace.open.tsp\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.curlybrace.close.tsp\\\"}},\\\"name\\\":\\\"meta.model-expression.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#model-property\\\"},{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#directive\\\"},{\\\"include\\\":\\\"#decorator\\\"},{\\\"include\\\":\\\"#spread-operator\\\"},{\\\"include\\\":\\\"#punctuation-semicolon\\\"}]},\\\"model-heritage\\\":{\\\"begin\\\":\\\"\\\\\\\\b(extends|is)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.tsp\\\"}},\\\"end\\\":\\\"((?=\\\\\\\\{)|(?=[;@)}]|\\\\\\\\bextern\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b))\\\",\\\"name\\\":\\\"meta.model-heritage.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"},{\\\"include\\\":\\\"#punctuation-comma\\\"}]},\\\"model-property\\\":{\\\"begin\\\":\\\"(?:(\\\\\\\\b[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\b|`(?:[^`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*`)|(\\\\\\\"(?:[^\\\\\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\"))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.name.tsp\\\"},\\\"2\\\":{\\\"name\\\":\\\"string.quoted.double.tsp\\\"}},\\\"end\\\":\\\"(?=[,;@)}]|\\\\\\\\bextern\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.model-property.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#type-annotation\\\"},{\\\"include\\\":\\\"#operator-assignment\\\"},{\\\"include\\\":\\\"#expression\\\"}]},\\\"model-statement\\\":{\\\"begin\\\":\\\"\\\\\\\\b(model)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.tsp\\\"}},\\\"end\\\":\\\"(?<=})|(?=[,;@)}]|\\\\\\\\bextern\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.model-statement.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#type-parameters\\\"},{\\\"include\\\":\\\"#model-heritage\\\"},{\\\"include\\\":\\\"#expression\\\"}]},\\\"namespace-body\\\":{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.curlybrace.open.tsp\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.curlybrace.close.tsp\\\"}},\\\"name\\\":\\\"meta.namespace-body.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#statement\\\"}]},\\\"namespace-name\\\":{\\\"begin\\\":\\\"(?=([_$[:alpha:]`]))\\\",\\\"end\\\":\\\"((?=\\\\\\\\{)|(?=[,;@)}]|\\\\\\\\bextern\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b))\\\",\\\"name\\\":\\\"meta.namespace-name.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#identifier-expression\\\"},{\\\"include\\\":\\\"#punctuation-accessor\\\"}]},\\\"namespace-statement\\\":{\\\"begin\\\":\\\"\\\\\\\\b(namespace)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.tsp\\\"}},\\\"end\\\":\\\"((?<=})|(?=[,;@)}]|\\\\\\\\bextern\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b))\\\",\\\"name\\\":\\\"meta.namespace-statement.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#namespace-name\\\"},{\\\"include\\\":\\\"#namespace-body\\\"}]},\\\"numeric-literal\\\":{\\\"match\\\":\\\"(?:\\\\\\\\b(?<!\\\\\\\\$)0[xX]\\\\\\\\h[_\\\\\\\\h]*(n)?\\\\\\\\b(?!\\\\\\\\$)|\\\\\\\\b(?<!\\\\\\\\$)0[bB][01][01_]*(n)?\\\\\\\\b(?!\\\\\\\\$)|(?<!\\\\\\\\$)(?:\\\\\\\\b[0-9][0-9_]*(\\\\\\\\.)[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*(n)?\\\\\\\\b|\\\\\\\\b[0-9][0-9_]*(\\\\\\\\.)[eE][+-]?[0-9][0-9_]*(n)?\\\\\\\\b|\\\\\\\\B(\\\\\\\\.)[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*(n)?\\\\\\\\b|\\\\\\\\b[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*(n)?\\\\\\\\b|\\\\\\\\b[0-9][0-9_]*(\\\\\\\\.)[0-9][0-9_]*(n)?\\\\\\\\b|\\\\\\\\b[0-9][0-9_]*(\\\\\\\\.)(n)?\\\\\\\\B|\\\\\\\\B(\\\\\\\\.)[0-9][0-9_]*(n)?\\\\\\\\b|\\\\\\\\b[0-9][0-9_]*(n)?\\\\\\\\b(?!\\\\\\\\.))(?!\\\\\\\\$))\\\",\\\"name\\\":\\\"constant.numeric.tsp\\\"},\\\"object-literal\\\":{\\\"begin\\\":\\\"#\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.hashcurlybrace.open.tsp\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.curlybrace.close.tsp\\\"}},\\\"name\\\":\\\"meta.object-literal.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#object-literal-property\\\"},{\\\"include\\\":\\\"#directive\\\"},{\\\"include\\\":\\\"#spread-operator\\\"},{\\\"include\\\":\\\"#punctuation-comma\\\"}]},\\\"object-literal-property\\\":{\\\"begin\\\":\\\"(\\\\\\\\b[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\b|`(?:[^`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*`)\\\\\\\\s*(:)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.name.tsp\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.type.annotation.tsp\\\"}},\\\"end\\\":\\\"(?=[,;@)}]|\\\\\\\\bextern\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.object-literal-property.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#expression\\\"}]},\\\"operation-heritage\\\":{\\\"begin\\\":\\\"\\\\\\\\b(is)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.tsp\\\"}},\\\"end\\\":\\\"(?=[,;@)}]|\\\\\\\\bextern\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.operation-heritage.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},\\\"operation-parameters\\\":{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.parenthesis.open.tsp\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.parenthesis.close.tsp\\\"}},\\\"name\\\":\\\"meta.operation-parameters.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#decorator\\\"},{\\\"include\\\":\\\"#model-property\\\"},{\\\"include\\\":\\\"#spread-operator\\\"},{\\\"include\\\":\\\"#punctuation-comma\\\"}]},\\\"operation-signature\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-parameters\\\"},{\\\"include\\\":\\\"#operation-heritage\\\"},{\\\"include\\\":\\\"#operation-parameters\\\"},{\\\"include\\\":\\\"#type-annotation\\\"}]},\\\"operation-statement\\\":{\\\"begin\\\":\\\"\\\\\\\\b(op)\\\\\\\\b\\\\\\\\s+(\\\\\\\\b[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\b|`(?:[^`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*`)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.tsp\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.tsp\\\"}},\\\"end\\\":\\\"(?=[,;@)}]|\\\\\\\\bextern\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.operation-statement.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#operation-signature\\\"}]},\\\"operator-assignment\\\":{\\\"match\\\":\\\"=\\\",\\\"name\\\":\\\"keyword.operator.assignment.tsp\\\"},\\\"parenthesized-expression\\\":{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.parenthesis.open.tsp\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.parenthesis.close.tsp\\\"}},\\\"name\\\":\\\"meta.parenthesized-expression.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"},{\\\"include\\\":\\\"#punctuation-comma\\\"}]},\\\"punctuation-accessor\\\":{\\\"match\\\":\\\"\\\\\\\\.\\\",\\\"name\\\":\\\"punctuation.accessor.tsp\\\"},\\\"punctuation-comma\\\":{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.comma.tsp\\\"},\\\"punctuation-semicolon\\\":{\\\"match\\\":\\\";\\\",\\\"name\\\":\\\"punctuation.terminator.statement.tsp\\\"},\\\"scalar-body\\\":{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.curlybrace.open.tsp\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.curlybrace.close.tsp\\\"}},\\\"name\\\":\\\"meta.scalar-body.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#directive\\\"},{\\\"include\\\":\\\"#scalar-constructor\\\"},{\\\"include\\\":\\\"#punctuation-semicolon\\\"}]},\\\"scalar-constructor\\\":{\\\"begin\\\":\\\"\\\\\\\\b(init)\\\\\\\\b\\\\\\\\s+(\\\\\\\\b[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\b|`(?:[^`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*`)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.tsp\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.tsp\\\"}},\\\"end\\\":\\\"(?=[,;@)}]|\\\\\\\\bextern\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.scalar-constructor.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#operation-parameters\\\"}]},\\\"scalar-extends\\\":{\\\"begin\\\":\\\"\\\\\\\\b(extends)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.tsp\\\"}},\\\"end\\\":\\\"(?=[;@)}]|\\\\\\\\bextern\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.scalar-extends.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"},{\\\"include\\\":\\\"#punctuation-comma\\\"}]},\\\"scalar-statement\\\":{\\\"begin\\\":\\\"\\\\\\\\b(scalar)\\\\\\\\b\\\\\\\\s+(\\\\\\\\b[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\b|`(?:[^`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*`)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.tsp\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.type.tsp\\\"}},\\\"end\\\":\\\"(?<=})|(?=[,;@)}]|\\\\\\\\bextern\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.scalar-statement.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#type-parameters\\\"},{\\\"include\\\":\\\"#scalar-extends\\\"},{\\\"include\\\":\\\"#scalar-body\\\"}]},\\\"spread-operator\\\":{\\\"begin\\\":\\\"\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.spread.tsp\\\"}},\\\"end\\\":\\\"(?=[,;@)}]|\\\\\\\\bextern\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.spread-operator.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},\\\"statement\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#directive\\\"},{\\\"include\\\":\\\"#augment-decorator-statement\\\"},{\\\"include\\\":\\\"#decorator\\\"},{\\\"include\\\":\\\"#model-statement\\\"},{\\\"include\\\":\\\"#scalar-statement\\\"},{\\\"include\\\":\\\"#union-statement\\\"},{\\\"include\\\":\\\"#interface-statement\\\"},{\\\"include\\\":\\\"#enum-statement\\\"},{\\\"include\\\":\\\"#alias-statement\\\"},{\\\"include\\\":\\\"#const-statement\\\"},{\\\"include\\\":\\\"#namespace-statement\\\"},{\\\"include\\\":\\\"#operation-statement\\\"},{\\\"include\\\":\\\"#import-statement\\\"},{\\\"include\\\":\\\"#using-statement\\\"},{\\\"include\\\":\\\"#decorator-declaration-statement\\\"},{\\\"include\\\":\\\"#function-declaration-statement\\\"},{\\\"include\\\":\\\"#punctuation-semicolon\\\"}]},\\\"string-literal\\\":{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"end\\\":\\\"\\\\\\\"|$\\\",\\\"name\\\":\\\"string.quoted.double.tsp\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#template-expression\\\"},{\\\"include\\\":\\\"#escape-character\\\"}]},\\\"template-expression\\\":{\\\"begin\\\":\\\"\\\\\\\\$\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.template-expression.begin.tsp\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.template-expression.end.tsp\\\"}},\\\"name\\\":\\\"meta.template-expression.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},\\\"token\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#doc-comment\\\"},{\\\"include\\\":\\\"#line-comment\\\"},{\\\"include\\\":\\\"#block-comment\\\"},{\\\"include\\\":\\\"#triple-quoted-string-literal\\\"},{\\\"include\\\":\\\"#string-literal\\\"},{\\\"include\\\":\\\"#boolean-literal\\\"},{\\\"include\\\":\\\"#numeric-literal\\\"}]},\\\"triple-quoted-string-literal\\\":{\\\"begin\\\":\\\"\\\\\\\"\\\\\\\"\\\\\\\"\\\",\\\"end\\\":\\\"\\\\\\\"\\\\\\\"\\\\\\\"\\\",\\\"name\\\":\\\"string.quoted.triple.tsp\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#template-expression\\\"},{\\\"include\\\":\\\"#escape-character\\\"}]},\\\"tuple-expression\\\":{\\\"begin\\\":\\\"\\\\\\\\[\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.squarebracket.open.tsp\\\"}},\\\"end\\\":\\\"]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.squarebracket.close.tsp\\\"}},\\\"name\\\":\\\"meta.tuple-expression.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},\\\"tuple-literal\\\":{\\\"begin\\\":\\\"#\\\\\\\\[\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.hashsquarebracket.open.tsp\\\"}},\\\"end\\\":\\\"]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.squarebracket.close.tsp\\\"}},\\\"name\\\":\\\"meta.tuple-literal.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"},{\\\"include\\\":\\\"#punctuation-comma\\\"}]},\\\"type-annotation\\\":{\\\"begin\\\":\\\"\\\\\\\\s*(\\\\\\\\??)\\\\\\\\s*(:)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.optional.tsp\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.type.annotation.tsp\\\"}},\\\"end\\\":\\\"(?=[,;@)}=]|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.type-annotation.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},\\\"type-argument\\\":{\\\"begin\\\":\\\"(\\\\\\\\b[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\b|`(?:[^`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*`)\\\\\\\\s*(=)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.type.tsp\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.assignment.tsp\\\"}},\\\"end\\\":\\\"(?=>)|(?=[,;@)}]|\\\\\\\\bextern\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.assignment.tsp\\\"}},\\\"name\\\":\\\"meta.type-argument.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#expression\\\"},{\\\"include\\\":\\\"#punctuation-comma\\\"}]},\\\"type-arguments\\\":{\\\"begin\\\":\\\"<\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.typeparameters.begin.tsp\\\"}},\\\"end\\\":\\\">\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.typeparameters.end.tsp\\\"}},\\\"name\\\":\\\"meta.type-arguments.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type-argument\\\"},{\\\"include\\\":\\\"#expression\\\"},{\\\"include\\\":\\\"#punctuation-comma\\\"}]},\\\"type-parameter\\\":{\\\"begin\\\":\\\"(\\\\\\\\b[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\b|`(?:[^`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*`)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.type.tsp\\\"}},\\\"end\\\":\\\"(?=>)|(?=[,;@)}]|\\\\\\\\bextern\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.type-parameter.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#type-parameter-constraint\\\"},{\\\"include\\\":\\\"#type-parameter-default\\\"}]},\\\"type-parameter-constraint\\\":{\\\"begin\\\":\\\"extends\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.other.tsp\\\"}},\\\"end\\\":\\\"(?=>)|(?=[,;@)}]|\\\\\\\\bextern\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.type-parameter-constraint.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},\\\"type-parameter-default\\\":{\\\"begin\\\":\\\"=\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.assignment.tsp\\\"}},\\\"end\\\":\\\"(?=>)|(?=[,;@)}]|\\\\\\\\bextern\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.type-parameter-default.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},\\\"type-parameters\\\":{\\\"begin\\\":\\\"<\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.typeparameters.begin.tsp\\\"}},\\\"end\\\":\\\">\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.typeparameters.end.tsp\\\"}},\\\"name\\\":\\\"meta.type-parameters.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type-parameter\\\"},{\\\"include\\\":\\\"#punctuation-comma\\\"}]},\\\"typeof\\\":{\\\"begin\\\":\\\"\\\\\\\\b(typeof)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.tsp\\\"}},\\\"end\\\":\\\"(?=>)|(?=[,;@)}]|\\\\\\\\bextern\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.typeof.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},\\\"union-body\\\":{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.curlybrace.open.tsp\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.curlybrace.close.tsp\\\"}},\\\"name\\\":\\\"meta.union-body.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#union-variant\\\"},{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#directive\\\"},{\\\"include\\\":\\\"#decorator\\\"},{\\\"include\\\":\\\"#expression\\\"},{\\\"include\\\":\\\"#punctuation-comma\\\"}]},\\\"union-statement\\\":{\\\"begin\\\":\\\"\\\\\\\\b(union)\\\\\\\\b\\\\\\\\s+(\\\\\\\\b[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\b|`(?:[^`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*`)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.tsp\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.type.tsp\\\"}},\\\"end\\\":\\\"(?<=})|(?=[,;@)}]|\\\\\\\\bextern\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.union-statement.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#union-body\\\"}]},\\\"union-variant\\\":{\\\"begin\\\":\\\"(\\\\\\\\b[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\b|`(?:[^`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*`)\\\\\\\\s*(:)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.name.tsp\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.type.annotation.tsp\\\"}},\\\"end\\\":\\\"(?=[,;@)}]|\\\\\\\\bextern\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.union-variant.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#expression\\\"}]},\\\"using-statement\\\":{\\\"begin\\\":\\\"\\\\\\\\b(using)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.tsp\\\"}},\\\"end\\\":\\\"(?=[,;@)}]|\\\\\\\\bextern\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.using-statement.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#identifier-expression\\\"},{\\\"include\\\":\\\"#punctuation-accessor\\\"}]},\\\"valueof\\\":{\\\"begin\\\":\\\"\\\\\\\\b(valueof)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.tsp\\\"}},\\\"end\\\":\\\"(?=>)|(?=[,;@)}]|\\\\\\\\bextern\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.valueof.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]}},\\\"scopeName\\\":\\\"source.tsp\\\",\\\"aliases\\\":[\\\"tsp\\\"]}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/typespec.mjs\n"));

/***/ })

}]);