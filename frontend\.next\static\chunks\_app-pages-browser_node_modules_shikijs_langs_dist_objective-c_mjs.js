"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_objective-c_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/objective-c.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/objective-c.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Objective-C\\\",\\\"name\\\":\\\"objective-c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#anonymous_pattern_1\\\"},{\\\"include\\\":\\\"#anonymous_pattern_2\\\"},{\\\"include\\\":\\\"#anonymous_pattern_3\\\"},{\\\"include\\\":\\\"#anonymous_pattern_4\\\"},{\\\"include\\\":\\\"#anonymous_pattern_5\\\"},{\\\"include\\\":\\\"#apple_foundation_functional_macros\\\"},{\\\"include\\\":\\\"#anonymous_pattern_7\\\"},{\\\"include\\\":\\\"#anonymous_pattern_8\\\"},{\\\"include\\\":\\\"#anonymous_pattern_9\\\"},{\\\"include\\\":\\\"#anonymous_pattern_10\\\"},{\\\"include\\\":\\\"#anonymous_pattern_11\\\"},{\\\"include\\\":\\\"#anonymous_pattern_12\\\"},{\\\"include\\\":\\\"#anonymous_pattern_13\\\"},{\\\"include\\\":\\\"#anonymous_pattern_14\\\"},{\\\"include\\\":\\\"#anonymous_pattern_15\\\"},{\\\"include\\\":\\\"#anonymous_pattern_16\\\"},{\\\"include\\\":\\\"#anonymous_pattern_17\\\"},{\\\"include\\\":\\\"#anonymous_pattern_18\\\"},{\\\"include\\\":\\\"#anonymous_pattern_19\\\"},{\\\"include\\\":\\\"#anonymous_pattern_20\\\"},{\\\"include\\\":\\\"#anonymous_pattern_21\\\"},{\\\"include\\\":\\\"#anonymous_pattern_22\\\"},{\\\"include\\\":\\\"#anonymous_pattern_23\\\"},{\\\"include\\\":\\\"#anonymous_pattern_24\\\"},{\\\"include\\\":\\\"#anonymous_pattern_25\\\"},{\\\"include\\\":\\\"#anonymous_pattern_26\\\"},{\\\"include\\\":\\\"#anonymous_pattern_27\\\"},{\\\"include\\\":\\\"#anonymous_pattern_28\\\"},{\\\"include\\\":\\\"#anonymous_pattern_29\\\"},{\\\"include\\\":\\\"#anonymous_pattern_30\\\"},{\\\"include\\\":\\\"#bracketed_content\\\"},{\\\"include\\\":\\\"#c_lang\\\"}],\\\"repository\\\":{\\\"anonymous_pattern_1\\\":{\\\"begin\\\":\\\"((@)(interface|protocol))(?!.+;)\\\\\\\\s+([A-Za-z_][A-Za-z0-9_]*)\\\\\\\\s*((:)\\\\\\\\s*([A-Za-z][A-Za-z0-9]*))?([\\\\\\\\s\\\\\\\\n])?\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.storage.type.objc\\\"},\\\"4\\\":{\\\"name\\\":\\\"entity.name.type.objc\\\"},\\\"6\\\":{\\\"name\\\":\\\"punctuation.definition.entity.other.inherited-class.objc\\\"},\\\"7\\\":{\\\"name\\\":\\\"entity.other.inherited-class.objc\\\"},\\\"8\\\":{\\\"name\\\":\\\"meta.divider.objc\\\"},\\\"9\\\":{\\\"name\\\":\\\"meta.inherited-class.objc\\\"}},\\\"contentName\\\":\\\"meta.scope.interface.objc\\\",\\\"end\\\":\\\"((@)end)\\\\\\\\b\\\",\\\"name\\\":\\\"meta.interface-or-protocol.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#interface_innards\\\"}]},\\\"anonymous_pattern_10\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.objc\\\"}},\\\"match\\\":\\\"(@)(defs|encode)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.objc\\\"},\\\"anonymous_pattern_11\\\":{\\\"match\\\":\\\"\\\\\\\\bid\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.id.objc\\\"},\\\"anonymous_pattern_12\\\":{\\\"match\\\":\\\"\\\\\\\\b(IBOutlet|IBAction|BOOL|SEL|id|unichar|IMP|Class|instancetype)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.objc\\\"},\\\"anonymous_pattern_13\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.storage.type.objc\\\"}},\\\"match\\\":\\\"(@)(class|protocol)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.objc\\\"},\\\"anonymous_pattern_14\\\":{\\\"begin\\\":\\\"((@)selector)\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.storage.type.objc\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.storage.type.objc\\\"}},\\\"contentName\\\":\\\"meta.selector.method-name.objc\\\",\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.storage.type.objc\\\"}},\\\"name\\\":\\\"meta.selector.objc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.arguments.objc\\\"}},\\\"match\\\":\\\"\\\\\\\\b(?:[a-zA-Z_:]\\\\\\\\w*)+\\\",\\\"name\\\":\\\"support.function.any-method.name-of-parameter.objc\\\"}]},\\\"anonymous_pattern_15\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.storage.modifier.objc\\\"}},\\\"match\\\":\\\"(@)(synchronized|public|package|private|protected)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.modifier.objc\\\"},\\\"anonymous_pattern_16\\\":{\\\"match\\\":\\\"\\\\\\\\b(YES|NO|Nil|nil)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.objc\\\"},\\\"anonymous_pattern_17\\\":{\\\"match\\\":\\\"\\\\\\\\bNSApp\\\\\\\\b\\\",\\\"name\\\":\\\"support.variable.foundation.objc\\\"},\\\"anonymous_pattern_18\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.support.function.cocoa.leopard.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.function.cocoa.leopard.objc\\\"}},\\\"match\\\":\\\"(\\\\\\\\s*)\\\\\\\\b(NS(Rect(ToCGRect|FromCGRect)|MakeCollectable|S(tringFromProtocol|ize(ToCGSize|FromCGSize))|Draw(NinePartImage|ThreePartImage)|P(oint(ToCGPoint|FromCGPoint)|rotocolFromString)|EventMaskFromType|Value))\\\\\\\\b\\\"},\\\"anonymous_pattern_19\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.support.function.leading.cocoa.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.function.cocoa.objc\\\"}},\\\"match\\\":\\\"(\\\\\\\\s*)\\\\\\\\b(NS(R(ound(DownToMultipleOfPageSize|UpToMultipleOfPageSize)|un(CriticalAlertPanel(RelativeToWindow)?|InformationalAlertPanel(RelativeToWindow)?|AlertPanel(RelativeToWindow)?)|e(set(MapTable|HashTable)|c(ycleZone|t(Clip(List)?|F(ill(UsingOperation|List(UsingOperation|With(Grays|Colors(UsingOperation)?))?)?|romString))|ordAllocationEvent)|turnAddress|leaseAlertPanel|a(dPixel|l(MemoryAvailable|locateCollectable))|gisterServicesProvider)|angeFromString)|Get(SizeAndAlignment|CriticalAlertPanel|InformationalAlertPanel|UncaughtExceptionHandler|FileType(s)?|WindowServerMemory|AlertPanel)|M(i(n([XY])|d([XY]))|ouseInRect|a(p(Remove|Get|Member|Insert(IfAbsent|KnownAbsent)?)|ke(R(ect|ange)|Size|Point)|x(Range|[XY])))|B(itsPer(SampleFromDepth|PixelFromDepth)|e(stDepth|ep|gin(CriticalAlertSheet|InformationalAlertSheet|AlertSheet)))|S(ho(uldRetainWithZone|w(sServicesMenuItem|AnimationEffect))|tringFrom(R(ect|ange)|MapTable|S(ize|elector)|HashTable|Class|Point)|izeFromString|e(t(ShowsServicesMenuItem|ZoneName|UncaughtExceptionHandler|FocusRingStyle)|lectorFromString|archPathForDirectoriesInDomains)|wap(Big(ShortToHost|IntToHost|DoubleToHost|FloatToHost|Long(ToHost|LongToHost))|Short|Host(ShortTo(Big|Little)|IntTo(Big|Little)|DoubleTo(Big|Little)|FloatTo(Big|Little)|Long(To(Big|Little)|LongTo(Big|Little)))|Int|Double|Float|L(ittle(ShortToHost|IntToHost|DoubleToHost|FloatToHost|Long(ToHost|LongToHost))|ong(Long)?)))|H(ighlightRect|o(stByteOrder|meDirectory(ForUser)?)|eight|ash(Remove|Get|Insert(IfAbsent|KnownAbsent)?)|FSType(CodeFromFileType|OfFile))|N(umberOfColorComponents|ext(MapEnumeratorPair|HashEnumeratorItem))|C(o(n(tainsRect|vert(GlyphsToPackedGlyphs|Swapped(DoubleToHost|FloatToHost)|Host(DoubleToSwapped|FloatToSwapped)))|unt(MapTable|HashTable|Frames|Windows(ForContext)?)|py(M(emoryPages|apTableWithZone)|Bits|HashTableWithZone|Object)|lorSpaceFromDepth|mpare(MapTables|HashTables))|lassFromString|reate(MapTable(WithZone)?|HashTable(WithZone)?|Zone|File(namePboardType|ContentsPboardType)))|TemporaryDirectory|I(s(ControllerMarker|EmptyRect|FreedObject)|n(setRect|crementExtraRefCount|te(r(sect(sRect|ionR(ect|ange))|faceStyleForKey)|gralRect)))|Zone(Realloc|Malloc|Name|Calloc|Fr(omPointer|ee))|O(penStepRootDirectory|ffsetRect)|D(i(sableScreenUpdates|videRect)|ottedFrameRect|e(c(imal(Round|Multiply|S(tring|ubtract)|Normalize|Co(py|mpa(ct|re))|IsNotANumber|Divide|Power|Add)|rementExtraRefCountWasZero)|faultMallocZone|allocate(MemoryPages|Object))|raw(Gr(oove|ayBezel)|B(itmap|utton)|ColorTiledRects|TiledRects|DarkBezel|W(hiteBezel|indowBackground)|LightBezel))|U(serName|n(ionR(ect|ange)|registerServicesProvider)|pdateDynamicServices)|Java(Bundle(Setup|Cleanup)|Setup(VirtualMachine)?|Needs(ToLoadClasses|VirtualMachine)|ClassesF(orBundle|romPath)|ObjectNamedInPath|ProvidesClasses)|P(oint(InRect|FromString)|erformService|lanarFromDepth|ageSize)|E(n(d(MapTableEnumeration|HashTableEnumeration)|umerate(MapTable|HashTable)|ableScreenUpdates)|qual(R(ects|anges)|Sizes|Points)|raseRect|xtraRefCount)|F(ileTypeForHFSTypeCode|ullUserName|r(ee(MapTable|HashTable)|ame(Rect(WithWidth(UsingOperation)?)?|Address)))|Wi(ndowList(ForContext)?|dth)|Lo(cationInRange|g(v|PageSize)?)|A(ccessibility(R(oleDescription(ForUIElement)?|aiseBadArgumentException)|Unignored(Children(ForOnlyChild)?|Descendant|Ancestor)|PostNotification|ActionDescription)|pplication(Main|Load)|vailableWindowDepths|ll(MapTable(Values|Keys)|HashTableObjects|ocate(MemoryPages|Collectable|Object)))))\\\\\\\\b\\\"},\\\"anonymous_pattern_2\\\":{\\\"begin\\\":\\\"((@)(implementation))\\\\\\\\s+([A-Za-z_][A-Za-z0-9_]*)\\\\\\\\s*(?::\\\\\\\\s*([A-Za-z][A-Za-z0-9]*))?\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.storage.type.objc\\\"},\\\"4\\\":{\\\"name\\\":\\\"entity.name.type.objc\\\"},\\\"5\\\":{\\\"name\\\":\\\"entity.other.inherited-class.objc\\\"}},\\\"contentName\\\":\\\"meta.scope.implementation.objc\\\",\\\"end\\\":\\\"((@)end)\\\\\\\\b\\\",\\\"name\\\":\\\"meta.implementation.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#implementation_innards\\\"}]},\\\"anonymous_pattern_20\\\":{\\\"match\\\":\\\"\\\\\\\\bNS(RuleEditor|G(arbageCollector|radient)|MapTable|HashTable|Co(ndition|llectionView(Item)?)|T(oolbarItemGroup|extInputClient|r(eeNode|ackingArea))|InvocationOperation|Operation(Queue)?|D(ictionaryController|ockTile)|P(ointer(Functions|Array)|athC(o(ntrol(Delegate)?|mponentCell)|ell(Delegate)?)|r(intPanelAccessorizing|edicateEditor(RowTemplate)?))|ViewController|FastEnumeration|Animat(ionContext|ablePropertyContainer))\\\\\\\\b\\\",\\\"name\\\":\\\"support.class.cocoa.leopard.objc\\\"},\\\"anonymous_pattern_21\\\":{\\\"match\\\":\\\"\\\\\\\\bNS(R(u(nLoop|ler(Marker|View))|e(sponder|cursiveLock|lativeSpecifier)|an(domSpecifier|geSpecifier))|G(etCommand|lyph(Generator|Storage|Info)|raphicsContext)|XML(Node|D(ocument|TD(Node)?)|Parser|Element)|M(iddleSpecifier|ov(ie(View)?|eCommand)|utable(S(tring|et)|C(haracterSet|opying)|IndexSet|D(ictionary|ata)|URLRequest|ParagraphStyle|A(ttributedString|rray))|e(ssagePort(NameServer)?|nu(Item(Cell)?|View)?|t(hodSignature|adata(Item|Query(ResultGroup|AttributeValueTuple)?)))|a(ch(BootstrapServer|Port)|trix))|B(itmapImageRep|ox|u(ndle|tton(Cell)?)|ezierPath|rowser(Cell)?)|S(hadow|c(anner|r(ipt(SuiteRegistry|C(o(ercionHandler|mmand(Description)?)|lassDescription)|ObjectSpecifier|ExecutionContext|WhoseTest)|oll(er|View)|een))|t(epper(Cell)?|atus(Bar|Item)|r(ing|eam))|imple(HorizontalTypesetter|CString)|o(cketPort(NameServer)?|und|rtDescriptor)|p(e(cifierTest|ech(Recognizer|Synthesizer)|ll(Server|Checker))|litView)|e(cureTextField(Cell)?|t(Command)?|archField(Cell)?|rializer|gmentedC(ontrol|ell))|lider(Cell)?|avePanel)|H(ost|TTP(Cookie(Storage)?|URLResponse)|elpManager)|N(ib(Con(nector|trolConnector)|OutletConnector)?|otification(Center|Queue)?|u(ll|mber(Formatter)?)|etService(Browser)?|ameSpecifier)|C(ha(ngeSpelling|racterSet)|o(n(stantString|nection|trol(ler)?|ditionLock)|d(ing|er)|unt(Command|edSet)|pying|lor(Space|P(ick(ing(Custom|Default)|er)|anel)|Well|List)?|m(p(oundPredicate|arisonPredicate)|boBox(Cell)?))|u(stomImageRep|rsor)|IImageRep|ell|l(ipView|o(seCommand|neCommand)|assDescription)|a(ched(ImageRep|URLResponse)|lendar(Date)?)|reateCommand)|T(hread|ypesetter|ime(Zone|r)|o(olbar(Item(Validations)?)?|kenField(Cell)?)|ext(Block|Storage|Container|Tab(le(Block)?)?|Input|View|Field(Cell)?|List|Attachment(Cell)?)?|a(sk|b(le(Header(Cell|View)|Column|View)|View(Item)?))|reeController)|I(n(dex(S(pecifier|et)|Path)|put(Manager|S(tream|erv(iceProvider|er(MouseTracker)?)))|vocation)|gnoreMisspelledWords|mage(Rep|Cell|View)?)|O(ut(putStream|lineView)|pen(GL(Context|Pixel(Buffer|Format)|View)|Panel)|bj(CTypeSerializationCallBack|ect(Controller)?))|D(i(st(antObject(Request)?|ributed(NotificationCenter|Lock))|ctionary|rectoryEnumerator)|ocument(Controller)?|e(serializer|cimalNumber(Behaviors|Handler)?|leteCommand)|at(e(Components|Picker(Cell)?|Formatter)?|a)|ra(wer|ggingInfo))|U(ser(InterfaceValidations|Defaults(Controller)?)|RL(Re(sponse|quest)|Handle(Client)?|C(onnection|ache|redential(Storage)?)|Download(Delegate)?|Prot(ocol(Client)?|ectionSpace)|AuthenticationChallenge(Sender)?)?|n(iqueIDSpecifier|doManager|archiver))|P(ipe|o(sitionalSpecifier|pUpButton(Cell)?|rt(Message|NameServer|Coder)?)|ICTImageRep|ersistentDocument|DFImageRep|a(steboard|nel|ragraphStyle|geLayout)|r(int(Info|er|Operation|Panel)|o(cessInfo|tocolChecker|perty(Specifier|ListSerialization)|gressIndicator|xy)|edicate))|E(numerator|vent|PSImageRep|rror|x(ception|istsCommand|pression))|V(iew(Animation)?|al(idated(ToobarItem|UserInterfaceItem)|ue(Transformer)?))|Keyed(Unarchiver|Archiver)|Qui(ckDrawView|tCommand)|F(ile(Manager|Handle|Wrapper)|o(nt(Manager|Descriptor|Panel)?|rm(Cell|atter)))|W(hoseSpecifier|indow(Controller)?|orkspace)|L(o(c(k(ing)?|ale)|gicalTest)|evelIndicator(Cell)?|ayoutManager)|A(ssertionHandler|nimation|ctionCell|ttributedString|utoreleasePool|TSTypesetter|ppl(ication|e(Script|Event(Manager|Descriptor)))|ffineTransform|lert|r(chiver|ray(Controller)?)))\\\\\\\\b\\\",\\\"name\\\":\\\"support.class.cocoa.objc\\\"},\\\"anonymous_pattern_22\\\":{\\\"match\\\":\\\"\\\\\\\\bNS(R(oundingMode|ule(Editor(RowType|NestingMode)|rOrientation)|e(questUserAttentionType|lativePosition))|G(lyphInscription|radientDrawingOptions)|XML(NodeKind|D(ocumentContentKind|TDNodeKind)|ParserError)|M(ultibyteGlyphPacking|apTableOptions)|B(itmapFormat|oxType|ezierPathElement|ackgroundStyle|rowserDropOperation)|S(tr(ing(CompareOptions|DrawingOptions|EncodingConversionOptions)|eam(Status|Event))|p(eechBoundary|litViewDividerStyle)|e(archPathD(irectory|omainMask)|gmentS(tyle|witchTracking))|liderType|aveOptions)|H(TTPCookieAcceptPolicy|ashTableOptions)|N(otification(SuspensionBehavior|Coalescing)|umberFormatter(RoundingMode|Behavior|Style|PadPosition)|etService(sError|Options))|C(haracterCollection|o(lor(RenderingIntent|SpaceModel|PanelMode)|mp(oundPredicateType|arisonPredicateModifier))|ellStateValue|al(culationError|endarUnit))|T(ypesetterControlCharacterAction|imeZoneNameStyle|e(stComparisonOperation|xt(Block(Dimension|V(erticalAlignment|alueType)|Layer)|TableLayoutAlgorithm|FieldBezelStyle))|ableView(SelectionHighlightStyle|ColumnAutoresizingStyle)|rackingAreaOptions)|I(n(sertionPosition|te(rfaceStyle|ger))|mage(RepLoadStatus|Scaling|CacheMode|FrameStyle|LoadStatus|Alignment))|Ope(nGLPixelFormatAttribute|rationQueuePriority)|Date(Picker(Mode|Style)|Formatter(Behavior|Style))|U(RL(RequestCachePolicy|HandleStatus|C(acheStoragePolicy|redentialPersistence))|Integer)|P(o(stingStyle|int(ingDeviceType|erFunctionsOptions)|pUpArrowPosition)|athStyle|r(int(ing(Orientation|PaginationMode)|erTableStatus|PanelOptions)|opertyList(MutabilityOptions|Format)|edicateOperatorType))|ExpressionType|KeyValue(SetMutationKind|Change)|QTMovieLoopMode|F(indPanel(SubstringMatchType|Action)|o(nt(RenderingMode|FamilyClass)|cusRingPlacement))|W(hoseSubelementIdentifier|ind(ingRule|ow(B(utton|ackingLocation)|SharingType|CollectionBehavior)))|L(ine(MovementDirection|SweepDirection|CapStyle|JoinStyle)|evelIndicatorStyle)|Animation(BlockingMode|Curve))\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.cocoa.leopard.objc\\\"},\\\"anonymous_pattern_23\\\":{\\\"match\\\":\\\"\\\\\\\\bC(I(Sampler|Co(ntext|lor)|Image(Accumulator)?|PlugIn(Registration)?|Vector|Kernel|Filter(Generator|Shape)?)|A(Renderer|MediaTiming(Function)?|BasicAnimation|ScrollLayer|Constraint(LayoutManager)?|T(iledLayer|extLayer|rans(ition|action))|OpenGLLayer|PropertyAnimation|KeyframeAnimation|Layer|A(nimation(Group)?|ction)))\\\\\\\\b\\\",\\\"name\\\":\\\"support.class.quartz.objc\\\"},\\\"anonymous_pattern_24\\\":{\\\"match\\\":\\\"\\\\\\\\bC(G(Float|Point|Size|Rect)|IFormat|AConstraintAttribute)\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.quartz.objc\\\"},\\\"anonymous_pattern_25\\\":{\\\"match\\\":\\\"\\\\\\\\bNS(R(ect(Edge)?|ange)|G(lyph(Relation|LayoutMode)?|radientType)|M(odalSession|a(trixMode|p(Table|Enumerator)))|B(itmapImageFileType|orderType|uttonType|ezelStyle|ackingStoreType|rowserColumnResizingType)|S(cr(oll(er(Part|Arrow)|ArrowPosition)|eenAuxiliaryOpaque)|tringEncoding|ize|ocketNativeHandle|election(Granularity|Direction|Affinity)|wapped(Double|Float)|aveOperationType)|Ha(sh(Table|Enumerator)|ndler(2)?)|C(o(ntrol(Size|Tint)|mp(ositingOperation|arisonResult))|ell(State|Type|ImagePosition|Attribute))|T(hreadPrivate|ypesetterGlyphInfo|i(ckMarkPosition|tlePosition|meInterval)|o(ol(TipTag|bar(SizeMode|DisplayMode))|kenStyle)|IFFCompression|ext(TabType|Alignment)|ab(State|leViewDropOperation|ViewType)|rackingRectTag)|ImageInterpolation|Zone|OpenGL(ContextAuxiliary|PixelFormatAuxiliary)|D(ocumentChangeType|atePickerElementFlags|ra(werState|gOperation))|UsableScrollerParts|P(oint|r(intingPageOrder|ogressIndicator(Style|Th(ickness|readInfo))))|EventType|KeyValueObservingOptions|Fo(nt(SymbolicTraits|TraitMask|Action)|cusRingType)|W(indow(OrderingMode|Depth)|orkspace(IconCreationOptions|LaunchOptions)|ritingDirection)|L(ineBreakMode|ayout(Status|Direction))|A(nimation(Progress|Effect)|ppl(ication(TerminateReply|DelegateReply|PrintReply)|eEventManagerSuspensionID)|ffineTransformStruct|lertStyle))\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.cocoa.objc\\\"},\\\"anonymous_pattern_26\\\":{\\\"match\\\":\\\"\\\\\\\\bNS(NotFound|Ordered(Ascending|Descending|Same))\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.cocoa.objc\\\"},\\\"anonymous_pattern_27\\\":{\\\"match\\\":\\\"\\\\\\\\bNS(MenuDidBeginTracking|ViewDidUpdateTrackingAreas)?Notification\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.notification.cocoa.leopard.objc\\\"},\\\"anonymous_pattern_28\\\":{\\\"match\\\":\\\"\\\\\\\\bNS(Menu(Did(RemoveItem|SendAction|ChangeItem|EndTracking|AddItem)|WillSendAction)|S(ystemColorsDidChange|plitView(DidResizeSubviews|WillResizeSubviews))|C(o(nt(extHelpModeDid(Deactivate|Activate)|rolT(intDidChange|extDid(BeginEditing|Change|EndEditing)))|lor(PanelColorDidChange|ListDidChange)|mboBox(Selection(IsChanging|DidChange)|Will(Dismiss|PopUp)))|lassDescriptionNeededForClass)|T(oolbar(DidRemoveItem|WillAddItem)|ext(Storage(DidProcessEditing|WillProcessEditing)|Did(BeginEditing|Change|EndEditing)|View(DidChange(Selection|TypingAttributes)|WillChangeNotifyingTextView))|ableView(Selection(IsChanging|DidChange)|ColumnDid(Resize|Move)))|ImageRepRegistryDidChange|OutlineView(Selection(IsChanging|DidChange)|ColumnDid(Resize|Move)|Item(Did(Collapse|Expand)|Will(Collapse|Expand)))|Drawer(Did(Close|Open)|Will(Close|Open))|PopUpButton(CellWillPopUp|WillPopUp)|View(GlobalFrameDidChange|BoundsDidChange|F(ocusDidChange|rameDidChange))|FontSetChanged|W(indow(Did(Resi(ze|gn(Main|Key))|M(iniaturize|ove)|Become(Main|Key)|ChangeScreen(|Profile)|Deminiaturize|Update|E(ndSheet|xpose))|Will(M(iniaturize|ove)|BeginSheet|Close))|orkspace(SessionDid(ResignActive|BecomeActive)|Did(Mount|TerminateApplication|Unmount|PerformFileOperation|Wake|LaunchApplication)|Will(Sleep|Unmount|PowerOff|LaunchApplication)))|A(ntialiasThresholdChanged|ppl(ication(Did(ResignActive|BecomeActive|Hide|ChangeScreenParameters|U(nhide|pdate)|FinishLaunching)|Will(ResignActive|BecomeActive|Hide|Terminate|U(nhide|pdate)|FinishLaunching))|eEventManagerWillProcessFirstEvent)))Notification\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.notification.cocoa.objc\\\"},\\\"anonymous_pattern_29\\\":{\\\"match\\\":\\\"\\\\\\\\bNS(RuleEditor(RowType(Simple|Compound)|NestingMode(Si(ngle|mple)|Compound|List))|GradientDraws(BeforeStartingLocation|AfterEndingLocation)|M(inusSetExpressionType|a(chPortDeallocate(ReceiveRight|SendRight|None)|pTable(StrongMemory|CopyIn|ZeroingWeakMemory|ObjectPointerPersonality)))|B(oxCustom|undleExecutableArchitecture(X86|I386|PPC(64)?)|etweenPredicateOperatorType|ackgroundStyle(Raised|Dark|L(ight|owered)))|S(tring(DrawingTruncatesLastVisibleLine|EncodingConversion(ExternalRepresentation|AllowLossy))|ubqueryExpressionType|p(e(ech(SentenceBoundary|ImmediateBoundary|WordBoundary)|llingState(GrammarFlag|SpellingFlag))|litViewDividerStyleThi(n|ck))|e(rvice(RequestTimedOutError|M(iscellaneousError|alformedServiceDictionaryError)|InvalidPasteboardDataError|ErrorM(inimum|aximum)|Application(NotFoundError|LaunchFailedError))|gmentStyle(Round(Rect|ed)|SmallSquare|Capsule|Textured(Rounded|Square)|Automatic)))|H(UDWindowMask|ashTable(StrongMemory|CopyIn|ZeroingWeakMemory|ObjectPointerPersonality))|N(oModeColorPanel|etServiceNoAutoRename)|C(hangeRedone|o(ntainsPredicateOperatorType|l(orRenderingIntent(RelativeColorimetric|Saturation|Default|Perceptual|AbsoluteColorimetric)|lectorDisabledOption))|ellHit(None|ContentArea|TrackableArea|EditableTextArea))|T(imeZoneNameStyle(S(hort(Standard|DaylightSaving)|tandard)|DaylightSaving)|extFieldDatePickerStyle|ableViewSelectionHighlightStyle(Regular|SourceList)|racking(Mouse(Moved|EnteredAndExited)|CursorUpdate|InVisibleRect|EnabledDuringMouseDrag|A(ssumeInside|ctive(In(KeyWindow|ActiveApp)|WhenFirstResponder|Always))))|I(n(tersectSetExpressionType|dexedColorSpaceModel)|mageScale(None|Proportionally(Down|UpOrDown)|AxesIndependently))|Ope(nGLPFAAllowOfflineRenderers|rationQueue(DefaultMaxConcurrentOperationCount|Priority(High|Normal|Very(High|Low)|Low)))|D(iacriticInsensitiveSearch|ownloadsDirectory)|U(nionSetExpressionType|TF(16(BigEndianStringEncoding|StringEncoding|LittleEndianStringEncoding)|32(BigEndianStringEncoding|StringEncoding|LittleEndianStringEncoding)))|P(ointerFunctions(Ma(chVirtualMemory|llocMemory)|Str(ongMemory|uctPersonality)|C(StringPersonality|opyIn)|IntegerPersonality|ZeroingWeakMemory|O(paque(Memory|Personality)|bjectP(ointerPersonality|ersonality)))|at(hStyle(Standard|NavigationBar|PopUp)|ternColorSpaceModel)|rintPanelShows(Scaling|Copies|Orientation|P(a(perSize|ge(Range|SetupAccessory))|review)))|Executable(RuntimeMismatchError|NotLoadableError|ErrorM(inimum|aximum)|L(inkError|oadError)|ArchitectureMismatchError)|KeyValueObservingOption(Initial|Prior)|F(i(ndPanelSubstringMatchType(StartsWith|Contains|EndsWith|FullWord)|leRead(TooLargeError|UnknownStringEncodingError))|orcedOrderingSearch)|Wi(ndow(BackingLocation(MainMemory|Default|VideoMemory)|Sharing(Read(Only|Write)|None)|CollectionBehavior(MoveToActiveSpace|CanJoinAllSpaces|Default))|dthInsensitiveSearch)|AggregateExpressionType)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.cocoa.leopard.objc\\\"},\\\"anonymous_pattern_3\\\":{\\\"begin\\\":\\\"@\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.objc\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.objc\\\"}},\\\"name\\\":\\\"string.quoted.double.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escaped_char\\\"},{\\\"match\\\":\\\"%(\\\\\\\\d+\\\\\\\\$)?[#0\\\\\\\\- +']*((-?\\\\\\\\d+)|\\\\\\\\*(-?\\\\\\\\d+\\\\\\\\$)?)?(\\\\\\\\.((-?\\\\\\\\d+)|\\\\\\\\*(-?\\\\\\\\d+\\\\\\\\$)?)?)?@\\\",\\\"name\\\":\\\"constant.other.placeholder.objc\\\"},{\\\"include\\\":\\\"#string_placeholder\\\"}]},\\\"anonymous_pattern_30\\\":{\\\"match\\\":\\\"\\\\\\\\bNS(R(GB(ModeColorPanel|ColorSpaceModel)|ight(Mouse(D(own(Mask)?|ragged(Mask)?)|Up(Mask)?)|T(ext(Movement|Alignment)|ab(sBezelBorder|StopType))|ArrowFunctionKey)|ound(RectBezelStyle|Bankers|ed(BezelStyle|TokenStyle|DisclosureBezelStyle)|Down|Up|Plain|Line(CapStyle|JoinStyle))|un(StoppedResponse|ContinuesResponse|AbortedResponse)|e(s(izableWindowMask|et(CursorRectsRunLoopOrdering|FunctionKey))|ce(ssedBezelStyle|iver(sCantHandleCommandScriptError|EvaluationScriptError))|turnTextMovement|doFunctionKey|quiredArgumentsMissingScriptError|l(evancyLevelIndicatorStyle|ative(Before|After))|gular(SquareBezelStyle|ControlSize)|moveTraitFontAction)|a(n(domSubelement|geDateMode)|tingLevelIndicatorStyle|dio(ModeMatrix|Button)))|G(IFFileType|lyph(Below|Inscribe(B(elow|ase)|Over(strike|Below)|Above)|Layout(WithPrevious|A(tAPoint|gainstAPoint))|A(ttribute(BidiLevel|Soft|Inscribe|Elastic)|bove))|r(ooveBorder|eaterThan(Comparison|OrEqualTo(Comparison|PredicateOperatorType)|PredicateOperatorType)|a(y(ModeColorPanel|ColorSpaceModel)|dient(None|Con(cave(Strong|Weak)|vex(Strong|Weak)))|phiteControlTint)))|XML(N(o(tationDeclarationKind|de(CompactEmptyElement|IsCDATA|OptionsNone|Use(SingleQuotes|DoubleQuotes)|Pre(serve(NamespaceOrder|C(haracterReferences|DATA)|DTD|Prefixes|E(ntities|mptyElements)|Quotes|Whitespace|A(ttributeOrder|ll))|ttyPrint)|ExpandEmptyElement))|amespaceKind)|CommentKind|TextKind|InvalidKind|D(ocument(X(MLKind|HTMLKind|Include)|HTMLKind|T(idy(XML|HTML)|extKind)|IncludeContentTypeDeclaration|Validate|Kind)|TDKind)|P(arser(GTRequiredError|XMLDeclNot(StartedError|FinishedError)|Mi(splaced(XMLDeclarationError|CDATAEndStringError)|xedContentDeclNot(StartedError|FinishedError))|S(t(andaloneValueError|ringNot(StartedError|ClosedError))|paceRequiredError|eparatorRequiredError)|N(MTOKENRequiredError|o(t(ationNot(StartedError|FinishedError)|WellBalancedError)|DTDError)|amespaceDeclarationError|AMERequiredError)|C(haracterRef(In(DTDError|PrologError|EpilogError)|AtEOFError)|o(nditionalSectionNot(StartedError|FinishedError)|mment(NotFinishedError|ContainsDoubleHyphenError))|DATANotFinishedError)|TagNameMismatchError|In(ternalError|valid(HexCharacterRefError|C(haracter(RefError|InEntityError|Error)|onditionalSectionError)|DecimalCharacterRefError|URIError|Encoding(NameError|Error)))|OutOfMemoryError|D(ocumentStartError|elegateAbortedParseError|OCTYPEDeclNotFinishedError)|U(RI(RequiredError|FragmentError)|n(declaredEntityError|parsedEntityError|knownEncodingError|finishedTagError))|P(CDATARequiredError|ublicIdentifierRequiredError|arsedEntityRef(MissingSemiError|NoNameError|In(Internal(SubsetError|Error)|PrologError|EpilogError)|AtEOFError)|r(ocessingInstructionNot(StartedError|FinishedError)|ematureDocumentEndError))|E(n(codingNotSupportedError|tity(Ref(In(DTDError|PrologError|EpilogError)|erence(MissingSemiError|WithoutNameError)|LoopError|AtEOFError)|BoundaryError|Not(StartedError|FinishedError)|Is(ParameterError|ExternalError)|ValueRequiredError))|qualExpectedError|lementContentDeclNot(StartedError|FinishedError)|xt(ernalS(tandaloneEntityError|ubsetNotFinishedError)|raContentError)|mptyDocumentError)|L(iteralNot(StartedError|FinishedError)|T(RequiredError|SlashRequiredError)|essThanSymbolInAttributeError)|Attribute(RedefinedError|HasNoValueError|Not(StartedError|FinishedError)|ListNot(StartedError|FinishedError)))|rocessingInstructionKind)|E(ntity(GeneralKind|DeclarationKind|UnparsedKind|P(ar(sedKind|ameterKind)|redefined))|lement(Declaration(MixedKind|UndefinedKind|E(lementKind|mptyKind)|Kind|AnyKind)|Kind))|Attribute(N(MToken(sKind|Kind)|otationKind)|CDATAKind|ID(Ref(sKind|Kind)|Kind)|DeclarationKind|En(tit(yKind|iesKind)|umerationKind)|Kind))|M(i(n(XEdge|iaturizableWindowMask|YEdge|uteCalendarUnit)|terLineJoinStyle|ddleSubelement|xedState)|o(nthCalendarUnit|deSwitchFunctionKey|use(Moved(Mask)?|E(ntered(Mask)?|ventSubtype|xited(Mask)?))|veToBezierPathElement|mentary(ChangeButton|Push(Button|InButton)|Light(Button)?))|enuFunctionKey|a(c(intoshInterfaceStyle|OSRomanStringEncoding)|tchesPredicateOperatorType|ppedRead|x(XEdge|YEdge))|ACHOperatingSystem)|B(MPFileType|o(ttomTabsBezelBorder|ldFontMask|rderlessWindowMask|x(Se(condary|parator)|OldStyle|Primary))|uttLineCapStyle|e(zelBorder|velLineJoinStyle|low(Bottom|Top)|gin(sWith(Comparison|PredicateOperatorType)|FunctionKey))|lueControlTint|ack(spaceCharacter|tabTextMovement|ingStore(Retained|Buffered|Nonretained)|TabCharacter|wardsSearch|groundTab)|r(owser(NoColumnResizing|UserColumnResizing|AutoColumnResizing)|eakFunctionKey))|S(h(ift(JISStringEncoding|KeyMask)|ow(ControlGlyphs|InvisibleGlyphs)|adowlessSquareBezelStyle)|y(s(ReqFunctionKey|tem(D(omainMask|efined(Mask)?)|FunctionKey))|mbolStringEncoding)|c(a(nnedOption|le(None|ToFit|Proportionally))|r(oll(er(NoPart|Increment(Page|Line|Arrow)|Decrement(Page|Line|Arrow)|Knob(Slot)?|Arrows(M(inEnd|axEnd)|None|DefaultSetting))|Wheel(Mask)?|LockFunctionKey)|eenChangedEventType))|t(opFunctionKey|r(ingDrawing(OneShot|DisableScreenFontSubstitution|Uses(DeviceMetrics|FontLeading|LineFragmentOrigin))|eam(Status(Reading|NotOpen|Closed|Open(ing)?|Error|Writing|AtEnd)|Event(Has(BytesAvailable|SpaceAvailable)|None|OpenCompleted|E(ndEncountered|rrorOccurred)))))|i(ngle(DateMode|UnderlineStyle)|ze(DownFontAction|UpFontAction))|olarisOperatingSystem|unOSOperatingSystem|pecialPageOrder|e(condCalendarUnit|lect(By(Character|Paragraph|Word)|i(ng(Next|Previous)|onAffinity(Downstream|Upstream))|edTab|FunctionKey)|gmentSwitchTracking(Momentary|Select(One|Any)))|quareLineCapStyle|witchButton|ave(ToOperation|Op(tions(Yes|No|Ask)|eration)|AsOperation)|mall(SquareBezelStyle|C(ontrolSize|apsFontMask)|IconButtonBezelStyle))|H(ighlightModeMatrix|SBModeColorPanel|o(ur(Minute(SecondDatePickerElementFlag|DatePickerElementFlag)|CalendarUnit)|rizontalRuler|meFunctionKey)|TTPCookieAcceptPolicy(Never|OnlyFromMainDocumentDomain|Always)|e(lp(ButtonBezelStyle|KeyMask|FunctionKey)|avierFontAction)|PUXOperatingSystem)|Year(MonthDa(yDatePickerElementFlag|tePickerElementFlag)|CalendarUnit)|N(o(n(StandardCharacterSetFontMask|ZeroWindingRule|activatingPanelMask|LossyASCIIStringEncoding)|Border|t(ification(SuspensionBehavior(Hold|Coalesce|D(eliverImmediately|rop))|NoCoalescing|CoalescingOn(Sender|Name)|DeliverImmediately|PostToAllSessions)|PredicateType|EqualToPredicateOperatorType)|S(cr(iptError|ollerParts)|ubelement|pecifierError)|CellMask|T(itle|opLevelContainersSpecifierError|abs(BezelBorder|NoBorder|LineBorder))|I(nterfaceStyle|mage)|UnderlineStyle|FontChangeAction)|u(ll(Glyph|CellType)|m(eric(Search|PadKeyMask)|berFormatter(Round(Half(Down|Up|Even)|Ceiling|Down|Up|Floor)|Behavior(10|Default)|S(cientificStyle|pellOutStyle)|NoStyle|CurrencyStyle|DecimalStyle|P(ercentStyle|ad(Before(Suffix|Prefix)|After(Suffix|Prefix))))))|e(t(Services(BadArgumentError|NotFoundError|C(ollisionError|ancelledError)|TimeoutError|InvalidError|UnknownError|ActivityInProgress)|workDomainMask)|wlineCharacter|xt(StepInterfaceStyle|FunctionKey))|EXTSTEPStringEncoding|a(t(iveShortGlyphPacking|uralTextAlignment)|rrowFontMask))|C(hange(ReadOtherContents|GrayCell(Mask)?|BackgroundCell(Mask)?|Cleared|Done|Undone|Autosaved)|MYK(ModeColorPanel|ColorSpaceModel)|ircular(BezelStyle|Slider)|o(n(stantValueExpressionType|t(inuousCapacityLevelIndicatorStyle|entsCellMask|ain(sComparison|erSpecifierError)|rol(Glyph|KeyMask))|densedFontMask)|lor(Panel(RGBModeMask|GrayModeMask|HSBModeMask|C(MYKModeMask|olorListModeMask|ustomPaletteModeMask|rayonModeMask)|WheelModeMask|AllModesMask)|ListModeColorPanel)|reServiceDirectory|m(p(osite(XOR|Source(In|O(ut|ver)|Atop)|Highlight|C(opy|lear)|Destination(In|O(ut|ver)|Atop)|Plus(Darker|Lighter))|ressedFontMask)|mandKeyMask))|u(stom(SelectorPredicateOperatorType|PaletteModeColorPanel)|r(sor(Update(Mask)?|PointingDevice)|veToBezierPathElement))|e(nterT(extAlignment|abStopType)|ll(State|H(ighlighted|as(Image(Horizontal|OnLeftOrBottom)|OverlappingImage))|ChangesContents|Is(Bordered|InsetButton)|Disabled|Editable|LightsBy(Gray|Background|Contents)|AllowsMixedState))|l(ipPagination|o(s(ePathBezierPathElement|ableWindowMask)|ckAndCalendarDatePickerStyle)|ear(ControlTint|DisplayFunctionKey|LineFunctionKey))|a(seInsensitive(Search|PredicateOption)|n(notCreateScriptCommandError|cel(Button|TextMovement))|chesDirectory|lculation(NoError|Overflow|DivideByZero|Underflow|LossOfPrecision)|rriageReturnCharacter)|r(itical(Request|AlertStyle)|ayonModeColorPanel))|T(hick(SquareBezelStyle|erSquareBezelStyle)|ypesetter(Behavior|HorizontalTabAction|ContainerBreakAction|ZeroAdvancementAction|OriginalBehavior|ParagraphBreakAction|WhitespaceAction|L(ineBreakAction|atestBehavior))|i(ckMark(Right|Below|Left|Above)|tledWindowMask|meZoneDatePickerElementFlag)|o(olbarItemVisibilityPriority(Standard|High|User|Low)|pTabsBezelBorder|ggleButton)|IFF(Compression(N(one|EXT)|CCITTFAX([34])|OldJPEG|JPEG|PackBits|LZW)|FileType)|e(rminate(Now|Cancel|Later)|xt(Read(InapplicableDocumentTypeError|WriteErrorM(inimum|aximum))|Block(M(i(nimum(Height|Width)|ddleAlignment)|a(rgin|ximum(Height|Width)))|B(o(ttomAlignment|rder)|aselineAlignment)|Height|TopAlignment|P(ercentageValueType|adding)|Width|AbsoluteValueType)|StorageEdited(Characters|Attributes)|CellType|ured(RoundedBezelStyle|BackgroundWindowMask|SquareBezelStyle)|Table(FixedLayoutAlgorithm|AutomaticLayoutAlgorithm)|Field(RoundedBezel|SquareBezel|AndStepperDatePickerStyle)|WriteInapplicableDocumentTypeError|ListPrependEnclosingMarker))|woByteGlyphPacking|ab(Character|TextMovement|le(tP(oint(Mask|EventSubtype)?|roximity(Mask|EventSubtype)?)|Column(NoResizing|UserResizingMask|AutoresizingMask)|View(ReverseSequentialColumnAutoresizingStyle|GridNone|S(olid(HorizontalGridLineMask|VerticalGridLineMask)|equentialColumnAutoresizingStyle)|NoColumnAutoresizing|UniformColumnAutoresizingStyle|FirstColumnOnlyAutoresizingStyle|LastColumnOnlyAutoresizingStyle)))|rackModeMatrix)|I(n(sert(CharFunctionKey|FunctionKey|LineFunctionKey)|t(Type|ernalS(criptError|pecifierError))|dexSubelement|validIndexSpecifierError|formational(Request|AlertStyle)|PredicateOperatorType)|talicFontMask|SO(2022JPStringEncoding|Latin(1StringEncoding|2StringEncoding))|dentityMappingCharacterCollection|llegalTextMovement|mage(R(ight|ep(MatchesDevice|LoadStatus(ReadingHeader|Completed|InvalidData|Un(expectedEOF|knownType)|WillNeedAllData)))|Below|C(ellType|ache(BySize|Never|Default|Always))|Interpolation(High|None|Default|Low)|O(nly|verlaps)|Frame(Gr(oove|ayBezel)|Button|None|Photo)|L(oadStatus(ReadError|C(ompleted|ancelled)|InvalidData|UnexpectedEOF)|eft)|A(lign(Right|Bottom(Right|Left)?|Center|Top(Right|Left)?|Left)|bove)))|O(n(State|eByteGlyphPacking|OffButton|lyScrollerArrows)|ther(Mouse(D(own(Mask)?|ragged(Mask)?)|Up(Mask)?)|TextMovement)|SF1OperatingSystem|pe(n(GL(GO(Re(setLibrary|tainRenderers)|ClearFormatCache|FormatCacheSize)|PFA(R(obust|endererID)|M(inimumPolicy|ulti(sample|Screen)|PSafe|aximumPolicy)|BackingStore|S(creenMask|te(ncilSize|reo)|ingleRenderer|upersample|ample(s|Buffers|Alpha))|NoRecovery|C(o(lor(Size|Float)|mpliant)|losestPolicy)|OffScreen|D(oubleBuffer|epthSize)|PixelBuffer|VirtualScreenCount|FullScreen|Window|A(cc(umSize|elerated)|ux(Buffers|DepthStencil)|l(phaSize|lRenderers))))|StepUnicodeReservedBase)|rationNotSupportedForKeyS(criptError|pecifierError))|ffState|KButton|rPredicateType|bjC(B(itfield|oolType)|S(hortType|tr(ingType|uctType)|electorType)|NoType|CharType|ObjectType|DoubleType|UnionType|PointerType|VoidType|FloatType|Long(Type|longType)|ArrayType))|D(i(s(c(losureBezelStyle|reteCapacityLevelIndicatorStyle)|playWindowRunLoopOrdering)|acriticInsensitivePredicateOption|rect(Selection|PredicateModifier))|o(c(ModalWindowMask|ument(Directory|ationDirectory))|ubleType|wn(TextMovement|ArrowFunctionKey))|e(s(cendingPageOrder|ktopDirectory)|cimalTabStopType|v(ice(NColorSpaceModel|IndependentModifierFlagsMask)|eloper(Directory|ApplicationDirectory))|fault(ControlTint|TokenStyle)|lete(Char(acter|FunctionKey)|FunctionKey|LineFunctionKey)|moApplicationDirectory)|a(yCalendarUnit|teFormatter(MediumStyle|Behavior(10|Default)|ShortStyle|NoStyle|FullStyle|LongStyle))|ra(wer(Clos(ingState|edState)|Open(ingState|State))|gOperation(Generic|Move|None|Copy|Delete|Private|Every|Link|All)))|U(ser(CancelledError|D(irectory|omainMask)|FunctionKey)|RL(Handle(NotLoaded|Load(Succeeded|InProgress|Failed))|CredentialPersistence(None|Permanent|ForSession))|n(scaledWindowMask|cachedRead|i(codeStringEncoding|talicFontMask|fiedTitleAndToolbarWindowMask)|d(o(CloseGroupingRunLoopOrdering|FunctionKey)|e(finedDateComponent|rline(Style(Single|None|Thick|Double)|Pattern(Solid|D(ot|ash(Dot(Dot)?)?)))))|known(ColorSpaceModel|P(ointingDevice|ageOrder)|KeyS(criptError|pecifierError))|boldFontMask)|tilityWindowMask|TF8StringEncoding|p(dateWindowsRunLoopOrdering|TextMovement|ArrowFunctionKey))|J(ustifiedTextAlignment|PEG(2000FileType|FileType)|apaneseEUC(GlyphPacking|StringEncoding))|P(o(s(t(Now|erFontMask|WhenIdle|ASAP)|iti(on(Replace|Be(fore|ginning)|End|After)|ve(IntType|DoubleType|FloatType)))|pUp(NoArrow|ArrowAt(Bottom|Center))|werOffEventType|rtraitOrientation)|NGFileType|ush(InCell(Mask)?|OnPushOffButton)|e(n(TipMask|UpperSideMask|PointingDevice|LowerSideMask)|riodic(Mask)?)|P(S(caleField|tatus(Title|Field)|aveButton)|N(ote(Title|Field)|ame(Title|Field))|CopiesField|TitleField|ImageButton|OptionsButton|P(a(perFeedButton|ge(Range(To|From)|ChoiceMatrix))|reviewButton)|LayoutButton)|lainTextTokenStyle|a(useFunctionKey|ragraphSeparatorCharacter|ge(DownFunctionKey|UpFunctionKey))|r(int(ing(ReplyLater|Success|Cancelled|Failure)|ScreenFunctionKey|erTable(NotFound|OK|Error)|FunctionKey)|o(p(ertyList(XMLFormat|MutableContainers(AndLeaves)?|BinaryFormat|Immutable|OpenStepFormat)|rietaryStringEncoding)|gressIndicator(BarStyle|SpinningStyle|Preferred(SmallThickness|Thickness|LargeThickness|AquaThickness)))|e(ssedTab|vFunctionKey))|L(HeightForm|CancelButton|TitleField|ImageButton|O(KButton|rientationMatrix)|UnitsButton|PaperNameButton|WidthForm))|E(n(terCharacter|d(sWith(Comparison|PredicateOperatorType)|FunctionKey))|v(e(nOddWindingRule|rySubelement)|aluatedObjectExpressionType)|qualTo(Comparison|PredicateOperatorType)|ra(serPointingDevice|CalendarUnit|DatePickerElementFlag)|x(clude(10|QuickDrawElementsIconCreationOption)|pandedFontMask|ecuteFunctionKey))|V(i(ew(M(in(XMargin|YMargin)|ax(XMargin|YMargin))|HeightSizable|NotSizable|WidthSizable)|aPanelFontAction)|erticalRuler|a(lidationErrorM(inimum|aximum)|riableExpressionType))|Key(SpecifierEvaluationScriptError|Down(Mask)?|Up(Mask)?|PathExpressionType|Value(MinusSetMutation|SetSetMutation|Change(Re(placement|moval)|Setting|Insertion)|IntersectSetMutation|ObservingOption(New|Old)|UnionSetMutation|ValidationError))|QTMovie(NormalPlayback|Looping(BackAndForthPlayback|Playback))|F(1(1FunctionKey|7FunctionKey|2FunctionKey|8FunctionKey|3FunctionKey|9FunctionKey|4FunctionKey|5FunctionKey|FunctionKey|0FunctionKey|6FunctionKey)|7FunctionKey|i(nd(PanelAction(Replace(A(ndFind|ll(InSelection)?))?|S(howFindPanel|e(tFindString|lectAll(InSelection)?))|Next|Previous)|FunctionKey)|tPagination|le(Read(No(SuchFileError|PermissionError)|CorruptFileError|In(validFileNameError|applicableStringEncodingError)|Un(supportedSchemeError|knownError))|HandlingPanel(CancelButton|OKButton)|NoSuchFileError|ErrorM(inimum|aximum)|Write(NoPermissionError|In(validFileNameError|applicableStringEncodingError)|OutOfSpaceError|Un(supportedSchemeError|knownError))|LockingError)|xedPitchFontMask)|2(1FunctionKey|7FunctionKey|2FunctionKey|8FunctionKey|3FunctionKey|9FunctionKey|4FunctionKey|5FunctionKey|FunctionKey|0FunctionKey|6FunctionKey)|o(nt(Mo(noSpaceTrait|dernSerifsClass)|BoldTrait|S(ymbolicClass|criptsClass|labSerifsClass|ansSerifClass)|C(o(ndensedTrait|llectionApplicationOnlyMask)|larendonSerifsClass)|TransitionalSerifsClass|I(ntegerAdvancementsRenderingMode|talicTrait)|O(ldStyleSerifsClass|rnamentalsClass)|DefaultRenderingMode|U(nknownClass|IOptimizedTrait)|Panel(S(hadowEffectModeMask|t(andardModesMask|rikethroughEffectModeMask)|izeModeMask)|CollectionModeMask|TextColorEffectModeMask|DocumentColorEffectModeMask|UnderlineEffectModeMask|FaceModeMask|All(ModesMask|EffectsModeMask))|ExpandedTrait|VerticalTrait|F(amilyClassMask|reeformSerifsClass)|Antialiased(RenderingMode|IntegerAdvancementsRenderingMode))|cusRing(Below|Type(None|Default|Exterior)|Only|Above)|urByteGlyphPacking|rm(attingError(M(inimum|aximum))?|FeedCharacter))|8FunctionKey|unction(ExpressionType|KeyMask)|3(1FunctionKey|2FunctionKey|3FunctionKey|4FunctionKey|5FunctionKey|FunctionKey|0FunctionKey)|9FunctionKey|4FunctionKey|P(RevertButton|S(ize(Title|Field)|etButton)|CurrentField|Preview(Button|Field))|l(oat(ingPointSamplesBitmapFormat|Type)|agsChanged(Mask)?)|axButton|5FunctionKey|6FunctionKey)|W(heelModeColorPanel|indow(s(NTOperatingSystem|CP125(1StringEncoding|2StringEncoding|3StringEncoding|4StringEncoding|0StringEncoding)|95(InterfaceStyle|OperatingSystem))|M(iniaturizeButton|ovedEventType)|Below|CloseButton|ToolbarButton|ZoomButton|Out|DocumentIconButton|ExposedEventType|Above)|orkspaceLaunch(NewInstance|InhibitingBackgroundOnly|Default|PreferringClassic|WithoutA(ctivation|ddingToRecents)|A(sync|nd(Hide(Others)?|Print)|llowingClassicStartup))|eek(day(CalendarUnit|OrdinalCalendarUnit)|CalendarUnit)|a(ntsBidiLevels|rningAlertStyle)|r(itingDirection(RightToLeft|Natural|LeftToRight)|apCalendarComponents))|L(i(stModeMatrix|ne(Moves(Right|Down|Up|Left)|B(order|reakBy(C(harWrapping|lipping)|Truncating(Middle|Head|Tail)|WordWrapping))|S(eparatorCharacter|weep(Right|Down|Up|Left))|ToBezierPathElement|DoesntMove|arSlider)|teralSearch|kePredicateOperatorType|ghterFontAction|braryDirectory)|ocalDomainMask|e(ssThan(Comparison|OrEqualTo(Comparison|PredicateOperatorType)|PredicateOperatorType)|ft(Mouse(D(own(Mask)?|ragged(Mask)?)|Up(Mask)?)|T(ext(Movement|Alignment)|ab(sBezelBorder|StopType))|ArrowFunctionKey))|a(yout(RightToLeft|NotDone|CantFit|OutOfGlyphs|Done|LeftToRight)|ndscapeOrientation)|ABColorSpaceModel)|A(sc(iiWithDoubleByteEUCGlyphPacking|endingPageOrder)|n(y(Type|PredicateModifier|EventMask)|choredSearch|imation(Blocking|Nonblocking(Threaded)?|E(ffect(DisappearingItemDefault|Poof)|ase(In(Out)?|Out))|Linear)|dPredicateType)|t(Bottom|tachmentCharacter|omicWrite|Top)|SCIIStringEncoding|d(obe(GB1CharacterCollection|CNS1CharacterCollection|Japan(1CharacterCollection|2CharacterCollection)|Korea1CharacterCollection)|dTraitFontAction|minApplicationDirectory)|uto(saveOperation|Pagination)|pp(lication(SupportDirectory|D(irectory|e(fined(Mask)?|legateReply(Success|Cancel|Failure)|activatedEventType))|ActivatedEventType)|KitDefined(Mask)?)|l(ternateKeyMask|pha(ShiftKeyMask|NonpremultipliedBitmapFormat|FirstBitmapFormat)|ert(SecondButtonReturn|ThirdButtonReturn|OtherReturn|DefaultReturn|ErrorReturn|FirstButtonReturn|AlternateReturn)|l(ScrollerParts|DomainsMask|PredicateModifier|LibrariesDirectory|ApplicationsDirectory))|rgument(sWrongScriptError|EvaluationScriptError)|bove(Bottom|Top)|WTEventType))\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.cocoa.objc\\\"},\\\"anonymous_pattern_4\\\":{\\\"begin\\\":\\\"\\\\\\\\b(id)\\\\\\\\s*(?=<)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.objc\\\"}},\\\"end\\\":\\\"(?<=>)\\\",\\\"name\\\":\\\"meta.id-with-protocol.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#protocol_list\\\"}]},\\\"anonymous_pattern_5\\\":{\\\"match\\\":\\\"\\\\\\\\b(NS_(?:DURING|HANDLER|ENDHANDLER))\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.macro.objc\\\"},\\\"anonymous_pattern_7\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.objc\\\"}},\\\"match\\\":\\\"(@)(try|catch|finally|throw)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.exception.objc\\\"},\\\"anonymous_pattern_8\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.objc\\\"}},\\\"match\\\":\\\"(@)(synchronized)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.synchronize.objc\\\"},\\\"anonymous_pattern_9\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.objc\\\"}},\\\"match\\\":\\\"(@)(required|optional)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.protocol-specification.objc\\\"},\\\"apple_foundation_functional_macros\\\":{\\\"begin\\\":\\\"(\\\\\\\\b(?:API_AVAILABLE|API_DEPRECATED|API_UNAVAILABLE|NS_AVAILABLE|NS_AVAILABLE_MAC|NS_AVAILABLE_IOS|NS_DEPRECATED|NS_DEPRECATED_MAC|NS_DEPRECATED_IOS|NS_SWIFT_NAME))\\\\\\\\s+{0,1}(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.preprocessor.apple-foundation.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.section.macro.arguments.begin.bracket.round.apple-foundation.objc\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.macro.arguments.end.bracket.round.apple-foundation.objc\\\"}},\\\"name\\\":\\\"meta.preprocessor.macro.callable.apple-foundation.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#c_lang\\\"}]},\\\"bracketed_content\\\":{\\\"begin\\\":\\\"\\\\\\\\[\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.scope.begin.objc\\\"}},\\\"end\\\":\\\"]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.scope.end.objc\\\"}},\\\"name\\\":\\\"meta.bracketed.objc\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?=predicateWithFormat:)(?<=NSPredicate )(predicateWithFormat:)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.any-method.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.arguments.objc\\\"}},\\\"end\\\":\\\"(?=])\\\",\\\"name\\\":\\\"meta.function-call.predicate.objc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.arguments.objc\\\"}},\\\"match\\\":\\\"\\\\\\\\bargument(Array|s)(:)\\\",\\\"name\\\":\\\"support.function.any-method.name-of-parameter.objc\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.arguments.objc\\\"}},\\\"match\\\":\\\"\\\\\\\\b\\\\\\\\w+(:)\\\",\\\"name\\\":\\\"invalid.illegal.unknown-method.objc\\\"},{\\\"begin\\\":\\\"@\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.objc\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.objc\\\"}},\\\"name\\\":\\\"string.quoted.double.objc\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(AND|OR|NOT|IN)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.operator.logical.predicate.cocoa.objc\\\"},{\\\"match\\\":\\\"\\\\\\\\b(ALL|ANY|SOME|NONE)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.predicate.cocoa.objc\\\"},{\\\"match\\\":\\\"\\\\\\\\b(NULL|NIL|SELF|TRUE|YES|FALSE|NO|FIRST|LAST|SIZE)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.predicate.cocoa.objc\\\"},{\\\"match\\\":\\\"\\\\\\\\b(MATCHES|CONTAINS|BEGINSWITH|ENDSWITH|BETWEEN)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.operator.comparison.predicate.cocoa.objc\\\"},{\\\"match\\\":\\\"\\\\\\\\bC(ASEINSENSITIVE|I)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.modifier.predicate.cocoa.objc\\\"},{\\\"match\\\":\\\"\\\\\\\\b(ANYKEY|SUBQUERY|CAST|TRUEPREDICATE|FALSEPREDICATE)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.predicate.cocoa.objc\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\([\\\\\\\\\\\\\\\\abefnrtv'\\\\\\\"?]|[0-3]\\\\\\\\d{0,2}|[4-7]\\\\\\\\d?|x[a-zA-Z0-9]+)\\\",\\\"name\\\":\\\"constant.character.escape.objc\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"invalid.illegal.unknown-escape.objc\\\"}]},{\\\"include\\\":\\\"#special_variables\\\"},{\\\"include\\\":\\\"#c_functions\\\"},{\\\"include\\\":\\\"$base\\\"}]},{\\\"begin\\\":\\\"(?=\\\\\\\\w)(?<=[\\\\\\\\w\\\\\\\\])\\\\\\\"] )(\\\\\\\\w+(?:(:)|(?=])))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.any-method.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.arguments.objc\\\"}},\\\"end\\\":\\\"(?=])\\\",\\\"name\\\":\\\"meta.function-call.objc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.arguments.objc\\\"}},\\\"match\\\":\\\"\\\\\\\\b\\\\\\\\w+(:)\\\",\\\"name\\\":\\\"support.function.any-method.name-of-parameter.objc\\\"},{\\\"include\\\":\\\"#special_variables\\\"},{\\\"include\\\":\\\"#c_functions\\\"},{\\\"include\\\":\\\"$base\\\"}]},{\\\"include\\\":\\\"#special_variables\\\"},{\\\"include\\\":\\\"#c_functions\\\"},{\\\"include\\\":\\\"$self\\\"}]},\\\"c_functions\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.support.function.leading.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.function.C99.objc\\\"}},\\\"match\\\":\\\"(\\\\\\\\s*)\\\\\\\\b(hypot([fl])?|s(scanf|ystem|nprintf|ca(nf|lb(n([fl])?|ln([fl])?))|i(n(h([fl])?|[fl])?|gn(al|bit))|tr(s(tr|pn)|nc(py|at|mp)|c(spn|hr|oll|py|at|mp)|to(imax|d|u(l(l)?|max)|[kf]|l([dl])?)|error|pbrk|ftime|len|rchr|xfrm)|printf|et(jmp|vbuf|locale|buf)|qrt([fl])?|w(scanf|printf)|rand)|n(e(arbyint([fl])?|xt(toward([fl])?|after([fl])?))|an([fl])?)|c(s(in(h([fl])?|[fl])?|qrt([fl])?)|cos(h(f)?|[fl])?|imag([fl])?|t(ime|an(h([fl])?|[fl])?)|o(s(h([fl])?|[fl])?|nj([fl])?|pysign([fl])?)|p(ow([fl])?|roj([fl])?)|e(il([fl])?|xp([fl])?)|l(o(ck|g([fl])?)|earerr)|a(sin(h([fl])?|[fl])?|cos(h([fl])?|[fl])?|tan(h([fl])?|[fl])?|lloc|rg([fl])?|bs([fl])?)|real([fl])?|brt([fl])?)|t(ime|o(upper|lower)|an(h([fl])?|[fl])?|runc([fl])?|gamma([fl])?|mp(nam|file))|i(s(space|n(ormal|an)|cntrl|inf|digit|u(nordered|pper)|p(unct|rint)|finite|w(space|c(ntrl|type)|digit|upper|p(unct|rint)|lower|al(num|pha)|graph|xdigit|blank)|l(ower|ess(equal|greater)?)|al(num|pha)|gr(eater(equal)?|aph)|xdigit|blank)|logb([fl])?|max(div|abs))|di(v|fftime)|_Exit|unget(c|wc)|p(ow([fl])?|ut(s|c(har)?|wc(har)?)|error|rintf)|e(rf(c([fl])?|[fl])?|x(it|p(2([fl])?|[fl]|m1([fl])?)?))|v(s(scanf|nprintf|canf|printf|w(scanf|printf))|printf|f(scanf|printf|w(scanf|printf))|w(scanf|printf)|a_(start|copy|end|arg))|qsort|f(s(canf|e(tpos|ek))|close|tell|open|dim([fl])?|p(classify|ut([sc]|w([sc]))|rintf)|e(holdexcept|set(e(nv|xceptflag)|round)|clearexcept|testexcept|of|updateenv|r(aiseexcept|ror)|get(e(nv|xceptflag)|round))|flush|w(scanf|ide|printf|rite)|loor([fl])?|abs([fl])?|get([sc]|pos|w([sc]))|re(open|e|ad|xp([fl])?)|m(in([fl])?|od([fl])?|a([fl]|x([fl])?)?))|l(d(iv|exp([fl])?)|o(ngjmp|cal(time|econv)|g(1(p([fl])?|0([fl])?)|2([fl])?|[fl]|b([fl])?)?)|abs|l(div|abs|r(int([fl])?|ound([fl])?))|r(int([fl])?|ound([fl])?)|gamma([fl])?)|w(scanf|c(s(s(tr|pn)|nc(py|at|mp)|c(spn|hr|oll|py|at|mp)|to(imax|d|u(l(l)?|max)|[kf]|l([dl])?|mbs)|pbrk|ftime|len|r(chr|tombs)|xfrm)|to(b|mb)|rtomb)|printf|mem(set|c(hr|py|mp)|move))|a(s(sert|ctime|in(h([fl])?|[fl])?)|cos(h([fl])?|[fl])?|t(o([if]|l(l)?)|exit|an(h([fl])?|2([fl])?|[fl])?)|b(s|ort))|g(et(s|c(har)?|env|wc(har)?)|mtime)|r(int([fl])?|ound([fl])?|e(name|alloc|wind|m(ove|quo([fl])?|ainder([fl])?))|a(nd|ise))|b(search|towc)|m(odf([fl])?|em(set|c(hr|py|mp)|move)|ktime|alloc|b(s(init|towcs|rtowcs)|towc|len|r(towc|len))))\\\\\\\\b\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.function-call.leading.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.function.any-method.objc\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.objc\\\"}},\\\"match\\\":\\\"(?:(?=\\\\\\\\s)(?:(?<=else|new|return)|(?<!\\\\\\\\w))(\\\\\\\\s+))?(\\\\\\\\b(?!(while|for|do|if|else|switch|catch|enumerate|return|r?iterate)\\\\\\\\s*\\\\\\\\()(?:(?!NS)[A-Za-z_][A-Za-z0-9_]*+\\\\\\\\b|::)++)\\\\\\\\s*(\\\\\\\\()\\\",\\\"name\\\":\\\"meta.function-call.objc\\\"}]},\\\"c_lang\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-enabled\\\"},{\\\"include\\\":\\\"#preprocessor-rule-disabled\\\"},{\\\"include\\\":\\\"#preprocessor-rule-conditional\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#switch_statement\\\"},{\\\"match\\\":\\\"\\\\\\\\b(break|continue|do|else|for|goto|if|_Pragma|return|while)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.objc\\\"},{\\\"include\\\":\\\"#storage_types\\\"},{\\\"match\\\":\\\"typedef\\\",\\\"name\\\":\\\"keyword.other.typedef.objc\\\"},{\\\"match\\\":\\\"\\\\\\\\bin\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.in.objc\\\"},{\\\"match\\\":\\\"\\\\\\\\b(const|extern|register|restrict|static|volatile|inline|__block)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.modifier.objc\\\"},{\\\"match\\\":\\\"\\\\\\\\bk[A-Z]\\\\\\\\w*\\\\\\\\b\\\",\\\"name\\\":\\\"constant.other.variable.mac-classic.objc\\\"},{\\\"match\\\":\\\"\\\\\\\\bg[A-Z]\\\\\\\\w*\\\\\\\\b\\\",\\\"name\\\":\\\"variable.other.readwrite.global.mac-classic.objc\\\"},{\\\"match\\\":\\\"\\\\\\\\bs[A-Z]\\\\\\\\w*\\\\\\\\b\\\",\\\"name\\\":\\\"variable.other.readwrite.static.mac-classic.objc\\\"},{\\\"match\\\":\\\"\\\\\\\\b(NULL|true|false|TRUE|FALSE)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.objc\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"include\\\":\\\"#numbers\\\"},{\\\"include\\\":\\\"#strings\\\"},{\\\"include\\\":\\\"#special_variables\\\"},{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*define)\\\\\\\\s+((?<id>[a-zA-Z_$][\\\\\\\\w$]*))(?:(\\\\\\\\()(\\\\\\\\s*\\\\\\\\g<id>\\\\\\\\s*((,)\\\\\\\\s*\\\\\\\\g<id>\\\\\\\\s*)*(?:\\\\\\\\.\\\\\\\\.\\\\\\\\.)?)(\\\\\\\\)))?\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.define.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.function.preprocessor.objc\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.begin.objc\\\"},\\\"6\\\":{\\\"name\\\":\\\"variable.parameter.preprocessor.objc\\\"},\\\"8\\\":{\\\"name\\\":\\\"punctuation.separator.parameters.objc\\\"},\\\"9\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.end.objc\\\"}},\\\"end\\\":\\\"(?=/[/*])|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.macro.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-define-line-contents\\\"}]},{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*(error|warning))\\\\\\\\b\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.diagnostic.$3.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"end\\\":\\\"(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.diagnostic.objc\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.objc\\\"}},\\\"end\\\":\\\"\\\\\\\"|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\s*\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.objc\\\"}},\\\"name\\\":\\\"string.quoted.double.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#line_continuation_character\\\"}]},{\\\"begin\\\":\\\"'\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.objc\\\"}},\\\"end\\\":\\\"'|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\s*\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.objc\\\"}},\\\"name\\\":\\\"string.quoted.single.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#line_continuation_character\\\"}]},{\\\"begin\\\":\\\"[^'\\\\\\\"]\\\",\\\"end\\\":\\\"(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\s*\\\\\\\\n)\\\",\\\"name\\\":\\\"string.unquoted.single.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#line_continuation_character\\\"},{\\\"include\\\":\\\"#comments\\\"}]}]},{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*(i(?:nclude(?:_next)?|mport)))\\\\\\\\b\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.$3.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"end\\\":\\\"(?=/[/*])|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.include.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#line_continuation_character\\\"},{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.objc\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.objc\\\"}},\\\"name\\\":\\\"string.quoted.double.include.objc\\\"},{\\\"begin\\\":\\\"<\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.objc\\\"}},\\\"end\\\":\\\">\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.objc\\\"}},\\\"name\\\":\\\"string.quoted.other.lt-gt.include.objc\\\"}]},{\\\"include\\\":\\\"#pragma-mark\\\"},{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*line)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.line.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"end\\\":\\\"(?=/[/*])|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#strings\\\"},{\\\"include\\\":\\\"#numbers\\\"},{\\\"include\\\":\\\"#line_continuation_character\\\"}]},{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*undef)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.undef.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"end\\\":\\\"(?=/[/*])|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.objc\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"[a-zA-Z_$][\\\\\\\\w$]*\\\",\\\"name\\\":\\\"entity.name.function.preprocessor.objc\\\"},{\\\"include\\\":\\\"#line_continuation_character\\\"}]},{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*pragma)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.pragma.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"end\\\":\\\"(?=/[/*])|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.pragma.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#strings\\\"},{\\\"match\\\":\\\"[a-zA-Z_$][\\\\\\\\w\\\\\\\\-$]*\\\",\\\"name\\\":\\\"entity.other.attribute-name.pragma.preprocessor.objc\\\"},{\\\"include\\\":\\\"#numbers\\\"},{\\\"include\\\":\\\"#line_continuation_character\\\"}]},{\\\"match\\\":\\\"\\\\\\\\b(u_char|u_short|u_int|u_long|ushort|uint|u_quad_t|quad_t|qaddr_t|caddr_t|daddr_t|div_t|dev_t|fixpt_t|blkcnt_t|blksize_t|gid_t|in_addr_t|in_port_t|ino_t|key_t|mode_t|nlink_t|id_t|pid_t|off_t|segsz_t|swblk_t|uid_t|id_t|clock_t|size_t|ssize_t|time_t|useconds_t|suseconds_t)\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.sys-types.objc\\\"},{\\\"match\\\":\\\"\\\\\\\\b(pthread_(?:attr_t|cond_t|condattr_t|mutex_t|mutexattr_t|once_t|rwlock_t|rwlockattr_t|t|key_t))\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.pthread.objc\\\"},{\\\"match\\\":\\\"\\\\\\\\b(int8_t|int16_t|int32_t|int64_t|uint8_t|uint16_t|uint32_t|uint64_t|int_least8_t|int_least16_t|int_least32_t|int_least64_t|uint_least8_t|uint_least16_t|uint_least32_t|uint_least64_t|int_fast8_t|int_fast16_t|int_fast32_t|int_fast64_t|uint_fast8_t|uint_fast16_t|uint_fast32_t|uint_fast64_t|intptr_t|uintptr_t|intmax_t|intmax_t|uintmax_t|uintmax_t)\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.stdint.objc\\\"},{\\\"match\\\":\\\"\\\\\\\\b(noErr|kNilOptions|kInvalidID|kVariableLengthArray)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.mac-classic.objc\\\"},{\\\"match\\\":\\\"\\\\\\\\b(AbsoluteTime|Boolean|Byte|ByteCount|ByteOffset|BytePtr|CompTimeValue|ConstLogicalAddress|ConstStrFileNameParam|ConstStringPtr|Duration|Fixed|FixedPtr|Float32|Float32Point|Float64|Float80|Float96|FourCharCode|Fract|FractPtr|Handle|ItemCount|LogicalAddress|OptionBits|OSErr|OSStatus|OSType|OSTypePtr|PhysicalAddress|ProcessSerialNumber|ProcessSerialNumberPtr|ProcHandle|Ptr|ResType|ResTypePtr|ShortFixed|ShortFixedPtr|SignedByte|SInt16|SInt32|SInt64|SInt8|Size|StrFileName|StringHandle|StringPtr|TimeBase|TimeRecord|TimeScale|TimeValue|TimeValue64|UInt16|UInt32|UInt64|UInt8|UniChar|UniCharCount|UniCharCountPtr|UniCharPtr|UnicodeScalarValue|UniversalProcHandle|UniversalProcPtr|UnsignedFixed|UnsignedFixedPtr|UnsignedWide|UTF16Char|UTF32Char|UTF8Char)\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.mac-classic.objc\\\"},{\\\"match\\\":\\\"\\\\\\\\b([A-Za-z0-9_]+_t)\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.posix-reserved.objc\\\"},{\\\"include\\\":\\\"#block\\\"},{\\\"include\\\":\\\"#parens\\\"},{\\\"begin\\\":\\\"(?<!\\\\\\\\w)(?!\\\\\\\\s*(?:not|compl|sizeof|not_eq|bitand|xor|bitor|and|or|and_eq|xor_eq|or_eq|alignof|alignas|_Alignof|_Alignas|while|for|do|if|else|goto|switch|return|break|case|continue|default|void|char|short|int|signed|unsigned|long|float|double|bool|_Bool|_Complex|_Imaginary|u_char|u_short|u_int|u_long|ushort|uint|u_quad_t|quad_t|qaddr_t|caddr_t|daddr_t|div_t|dev_t|fixpt_t|blkcnt_t|blksize_t|gid_t|in_addr_t|in_port_t|ino_t|key_t|mode_t|nlink_t|id_t|pid_t|off_t|segsz_t|swblk_t|uid_t|id_t|clock_t|size_t|ssize_t|time_t|useconds_t|suseconds_t|pthread_attr_t|pthread_cond_t|pthread_condattr_t|pthread_mutex_t|pthread_mutexattr_t|pthread_once_t|pthread_rwlock_t|pthread_rwlockattr_t|pthread_t|pthread_key_t|int8_t|int16_t|int32_t|int64_t|uint8_t|uint16_t|uint32_t|uint64_t|int_least8_t|int_least16_t|int_least32_t|int_least64_t|uint_least8_t|uint_least16_t|uint_least32_t|uint_least64_t|int_fast8_t|int_fast16_t|int_fast32_t|int_fast64_t|uint_fast8_t|uint_fast16_t|uint_fast32_t|uint_fast64_t|intptr_t|uintptr_t|intmax_t|intmax_t|uintmax_t|uintmax_t|NULL|true|false|memory_order|atomic_bool|atomic_char|atomic_schar|atomic_uchar|atomic_short|atomic_ushort|atomic_int|atomic_uint|atomic_long|atomic_ulong|atomic_llong|atomic_ullong|atomic_char16_t|atomic_char32_t|atomic_wchar_t|atomic_int_least8_t|atomic_uint_least8_t|atomic_int_least16_t|atomic_uint_least16_t|atomic_int_least32_t|atomic_uint_least32_t|atomic_int_least64_t|atomic_uint_least64_t|atomic_int_fast8_t|atomic_uint_fast8_t|atomic_int_fast16_t|atomic_uint_fast16_t|atomic_int_fast32_t|atomic_uint_fast32_t|atomic_int_fast64_t|atomic_uint_fast64_t|atomic_intptr_t|atomic_uintptr_t|atomic_size_t|atomic_ptrdiff_t|atomic_intmax_t|atomic_uintmax_t|struct|union|enum|typedef|auto|register|static|extern|thread_local|inline|_Noreturn|const|volatile|restrict|_Atomic)\\\\\\\\s*\\\\\\\\()(?=[a-zA-Z_]\\\\\\\\w*\\\\\\\\s*\\\\\\\\()\\\",\\\"end\\\":\\\"(?<=\\\\\\\\))\\\",\\\"name\\\":\\\"meta.function.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-innards\\\"}]},{\\\"include\\\":\\\"#line_continuation_character\\\"},{\\\"begin\\\":\\\"([a-zA-Z_][a-zA-Z_0-9]*|(?<=[\\\\\\\\])]))?(\\\\\\\\[)(?!])\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.object.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.begin.bracket.square.objc\\\"}},\\\"end\\\":\\\"]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.end.bracket.square.objc\\\"}},\\\"name\\\":\\\"meta.bracket.square.access.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-call-innards\\\"}]},{\\\"match\\\":\\\"\\\\\\\\[\\\\\\\\s*]\\\",\\\"name\\\":\\\"storage.modifier.array.bracket.square.objc\\\"},{\\\"match\\\":\\\";\\\",\\\"name\\\":\\\"punctuation.terminator.statement.objc\\\"},{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.separator.delimiter.objc\\\"}],\\\"repository\\\":{\\\"access-method\\\":{\\\"begin\\\":\\\"([a-zA-Z_][a-zA-Z_0-9]*|(?<=[\\\\\\\\])]))\\\\\\\\s*(?:(\\\\\\\\.)|(->))((?:[a-zA-Z_][a-zA-Z_0-9]*\\\\\\\\s*(?:\\\\\\\\.|->))*)\\\\\\\\s*([a-zA-Z_][a-zA-Z_0-9]*)(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.object.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.dot-access.objc\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.pointer-access.objc\\\"},\\\"4\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\.\\\",\\\"name\\\":\\\"punctuation.separator.dot-access.objc\\\"},{\\\"match\\\":\\\"->\\\",\\\"name\\\":\\\"punctuation.separator.pointer-access.objc\\\"},{\\\"match\\\":\\\"[a-zA-Z_][a-zA-Z_0-9]*\\\",\\\"name\\\":\\\"variable.object.objc\\\"},{\\\"match\\\":\\\".+\\\",\\\"name\\\":\\\"everything.else.objc\\\"}]},\\\"5\\\":{\\\"name\\\":\\\"entity.name.function.member.objc\\\"},\\\"6\\\":{\\\"name\\\":\\\"punctuation.section.arguments.begin.bracket.round.function.member.objc\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.arguments.end.bracket.round.function.member.objc\\\"}},\\\"name\\\":\\\"meta.function-call.member.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-call-innards\\\"}]},\\\"block\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.block.begin.bracket.curly.objc\\\"}},\\\"end\\\":\\\"}|(?=\\\\\\\\s*#\\\\\\\\s*e(?:lif|lse|ndif)\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.block.end.bracket.curly.objc\\\"}},\\\"name\\\":\\\"meta.block.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block_innards\\\"}]}]},\\\"block_innards\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-enabled-block\\\"},{\\\"include\\\":\\\"#preprocessor-rule-disabled-block\\\"},{\\\"include\\\":\\\"#preprocessor-rule-conditional-block\\\"},{\\\"include\\\":\\\"#method_access\\\"},{\\\"include\\\":\\\"#member_access\\\"},{\\\"include\\\":\\\"#c_function_call\\\"},{\\\"begin\\\":\\\"(?=\\\\\\\\s)(?<!else|new|return)(?<=\\\\\\\\w)\\\\\\\\s+(and|and_eq|bitand|bitor|compl|not|not_eq|or|or_eq|typeid|xor|xor_eq|alignof|alignas)((?:[A-Za-z_][A-Za-z0-9_]*+|::)++|(?<=operator)(?:[-*\\\\\\\\&<>=+!]+|\\\\\\\\(\\\\\\\\)|\\\\\\\\[]))\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.section.parens.begin.bracket.round.initialization.objc\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.end.bracket.round.initialization.objc\\\"}},\\\"name\\\":\\\"meta.initialization.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-call-innards\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.block.begin.bracket.curly.objc\\\"}},\\\"end\\\":\\\"}|(?=\\\\\\\\s*#\\\\\\\\s*e(?:lif|lse|ndif)\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.block.end.bracket.curly.objc\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#block_innards\\\"}]},{\\\"include\\\":\\\"#parens-block\\\"},{\\\"include\\\":\\\"$base\\\"}]},\\\"c_function_call\\\":{\\\"begin\\\":\\\"(?!(?:while|for|do|if|else|switch|catch|enumerate|return|typeid|alignof|alignas|sizeof|[cr]?iterate|and|and_eq|bitand|bitor|compl|not|not_eq|or|or_eq|typeid|xor|xor_eq|alignof|alignas)\\\\\\\\s*\\\\\\\\()(?=(?:[A-Za-z_][A-Za-z0-9_]*+|::)++\\\\\\\\s*\\\\\\\\(|(?<=operator)(?:[-*\\\\\\\\&<>=+!]+|\\\\\\\\(\\\\\\\\)|\\\\\\\\[])\\\\\\\\s*\\\\\\\\()\\\",\\\"end\\\":\\\"(?<=\\\\\\\\))(?!\\\\\\\\w)\\\",\\\"name\\\":\\\"meta.function-call.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-call-innards\\\"}]},\\\"case_statement\\\":{\\\"begin\\\":\\\"((?<!\\\\\\\\w)case(?!\\\\\\\\w))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.case.objc\\\"}},\\\"end\\\":\\\"(:)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.case.objc\\\"}},\\\"name\\\":\\\"meta.conditional.case.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#conditional_context\\\"}]},\\\"comments\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.toc-list.banner.block.objc\\\"}},\\\"match\\\":\\\"^/\\\\\\\\* =(\\\\\\\\s*.*?)\\\\\\\\s*= \\\\\\\\*/$\\\\\\\\n?\\\",\\\"name\\\":\\\"comment.block.objc\\\"},{\\\"begin\\\":\\\"/\\\\\\\\*\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.begin.objc\\\"}},\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.end.objc\\\"}},\\\"name\\\":\\\"comment.block.objc\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.toc-list.banner.line.objc\\\"}},\\\"match\\\":\\\"^// =(\\\\\\\\s*.*?)\\\\\\\\s*=\\\\\\\\s*$\\\\\\\\n?\\\",\\\"name\\\":\\\"comment.line.banner.objc\\\"},{\\\"begin\\\":\\\"(^[ \\\\\\\\t]+)?(?=//)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.comment.leading.objc\\\"}},\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"//\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.objc\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"comment.line.double-slash.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#line_continuation_character\\\"}]}]}]},\\\"conditional_context\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"$base\\\"},{\\\"include\\\":\\\"#block_innards\\\"}]},\\\"default_statement\\\":{\\\"begin\\\":\\\"((?<!\\\\\\\\w)default(?!\\\\\\\\w))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.default.objc\\\"}},\\\"end\\\":\\\"(:)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.case.default.objc\\\"}},\\\"name\\\":\\\"meta.conditional.case.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#conditional_context\\\"}]},\\\"disabled\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*#\\\\\\\\s*if(n?def)?\\\\\\\\b.*$\\\",\\\"end\\\":\\\"^\\\\\\\\s*#\\\\\\\\s*endif\\\\\\\\b\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"}]},\\\"function-call-innards\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#storage_types\\\"},{\\\"include\\\":\\\"#method_access\\\"},{\\\"include\\\":\\\"#member_access\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"begin\\\":\\\"(?!(?:while|for|do|if|else|switch|catch|enumerate|return|typeid|alignof|alignas|sizeof|[cr]?iterate|and|and_eq|bitand|bitor|compl|not|not_eq|or|or_eq|typeid|xor|xor_eq|alignof|alignas)\\\\\\\\s*\\\\\\\\()((?:[A-Za-z_][A-Za-z0-9_]*+|::)++|(?<=operator)(?:[-*\\\\\\\\&<>=+!]+|\\\\\\\\(\\\\\\\\)|\\\\\\\\[]))\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.section.arguments.begin.bracket.round.objc\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.arguments.end.bracket.round.objc\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#function-call-innards\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.begin.bracket.round.objc\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.end.bracket.round.objc\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#function-call-innards\\\"}]},{\\\"include\\\":\\\"#block_innards\\\"}]},\\\"function-innards\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#storage_types\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"include\\\":\\\"#vararg_ellipses\\\"},{\\\"begin\\\":\\\"(?!(?:while|for|do|if|else|switch|catch|enumerate|return|typeid|alignof|alignas|sizeof|[cr]?iterate|and|and_eq|bitand|bitor|compl|not|not_eq|or|or_eq|typeid|xor|xor_eq|alignof|alignas)\\\\\\\\s*\\\\\\\\()((?:[A-Za-z_][A-Za-z0-9_]*+|::)++|(?<=operator)(?:[-*\\\\\\\\&<>=+!]+|\\\\\\\\(\\\\\\\\)|\\\\\\\\[]))\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.section.parameters.begin.bracket.round.objc\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parameters.end.bracket.round.objc\\\"}},\\\"name\\\":\\\"meta.function.definition.parameters.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#probably_a_parameter\\\"},{\\\"include\\\":\\\"#function-innards\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.begin.bracket.round.objc\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.end.bracket.round.objc\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#function-innards\\\"}]},{\\\"include\\\":\\\"$base\\\"}]},\\\"line_continuation_character\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.character.escape.line-continuation.objc\\\"}},\\\"match\\\":\\\"(\\\\\\\\\\\\\\\\)\\\\\\\\n\\\"}]},\\\"member_access\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#special_variables\\\"},{\\\"match\\\":\\\"(.+)\\\",\\\"name\\\":\\\"variable.other.object.access.objc\\\"}]},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.dot-access.objc\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.pointer-access.objc\\\"},\\\"4\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#member_access\\\"},{\\\"include\\\":\\\"#method_access\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#special_variables\\\"},{\\\"match\\\":\\\"(.+)\\\",\\\"name\\\":\\\"variable.other.object.access.objc\\\"}]},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.dot-access.objc\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.pointer-access.objc\\\"}},\\\"match\\\":\\\"((?:[a-zA-Z_]\\\\\\\\w*|(?<=[\\\\\\\\])]))\\\\\\\\s*)(?:(\\\\\\\\.(?:\\\\\\\\*|))|(->(?:\\\\\\\\*|)))\\\"}]},\\\"5\\\":{\\\"name\\\":\\\"variable.other.member.objc\\\"}},\\\"match\\\":\\\"((?:[a-zA-Z_]\\\\\\\\w*|(?<=[\\\\\\\\])]))\\\\\\\\s*)(?:(\\\\\\\\.(?:\\\\\\\\*|))|(->(?:\\\\\\\\*|)))((?:[a-zA-Z_]\\\\\\\\w*\\\\\\\\s*(?-im:\\\\\\\\.(?:\\\\\\\\*|)|->(?:\\\\\\\\*|))\\\\\\\\s*)*)\\\\\\\\s*(\\\\\\\\b(?!(?:void|char|short|int|signed|unsigned|long|float|double|bool|_Bool|_Complex|_Imaginary|u_char|u_short|u_int|u_long|ushort|uint|u_quad_t|quad_t|qaddr_t|caddr_t|daddr_t|div_t|dev_t|fixpt_t|blkcnt_t|blksize_t|gid_t|in_addr_t|in_port_t|ino_t|key_t|mode_t|nlink_t|id_t|pid_t|off_t|segsz_t|swblk_t|uid_t|id_t|clock_t|size_t|ssize_t|time_t|useconds_t|suseconds_t|pthread_attr_t|pthread_cond_t|pthread_condattr_t|pthread_mutex_t|pthread_mutexattr_t|pthread_once_t|pthread_rwlock_t|pthread_rwlockattr_t|pthread_t|pthread_key_t|int8_t|int16_t|int32_t|int64_t|uint8_t|uint16_t|uint32_t|uint64_t|int_least8_t|int_least16_t|int_least32_t|int_least64_t|uint_least8_t|uint_least16_t|uint_least32_t|uint_least64_t|int_fast8_t|int_fast16_t|int_fast32_t|int_fast64_t|uint_fast8_t|uint_fast16_t|uint_fast32_t|uint_fast64_t|intptr_t|uintptr_t|intmax_t|intmax_t|uintmax_t|uintmax_t|memory_order|atomic_bool|atomic_char|atomic_schar|atomic_uchar|atomic_short|atomic_ushort|atomic_int|atomic_uint|atomic_long|atomic_ulong|atomic_llong|atomic_ullong|atomic_char16_t|atomic_char32_t|atomic_wchar_t|atomic_int_least8_t|atomic_uint_least8_t|atomic_int_least16_t|atomic_uint_least16_t|atomic_int_least32_t|atomic_uint_least32_t|atomic_int_least64_t|atomic_uint_least64_t|atomic_int_fast8_t|atomic_uint_fast8_t|atomic_int_fast16_t|atomic_uint_fast16_t|atomic_int_fast32_t|atomic_uint_fast32_t|atomic_int_fast64_t|atomic_uint_fast64_t|atomic_intptr_t|atomic_uintptr_t|atomic_size_t|atomic_ptrdiff_t|atomic_intmax_t|atomic_uintmax_t))[a-zA-Z_]\\\\\\\\w*\\\\\\\\b(?!\\\\\\\\())\\\"},\\\"method_access\\\":{\\\"begin\\\":\\\"((?:[a-zA-Z_]\\\\\\\\w*|(?<=[\\\\\\\\])]))\\\\\\\\s*)(?:(\\\\\\\\.(?:\\\\\\\\*|))|(->(?:\\\\\\\\*|)))((?:[a-zA-Z_]\\\\\\\\w*\\\\\\\\s*(?-im:\\\\\\\\.(?:\\\\\\\\*|)|->(?:\\\\\\\\*|))\\\\\\\\s*)*)\\\\\\\\s*([a-zA-Z_]\\\\\\\\w*)(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#special_variables\\\"},{\\\"match\\\":\\\"(.+)\\\",\\\"name\\\":\\\"variable.other.object.access.objc\\\"}]},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.dot-access.objc\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.pointer-access.objc\\\"},\\\"4\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#member_access\\\"},{\\\"include\\\":\\\"#method_access\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#special_variables\\\"},{\\\"match\\\":\\\"(.+)\\\",\\\"name\\\":\\\"variable.other.object.access.objc\\\"}]},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.dot-access.objc\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.pointer-access.objc\\\"}},\\\"match\\\":\\\"((?:[a-zA-Z_]\\\\\\\\w*|(?<=[\\\\\\\\])]))\\\\\\\\s*)(?:(\\\\\\\\.(?:\\\\\\\\*|))|(->(?:\\\\\\\\*|)))\\\"}]},\\\"5\\\":{\\\"name\\\":\\\"entity.name.function.member.objc\\\"},\\\"6\\\":{\\\"name\\\":\\\"punctuation.section.arguments.begin.bracket.round.function.member.objc\\\"}},\\\"contentName\\\":\\\"meta.function-call.member.objc\\\",\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.arguments.end.bracket.round.function.member.objc\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#function-call-innards\\\"}]},\\\"numbers\\\":{\\\"begin\\\":\\\"(?<!\\\\\\\\w)(?=\\\\\\\\d|\\\\\\\\.\\\\\\\\d)\\\",\\\"end\\\":\\\"(?!(?:['0-9a-zA-Z_.]|(?<=[eEpP])[+-]))\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.unit.hexadecimal.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.numeric.hexadecimal.objc\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=\\\\\\\\h)'(?=\\\\\\\\h)\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.objc\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric.objc\\\"},\\\"4\\\":{\\\"name\\\":\\\"constant.numeric.hexadecimal.objc\\\"},\\\"5\\\":{\\\"name\\\":\\\"constant.numeric.hexadecimal.objc\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=\\\\\\\\h)'(?=\\\\\\\\h)\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.objc\\\"}]},\\\"6\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric.objc\\\"},\\\"8\\\":{\\\"name\\\":\\\"keyword.other.unit.exponent.hexadecimal.objc\\\"},\\\"9\\\":{\\\"name\\\":\\\"keyword.operator.plus.exponent.hexadecimal.objc\\\"},\\\"10\\\":{\\\"name\\\":\\\"keyword.operator.minus.exponent.hexadecimal.objc\\\"},\\\"11\\\":{\\\"name\\\":\\\"constant.numeric.exponent.hexadecimal.objc\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=\\\\\\\\h)'(?=\\\\\\\\h)\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.objc\\\"}]},\\\"12\\\":{\\\"name\\\":\\\"keyword.other.unit.suffix.floating-point.objc\\\"}},\\\"match\\\":\\\"(\\\\\\\\G0[xX])(\\\\\\\\h(?:\\\\\\\\h|((?<=\\\\\\\\h)'(?=\\\\\\\\h)))*)?((?:(?<=\\\\\\\\h)\\\\\\\\.|\\\\\\\\.(?=\\\\\\\\h)))(\\\\\\\\h(?:\\\\\\\\h|((?<=\\\\\\\\h)'(?=\\\\\\\\h)))*)?((?<!')([pP])(\\\\\\\\+)?(-)?((?-im:[0-9](?:[0-9]|(?<=\\\\\\\\h)'(?=\\\\\\\\h))*)))?([lLfF](?!\\\\\\\\w))?(?!(?:['0-9a-zA-Z_.]|(?<=[eEpP])[+-]))\\\"},{\\\"captures\\\":{\\\"2\\\":{\\\"name\\\":\\\"constant.numeric.decimal.objc\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=\\\\\\\\h)'(?=\\\\\\\\h)\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.objc\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric.objc\\\"},\\\"4\\\":{\\\"name\\\":\\\"constant.numeric.decimal.point.objc\\\"},\\\"5\\\":{\\\"name\\\":\\\"constant.numeric.decimal.objc\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=\\\\\\\\h)'(?=\\\\\\\\h)\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.objc\\\"}]},\\\"6\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric.objc\\\"},\\\"8\\\":{\\\"name\\\":\\\"keyword.other.unit.exponent.decimal.objc\\\"},\\\"9\\\":{\\\"name\\\":\\\"keyword.operator.plus.exponent.decimal.objc\\\"},\\\"10\\\":{\\\"name\\\":\\\"keyword.operator.minus.exponent.decimal.objc\\\"},\\\"11\\\":{\\\"name\\\":\\\"constant.numeric.exponent.decimal.objc\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=\\\\\\\\h)'(?=\\\\\\\\h)\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.objc\\\"}]},\\\"12\\\":{\\\"name\\\":\\\"keyword.other.unit.suffix.floating-point.objc\\\"}},\\\"match\\\":\\\"(\\\\\\\\G(?=[0-9.])(?!0[xXbB]))([0-9](?:[0-9]|((?<=\\\\\\\\h)'(?=\\\\\\\\h)))*)?((?:(?<=[0-9])\\\\\\\\.|\\\\\\\\.(?=[0-9])))([0-9](?:[0-9]|((?<=\\\\\\\\h)'(?=\\\\\\\\h)))*)?((?<!')([eE])(\\\\\\\\+)?(-)?((?-im:[0-9](?:[0-9]|(?<=\\\\\\\\h)'(?=\\\\\\\\h))*)))?([lLfF](?!\\\\\\\\w))?(?!(?:['0-9a-zA-Z_.]|(?<=[eEpP])[+-]))\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.unit.binary.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.numeric.binary.objc\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=\\\\\\\\h)'(?=\\\\\\\\h)\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.objc\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric.objc\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.other.unit.suffix.integer.objc\\\"}},\\\"match\\\":\\\"(\\\\\\\\G0[bB])([01](?:[01]|((?<=\\\\\\\\h)'(?=\\\\\\\\h)))*)((?:(?:(?:(?:(?:[uU]|[uU]ll?)|[uU]LL?)|ll?[uU]?)|LL?[uU]?)|[fF])(?!\\\\\\\\w))?(?!(?:['0-9a-zA-Z_.]|(?<=[eEpP])[+-]))\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.unit.octal.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.numeric.octal.objc\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=\\\\\\\\h)'(?=\\\\\\\\h)\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.objc\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric.objc\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.other.unit.suffix.integer.objc\\\"}},\\\"match\\\":\\\"(\\\\\\\\G0)((?:[0-7]|((?<=\\\\\\\\h)'(?=\\\\\\\\h)))+)((?:(?:(?:(?:(?:[uU]|[uU]ll?)|[uU]LL?)|ll?[uU]?)|LL?[uU]?)|[fF])(?!\\\\\\\\w))?(?!(?:['0-9a-zA-Z_.]|(?<=[eEpP])[+-]))\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.unit.hexadecimal.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.numeric.hexadecimal.objc\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=\\\\\\\\h)'(?=\\\\\\\\h)\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.objc\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric.objc\\\"},\\\"5\\\":{\\\"name\\\":\\\"keyword.other.unit.exponent.hexadecimal.objc\\\"},\\\"6\\\":{\\\"name\\\":\\\"keyword.operator.plus.exponent.hexadecimal.objc\\\"},\\\"7\\\":{\\\"name\\\":\\\"keyword.operator.minus.exponent.hexadecimal.objc\\\"},\\\"8\\\":{\\\"name\\\":\\\"constant.numeric.exponent.hexadecimal.objc\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=\\\\\\\\h)'(?=\\\\\\\\h)\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.objc\\\"}]},\\\"9\\\":{\\\"name\\\":\\\"keyword.other.unit.suffix.integer.objc\\\"}},\\\"match\\\":\\\"(\\\\\\\\G0[xX])(\\\\\\\\h(?:\\\\\\\\h|((?<=\\\\\\\\h)'(?=\\\\\\\\h)))*)((?<!')([pP])(\\\\\\\\+)?(-)?((?-im:[0-9](?:[0-9]|(?<=\\\\\\\\h)'(?=\\\\\\\\h))*)))?((?:(?:(?:(?:(?:[uU]|[uU]ll?)|[uU]LL?)|ll?[uU]?)|LL?[uU]?)|[fF])(?!\\\\\\\\w))?(?!(?:['0-9a-zA-Z_.]|(?<=[eEpP])[+-]))\\\"},{\\\"captures\\\":{\\\"2\\\":{\\\"name\\\":\\\"constant.numeric.decimal.objc\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=\\\\\\\\h)'(?=\\\\\\\\h)\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.objc\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric.objc\\\"},\\\"5\\\":{\\\"name\\\":\\\"keyword.other.unit.exponent.decimal.objc\\\"},\\\"6\\\":{\\\"name\\\":\\\"keyword.operator.plus.exponent.decimal.objc\\\"},\\\"7\\\":{\\\"name\\\":\\\"keyword.operator.minus.exponent.decimal.objc\\\"},\\\"8\\\":{\\\"name\\\":\\\"constant.numeric.exponent.decimal.objc\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=\\\\\\\\h)'(?=\\\\\\\\h)\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.objc\\\"}]},\\\"9\\\":{\\\"name\\\":\\\"keyword.other.unit.suffix.integer.objc\\\"}},\\\"match\\\":\\\"(\\\\\\\\G(?=[0-9.])(?!0[xXbB]))([0-9](?:[0-9]|((?<=\\\\\\\\h)'(?=\\\\\\\\h)))*)((?<!')([eE])(\\\\\\\\+)?(-)?((?-im:[0-9](?:[0-9]|(?<=\\\\\\\\h)'(?=\\\\\\\\h))*)))?((?:(?:(?:(?:(?:[uU]|[uU]ll?)|[uU]LL?)|ll?[uU]?)|LL?[uU]?)|[fF])(?!\\\\\\\\w))?(?!(?:['0-9a-zA-Z_.]|(?<=[eEpP])[+-]))\\\"},{\\\"match\\\":\\\"(?:['0-9a-zA-Z_.]|(?<=[eEpP])[+-])+\\\",\\\"name\\\":\\\"invalid.illegal.constant.numeric.objc\\\"}]},\\\"operators\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?<![\\\\\\\\w$])(sizeof)(?![\\\\\\\\w$])\\\",\\\"name\\\":\\\"keyword.operator.sizeof.objc\\\"},{\\\"match\\\":\\\"--\\\",\\\"name\\\":\\\"keyword.operator.decrement.objc\\\"},{\\\"match\\\":\\\"\\\\\\\\+\\\\\\\\+\\\",\\\"name\\\":\\\"keyword.operator.increment.objc\\\"},{\\\"match\\\":\\\"%=|\\\\\\\\+=|-=|\\\\\\\\*=|(?<!\\\\\\\\()/=\\\",\\\"name\\\":\\\"keyword.operator.assignment.compound.objc\\\"},{\\\"match\\\":\\\"&=|\\\\\\\\^=|<<=|>>=|\\\\\\\\|=\\\",\\\"name\\\":\\\"keyword.operator.assignment.compound.bitwise.objc\\\"},{\\\"match\\\":\\\"<<|>>\\\",\\\"name\\\":\\\"keyword.operator.bitwise.shift.objc\\\"},{\\\"match\\\":\\\"!=|<=|>=|==|[<>]\\\",\\\"name\\\":\\\"keyword.operator.comparison.objc\\\"},{\\\"match\\\":\\\"&&|!|\\\\\\\\|\\\\\\\\|\\\",\\\"name\\\":\\\"keyword.operator.logical.objc\\\"},{\\\"match\\\":\\\"[\\\\\\\\&|^~]\\\",\\\"name\\\":\\\"keyword.operator.objc\\\"},{\\\"match\\\":\\\"=\\\",\\\"name\\\":\\\"keyword.operator.assignment.objc\\\"},{\\\"match\\\":\\\"[%*/\\\\\\\\-+]\\\",\\\"name\\\":\\\"keyword.operator.objc\\\"},{\\\"begin\\\":\\\"(\\\\\\\\?)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.ternary.objc\\\"}},\\\"end\\\":\\\"(:)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.ternary.objc\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#function-call-innards\\\"},{\\\"include\\\":\\\"$base\\\"}]}]},\\\"parens\\\":{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.begin.bracket.round.objc\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.end.bracket.round.objc\\\"}},\\\"name\\\":\\\"meta.parens.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$base\\\"}]},\\\"parens-block\\\":{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.begin.bracket.round.objc\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.end.bracket.round.objc\\\"}},\\\"name\\\":\\\"meta.parens.block.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block_innards\\\"},{\\\"match\\\":\\\"(?-im:(?<!:):(?!:))\\\",\\\"name\\\":\\\"punctuation.range-based.objc\\\"}]},\\\"pragma-mark\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.preprocessor.pragma.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.directive.pragma.pragma-mark.objc\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"},\\\"4\\\":{\\\"name\\\":\\\"entity.name.tag.pragma-mark.objc\\\"}},\\\"match\\\":\\\"^\\\\\\\\s*(((#)\\\\\\\\s*pragma\\\\\\\\s+mark)\\\\\\\\s+(.*))\\\",\\\"name\\\":\\\"meta.section.objc\\\"},\\\"preprocessor-rule-conditional\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*if(?:n?def)?\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"end\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*endif\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(?=.)(?!/(?:/|\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n)))\\\",\\\"end\\\":\\\"(?=//)|(?=/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]},{\\\"include\\\":\\\"#preprocessor-rule-enabled-elif\\\"},{\\\"include\\\":\\\"#preprocessor-rule-enabled-else\\\"},{\\\"include\\\":\\\"#preprocessor-rule-disabled-elif\\\"},{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*elif\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"end\\\":\\\"(?=//)|(?=/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]},{\\\"include\\\":\\\"$base\\\"}]},{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"invalid.illegal.stray-$1.objc\\\"}},\\\"match\\\":\\\"^\\\\\\\\s*#\\\\\\\\s*(e(?:lse|lif|ndif))\\\\\\\\b\\\"}]},\\\"preprocessor-rule-conditional-block\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*if(?:n?def)?\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"end\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*endif\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(?=.)(?!/(?:/|\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n)))\\\",\\\"end\\\":\\\"(?=//)|(?=/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]},{\\\"include\\\":\\\"#preprocessor-rule-enabled-elif-block\\\"},{\\\"include\\\":\\\"#preprocessor-rule-enabled-else-block\\\"},{\\\"include\\\":\\\"#preprocessor-rule-disabled-elif\\\"},{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*elif\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"end\\\":\\\"(?=//)|(?=/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]},{\\\"include\\\":\\\"#block_innards\\\"}]},{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"invalid.illegal.stray-$1.objc\\\"}},\\\"match\\\":\\\"^\\\\\\\\s*#\\\\\\\\s*(e(?:lse|lif|ndif))\\\\\\\\b\\\"}]},\\\"preprocessor-rule-conditional-line\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\bdefined\\\\\\\\b(?:\\\\\\\\s*$|(?=\\\\\\\\s*\\\\\\\\(*\\\\\\\\s*(?!defined\\\\\\\\b)[a-zA-Z_$][\\\\\\\\w$]*\\\\\\\\b\\\\\\\\s*\\\\\\\\)*\\\\\\\\s*(?:\\\\\\\\n|//|/\\\\\\\\*|[?:]|&&|\\\\\\\\|\\\\\\\\||\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n)))\\\",\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},{\\\"match\\\":\\\"\\\\\\\\bdefined\\\\\\\\b\\\",\\\"name\\\":\\\"invalid.illegal.macro-name.objc\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#strings\\\"},{\\\"include\\\":\\\"#numbers\\\"},{\\\"begin\\\":\\\"\\\\\\\\?\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.ternary.objc\\\"}},\\\"end\\\":\\\":\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.ternary.objc\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]},{\\\"include\\\":\\\"#operators\\\"},{\\\"match\\\":\\\"\\\\\\\\b(NULL|true|false|TRUE|FALSE)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.objc\\\"},{\\\"match\\\":\\\"[a-zA-Z_$][\\\\\\\\w$]*\\\",\\\"name\\\":\\\"entity.name.function.preprocessor.objc\\\"},{\\\"include\\\":\\\"#line_continuation_character\\\"},{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.begin.bracket.round.objc\\\"}},\\\"end\\\":\\\"\\\\\\\\)|(?=//)|(?=/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.end.bracket.round.objc\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]}]},\\\"preprocessor-rule-define-line-blocks\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.block.begin.bracket.curly.objc\\\"}},\\\"end\\\":\\\"}|(?=\\\\\\\\s*#\\\\\\\\s*e(?:lif|lse|ndif)\\\\\\\\b)|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\s*\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.block.end.bracket.curly.objc\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-define-line-blocks\\\"},{\\\"include\\\":\\\"#preprocessor-rule-define-line-contents\\\"}]},{\\\"include\\\":\\\"#preprocessor-rule-define-line-contents\\\"}]},\\\"preprocessor-rule-define-line-contents\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#vararg_ellipses\\\"},{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.block.begin.bracket.curly.objc\\\"}},\\\"end\\\":\\\"}|(?=\\\\\\\\s*#\\\\\\\\s*e(?:lif|lse|ndif)\\\\\\\\b)|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\s*\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.block.end.bracket.curly.objc\\\"}},\\\"name\\\":\\\"meta.block.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-define-line-blocks\\\"}]},{\\\"match\\\":\\\"\\\\\\\\(\\\",\\\"name\\\":\\\"punctuation.section.parens.begin.bracket.round.objc\\\"},{\\\"match\\\":\\\"\\\\\\\\)\\\",\\\"name\\\":\\\"punctuation.section.parens.end.bracket.round.objc\\\"},{\\\"begin\\\":\\\"(?!(?:while|for|do|if|else|switch|catch|enumerate|return|typeid|alignof|alignas|sizeof|[cr]?iterate|and|and_eq|bitand|bitor|compl|not|not_eq|or|or_eq|typeid|xor|xor_eq|alignof|alignas|asm|__asm__|auto|bool|_Bool|char|_Complex|double|enum|float|_Imaginary|int|long|short|signed|struct|typedef|union|unsigned|void)\\\\\\\\s*\\\\\\\\()(?=(?:[A-Za-z_][A-Za-z0-9_]*+|::)++\\\\\\\\s*\\\\\\\\(|(?<=operator)(?:[-*\\\\\\\\&<>=+!]+|\\\\\\\\(\\\\\\\\)|\\\\\\\\[])\\\\\\\\s*\\\\\\\\()\\\",\\\"end\\\":\\\"(?<=\\\\\\\\))(?!\\\\\\\\w)|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\s*\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.function.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-define-line-functions\\\"}]},{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.objc\\\"}},\\\"end\\\":\\\"\\\\\\\"|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\s*\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.objc\\\"}},\\\"name\\\":\\\"string.quoted.double.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escaped_char\\\"},{\\\"include\\\":\\\"#string_placeholder\\\"},{\\\"include\\\":\\\"#line_continuation_character\\\"}]},{\\\"begin\\\":\\\"'\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.objc\\\"}},\\\"end\\\":\\\"'|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\s*\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.objc\\\"}},\\\"name\\\":\\\"string.quoted.single.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escaped_char\\\"},{\\\"include\\\":\\\"#line_continuation_character\\\"}]},{\\\"include\\\":\\\"#method_access\\\"},{\\\"include\\\":\\\"#member_access\\\"},{\\\"include\\\":\\\"$base\\\"}]},\\\"preprocessor-rule-define-line-functions\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#storage_types\\\"},{\\\"include\\\":\\\"#vararg_ellipses\\\"},{\\\"include\\\":\\\"#method_access\\\"},{\\\"include\\\":\\\"#member_access\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"begin\\\":\\\"(?!(?:while|for|do|if|else|switch|catch|enumerate|return|typeid|alignof|alignas|sizeof|[cr]?iterate|and|and_eq|bitand|bitor|compl|not|not_eq|or|or_eq|typeid|xor|xor_eq|alignof|alignas)\\\\\\\\s*\\\\\\\\()((?:[A-Za-z_][A-Za-z0-9_]*+|::)++|(?<=operator)(?:[-*\\\\\\\\&<>=+!]+|\\\\\\\\(\\\\\\\\)|\\\\\\\\[]))\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.section.arguments.begin.bracket.round.objc\\\"}},\\\"end\\\":\\\"(\\\\\\\\))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\s*\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.arguments.end.bracket.round.objc\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-define-line-functions\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.begin.bracket.round.objc\\\"}},\\\"end\\\":\\\"(\\\\\\\\))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\s*\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.parens.end.bracket.round.objc\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-define-line-functions\\\"}]},{\\\"include\\\":\\\"#preprocessor-rule-define-line-contents\\\"}]},\\\"preprocessor-rule-disabled\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*if\\\\\\\\b)(?=\\\\\\\\s*\\\\\\\\(*\\\\\\\\b0+\\\\\\\\b\\\\\\\\)*\\\\\\\\s*(?:$|//|/\\\\\\\\*))\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"end\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*endif\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(?=.)(?!/(?:/|\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n)))\\\",\\\"end\\\":\\\"(?=//)|(?=/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))|(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]},{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#preprocessor-rule-enabled-elif\\\"},{\\\"include\\\":\\\"#preprocessor-rule-enabled-else\\\"},{\\\"include\\\":\\\"#preprocessor-rule-disabled-elif\\\"},{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*elif\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*e(?:lif|lse|ndif)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(?=.)(?!/(?:/|\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n)))\\\",\\\"end\\\":\\\"(?=//)|(?=/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]},{\\\"include\\\":\\\"$base\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\n\\\",\\\"contentName\\\":\\\"comment.block.preprocessor.if-branch.objc\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*e(?:lse|lif|ndif)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"}]}]}]},\\\"preprocessor-rule-disabled-block\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*if\\\\\\\\b)(?=\\\\\\\\s*\\\\\\\\(*\\\\\\\\b0+\\\\\\\\b\\\\\\\\)*\\\\\\\\s*(?:$|//|/\\\\\\\\*))\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"end\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*endif\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(?=.)(?!/(?:/|\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n)))\\\",\\\"end\\\":\\\"(?=//)|(?=/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))|(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]},{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#preprocessor-rule-enabled-elif-block\\\"},{\\\"include\\\":\\\"#preprocessor-rule-enabled-else-block\\\"},{\\\"include\\\":\\\"#preprocessor-rule-disabled-elif\\\"},{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*elif\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*e(?:lif|lse|ndif)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(?=.)(?!/(?:/|\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n)))\\\",\\\"end\\\":\\\"(?=//)|(?=/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]},{\\\"include\\\":\\\"#block_innards\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\n\\\",\\\"contentName\\\":\\\"comment.block.preprocessor.if-branch.in-block.objc\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*e(?:lse|lif|ndif)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"}]}]}]},\\\"preprocessor-rule-disabled-elif\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*elif\\\\\\\\b)(?=\\\\\\\\s*\\\\\\\\(*\\\\\\\\b0+\\\\\\\\b\\\\\\\\)*\\\\\\\\s*(?:$|//|/\\\\\\\\*))\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*e(?:lif|lse|ndif)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(?=.)(?!/(?:/|\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n)))\\\",\\\"end\\\":\\\"(?=//)|(?=/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]},{\\\"include\\\":\\\"#comments\\\"},{\\\"begin\\\":\\\"\\\\\\\\n\\\",\\\"contentName\\\":\\\"comment.block.preprocessor.elif-branch.objc\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*e(?:lse|lif|ndif)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"}]}]},\\\"preprocessor-rule-enabled\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*if\\\\\\\\b)(?=\\\\\\\\s*\\\\\\\\(*\\\\\\\\b0*1\\\\\\\\b\\\\\\\\)*\\\\\\\\s*(?:$|//|/\\\\\\\\*))\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.numeric.preprocessor.objc\\\"}},\\\"end\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*endif\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(?=.)(?!/(?:/|\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n)))\\\",\\\"end\\\":\\\"(?=//)|(?=/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))|(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]},{\\\"include\\\":\\\"#comments\\\"},{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*else\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"contentName\\\":\\\"comment.block.preprocessor.else-branch.objc\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*endif\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"}]},{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*elif\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"contentName\\\":\\\"comment.block.preprocessor.if-branch.objc\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*e(?:lse|lif|ndif)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\n\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*e(?:lse|lif|ndif)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$base\\\"}]}]}]},\\\"preprocessor-rule-enabled-block\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*if\\\\\\\\b)(?=\\\\\\\\s*\\\\\\\\(*\\\\\\\\b0*1\\\\\\\\b\\\\\\\\)*\\\\\\\\s*(?:$|//|/\\\\\\\\*))\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"end\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*endif\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(?=.)(?!/(?:/|\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n)))\\\",\\\"end\\\":\\\"(?=//)|(?=/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))|(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]},{\\\"include\\\":\\\"#comments\\\"},{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*else\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"contentName\\\":\\\"comment.block.preprocessor.else-branch.in-block.objc\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*endif\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"}]},{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*elif\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"contentName\\\":\\\"comment.block.preprocessor.if-branch.in-block.objc\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*e(?:lse|lif|ndif)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\n\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*e(?:lse|lif|ndif)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block_innards\\\"}]}]}]},\\\"preprocessor-rule-enabled-elif\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*elif\\\\\\\\b)(?=\\\\\\\\s*\\\\\\\\(*\\\\\\\\b0*1\\\\\\\\b\\\\\\\\)*\\\\\\\\s*(?:$|//|/\\\\\\\\*))\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*endif\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(?=.)(?!/(?:/|\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n)))\\\",\\\"end\\\":\\\"(?=//)|(?=/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]},{\\\"include\\\":\\\"#comments\\\"},{\\\"begin\\\":\\\"\\\\\\\\n\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*endif\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*(else)\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"contentName\\\":\\\"comment.block.preprocessor.elif-branch.objc\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*endif\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"}]},{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*(elif)\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"contentName\\\":\\\"comment.block.preprocessor.elif-branch.objc\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*e(?:lse|lif|ndif)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"}]},{\\\"include\\\":\\\"$base\\\"}]}]},\\\"preprocessor-rule-enabled-elif-block\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*elif\\\\\\\\b)(?=\\\\\\\\s*\\\\\\\\(*\\\\\\\\b0*1\\\\\\\\b\\\\\\\\)*\\\\\\\\s*(?:$|//|/\\\\\\\\*))\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*endif\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(?=.)(?!/(?:/|\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n)))\\\",\\\"end\\\":\\\"(?=//)|(?=/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]},{\\\"include\\\":\\\"#comments\\\"},{\\\"begin\\\":\\\"\\\\\\\\n\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*endif\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*(else)\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"contentName\\\":\\\"comment.block.preprocessor.elif-branch.in-block.objc\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*endif\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"}]},{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*(elif)\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"contentName\\\":\\\"comment.block.preprocessor.elif-branch.objc\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*e(?:lse|lif|ndif)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"}]},{\\\"include\\\":\\\"#block_innards\\\"}]}]},\\\"preprocessor-rule-enabled-else\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*else\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*endif\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$base\\\"}]},\\\"preprocessor-rule-enabled-else-block\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*else\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*endif\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block_innards\\\"}]},\\\"probably_a_parameter\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.parameter.probably.objc\\\"}},\\\"match\\\":\\\"(?<=(?:[a-zA-Z_0-9] |[\\\\\\\\&*>\\\\\\\\])]))\\\\\\\\s*([a-zA-Z_]\\\\\\\\w*)\\\\\\\\s*(?=(?:\\\\\\\\[]\\\\\\\\s*)?[,)])\\\"},\\\"static_assert\\\":{\\\"begin\\\":\\\"(static_assert|_Static_assert)\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.static_assert.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.section.arguments.begin.bracket.round.objc\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.arguments.end.bracket.round.objc\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"(,)\\\\\\\\s*(?=(?:L|u8|u|U\\\\\\\\s*\\\\\\\")?)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.delimiter.objc\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"name\\\":\\\"meta.static_assert.message.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_context\\\"},{\\\"include\\\":\\\"#string_context_c\\\"}]},{\\\"include\\\":\\\"#function_call_context\\\"}]},\\\"storage_types\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?-im:(?<!\\\\\\\\w)(?:void|char|short|int|signed|unsigned|long|float|double|bool|_Bool)(?!\\\\\\\\w))\\\",\\\"name\\\":\\\"storage.type.built-in.primitive.objc\\\"},{\\\"match\\\":\\\"(?-im:(?<!\\\\\\\\w)(?:_Complex|_Imaginary|u_char|u_short|u_int|u_long|ushort|uint|u_quad_t|quad_t|qaddr_t|caddr_t|daddr_t|div_t|dev_t|fixpt_t|blkcnt_t|blksize_t|gid_t|in_addr_t|in_port_t|ino_t|key_t|mode_t|nlink_t|id_t|pid_t|off_t|segsz_t|swblk_t|uid_t|id_t|clock_t|size_t|ssize_t|time_t|useconds_t|suseconds_t|pthread_attr_t|pthread_cond_t|pthread_condattr_t|pthread_mutex_t|pthread_mutexattr_t|pthread_once_t|pthread_rwlock_t|pthread_rwlockattr_t|pthread_t|pthread_key_t|int8_t|int16_t|int32_t|int64_t|uint8_t|uint16_t|uint32_t|uint64_t|int_least8_t|int_least16_t|int_least32_t|int_least64_t|uint_least8_t|uint_least16_t|uint_least32_t|uint_least64_t|int_fast8_t|int_fast16_t|int_fast32_t|int_fast64_t|uint_fast8_t|uint_fast16_t|uint_fast32_t|uint_fast64_t|intptr_t|uintptr_t|intmax_t|intmax_t|uintmax_t|uintmax_t|memory_order|atomic_bool|atomic_char|atomic_schar|atomic_uchar|atomic_short|atomic_ushort|atomic_int|atomic_uint|atomic_long|atomic_ulong|atomic_llong|atomic_ullong|atomic_char16_t|atomic_char32_t|atomic_wchar_t|atomic_int_least8_t|atomic_uint_least8_t|atomic_int_least16_t|atomic_uint_least16_t|atomic_int_least32_t|atomic_uint_least32_t|atomic_int_least64_t|atomic_uint_least64_t|atomic_int_fast8_t|atomic_uint_fast8_t|atomic_int_fast16_t|atomic_uint_fast16_t|atomic_int_fast32_t|atomic_uint_fast32_t|atomic_int_fast64_t|atomic_uint_fast64_t|atomic_intptr_t|atomic_uintptr_t|atomic_size_t|atomic_ptrdiff_t|atomic_intmax_t|atomic_uintmax_t)(?!\\\\\\\\w))\\\",\\\"name\\\":\\\"storage.type.built-in.objc\\\"},{\\\"match\\\":\\\"(?-im:\\\\\\\\b(asm|__asm__|enum|struct|union)\\\\\\\\b)\\\",\\\"name\\\":\\\"storage.type.$1.objc\\\"}]},\\\"string_escaped_char\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\([\\\\\\\\\\\\\\\\abefnprtv'\\\\\\\"?]|[0-3]\\\\\\\\d{0,2}|[4-7]\\\\\\\\d?|x\\\\\\\\h{0,2}|u\\\\\\\\h{0,4}|U\\\\\\\\h{0,8})\\\",\\\"name\\\":\\\"constant.character.escape.objc\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"invalid.illegal.unknown-escape.objc\\\"}]},\\\"string_placeholder\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"%(\\\\\\\\d+\\\\\\\\$)?[#0\\\\\\\\- +']*[,;:_]?((-?\\\\\\\\d+)|\\\\\\\\*(-?\\\\\\\\d+\\\\\\\\$)?)?(\\\\\\\\.((-?\\\\\\\\d+)|\\\\\\\\*(-?\\\\\\\\d+\\\\\\\\$)?)?)?(hh|h|ll|[ljtzqL]|vh|vl|v|hv|hl)?[diouxXDOUeEfFgGaACcSspn%]\\\",\\\"name\\\":\\\"constant.other.placeholder.objc\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"invalid.illegal.placeholder.objc\\\"}},\\\"match\\\":\\\"(%)(?!\\\\\\\"\\\\\\\\s*(PRI|SCN))\\\"}]},\\\"strings\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.objc\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.objc\\\"}},\\\"name\\\":\\\"string.quoted.double.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escaped_char\\\"},{\\\"include\\\":\\\"#string_placeholder\\\"},{\\\"include\\\":\\\"#line_continuation_character\\\"}]},{\\\"begin\\\":\\\"'\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.objc\\\"}},\\\"end\\\":\\\"'\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.objc\\\"}},\\\"name\\\":\\\"string.quoted.single.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escaped_char\\\"},{\\\"include\\\":\\\"#line_continuation_character\\\"}]}]},\\\"switch_conditional_parentheses\\\":{\\\"begin\\\":\\\"(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.parens.begin.bracket.round.conditional.switch.objc\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.parens.end.bracket.round.conditional.switch.objc\\\"}},\\\"name\\\":\\\"meta.conditional.switch.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#conditional_context\\\"}]},\\\"switch_statement\\\":{\\\"begin\\\":\\\"(((?<!\\\\\\\\w)switch(?!\\\\\\\\w)))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.head.switch.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.switch.objc\\\"}},\\\"end\\\":\\\"(?:(?<=})|(?=[;>\\\\\\\\[\\\\\\\\]=]))\\\",\\\"name\\\":\\\"meta.block.switch.objc\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G ?\\\",\\\"end\\\":\\\"((?:\\\\\\\\{|(?=;)))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.block.begin.bracket.curly.switch.objc\\\"}},\\\"name\\\":\\\"meta.head.switch.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#switch_conditional_parentheses\\\"},{\\\"include\\\":\\\"$base\\\"}]},{\\\"begin\\\":\\\"(?<=\\\\\\\\{)\\\",\\\"end\\\":\\\"(})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.block.end.bracket.curly.switch.objc\\\"}},\\\"name\\\":\\\"meta.body.switch.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#default_statement\\\"},{\\\"include\\\":\\\"#case_statement\\\"},{\\\"include\\\":\\\"$base\\\"},{\\\"include\\\":\\\"#block_innards\\\"}]},{\\\"begin\\\":\\\"(?<=})[\\\\\\\\s\\\\\\\\n]*\\\",\\\"end\\\":\\\"[\\\\\\\\s\\\\\\\\n]*(?=;)\\\",\\\"name\\\":\\\"meta.tail.switch.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$base\\\"}]}]},\\\"vararg_ellipses\\\":{\\\"match\\\":\\\"(?<!\\\\\\\\.)\\\\\\\\.\\\\\\\\.\\\\\\\\.(?!\\\\\\\\.)\\\",\\\"name\\\":\\\"punctuation.vararg-ellipses.objc\\\"}}},\\\"comment\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"/\\\\\\\\*\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.objc\\\"}},\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"name\\\":\\\"comment.block.objc\\\"},{\\\"begin\\\":\\\"(^[ \\\\\\\\t]+)?(?=//)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.comment.leading.objc\\\"}},\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"//\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.objc\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.line.double-slash.objc\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?>\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n)\\\",\\\"name\\\":\\\"punctuation.separator.continuation.objc\\\"}]}]}]},\\\"disabled\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*#\\\\\\\\s*if(n?def)?\\\\\\\\b.*$\\\",\\\"end\\\":\\\"^\\\\\\\\s*#\\\\\\\\s*endif\\\\\\\\b.*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"}]},\\\"implementation_innards\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-enabled-implementation\\\"},{\\\"include\\\":\\\"#preprocessor-rule-disabled-implementation\\\"},{\\\"include\\\":\\\"#preprocessor-rule-other-implementation\\\"},{\\\"include\\\":\\\"#property_directive\\\"},{\\\"include\\\":\\\"#method_super\\\"},{\\\"include\\\":\\\"$base\\\"}]},\\\"interface_innards\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-enabled-interface\\\"},{\\\"include\\\":\\\"#preprocessor-rule-disabled-interface\\\"},{\\\"include\\\":\\\"#preprocessor-rule-other-interface\\\"},{\\\"include\\\":\\\"#properties\\\"},{\\\"include\\\":\\\"#protocol_list\\\"},{\\\"include\\\":\\\"#method\\\"},{\\\"include\\\":\\\"$base\\\"}]},\\\"method\\\":{\\\"begin\\\":\\\"^([-+])\\\\\\\\s*\\\",\\\"end\\\":\\\"(?=[{#])|;\\\",\\\"name\\\":\\\"meta.function.objc\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.type.begin.objc\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\\\\\\s*(\\\\\\\\w+\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.type.end.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.objc\\\"}},\\\"name\\\":\\\"meta.return-type.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#protocol_list\\\"},{\\\"include\\\":\\\"#protocol_type_qualifier\\\"},{\\\"include\\\":\\\"$base\\\"}]},{\\\"match\\\":\\\"\\\\\\\\b\\\\\\\\w+(?=:)\\\",\\\"name\\\":\\\"entity.name.function.name-of-parameter.objc\\\"},{\\\"begin\\\":\\\"((:))\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.name-of-parameter.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.arguments.objc\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.type.begin.objc\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\\\\\\s*(\\\\\\\\w+\\\\\\\\b)?\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.type.end.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.parameter.function.objc\\\"}},\\\"name\\\":\\\"meta.argument-type.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#protocol_list\\\"},{\\\"include\\\":\\\"#protocol_type_qualifier\\\"},{\\\"include\\\":\\\"$base\\\"}]},{\\\"include\\\":\\\"#comment\\\"}]},\\\"method_super\\\":{\\\"begin\\\":\\\"^(?=[-+])\\\",\\\"end\\\":\\\"(?<=})|(?=#)\\\",\\\"name\\\":\\\"meta.function-with-body.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#method\\\"},{\\\"include\\\":\\\"$base\\\"}]},\\\"pragma-mark\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.import.pragma.objc\\\"},\\\"3\\\":{\\\"name\\\":\\\"meta.toc-list.pragma-mark.objc\\\"}},\\\"match\\\":\\\"^\\\\\\\\s*(#\\\\\\\\s*(pragma\\\\\\\\s+mark)\\\\\\\\s+(.*))\\\",\\\"name\\\":\\\"meta.section.objc\\\"},\\\"preprocessor-rule-disabled-implementation\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*(#(if)\\\\\\\\s+(0)\\\\\\\\b).*\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.import.if.objc\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.numeric.preprocessor.objc\\\"}},\\\"end\\\":\\\"^\\\\\\\\s*(#\\\\\\\\s*(endif)\\\\\\\\b.*?(?:(?=/[/*])|$))\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*(#\\\\\\\\s*(else)\\\\\\\\b)\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.import.else.objc\\\"}},\\\"end\\\":\\\"(?=^\\\\\\\\s*#\\\\\\\\s*endif\\\\\\\\b.*?(?:(?=/[/*])|$))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#interface_innards\\\"}]},{\\\"begin\\\":\\\"\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*#\\\\\\\\s*(e(?:lse|ndif))\\\\\\\\b.*?(?:(?=/[/*])|$))\\\",\\\"name\\\":\\\"comment.block.preprocessor.if-branch.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"}]}]},\\\"preprocessor-rule-disabled-interface\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*(#(if)\\\\\\\\s+(0)\\\\\\\\b).*\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.import.if.objc\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.numeric.preprocessor.objc\\\"}},\\\"end\\\":\\\"^\\\\\\\\s*(#\\\\\\\\s*(endif)\\\\\\\\b.*?(?:(?=/[/*])|$))\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*(#\\\\\\\\s*(else)\\\\\\\\b)\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.import.else.objc\\\"}},\\\"end\\\":\\\"(?=^\\\\\\\\s*#\\\\\\\\s*endif\\\\\\\\b.*?(?:(?=/[/*])|$))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#interface_innards\\\"}]},{\\\"begin\\\":\\\"\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*#\\\\\\\\s*(e(?:lse|ndif))\\\\\\\\b.*?(?:(?=/[/*])|$))\\\",\\\"name\\\":\\\"comment.block.preprocessor.if-branch.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"}]}]},\\\"preprocessor-rule-enabled-implementation\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*(#(if)\\\\\\\\s+(0*1)\\\\\\\\b)\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.import.if.objc\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.numeric.preprocessor.objc\\\"}},\\\"end\\\":\\\"^\\\\\\\\s*(#\\\\\\\\s*(endif)\\\\\\\\b.*?(?:(?=/[/*])|$))\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*(#\\\\\\\\s*(else)\\\\\\\\b).*\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.import.else.objc\\\"}},\\\"contentName\\\":\\\"comment.block.preprocessor.else-branch.objc\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*#\\\\\\\\s*endif\\\\\\\\b.*?(?:(?=/[/*])|$))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"}]},{\\\"begin\\\":\\\"\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*#\\\\\\\\s*(e(?:lse|ndif))\\\\\\\\b.*?(?:(?=/[/*])|$))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#implementation_innards\\\"}]}]},\\\"preprocessor-rule-enabled-interface\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*(#(if)\\\\\\\\s+(0*1)\\\\\\\\b)\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.import.if.objc\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.numeric.preprocessor.objc\\\"}},\\\"end\\\":\\\"^\\\\\\\\s*(#\\\\\\\\s*(endif)\\\\\\\\b.*?(?:(?=/[/*])|$))\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*(#\\\\\\\\s*(else)\\\\\\\\b).*\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.import.else.objc\\\"}},\\\"contentName\\\":\\\"comment.block.preprocessor.else-branch.objc\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*#\\\\\\\\s*endif\\\\\\\\b.*?(?:(?=/[/*])|$))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"}]},{\\\"begin\\\":\\\"\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*#\\\\\\\\s*(e(?:lse|ndif))\\\\\\\\b.*?(?:(?=/[/*])|$))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#interface_innards\\\"}]}]},\\\"preprocessor-rule-other-implementation\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*(#\\\\\\\\s*(if(n?def)?)\\\\\\\\b.*?(?:(?=/[/*])|$))\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.import.objc\\\"}},\\\"end\\\":\\\"^\\\\\\\\s*(#\\\\\\\\s*(endif)\\\\\\\\b).*?(?:(?=/[/*])|$)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#implementation_innards\\\"}]},\\\"preprocessor-rule-other-interface\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*(#\\\\\\\\s*(if(n?def)?)\\\\\\\\b.*?(?:(?=/[/*])|$))\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.import.objc\\\"}},\\\"end\\\":\\\"^\\\\\\\\s*(#\\\\\\\\s*(endif)\\\\\\\\b).*?(?:(?=/[/*])|$)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#interface_innards\\\"}]},\\\"properties\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"((@)property)\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.property.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.objc\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.section.scope.begin.objc\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.scope.end.objc\\\"}},\\\"name\\\":\\\"meta.property-with-attributes.objc\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(getter|setter|readonly|readwrite|assign|retain|copy|nonatomic|atomic|strong|weak|nonnull|nullable|null_resettable|null_unspecified|class|direct)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.property.attribute.objc\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.property.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.objc\\\"}},\\\"match\\\":\\\"((@)property)\\\\\\\\b\\\",\\\"name\\\":\\\"meta.property.objc\\\"}]},\\\"property_directive\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.objc\\\"}},\\\"match\\\":\\\"(@)(dynamic|synthesize)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.property.directive.objc\\\"},\\\"protocol_list\\\":{\\\"begin\\\":\\\"(<)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.scope.begin.objc\\\"}},\\\"end\\\":\\\"(>)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.scope.end.objc\\\"}},\\\"name\\\":\\\"meta.protocol-list.objc\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\bNS(GlyphStorage|M(utableCopying|enuItem)|C(hangeSpelling|o(ding|pying|lorPicking(Custom|Default)))|T(oolbarItemValidations|ext(Input|AttachmentCell))|I(nputServ(iceProvider|erMouseTracker)|gnoreMisspelledWords)|Obj(CTypeSerializationCallBack|ect)|D(ecimalNumberBehaviors|raggingInfo)|U(serInterfaceValidations|RL(HandleClient|DownloadDelegate|ProtocolClient|AuthenticationChallengeSender))|Validated(ToobarItem|UserInterfaceItem)|Locking)\\\\\\\\b\\\",\\\"name\\\":\\\"support.other.protocol.objc\\\"}]},\\\"protocol_type_qualifier\\\":{\\\"match\\\":\\\"\\\\\\\\b(in|out|inout|oneway|bycopy|byref|nonnull|nullable|_Nonnull|_Nullable|_Null_unspecified)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.modifier.protocol.objc\\\"},\\\"special_variables\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b_cmd\\\\\\\\b\\\",\\\"name\\\":\\\"variable.other.selector.objc\\\"},{\\\"match\\\":\\\"\\\\\\\\b(s(?:elf|uper))\\\\\\\\b\\\",\\\"name\\\":\\\"variable.language.objc\\\"}]},\\\"string_escaped_char\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\([\\\\\\\\\\\\\\\\abefnprtv'\\\\\\\"?]|[0-3]\\\\\\\\d{0,2}|[4-7]\\\\\\\\d?|x\\\\\\\\h{0,2}|u\\\\\\\\h{0,4}|U\\\\\\\\h{0,8})\\\",\\\"name\\\":\\\"constant.character.escape.objc\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"invalid.illegal.unknown-escape.objc\\\"}]},\\\"string_placeholder\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"%(\\\\\\\\d+\\\\\\\\$)?[#0\\\\\\\\- +']*[,;:_]?((-?\\\\\\\\d+)|\\\\\\\\*(-?\\\\\\\\d+\\\\\\\\$)?)?(\\\\\\\\.((-?\\\\\\\\d+)|\\\\\\\\*(-?\\\\\\\\d+\\\\\\\\$)?)?)?(hh|h|ll|[ljtzqL]|vh|vl|v|hv|hl)?[diouxXDOUeEfFgGaACcSspn%]\\\",\\\"name\\\":\\\"constant.other.placeholder.objc\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"invalid.illegal.placeholder.objc\\\"}},\\\"match\\\":\\\"(%)(?!\\\\\\\"\\\\\\\\s*(PRI|SCN))\\\"}]}},\\\"scopeName\\\":\\\"source.objc\\\",\\\"aliases\\\":[\\\"objc\\\"]}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/objective-c.mjs\n"));

/***/ })

}]);