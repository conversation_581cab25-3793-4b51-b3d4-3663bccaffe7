"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_shellscript_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/shellscript.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/shellscript.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Shell\\\",\\\"name\\\":\\\"shellscript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#initial_context\\\"}],\\\"repository\\\":{\\\"alias_statement\\\":{\\\"begin\\\":\\\"[ \\\\\\\\t]*+(alias)[ \\\\\\\\t]*+((?:((?<!\\\\\\\\w)-\\\\\\\\w+\\\\\\\\b)[ \\\\\\\\t]*+)*)[ \\\\\\\\t]*+((?<!\\\\\\\\w)[a-zA-Z_0-9-]+(?!\\\\\\\\w))(?:(\\\\\\\\[)((?:(?:(?:\\\\\\\\$?(?<!\\\\\\\\w)[a-zA-Z_0-9-]+(?!\\\\\\\\w)|@)|\\\\\\\\*)|(-?\\\\\\\\d+)))(]))?(?:(?:(=)|(\\\\\\\\+=))|(-=))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.alias.shell\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?<!\\\\\\\\w)-\\\\\\\\w+\\\\\\\\b\\\",\\\"name\\\":\\\"string.unquoted.argument.shell constant.other.option.shell\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"string.unquoted.argument.shell constant.other.option.shell\\\"},\\\"4\\\":{\\\"name\\\":\\\"variable.other.assignment.shell\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.definition.array.access.shell\\\"},\\\"6\\\":{\\\"name\\\":\\\"variable.other.assignment.shell\\\"},\\\"7\\\":{\\\"name\\\":\\\"constant.numeric.shell constant.numeric.integer.shell\\\"},\\\"8\\\":{\\\"name\\\":\\\"punctuation.definition.array.access.shell\\\"},\\\"9\\\":{\\\"name\\\":\\\"keyword.operator.assignment.shell\\\"},\\\"10\\\":{\\\"name\\\":\\\"keyword.operator.assignment.compound.shell\\\"},\\\"11\\\":{\\\"name\\\":\\\"keyword.operator.assignment.compound.shell\\\"}},\\\"end\\\":\\\"(?:(?=[ \\\\\\\\t]|$)|(?:(?:(?:(;)|(&&))|(\\\\\\\\|\\\\\\\\|))|(&)))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.terminator.statement.semicolon.shell\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.statement.and.shell\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.statement.or.shell\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.separator.statement.background.shell\\\"}},\\\"name\\\":\\\"meta.expression.assignment.alias.shell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#normal_context\\\"}]},\\\"argument\\\":{\\\"begin\\\":\\\"[ \\\\\\\\t]++(?!(?:[\\\\\\\\&|(\\\\\\\\[#\\\\\\\\n]|$|;))\\\",\\\"beginCaptures\\\":{},\\\"end\\\":\\\"(?=[ \\\\\\\\t;|\\\\\\\\&]|$|[\\\\\\\\n)`])\\\",\\\"endCaptures\\\":{},\\\"name\\\":\\\"meta.argument.shell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#argument_context\\\"},{\\\"include\\\":\\\"#line_continuation\\\"}]},\\\"argument_context\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"string.unquoted.argument.shell\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\*\\\",\\\"name\\\":\\\"variable.language.special.wildcard.shell\\\"},{\\\"include\\\":\\\"#variable\\\"},{\\\"include\\\":\\\"#numeric_literal\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.language.$1.shell\\\"}},\\\"match\\\":\\\"(?<!\\\\\\\\w)(\\\\\\\\b(?:true|false)\\\\\\\\b)(?!\\\\\\\\w)\\\"}]}},\\\"match\\\":\\\"[ \\\\\\\\t]*+([^ \\\\\\\\t\\\\\\\\n>\\\\\\\\&;<()$`\\\\\\\\\\\\\\\\\\\\\\\"'|]+(?!>))\\\"},{\\\"include\\\":\\\"#normal_context\\\"}]},\\\"arithmetic_double\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.arithmetic.double.shell\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\\\\\\s*\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.arithmetic.double.shell\\\"}},\\\"name\\\":\\\"meta.arithmetic.shell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#math\\\"},{\\\"include\\\":\\\"#string\\\"}]}]},\\\"arithmetic_no_dollar\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.arithmetic.single.shell\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.arithmetic.single.shell\\\"}},\\\"name\\\":\\\"meta.arithmetic.shell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#math\\\"},{\\\"include\\\":\\\"#string\\\"}]}]},\\\"array_access_inline\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.array.shell\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#special_expansion\\\"},{\\\"include\\\":\\\"#string\\\"},{\\\"include\\\":\\\"#variable\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"punctuation.section.array.shell\\\"}},\\\"match\\\":\\\"(\\\\\\\\[)([^\\\\\\\\[\\\\\\\\]]+)(])\\\"},\\\"array_value\\\":{\\\"begin\\\":\\\"[ \\\\\\\\t]*+((?<!\\\\\\\\w)[a-zA-Z_0-9-]+(?!\\\\\\\\w))(?:(\\\\\\\\[)((?:(?:(?:\\\\\\\\$?(?<!\\\\\\\\w)[a-zA-Z_0-9-]+(?!\\\\\\\\w)|@)|\\\\\\\\*)|(-?\\\\\\\\d+)))(]))?(?:(?:(=)|(\\\\\\\\+=))|(-=))[ \\\\\\\\t]*+(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.assignment.shell\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.array.access.shell\\\"},\\\"3\\\":{\\\"name\\\":\\\"variable.other.assignment.shell\\\"},\\\"4\\\":{\\\"name\\\":\\\"constant.numeric.shell constant.numeric.integer.shell\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.definition.array.access.shell\\\"},\\\"6\\\":{\\\"name\\\":\\\"keyword.operator.assignment.shell\\\"},\\\"7\\\":{\\\"name\\\":\\\"keyword.operator.assignment.compound.shell\\\"},\\\"8\\\":{\\\"name\\\":\\\"keyword.operator.assignment.compound.shell\\\"},\\\"9\\\":{\\\"name\\\":\\\"punctuation.definition.array.shell\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.array.shell\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.assignment.array.shell entity.other.attribute-name.shell\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.assignment.shell punctuation.definition.assignment.shell\\\"}},\\\"match\\\":\\\"((?<!\\\\\\\\w)[a-zA-Z_0-9-]+(?!\\\\\\\\w))(=)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.bracket.named-array.shell\\\"},\\\"2\\\":{\\\"name\\\":\\\"string.unquoted.shell entity.other.attribute-name.bracket.shell\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.bracket.named-array.shell\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.assignment.shell\\\"}},\\\"match\\\":\\\"(\\\\\\\\[)(.+?)(])(=)\\\"},{\\\"include\\\":\\\"#normal_context\\\"},{\\\"include\\\":\\\"#simple_unquoted\\\"}]},\\\"assignment_statement\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#array_value\\\"},{\\\"include\\\":\\\"#modified_assignment_statement\\\"},{\\\"include\\\":\\\"#normal_assignment_statement\\\"}]},\\\"basic_command_name\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.$1.shell\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.call.shell entity.name.command.shell\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<!\\\\\\\\w)(?:continue|return|break)(?!\\\\\\\\w)\\\",\\\"name\\\":\\\"keyword.control.$0.shell\\\"},{\\\"match\\\":\\\"(?<!\\\\\\\\w)(?:unfunction|continue|autoload|unsetopt|bindkey|builtin|getopts|command|declare|unalias|history|unlimit|typeset|suspend|source|printf|unhash|disown|ulimit|return|which|alias|break|false|print|shift|times|umask|umask|unset|read|type|exec|eval|wait|echo|dirs|jobs|kill|hash|stat|exit|test|trap|true|let|set|pwd|cd|fg|bg|fc|[:.])(?!/)(?!\\\\\\\\w)(?!-)\\\",\\\"name\\\":\\\"support.function.builtin.shell\\\"},{\\\"include\\\":\\\"#variable\\\"}]}},\\\"match\\\":\\\"(?!(?:[!\\\\\\\\&|(){\\\\\\\\[<>#\\\\\\\\n]|$|[; \\\\\\\\t]))(?!nocorrect |nocorrect\\\\\\\\t|nocorrect$|readonly |readonly\\\\\\\\t|readonly$|function |function\\\\\\\\t|function$|foreach |foreach\\\\\\\\t|foreach$|coproc |coproc\\\\\\\\t|coproc$|logout |logout\\\\\\\\t|logout$|export |export\\\\\\\\t|export$|select |select\\\\\\\\t|select$|repeat |repeat\\\\\\\\t|repeat$|pushd |pushd\\\\\\\\t|pushd$|until |until\\\\\\\\t|until$|while |while\\\\\\\\t|while$|local |local\\\\\\\\t|local$|case |case\\\\\\\\t|case$|done |done\\\\\\\\t|done$|elif |elif\\\\\\\\t|elif$|else |else\\\\\\\\t|else$|esac |esac\\\\\\\\t|esac$|popd |popd\\\\\\\\t|popd$|then |then\\\\\\\\t|then$|time |time\\\\\\\\t|time$|for |for\\\\\\\\t|for$|end |end\\\\\\\\t|end$|fi |fi\\\\\\\\t|fi$|do |do\\\\\\\\t|do$|in |in\\\\\\\\t|in$|if |if\\\\\\\\t|if$)(?:((?<=^|[;\\\\\\\\& \\\\\\\\t])(?:readonly|declare|typeset|export|local)(?=[ \\\\\\\\t;\\\\\\\\&]|$))|((?![\\\\\\\"']|\\\\\\\\\\\\\\\\\\\\\\\\n?$)[^!'\\\\\\\"<> \\\\\\\\t\\\\\\\\n\\\\\\\\r]+?))(?:(?=[ \\\\\\\\t])|(?=[;|\\\\\\\\&\\\\\\\\n)`{}]|[ \\\\\\\\t]*#|])(?<!\\\\\\\\\\\\\\\\))\\\",\\\"name\\\":\\\"meta.statement.command.name.basic.shell\\\"},\\\"block_comment\\\":{\\\"begin\\\":\\\"\\\\\\\\s*+(/\\\\\\\\*)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.begin.shell\\\"}},\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.end.shell\\\"}},\\\"name\\\":\\\"comment.block.shell\\\"},\\\"boolean\\\":{\\\"match\\\":\\\"\\\\\\\\b(?:true|false)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.$0.shell\\\"},\\\"case_statement\\\":{\\\"begin\\\":\\\"(\\\\\\\\bcase\\\\\\\\b)[ \\\\\\\\t]*+(.+?)[ \\\\\\\\t]*+(\\\\\\\\bin\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.case.shell\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#initial_context\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"keyword.control.in.shell\\\"}},\\\"end\\\":\\\"\\\\\\\\besac\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.control.esac.shell\\\"}},\\\"name\\\":\\\"meta.case.shell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.pattern.case.default.shell\\\"}},\\\"match\\\":\\\"[ \\\\\\\\t]*+(\\\\\\\\* *\\\\\\\\))\\\"},{\\\"begin\\\":\\\"(?<!\\\\\\\\))(?![ \\\\\\\\t]*+(?:esac\\\\\\\\b|$))\\\",\\\"beginCaptures\\\":{},\\\"end\\\":\\\"(?:(?=\\\\\\\\besac\\\\\\\\b)|(\\\\\\\\)))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.pattern.case.shell\\\"}},\\\"name\\\":\\\"meta.case.entry.pattern.shell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#case_statement_context\\\"}]},{\\\"begin\\\":\\\"(?<=\\\\\\\\))\\\",\\\"beginCaptures\\\":{},\\\"end\\\":\\\"(?:(;;)|(?=\\\\\\\\besac\\\\\\\\b))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.terminator.statement.case.shell\\\"}},\\\"name\\\":\\\"meta.case.entry.body.shell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#typical_statements\\\"},{\\\"include\\\":\\\"#initial_context\\\"}]}]},\\\"case_statement_context\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\*\\\",\\\"name\\\":\\\"variable.language.special.quantifier.star.shell keyword.operator.quantifier.star.shell punctuation.definition.arbitrary-repetition.shell punctuation.definition.regex.arbitrary-repetition.shell\\\"},{\\\"match\\\":\\\"\\\\\\\\+\\\",\\\"name\\\":\\\"variable.language.special.quantifier.plus.shell keyword.operator.quantifier.plus.shell punctuation.definition.arbitrary-repetition.shell punctuation.definition.regex.arbitrary-repetition.shell\\\"},{\\\"match\\\":\\\"\\\\\\\\?\\\",\\\"name\\\":\\\"variable.language.special.quantifier.question.shell keyword.operator.quantifier.question.shell punctuation.definition.arbitrary-repetition.shell punctuation.definition.regex.arbitrary-repetition.shell\\\"},{\\\"match\\\":\\\"@\\\",\\\"name\\\":\\\"variable.language.special.at.shell keyword.operator.at.shell punctuation.definition.regex.at.shell\\\"},{\\\"match\\\":\\\"\\\\\\\\|\\\",\\\"name\\\":\\\"keyword.operator.orvariable.language.special.or.shell keyword.operator.alternation.ruby.shell punctuation.definition.regex.alternation.shell punctuation.separator.regex.alternation.shell\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"constant.character.escape.shell\\\"},{\\\"match\\\":\\\"(?<=\\\\\\\\tin| in|[ \\\\\\\\t]|;;)\\\\\\\\(\\\",\\\"name\\\":\\\"keyword.operator.pattern.case.shell\\\"},{\\\"begin\\\":\\\"(?<=\\\\\\\\S)(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.group.shell punctuation.definition.regex.group.shell\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.shell punctuation.definition.regex.group.shell\\\"}},\\\"name\\\":\\\"meta.parenthese.shell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#case_statement_context\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\[\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.character-class.shell\\\"}},\\\"end\\\":\\\"]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.character-class.shell\\\"}},\\\"name\\\":\\\"string.regexp.character-class.shell\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"constant.character.escape.shell\\\"}]},{\\\"include\\\":\\\"#string\\\"},{\\\"match\\\":\\\"[^) \\\\\\\\t\\\\\\\\n\\\\\\\\[?*|@]\\\",\\\"name\\\":\\\"string.unquoted.pattern.shell string.regexp.unquoted.shell\\\"}]},\\\"command_name_range\\\":{\\\"begin\\\":\\\"\\\\\\\\G\\\",\\\"beginCaptures\\\":{},\\\"end\\\":\\\"(?:(?=[ \\\\\\\\t;|\\\\\\\\&]|$|[\\\\\\\\n)`])|(?=<))\\\",\\\"endCaptures\\\":{},\\\"name\\\":\\\"meta.statement.command.name.shell\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<!\\\\\\\\w)(?:continue|return|break)(?!\\\\\\\\w)\\\",\\\"name\\\":\\\"entity.name.function.call.shell entity.name.command.shell keyword.control.$0.shell\\\"},{\\\"match\\\":\\\"(?<!\\\\\\\\w)(?:unfunction|continue|autoload|unsetopt|bindkey|builtin|getopts|command|declare|unalias|history|unlimit|typeset|suspend|source|printf|unhash|disown|ulimit|return|which|alias|break|false|print|shift|times|umask|umask|unset|read|type|exec|eval|wait|echo|dirs|jobs|kill|hash|stat|exit|test|trap|true|let|set|pwd|cd|fg|bg|fc|[:.])(?!/)(?!\\\\\\\\w)(?!-)\\\",\\\"name\\\":\\\"entity.name.function.call.shell entity.name.command.shell support.function.builtin.shell\\\"},{\\\"include\\\":\\\"#variable\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.call.shell entity.name.command.shell\\\"}},\\\"match\\\":\\\"(?<!\\\\\\\\w)(?<=\\\\\\\\G|['\\\\\\\"})])([^ \\\\\\\\n\\\\\\\\t\\\\\\\\r\\\\\\\"'=;\\\\\\\\&|`){<>]+)\\\"},{\\\"begin\\\":\\\"(?:\\\\\\\\G|(?<![ \\\\\\\\t;|\\\\\\\\&\\\\\\\\n{#]))(\\\\\\\\$?)((?:(\\\\\\\")|(')))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.statement.command.name.quoted.shell punctuation.definition.string.shell entity.name.function.call.shell entity.name.command.shell\\\"},\\\"2\\\":{},\\\"3\\\":{\\\"name\\\":\\\"meta.statement.command.name.quoted.shell string.quoted.double.shell punctuation.definition.string.begin.shell entity.name.function.call.shell entity.name.command.shell\\\"},\\\"4\\\":{\\\"name\\\":\\\"meta.statement.command.name.quoted.shell string.quoted.single.shell punctuation.definition.string.begin.shell entity.name.function.call.shell entity.name.command.shell\\\"}},\\\"end\\\":\\\"(?<!\\\\\\\\G)(?<=\\\\\\\\2)\\\",\\\"endCaptures\\\":{},\\\"patterns\\\":[{\\\"include\\\":\\\"#continuation_of_single_quoted_command_name\\\"},{\\\"include\\\":\\\"#continuation_of_double_quoted_command_name\\\"}]},{\\\"include\\\":\\\"#line_continuation\\\"},{\\\"include\\\":\\\"#simple_unquoted\\\"}]},\\\"command_statement\\\":{\\\"begin\\\":\\\"[ \\\\\\\\t]*+(?!(?:[!\\\\\\\\&|(){\\\\\\\\[<>#\\\\\\\\n]|$|[; \\\\\\\\t]))(?!nocorrect |nocorrect\\\\\\\\t|nocorrect$|readonly |readonly\\\\\\\\t|readonly$|function |function\\\\\\\\t|function$|foreach |foreach\\\\\\\\t|foreach$|coproc |coproc\\\\\\\\t|coproc$|logout |logout\\\\\\\\t|logout$|export |export\\\\\\\\t|export$|select |select\\\\\\\\t|select$|repeat |repeat\\\\\\\\t|repeat$|pushd |pushd\\\\\\\\t|pushd$|until |until\\\\\\\\t|until$|while |while\\\\\\\\t|while$|local |local\\\\\\\\t|local$|case |case\\\\\\\\t|case$|done |done\\\\\\\\t|done$|elif |elif\\\\\\\\t|elif$|else |else\\\\\\\\t|else$|esac |esac\\\\\\\\t|esac$|popd |popd\\\\\\\\t|popd$|then |then\\\\\\\\t|then$|time |time\\\\\\\\t|time$|for |for\\\\\\\\t|for$|end |end\\\\\\\\t|end$|fi |fi\\\\\\\\t|fi$|do |do\\\\\\\\t|do$|in |in\\\\\\\\t|in$|if |if\\\\\\\\t|if$)(?!\\\\\\\\\\\\\\\\\\\\\\\\n?$)\\\",\\\"beginCaptures\\\":{},\\\"end\\\":\\\"(?=[;|\\\\\\\\&\\\\\\\\n)`{}]|[ \\\\\\\\t]*#|])(?<!\\\\\\\\\\\\\\\\)\\\",\\\"endCaptures\\\":{},\\\"name\\\":\\\"meta.statement.command.shell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#command_name_range\\\"},{\\\"include\\\":\\\"#line_continuation\\\"},{\\\"include\\\":\\\"#option\\\"},{\\\"include\\\":\\\"#argument\\\"},{\\\"include\\\":\\\"#string\\\"},{\\\"include\\\":\\\"#heredoc\\\"}]},\\\"comment\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"comment.line.number-sign.shell meta.shebang.shell\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.comment.shebang.shell\\\"},\\\"3\\\":{\\\"name\\\":\\\"comment.line.number-sign.shell\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.comment.shell\\\"}},\\\"match\\\":\\\"(?:^|[ \\\\\\\\t]++)(?:((#!).*)|((#).*))\\\"},\\\"comments\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#block_comment\\\"},{\\\"include\\\":\\\"#line_comment\\\"}]},\\\"compound-command\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\[\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.logical-expression.shell\\\"}},\\\"end\\\":\\\"]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.logical-expression.shell\\\"}},\\\"name\\\":\\\"meta.scope.logical-expression.shell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#logical-expression\\\"},{\\\"include\\\":\\\"#initial_context\\\"}]},{\\\"begin\\\":\\\"(?<=\\\\\\\\s|^)\\\\\\\\{(?=\\\\\\\\s|$)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.shell\\\"}},\\\"end\\\":\\\"(?<=^|;)\\\\\\\\s*(})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.group.shell\\\"}},\\\"name\\\":\\\"meta.scope.group.shell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#initial_context\\\"}]}]},\\\"continuation_of_double_quoted_command_name\\\":{\\\"begin\\\":\\\"\\\\\\\\G(?<=\\\\\\\")\\\",\\\"beginCaptures\\\":{},\\\"contentName\\\":\\\"meta.statement.command.name.continuation string.quoted.double entity.name.function.call entity.name.command\\\",\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"string.quoted.double.shell punctuation.definition.string.end.shell entity.name.function.call.shell entity.name.command.shell\\\"}},\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\[$\\\\\\\\n`\\\\\\\"\\\\\\\\\\\\\\\\]\\\",\\\"name\\\":\\\"constant.character.escape.shell\\\"},{\\\"include\\\":\\\"#variable\\\"},{\\\"include\\\":\\\"#interpolation\\\"}]},\\\"continuation_of_single_quoted_command_name\\\":{\\\"begin\\\":\\\"\\\\\\\\G(?<=')\\\",\\\"beginCaptures\\\":{},\\\"contentName\\\":\\\"meta.statement.command.name.continuation string.quoted.single entity.name.function.call entity.name.command\\\",\\\"end\\\":\\\"'\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"string.quoted.single.shell punctuation.definition.string.end.shell entity.name.function.call.shell entity.name.command.shell\\\"}}},\\\"custom_command_names\\\":{\\\"patterns\\\":[]},\\\"custom_commands\\\":{\\\"patterns\\\":[]},\\\"double_quote_context\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\[$`\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\n]\\\",\\\"name\\\":\\\"constant.character.escape.shell\\\"},{\\\"include\\\":\\\"#variable\\\"},{\\\"include\\\":\\\"#interpolation\\\"}]},\\\"double_quote_escape_char\\\":{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\[$`\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\n]\\\",\\\"name\\\":\\\"constant.character.escape.shell\\\"},\\\"floating_keyword\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=^|[;\\\\\\\\& \\\\\\\\t])(?:then|elif|else|done|end|do|if|fi)(?=[ \\\\\\\\t;\\\\\\\\&]|$)\\\",\\\"name\\\":\\\"keyword.control.$0.shell\\\"}]},\\\"for_statement\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(\\\\\\\\bfor\\\\\\\\b)[ \\\\\\\\t]*+((?<!\\\\\\\\w)[a-zA-Z_0-9-]+(?!\\\\\\\\w))[ \\\\\\\\t]*+(\\\\\\\\bin\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.for.shell\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.other.for.shell\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.control.in.shell\\\"}},\\\"end\\\":\\\"(?=[;|\\\\\\\\&\\\\\\\\n)`{}]|[ \\\\\\\\t]*#|])(?<!\\\\\\\\\\\\\\\\)\\\",\\\"endCaptures\\\":{},\\\"name\\\":\\\"meta.for.in.shell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string\\\"},{\\\"include\\\":\\\"#simple_unquoted\\\"},{\\\"include\\\":\\\"#normal_context\\\"}]},{\\\"begin\\\":\\\"(\\\\\\\\bfor\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.for.shell\\\"}},\\\"end\\\":\\\"(?=[;|\\\\\\\\&\\\\\\\\n)`{}]|[ \\\\\\\\t]*#|])(?<!\\\\\\\\\\\\\\\\)\\\",\\\"endCaptures\\\":{},\\\"name\\\":\\\"meta.for.shell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#arithmetic_double\\\"},{\\\"include\\\":\\\"#normal_context\\\"}]}]},\\\"function_definition\\\":{\\\"applyEndPatternLast\\\":1,\\\"begin\\\":\\\"[ \\\\\\\\t]*+(?:(\\\\\\\\bfunction\\\\\\\\b)[ \\\\\\\\t]*+([^ \\\\\\\\t\\\\\\\\n\\\\\\\\r()=\\\\\\\"']+)(?:(\\\\\\\\()[ \\\\\\\\t]*+(\\\\\\\\)))?|([^ \\\\\\\\t\\\\\\\\n\\\\\\\\r()=\\\\\\\"']+)[ \\\\\\\\t]*+(\\\\\\\\()[ \\\\\\\\t]*+(\\\\\\\\)))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.function.shell\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.shell\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.shell\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.shell\\\"},\\\"5\\\":{\\\"name\\\":\\\"entity.name.function.shell\\\"},\\\"6\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.shell\\\"},\\\"7\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.shell\\\"}},\\\"end\\\":\\\"(?<=[})])\\\",\\\"endCaptures\\\":{},\\\"name\\\":\\\"meta.function.shell\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\G[\\\\\\\\t \\\\\\\\n]\\\"},{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.shell punctuation.section.function.definition.shell\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.shell punctuation.section.function.definition.shell\\\"}},\\\"name\\\":\\\"meta.function.body.shell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#initial_context\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.shell punctuation.section.function.definition.shell\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.shell punctuation.section.function.definition.shell\\\"}},\\\"name\\\":\\\"meta.function.body.shell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#initial_context\\\"}]},{\\\"include\\\":\\\"#initial_context\\\"}]},\\\"heredoc\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"((?<!<)<<-)[ \\\\\\\\t]*+([\\\\\\\"'])[ \\\\\\\\t]*+([^\\\\\\\"']+?)(?=[\\\\\\\\s;\\\\\\\\&<\\\\\\\"'])(\\\\\\\\2)(.*)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.heredoc.shell\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.string.heredoc.quote.shell\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.string.heredoc.delimiter.shell\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.string.heredoc.quote.shell\\\"},\\\"5\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#redirect_fix\\\"},{\\\"include\\\":\\\"#typical_statements\\\"}]}},\\\"contentName\\\":\\\"string.quoted.heredoc.indent.$3\\\",\\\"end\\\":\\\"^\\\\\\\\t*\\\\\\\\3(?=[\\\\\\\\s;\\\\\\\\&]|$)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.heredoc.$0.shell\\\"}},\\\"patterns\\\":[]},{\\\"begin\\\":\\\"((?<!<)<<(?!<))[ \\\\\\\\t]*+([\\\\\\\"'])[ \\\\\\\\t]*+([^\\\\\\\"']+?)(?=[\\\\\\\\s;\\\\\\\\&<\\\\\\\"'])(\\\\\\\\2)(.*)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.heredoc.shell\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.string.heredoc.quote.shell\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.string.heredoc.delimiter.shell\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.string.heredoc.quote.shell\\\"},\\\"5\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#redirect_fix\\\"},{\\\"include\\\":\\\"#typical_statements\\\"}]}},\\\"contentName\\\":\\\"string.quoted.heredoc.no-indent.$3\\\",\\\"end\\\":\\\"^\\\\\\\\3(?=[\\\\\\\\s;\\\\\\\\&]|$)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.heredoc.delimiter.shell\\\"}},\\\"patterns\\\":[]},{\\\"begin\\\":\\\"((?<!<)<<-)[ \\\\\\\\t]*+([^\\\\\\\"' \\\\\\\\t]+)(?=[\\\\\\\\s;\\\\\\\\&<\\\\\\\"'])(.*)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.heredoc.shell\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.string.heredoc.delimiter.shell\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#redirect_fix\\\"},{\\\"include\\\":\\\"#typical_statements\\\"}]}},\\\"contentName\\\":\\\"string.unquoted.heredoc.indent.$2\\\",\\\"end\\\":\\\"^\\\\\\\\t*\\\\\\\\2(?=[\\\\\\\\s;\\\\\\\\&]|$)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.heredoc.delimiter.shell\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#double_quote_escape_char\\\"},{\\\"include\\\":\\\"#variable\\\"},{\\\"include\\\":\\\"#interpolation\\\"}]},{\\\"begin\\\":\\\"((?<!<)<<(?!<))[ \\\\\\\\t]*+([^\\\\\\\"' \\\\\\\\t]+)(?=[\\\\\\\\s;\\\\\\\\&<\\\\\\\"'])(.*)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.heredoc.shell\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.string.heredoc.delimiter.shell\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#redirect_fix\\\"},{\\\"include\\\":\\\"#typical_statements\\\"}]}},\\\"contentName\\\":\\\"string.unquoted.heredoc.no-indent.$2\\\",\\\"end\\\":\\\"^\\\\\\\\2(?=[\\\\\\\\s;\\\\\\\\&]|$)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.heredoc.delimiter.shell\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#double_quote_escape_char\\\"},{\\\"include\\\":\\\"#variable\\\"},{\\\"include\\\":\\\"#interpolation\\\"}]}]},\\\"herestring\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(<<<)\\\\\\\\s*(('))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.herestring.shell\\\"},\\\"2\\\":{\\\"name\\\":\\\"string.quoted.single.shell\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.shell\\\"}},\\\"contentName\\\":\\\"string.quoted.single.shell\\\",\\\"end\\\":\\\"(')\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"string.quoted.single.shell\\\"},\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.shell\\\"}},\\\"name\\\":\\\"meta.herestring.shell\\\"},{\\\"begin\\\":\\\"(<<<)\\\\\\\\s*((\\\\\\\"))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.herestring.shell\\\"},\\\"2\\\":{\\\"name\\\":\\\"string.quoted.double.shell\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.shell\\\"}},\\\"contentName\\\":\\\"string.quoted.double.shell\\\",\\\"end\\\":\\\"(\\\\\\\")\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"string.quoted.double.shell\\\"},\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.shell\\\"}},\\\"name\\\":\\\"meta.herestring.shell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#double_quote_context\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.herestring.shell\\\"},\\\"2\\\":{\\\"name\\\":\\\"string.unquoted.herestring.shell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#initial_context\\\"}]}},\\\"match\\\":\\\"(<<<)\\\\\\\\s*(([^\\\\\\\\s)\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)+)\\\",\\\"name\\\":\\\"meta.herestring.shell\\\"}]},\\\"initial_context\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#pipeline\\\"},{\\\"include\\\":\\\"#normal_statement_seperator\\\"},{\\\"include\\\":\\\"#logical_expression_double\\\"},{\\\"include\\\":\\\"#logical_expression_single\\\"},{\\\"include\\\":\\\"#assignment_statement\\\"},{\\\"include\\\":\\\"#case_statement\\\"},{\\\"include\\\":\\\"#for_statement\\\"},{\\\"include\\\":\\\"#loop\\\"},{\\\"include\\\":\\\"#function_definition\\\"},{\\\"include\\\":\\\"#line_continuation\\\"},{\\\"include\\\":\\\"#arithmetic_double\\\"},{\\\"include\\\":\\\"#misc_ranges\\\"},{\\\"include\\\":\\\"#variable\\\"},{\\\"include\\\":\\\"#interpolation\\\"},{\\\"include\\\":\\\"#heredoc\\\"},{\\\"include\\\":\\\"#herestring\\\"},{\\\"include\\\":\\\"#redirection\\\"},{\\\"include\\\":\\\"#pathname\\\"},{\\\"include\\\":\\\"#floating_keyword\\\"},{\\\"include\\\":\\\"#alias_statement\\\"},{\\\"include\\\":\\\"#normal_statement\\\"},{\\\"include\\\":\\\"#string\\\"},{\\\"include\\\":\\\"#support\\\"}]},\\\"inline_comment\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"comment.block.shell punctuation.definition.comment.begin.shell\\\"},\\\"2\\\":{\\\"name\\\":\\\"comment.block.shell\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\*/\\\",\\\"name\\\":\\\"comment.block.shell punctuation.definition.comment.end.shell\\\"},{\\\"match\\\":\\\"\\\\\\\\*\\\",\\\"name\\\":\\\"comment.block.shell\\\"}]}},\\\"match\\\":\\\"(/\\\\\\\\*)((?:[^*]|\\\\\\\\*++[^/])*+(\\\\\\\\*++/))\\\"},\\\"interpolation\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#arithmetic_dollar\\\"},{\\\"include\\\":\\\"#subshell_dollar\\\"},{\\\"begin\\\":\\\"`\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.evaluation.backticks.shell\\\"}},\\\"end\\\":\\\"`\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.evaluation.backticks.shell\\\"}},\\\"name\\\":\\\"string.interpolated.backtick.shell\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\[`\\\\\\\\\\\\\\\\$]\\\",\\\"name\\\":\\\"constant.character.escape.shell\\\"},{\\\"begin\\\":\\\"(?<=\\\\\\\\W)(?=#)(?!#\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.comment.leading.shell\\\"}},\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"#\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.shell\\\"}},\\\"end\\\":\\\"(?=`)\\\",\\\"name\\\":\\\"comment.line.number-sign.shell\\\"}]},{\\\"include\\\":\\\"#initial_context\\\"}]}]},\\\"keyword\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=^|[;\\\\\\\\&\\\\\\\\s])(then|else|elif|fi|for|in|do|done|select|continue|esac|while|until|return)(?=[\\\\\\\\s;\\\\\\\\&]|$)\\\",\\\"name\\\":\\\"keyword.control.shell\\\"},{\\\"match\\\":\\\"(?<=^|[;\\\\\\\\&\\\\\\\\s])(?:export|declare|typeset|local|readonly)(?=[\\\\\\\\s;\\\\\\\\&]|$)\\\",\\\"name\\\":\\\"storage.modifier.shell\\\"}]},\\\"line_comment\\\":{\\\"begin\\\":\\\"\\\\\\\\s*+(//)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.shell\\\"}},\\\"end\\\":\\\"(?<=\\\\\\\\n)(?<!\\\\\\\\\\\\\\\\\\\\\\\\n)\\\",\\\"endCaptures\\\":{},\\\"name\\\":\\\"comment.line.double-slash.shell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#line_continuation_character\\\"}]},\\\"line_continuation\\\":{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"constant.character.escape.line-continuation.shell\\\"},\\\"logical-expression\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#arithmetic_no_dollar\\\"},{\\\"match\\\":\\\"=[=~]?|!=?|[<>]|&&|\\\\\\\\|\\\\\\\\|\\\",\\\"name\\\":\\\"keyword.operator.logical.shell\\\"},{\\\"match\\\":\\\"(?<!\\\\\\\\S)-(nt|ot|ef|eq|ne|l[te]|g[te]|[a-hknoprstuwxzOGLSN])\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.operator.logical.shell\\\"}]},\\\"logical_expression_context\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#regex_comparison\\\"},{\\\"include\\\":\\\"#arithmetic_no_dollar\\\"},{\\\"include\\\":\\\"#logical-expression\\\"},{\\\"include\\\":\\\"#logical_expression_single\\\"},{\\\"include\\\":\\\"#logical_expression_double\\\"},{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#boolean\\\"},{\\\"include\\\":\\\"#redirect_number\\\"},{\\\"include\\\":\\\"#numeric_literal\\\"},{\\\"include\\\":\\\"#pipeline\\\"},{\\\"include\\\":\\\"#normal_statement_seperator\\\"},{\\\"include\\\":\\\"#string\\\"},{\\\"include\\\":\\\"#variable\\\"},{\\\"include\\\":\\\"#interpolation\\\"},{\\\"include\\\":\\\"#heredoc\\\"},{\\\"include\\\":\\\"#herestring\\\"},{\\\"include\\\":\\\"#pathname\\\"},{\\\"include\\\":\\\"#floating_keyword\\\"},{\\\"include\\\":\\\"#support\\\"}]},\\\"logical_expression_double\\\":{\\\"begin\\\":\\\"\\\\\\\\[\\\\\\\\[\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.logical-expression.shell\\\"}},\\\"end\\\":\\\"]]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.logical-expression.shell\\\"}},\\\"name\\\":\\\"meta.scope.logical-expression.shell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#logical_expression_context\\\"}]},\\\"logical_expression_single\\\":{\\\"begin\\\":\\\"\\\\\\\\[\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.logical-expression.shell\\\"}},\\\"end\\\":\\\"]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.logical-expression.shell\\\"}},\\\"name\\\":\\\"meta.scope.logical-expression.shell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#logical_expression_context\\\"}]},\\\"loop\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=^|[;\\\\\\\\&\\\\\\\\s])(for)\\\\\\\\s+(.+?)\\\\\\\\s+(in)(?=[\\\\\\\\s;\\\\\\\\&]|$)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.shell\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.other.loop.shell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"keyword.control.shell\\\"}},\\\"end\\\":\\\"(?<=^|[;\\\\\\\\&\\\\\\\\s])done(?=[\\\\\\\\s;\\\\\\\\&]|$|\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.control.shell\\\"}},\\\"name\\\":\\\"meta.scope.for-in-loop.shell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#initial_context\\\"}]},{\\\"begin\\\":\\\"(?<=^|[;\\\\\\\\&\\\\\\\\s])(while|until)(?=[\\\\\\\\s;\\\\\\\\&]|$)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.shell\\\"}},\\\"end\\\":\\\"(?<=^|[;\\\\\\\\&\\\\\\\\s])done(?=[\\\\\\\\s;\\\\\\\\&]|$|\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.control.shell\\\"}},\\\"name\\\":\\\"meta.scope.while-loop.shell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#initial_context\\\"}]},{\\\"begin\\\":\\\"(?<=^|[;\\\\\\\\&\\\\\\\\s])(select)\\\\\\\\s+((?:[^\\\\\\\\s\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)+)(?=[\\\\\\\\s;\\\\\\\\&]|$)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.shell\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.other.loop.shell\\\"}},\\\"end\\\":\\\"(?<=^|[;\\\\\\\\&\\\\\\\\s])(done)(?=[\\\\\\\\s;\\\\\\\\&]|$|\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.shell\\\"}},\\\"name\\\":\\\"meta.scope.select-block.shell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#initial_context\\\"}]},{\\\"begin\\\":\\\"(?<=^|[;\\\\\\\\&\\\\\\\\s])if(?=[\\\\\\\\s;\\\\\\\\&]|$)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.control.if.shell\\\"}},\\\"end\\\":\\\"(?<=^|[;\\\\\\\\&\\\\\\\\s])fi(?=[\\\\\\\\s;\\\\\\\\&]|$)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.control.fi.shell\\\"}},\\\"name\\\":\\\"meta.scope.if-block.shell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#initial_context\\\"}]}]},\\\"math\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#variable\\\"},{\\\"match\\\":\\\"\\\\\\\\+{1,2}|-{1,2}|[!~]|\\\\\\\\*{1,2}|[/%]|<[<=]?|>[>=]?|==|!=|^|\\\\\\\\|{1,2}|&{1,2}|[?:,=]|[*/%+\\\\\\\\-\\\\\\\\&^|]=|<<=|>>=\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.shell\\\"},{\\\"match\\\":\\\"0[xX]\\\\\\\\h+\\\",\\\"name\\\":\\\"constant.numeric.hex.shell\\\"},{\\\"match\\\":\\\";\\\",\\\"name\\\":\\\"punctuation.separator.semicolon.range\\\"},{\\\"match\\\":\\\"0\\\\\\\\d+\\\",\\\"name\\\":\\\"constant.numeric.octal.shell\\\"},{\\\"match\\\":\\\"\\\\\\\\d{1,2}#[0-9a-zA-Z@_]+\\\",\\\"name\\\":\\\"constant.numeric.other.shell\\\"},{\\\"match\\\":\\\"\\\\\\\\d+\\\",\\\"name\\\":\\\"constant.numeric.integer.shell\\\"},{\\\"match\\\":\\\"(?<!\\\\\\\\w)[a-zA-Z_0-9]+(?!\\\\\\\\w)\\\",\\\"name\\\":\\\"variable.other.normal.shell\\\"}]},\\\"math_operators\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\+{1,2}|-{1,2}|[!~]|\\\\\\\\*{1,2}|[/%]|<[<=]?|>[>=]?|==|!=|^|\\\\\\\\|{1,2}|&{1,2}|[?:,=]|[*/%+\\\\\\\\-\\\\\\\\&^|]=|<<=|>>=\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.shell\\\"},{\\\"match\\\":\\\"0[xX]\\\\\\\\h+\\\",\\\"name\\\":\\\"constant.numeric.hex.shell\\\"},{\\\"match\\\":\\\"0\\\\\\\\d+\\\",\\\"name\\\":\\\"constant.numeric.octal.shell\\\"},{\\\"match\\\":\\\"\\\\\\\\d{1,2}#[0-9a-zA-Z@_]+\\\",\\\"name\\\":\\\"constant.numeric.other.shell\\\"},{\\\"match\\\":\\\"\\\\\\\\d+\\\",\\\"name\\\":\\\"constant.numeric.integer.shell\\\"}]},\\\"misc_ranges\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#logical_expression_single\\\"},{\\\"include\\\":\\\"#logical_expression_double\\\"},{\\\"include\\\":\\\"#subshell_dollar\\\"},{\\\"begin\\\":\\\"(?<![^ \\\\\\\\t])(\\\\\\\\{)(?![\\\\\\\\w$])\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.group.shell\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.shell\\\"}},\\\"name\\\":\\\"meta.scope.group.shell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#initial_context\\\"}]}]},\\\"modified_assignment_statement\\\":{\\\"begin\\\":\\\"(?<=^|[;\\\\\\\\& \\\\\\\\t])(?:readonly|declare|typeset|export|local)(?=[ \\\\\\\\t;\\\\\\\\&]|$)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"storage.modifier.$0.shell\\\"}},\\\"end\\\":\\\"(?=[;|\\\\\\\\&\\\\\\\\n)`{}]|[ \\\\\\\\t]*#|])(?<!\\\\\\\\\\\\\\\\)\\\",\\\"endCaptures\\\":{},\\\"name\\\":\\\"meta.statement.shell meta.expression.assignment.modified.shell\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<!\\\\\\\\w)-\\\\\\\\w+\\\\\\\\b\\\",\\\"name\\\":\\\"string.unquoted.argument.shell constant.other.option.shell\\\"},{\\\"include\\\":\\\"#array_value\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.assignment.shell\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.array.access.shell\\\"},\\\"3\\\":{\\\"name\\\":\\\"variable.other.assignment.shell\\\"},\\\"4\\\":{\\\"name\\\":\\\"constant.numeric.shell constant.numeric.integer.shell\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.definition.array.access.shell\\\"},\\\"6\\\":{\\\"name\\\":\\\"keyword.operator.assignment.shell\\\"},\\\"7\\\":{\\\"name\\\":\\\"keyword.operator.assignment.compound.shell\\\"},\\\"8\\\":{\\\"name\\\":\\\"keyword.operator.assignment.compound.shell\\\"},\\\"9\\\":{\\\"name\\\":\\\"constant.numeric.shell constant.numeric.hex.shell\\\"},\\\"10\\\":{\\\"name\\\":\\\"constant.numeric.shell constant.numeric.octal.shell\\\"},\\\"11\\\":{\\\"name\\\":\\\"constant.numeric.shell constant.numeric.other.shell\\\"},\\\"12\\\":{\\\"name\\\":\\\"constant.numeric.shell constant.numeric.decimal.shell\\\"},\\\"13\\\":{\\\"name\\\":\\\"constant.numeric.shell constant.numeric.version.shell\\\"},\\\"14\\\":{\\\"name\\\":\\\"constant.numeric.shell constant.numeric.integer.shell\\\"}},\\\"match\\\":\\\"((?<!\\\\\\\\w)[a-zA-Z_0-9-]+(?!\\\\\\\\w))(?:(\\\\\\\\[)((?:(?:(?:\\\\\\\\$?(?<!\\\\\\\\w)[a-zA-Z_0-9-]+(?!\\\\\\\\w)|@)|\\\\\\\\*)|(-?\\\\\\\\d+)))(]))?(?:(?:(=)|(\\\\\\\\+=))|(-=))?(?:(?<=[= \\\\\\\\t]|^|[{(\\\\\\\\[])(?:(?:(?:(?:(?:(0[xX]\\\\\\\\h+)|(0\\\\\\\\d+))|(\\\\\\\\d{1,2}#[0-9a-zA-Z@_]+))|(-?\\\\\\\\d+\\\\\\\\.\\\\\\\\d+))|(-?\\\\\\\\d+(?:\\\\\\\\.\\\\\\\\d+)+))|(-?\\\\\\\\d+))(?=[ \\\\\\\\t]|$|[});]))?\\\"},{\\\"include\\\":\\\"#normal_context\\\"}]},\\\"modifiers\\\":{\\\"match\\\":\\\"(?<=^|[;\\\\\\\\& \\\\\\\\t])(?:readonly|declare|typeset|export|local)(?=[ \\\\\\\\t;\\\\\\\\&]|$)\\\",\\\"name\\\":\\\"storage.modifier.$0.shell\\\"},\\\"normal_assignment_statement\\\":{\\\"begin\\\":\\\"[ \\\\\\\\t]*+((?<!\\\\\\\\w)[a-zA-Z_0-9-]+(?!\\\\\\\\w))(?:(\\\\\\\\[)((?:(?:(?:\\\\\\\\$?(?<!\\\\\\\\w)[a-zA-Z_0-9-]+(?!\\\\\\\\w)|@)|\\\\\\\\*)|(-?\\\\\\\\d+)))(]))?(?:(?:(=)|(\\\\\\\\+=))|(-=))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.assignment.shell\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.array.access.shell\\\"},\\\"3\\\":{\\\"name\\\":\\\"variable.other.assignment.shell\\\"},\\\"4\\\":{\\\"name\\\":\\\"constant.numeric.shell constant.numeric.integer.shell\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.definition.array.access.shell\\\"},\\\"6\\\":{\\\"name\\\":\\\"keyword.operator.assignment.shell\\\"},\\\"7\\\":{\\\"name\\\":\\\"keyword.operator.assignment.compound.shell\\\"},\\\"8\\\":{\\\"name\\\":\\\"keyword.operator.assignment.compound.shell\\\"}},\\\"end\\\":\\\"(?=[;|\\\\\\\\&\\\\\\\\n)`{}]|[ \\\\\\\\t]*#|])(?<!\\\\\\\\\\\\\\\\)\\\",\\\"endCaptures\\\":{},\\\"name\\\":\\\"meta.expression.assignment.shell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#string\\\"},{\\\"include\\\":\\\"#normal_assignment_statement\\\"},{\\\"begin\\\":\\\"(?<=[ \\\\\\\\t])(?![ \\\\\\\\t]|\\\\\\\\w+=)\\\",\\\"beginCaptures\\\":{},\\\"end\\\":\\\"(?=[;|\\\\\\\\&\\\\\\\\n)`{}]|[ \\\\\\\\t]*#|])(?<!\\\\\\\\\\\\\\\\)\\\",\\\"endCaptures\\\":{},\\\"name\\\":\\\"meta.statement.command.env.shell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#command_name_range\\\"},{\\\"include\\\":\\\"#line_continuation\\\"},{\\\"include\\\":\\\"#option\\\"},{\\\"include\\\":\\\"#argument\\\"},{\\\"include\\\":\\\"#string\\\"}]},{\\\"include\\\":\\\"#simple_unquoted\\\"},{\\\"include\\\":\\\"#normal_context\\\"}]},\\\"normal_context\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#pipeline\\\"},{\\\"include\\\":\\\"#normal_statement_seperator\\\"},{\\\"include\\\":\\\"#misc_ranges\\\"},{\\\"include\\\":\\\"#boolean\\\"},{\\\"include\\\":\\\"#redirect_number\\\"},{\\\"include\\\":\\\"#numeric_literal\\\"},{\\\"include\\\":\\\"#string\\\"},{\\\"include\\\":\\\"#variable\\\"},{\\\"include\\\":\\\"#interpolation\\\"},{\\\"include\\\":\\\"#heredoc\\\"},{\\\"include\\\":\\\"#herestring\\\"},{\\\"include\\\":\\\"#redirection\\\"},{\\\"include\\\":\\\"#pathname\\\"},{\\\"include\\\":\\\"#floating_keyword\\\"},{\\\"include\\\":\\\"#support\\\"},{\\\"include\\\":\\\"#parenthese\\\"}]},\\\"normal_statement\\\":{\\\"begin\\\":\\\"(?!^[ \\\\\\\\t]*+$)(?:(?<=^until | until |\\\\\\\\tuntil |^while | while |\\\\\\\\twhile |^elif | elif |\\\\\\\\telif |^else | else |\\\\\\\\telse |^then | then |\\\\\\\\tthen |^do | do |\\\\\\\\tdo |^if | if |\\\\\\\\tif )|(?<=(?:^|[;|\\\\\\\\&!({`])))[ \\\\\\\\t]*+(?!nocorrect\\\\\\\\W|nocorrect\\\\\\\\$|function\\\\\\\\W|function\\\\\\\\$|foreach\\\\\\\\W|foreach\\\\\\\\$|repeat\\\\\\\\W|repeat\\\\\\\\$|logout\\\\\\\\W|logout\\\\\\\\$|coproc\\\\\\\\W|coproc\\\\\\\\$|select\\\\\\\\W|select\\\\\\\\$|while\\\\\\\\W|while\\\\\\\\$|pushd\\\\\\\\W|pushd\\\\\\\\$|until\\\\\\\\W|until\\\\\\\\$|case\\\\\\\\W|case\\\\\\\\$|done\\\\\\\\W|done\\\\\\\\$|elif\\\\\\\\W|elif\\\\\\\\$|else\\\\\\\\W|else\\\\\\\\$|esac\\\\\\\\W|esac\\\\\\\\$|popd\\\\\\\\W|popd\\\\\\\\$|then\\\\\\\\W|then\\\\\\\\$|time\\\\\\\\W|time\\\\\\\\$|for\\\\\\\\W|for\\\\\\\\$|end\\\\\\\\W|end\\\\\\\\$|fi\\\\\\\\W|fi\\\\\\\\$|do\\\\\\\\W|do\\\\\\\\$|in\\\\\\\\W|in\\\\\\\\$|if\\\\\\\\W|if\\\\\\\\$)\\\",\\\"beginCaptures\\\":{},\\\"end\\\":\\\"(?=[;|\\\\\\\\&\\\\\\\\n)`{}]|[ \\\\\\\\t]*#|])(?<!\\\\\\\\\\\\\\\\)\\\",\\\"endCaptures\\\":{},\\\"name\\\":\\\"meta.statement.shell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#typical_statements\\\"}]},\\\"normal_statement_seperator\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.terminator.statement.semicolon.shell\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.statement.and.shell\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.statement.or.shell\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.separator.statement.background.shell\\\"}},\\\"match\\\":\\\"(?:(?:(?:(;)|(&&))|(\\\\\\\\|\\\\\\\\|))|(&))\\\"},\\\"numeric_literal\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.numeric.shell constant.numeric.hex.shell\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.numeric.shell constant.numeric.octal.shell\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.numeric.shell constant.numeric.other.shell\\\"},\\\"4\\\":{\\\"name\\\":\\\"constant.numeric.shell constant.numeric.decimal.shell\\\"},\\\"5\\\":{\\\"name\\\":\\\"constant.numeric.shell constant.numeric.version.shell\\\"},\\\"6\\\":{\\\"name\\\":\\\"constant.numeric.shell constant.numeric.integer.shell\\\"}},\\\"match\\\":\\\"(?<=[= \\\\\\\\t]|^|[{(\\\\\\\\[])(?:(?:(?:(?:(?:(0[xX]\\\\\\\\h+)|(0\\\\\\\\d+))|(\\\\\\\\d{1,2}#[0-9a-zA-Z@_]+))|(-?\\\\\\\\d+\\\\\\\\.\\\\\\\\d+))|(-?\\\\\\\\d+(?:\\\\\\\\.\\\\\\\\d+)+))|(-?\\\\\\\\d+))(?=[ \\\\\\\\t]|$|[});])\\\"},\\\"option\\\":{\\\"begin\\\":\\\"[ \\\\\\\\t]++(-)((?!(?:[!\\\\\\\\&|(){\\\\\\\\[<>#\\\\\\\\n]|$|[; \\\\\\\\t])))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"string.unquoted.argument.shell constant.other.option.dash.shell\\\"},\\\"2\\\":{\\\"name\\\":\\\"string.unquoted.argument.shell constant.other.option.shell\\\"}},\\\"contentName\\\":\\\"string.unquoted.argument constant.other.option\\\",\\\"end\\\":\\\"(?:(?=[ \\\\\\\\t])|(?=[;|\\\\\\\\&\\\\\\\\n)`{}]|[ \\\\\\\\t]*#|])(?<!\\\\\\\\\\\\\\\\))\\\",\\\"endCaptures\\\":{},\\\"patterns\\\":[{\\\"include\\\":\\\"#option_context\\\"}]},\\\"option_context\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#misc_ranges\\\"},{\\\"include\\\":\\\"#string\\\"},{\\\"include\\\":\\\"#variable\\\"},{\\\"include\\\":\\\"#interpolation\\\"},{\\\"include\\\":\\\"#heredoc\\\"},{\\\"include\\\":\\\"#herestring\\\"},{\\\"include\\\":\\\"#redirection\\\"},{\\\"include\\\":\\\"#pathname\\\"},{\\\"include\\\":\\\"#floating_keyword\\\"},{\\\"include\\\":\\\"#support\\\"}]},\\\"parenthese\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parenthese.shell\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parenthese.shell\\\"}},\\\"name\\\":\\\"meta.parenthese.group.shell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#initial_context\\\"}]}]},\\\"pathname\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[\\\\\\\\s:=]|^)~\\\",\\\"name\\\":\\\"keyword.operator.tilde.shell\\\"},{\\\"match\\\":\\\"[*?]\\\",\\\"name\\\":\\\"keyword.operator.glob.shell\\\"},{\\\"begin\\\":\\\"([?*+@!])(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.extglob.shell\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.extglob.shell\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.extglob.shell\\\"}},\\\"name\\\":\\\"meta.structure.extglob.shell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#initial_context\\\"}]}]},\\\"pipeline\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=^|[;\\\\\\\\&\\\\\\\\s])(time)(?=[\\\\\\\\s;\\\\\\\\&]|$)\\\",\\\"name\\\":\\\"keyword.other.shell\\\"},{\\\"match\\\":\\\"[|!]\\\",\\\"name\\\":\\\"keyword.operator.pipe.shell\\\"}]},\\\"redirect_fix\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.redirect.shell\\\"},\\\"2\\\":{\\\"name\\\":\\\"string.unquoted.argument.shell\\\"}},\\\"match\\\":\\\"(>>?)[ \\\\\\\\t]*+([^ \\\\\\\\t\\\\\\\\n>\\\\\\\\&;<()$`\\\\\\\\\\\\\\\\\\\\\\\"'|]+)\\\"},\\\"redirect_number\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.redirect.stdout.shell\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.redirect.stderr.shell\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.operator.redirect.$3.shell\\\"}},\\\"match\\\":\\\"(?<=[ \\\\\\\\t])(?:(1)|(2)|(\\\\\\\\d+))(?=>)\\\"},\\\"redirection\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"[><]\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.shell\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.shell\\\"}},\\\"name\\\":\\\"string.interpolated.process-substitution.shell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#initial_context\\\"}]},{\\\"match\\\":\\\"(?<![<>])(&>|\\\\\\\\d*>&\\\\\\\\d*|\\\\\\\\d*(>>|[><])|\\\\\\\\d*<&|\\\\\\\\d*<>)(?![<>])\\\",\\\"name\\\":\\\"keyword.operator.redirect.shell\\\"}]},\\\"regex_comparison\\\":{\\\"match\\\":\\\"=~\\\",\\\"name\\\":\\\"keyword.operator.logical.regex.shell\\\"},\\\"regexp\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\".+\\\"}]},\\\"simple_options\\\":{\\\"captures\\\":{\\\"0\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"string.unquoted.argument.shell constant.other.option.dash.shell\\\"},\\\"2\\\":{\\\"name\\\":\\\"string.unquoted.argument.shell constant.other.option.shell\\\"}},\\\"match\\\":\\\"[ \\\\\\\\t]++(-)(\\\\\\\\w+)\\\"}]}},\\\"match\\\":\\\"(?:[ \\\\\\\\t]++-\\\\\\\\w+)*\\\"},\\\"simple_unquoted\\\":{\\\"match\\\":\\\"[^ \\\\\\\\t\\\\\\\\n>\\\\\\\\&;<()$`\\\\\\\\\\\\\\\\\\\\\\\"'|]\\\",\\\"name\\\":\\\"string.unquoted.shell\\\"},\\\"special_expansion\\\":{\\\"match\\\":\\\"!|:[-=?]?|[*@]|##|#|%%|[%/]\\\",\\\"name\\\":\\\"keyword.operator.expansion.shell\\\"},\\\"start_of_command\\\":{\\\"match\\\":\\\"[ \\\\\\\\t]*+(?!(?:[!\\\\\\\\&|(){\\\\\\\\[<>#\\\\\\\\n]|$|[; \\\\\\\\t]))(?!nocorrect |nocorrect\\\\\\\\t|nocorrect$|readonly |readonly\\\\\\\\t|readonly$|function |function\\\\\\\\t|function$|foreach |foreach\\\\\\\\t|foreach$|coproc |coproc\\\\\\\\t|coproc$|logout |logout\\\\\\\\t|logout$|export |export\\\\\\\\t|export$|select |select\\\\\\\\t|select$|repeat |repeat\\\\\\\\t|repeat$|pushd |pushd\\\\\\\\t|pushd$|until |until\\\\\\\\t|until$|while |while\\\\\\\\t|while$|local |local\\\\\\\\t|local$|case |case\\\\\\\\t|case$|done |done\\\\\\\\t|done$|elif |elif\\\\\\\\t|elif$|else |else\\\\\\\\t|else$|esac |esac\\\\\\\\t|esac$|popd |popd\\\\\\\\t|popd$|then |then\\\\\\\\t|then$|time |time\\\\\\\\t|time$|for |for\\\\\\\\t|for$|end |end\\\\\\\\t|end$|fi |fi\\\\\\\\t|fi$|do |do\\\\\\\\t|do$|in |in\\\\\\\\t|in$|if |if\\\\\\\\t|if$)(?!\\\\\\\\\\\\\\\\\\\\\\\\n?$)\\\"},\\\"string\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"constant.character.escape.shell\\\"},{\\\"begin\\\":\\\"'\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.shell\\\"}},\\\"end\\\":\\\"'\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.shell\\\"}},\\\"name\\\":\\\"string.quoted.single.shell\\\"},{\\\"begin\\\":\\\"\\\\\\\\$?\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.shell\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.shell\\\"}},\\\"name\\\":\\\"string.quoted.double.shell\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\[$\\\\\\\\n`\\\\\\\"\\\\\\\\\\\\\\\\]\\\",\\\"name\\\":\\\"constant.character.escape.shell\\\"},{\\\"include\\\":\\\"#variable\\\"},{\\\"include\\\":\\\"#interpolation\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\$'\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.shell\\\"}},\\\"end\\\":\\\"'\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.shell\\\"}},\\\"name\\\":\\\"string.quoted.single.dollar.shell\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\[abefnrtv\\\\\\\\\\\\\\\\']\\\",\\\"name\\\":\\\"constant.character.escape.ansi-c.shell\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\[0-9]{3}\\\\\\\"\\\",\\\"name\\\":\\\"constant.character.escape.octal.shell\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\x\\\\\\\\h{2}\\\\\\\"\\\",\\\"name\\\":\\\"constant.character.escape.hex.shell\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\c.\\\\\\\"\\\",\\\"name\\\":\\\"constant.character.escape.control-char.shell\\\"}]}]},\\\"subshell_dollar\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\$\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.subshell.single.shell\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.subshell.single.shell\\\"}},\\\"name\\\":\\\"meta.scope.subshell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#parenthese\\\"},{\\\"include\\\":\\\"#initial_context\\\"}]}]},\\\"support\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=^|[;\\\\\\\\&\\\\\\\\s])[:.](?=[\\\\\\\\s;\\\\\\\\&]|$)\\\",\\\"name\\\":\\\"support.function.builtin.shell\\\"}]},\\\"typical_statements\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#assignment_statement\\\"},{\\\"include\\\":\\\"#case_statement\\\"},{\\\"include\\\":\\\"#for_statement\\\"},{\\\"include\\\":\\\"#while_statement\\\"},{\\\"include\\\":\\\"#function_definition\\\"},{\\\"include\\\":\\\"#command_statement\\\"},{\\\"include\\\":\\\"#line_continuation\\\"},{\\\"include\\\":\\\"#arithmetic_double\\\"},{\\\"include\\\":\\\"#normal_context\\\"}]},\\\"variable\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.variable.shell variable.parameter.positional.all.shell\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.parameter.positional.all.shell\\\"}},\\\"match\\\":\\\"(\\\\\\\\$)(@(?!\\\\\\\\w))\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.variable.shell variable.parameter.positional.shell\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.parameter.positional.shell\\\"}},\\\"match\\\":\\\"(\\\\\\\\$)([0-9](?!\\\\\\\\w))\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.variable.shell variable.language.special.shell\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.language.special.shell\\\"}},\\\"match\\\":\\\"(\\\\\\\\$)([-*#?$!0_](?!\\\\\\\\w))\\\"},{\\\"begin\\\":\\\"(\\\\\\\\$)(\\\\\\\\{)[ \\\\\\\\t]*+(?=\\\\\\\\d)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.variable.shell variable.parameter.positional.shell\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.section.bracket.curly.variable.begin.shell punctuation.definition.variable.shell variable.parameter.positional.shell\\\"}},\\\"contentName\\\":\\\"meta.parameter-expansion\\\",\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.bracket.curly.variable.end.shell punctuation.definition.variable.shell variable.parameter.positional.shell\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#special_expansion\\\"},{\\\"include\\\":\\\"#array_access_inline\\\"},{\\\"match\\\":\\\"[0-9]+\\\",\\\"name\\\":\\\"variable.parameter.positional.shell\\\"},{\\\"match\\\":\\\"(?<!\\\\\\\\w)[a-zA-Z_0-9-]+(?!\\\\\\\\w)\\\",\\\"name\\\":\\\"variable.other.normal.shell\\\"},{\\\"include\\\":\\\"#variable\\\"},{\\\"include\\\":\\\"#string\\\"}]},{\\\"begin\\\":\\\"(\\\\\\\\$)(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.variable.shell\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.section.bracket.curly.variable.begin.shell punctuation.definition.variable.shell\\\"}},\\\"contentName\\\":\\\"meta.parameter-expansion\\\",\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.bracket.curly.variable.end.shell punctuation.definition.variable.shell\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#special_expansion\\\"},{\\\"include\\\":\\\"#array_access_inline\\\"},{\\\"match\\\":\\\"(?<!\\\\\\\\w)[a-zA-Z_0-9-]+(?!\\\\\\\\w)\\\",\\\"name\\\":\\\"variable.other.normal.shell\\\"},{\\\"include\\\":\\\"#variable\\\"},{\\\"include\\\":\\\"#string\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.variable.shell variable.other.normal.shell\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.other.normal.shell\\\"}},\\\"match\\\":\\\"(\\\\\\\\$)(\\\\\\\\w+(?!\\\\\\\\w))\\\"}]},\\\"while_statement\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(\\\\\\\\bwhile\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.while.shell\\\"}},\\\"end\\\":\\\"(?=[;|\\\\\\\\&\\\\\\\\n)`{}]|[ \\\\\\\\t]*#|])(?<!\\\\\\\\\\\\\\\\)\\\",\\\"endCaptures\\\":{},\\\"name\\\":\\\"meta.while.shell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#line_continuation\\\"},{\\\"include\\\":\\\"#math_operators\\\"},{\\\"include\\\":\\\"#option\\\"},{\\\"include\\\":\\\"#simple_unquoted\\\"},{\\\"include\\\":\\\"#normal_context\\\"},{\\\"include\\\":\\\"#string\\\"}]}]}},\\\"scopeName\\\":\\\"source.shell\\\",\\\"aliases\\\":[\\\"bash\\\",\\\"sh\\\",\\\"shell\\\",\\\"zsh\\\"]}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/shellscript.mjs\n"));

/***/ })

}]);