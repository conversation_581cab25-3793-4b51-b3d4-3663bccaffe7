"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_codeowners_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/codeowners.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/codeowners.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"CODEOWNERS\\\",\\\"name\\\":\\\"codeowners\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#pattern\\\"},{\\\"include\\\":\\\"#owner\\\"}],\\\"repository\\\":{\\\"comment\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*#\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.codeowners\\\"}},\\\"end\\\":\\\"$\\\",\\\"name\\\":\\\"comment.line.codeowners\\\"}]},\\\"owner\\\":{\\\"match\\\":\\\"\\\\\\\\S*@\\\\\\\\S+\\\",\\\"name\\\":\\\"storage.type.function.codeowners\\\"},\\\"pattern\\\":{\\\"match\\\":\\\"^\\\\\\\\s*(\\\\\\\\S+)\\\",\\\"name\\\":\\\"variable.other.codeowners\\\"}},\\\"scopeName\\\":\\\"text.codeowners\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ac2hpa2lqcy9sYW5ncy9kaXN0L2NvZGVvd25lcnMubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSx3Q0FBd0Msc0VBQXNFLHlCQUF5QixFQUFFLHlCQUF5QixFQUFFLHVCQUF1QixrQkFBa0IsYUFBYSxlQUFlLHFDQUFxQyxPQUFPLHdEQUF3RCxvREFBb0QsRUFBRSxZQUFZLDBFQUEwRSxjQUFjLHNFQUFzRSxtQ0FBbUM7O0FBRWhsQixpRUFBZTtBQUNmO0FBQ0EsQ0FBQyIsInNvdXJjZXMiOlsiRDpcXHNvZnRcXFNlYVByb2plY3RcXGdpdGh1Yl9vcGVuX3Byb2plY3Rfc3VjY2Vzc1xcc3VuYVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcQHNoaWtpanNcXGxhbmdzXFxkaXN0XFxjb2Rlb3duZXJzLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBsYW5nID0gT2JqZWN0LmZyZWV6ZShKU09OLnBhcnNlKFwie1xcXCJkaXNwbGF5TmFtZVxcXCI6XFxcIkNPREVPV05FUlNcXFwiLFxcXCJuYW1lXFxcIjpcXFwiY29kZW93bmVyc1xcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2NvbW1lbnRcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjcGF0dGVyblxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNvd25lclxcXCJ9XSxcXFwicmVwb3NpdG9yeVxcXCI6e1xcXCJjb21tZW50XFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJiZWdpblxcXCI6XFxcIl5cXFxcXFxcXHMqI1xcXCIsXFxcImNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24uY29tbWVudC5jb2Rlb3duZXJzXFxcIn19LFxcXCJlbmRcXFwiOlxcXCIkXFxcIixcXFwibmFtZVxcXCI6XFxcImNvbW1lbnQubGluZS5jb2Rlb3duZXJzXFxcIn1dfSxcXFwib3duZXJcXFwiOntcXFwibWF0Y2hcXFwiOlxcXCJcXFxcXFxcXFMqQFxcXFxcXFxcUytcXFwiLFxcXCJuYW1lXFxcIjpcXFwic3RvcmFnZS50eXBlLmZ1bmN0aW9uLmNvZGVvd25lcnNcXFwifSxcXFwicGF0dGVyblxcXCI6e1xcXCJtYXRjaFxcXCI6XFxcIl5cXFxcXFxcXHMqKFxcXFxcXFxcUyspXFxcIixcXFwibmFtZVxcXCI6XFxcInZhcmlhYmxlLm90aGVyLmNvZGVvd25lcnNcXFwifX0sXFxcInNjb3BlTmFtZVxcXCI6XFxcInRleHQuY29kZW93bmVyc1xcXCJ9XCIpKVxuXG5leHBvcnQgZGVmYXVsdCBbXG5sYW5nXG5dXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/codeowners.mjs\n"));

/***/ })

}]);