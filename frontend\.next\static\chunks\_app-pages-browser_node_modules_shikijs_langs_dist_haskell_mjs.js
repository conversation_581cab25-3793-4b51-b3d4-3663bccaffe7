"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_haskell_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/haskell.mjs":
/*!******************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/haskell.mjs ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Haskell\\\",\\\"fileTypes\\\":[\\\"hs\\\",\\\"hs-boot\\\",\\\"hsig\\\"],\\\"name\\\":\\\"haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#liquid_haskell\\\"},{\\\"include\\\":\\\"#comment_like\\\"},{\\\"include\\\":\\\"#numeric_literals\\\"},{\\\"include\\\":\\\"#string_literal\\\"},{\\\"include\\\":\\\"#char_literal\\\"},{\\\"match\\\":\\\"(?<![@#])-}\\\",\\\"name\\\":\\\"invalid\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"match\\\":\\\"(\\\\\\\\()\\\\\\\\s*(\\\\\\\\))\\\",\\\"name\\\":\\\"constant.language.unit.haskell\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.hash.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.operator.hash.haskell\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"match\\\":\\\"(\\\\\\\\()(#)\\\\\\\\s*(#)(\\\\\\\\))\\\",\\\"name\\\":\\\"constant.language.unit.unboxed.haskell\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"match\\\":\\\"(\\\\\\\\()\\\\\\\\s*,[\\\\\\\\s,]*(\\\\\\\\))\\\",\\\"name\\\":\\\"support.constant.tuple.haskell\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.hash.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.operator.hash.haskell\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"match\\\":\\\"(\\\\\\\\()(#)\\\\\\\\s*,[\\\\\\\\s,]*(#)(\\\\\\\\))\\\",\\\"name\\\":\\\"support.constant.tuple.unboxed.haskell\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.bracket.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.bracket.haskell\\\"}},\\\"match\\\":\\\"(\\\\\\\\[)\\\\\\\\s*(])\\\",\\\"name\\\":\\\"constant.language.empty-list.haskell\\\"},{\\\"begin\\\":\\\"(\\\\\\\\b(?<!')(module)|^(signature))(\\\\\\\\b(?!'))\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"keyword.other.module.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.other.signature.haskell\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\b(?<!')where\\\\\\\\b(?!'))\\\",\\\"name\\\":\\\"meta.declaration.module.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"},{\\\"include\\\":\\\"#module_name\\\"},{\\\"include\\\":\\\"#module_exports\\\"},{\\\"match\\\":\\\"[a-z]+\\\",\\\"name\\\":\\\"invalid\\\"}]},{\\\"include\\\":\\\"#ffi\\\"},{\\\"begin\\\":\\\"^(\\\\\\\\s*)(class)(\\\\\\\\b(?!'))\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"keyword.other.class.haskell\\\"}},\\\"end\\\":\\\"(?=(?<!')\\\\\\\\bwhere\\\\\\\\b(?!'))|(?=[};])|^(?!\\\\\\\\1\\\\\\\\s+\\\\\\\\S|\\\\\\\\s*(?:$|\\\\\\\\{-[^@]|--+(?![[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]{}`_\\\\\\\"']]).*$))\\\",\\\"name\\\":\\\"meta.declaration.class.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"},{\\\"include\\\":\\\"#where\\\"},{\\\"include\\\":\\\"#type_signature\\\"}]},{\\\"begin\\\":\\\"^(\\\\\\\\s*)(data|newtype)(?:\\\\\\\\s+(instance))?\\\\\\\\s+((?:(?!(?<![[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']])(?:=|--+)(?![[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']])|\\\\\\\\b(?<!')(?:where|deriving)\\\\\\\\b(?!')|\\\\\\\\{-).)*)(?=\\\\\\\\b(?<!'')where\\\\\\\\b(?!''))\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"keyword.other.$2.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.other.instance.haskell\\\"},\\\"4\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type_signature\\\"}]}},\\\"end\\\":\\\"(?=(?<!')\\\\\\\\bderiving\\\\\\\\b(?!'))|(?=[};])|^(?!\\\\\\\\1\\\\\\\\s+\\\\\\\\S|\\\\\\\\s*(?:$|\\\\\\\\{-[^@]|--+(?![[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]{}`_\\\\\\\"']]).*$))\\\",\\\"name\\\":\\\"meta.declaration.$2.generalized.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"},{\\\"begin\\\":\\\"(?<!')\\\\\\\\b(where)\\\\\\\\s*(\\\\\\\\{)(?!-)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.where.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.brace.haskell\\\"}},\\\"end\\\":\\\"(})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.brace.haskell\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"},{\\\"include\\\":\\\"#gadt_constructor\\\"},{\\\"match\\\":\\\";\\\",\\\"name\\\":\\\"punctuation.semicolon.haskell\\\"}]},{\\\"match\\\":\\\"\\\\\\\\b(?<!')(where)\\\\\\\\b(?!')\\\",\\\"name\\\":\\\"keyword.other.where.haskell\\\"},{\\\"include\\\":\\\"#deriving\\\"},{\\\"include\\\":\\\"#gadt_constructor\\\"}]},{\\\"include\\\":\\\"#role_annotation\\\"},{\\\"begin\\\":\\\"^(\\\\\\\\s*)(pattern)\\\\\\\\s+(.*?)\\\\\\\\s+(::|∷)(?![[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']])\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"keyword.other.pattern.haskell\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comma\\\"},{\\\"include\\\":\\\"#data_constructor\\\"}]},\\\"4\\\":{\\\"name\\\":\\\"keyword.operator.double-colon.haskell\\\"}},\\\"end\\\":\\\"(?=[};])|^(?!\\\\\\\\1\\\\\\\\s+\\\\\\\\S|\\\\\\\\s*(?:$|\\\\\\\\{-[^@]|--+(?![[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]{}`_\\\\\\\"']]).*$))\\\",\\\"name\\\":\\\"meta.declaration.pattern.type.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type_signature\\\"}]},{\\\"begin\\\":\\\"^\\\\\\\\s*(pattern)\\\\\\\\b(?!')\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.pattern.haskell\\\"}},\\\"end\\\":\\\"(?=[};])|^(?!\\\\\\\\1\\\\\\\\s+\\\\\\\\S|\\\\\\\\s*(?:$|\\\\\\\\{-[^@]|--+(?![[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]{}`_\\\\\\\"']]).*$))\\\",\\\"name\\\":\\\"meta.declaration.pattern.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"^(\\\\\\\\s*)(data|newtype)(?:\\\\\\\\s+(family|instance))?\\\\\\\\s+(((?!(?<![[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']])(?:=|--+)(?![[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']])|\\\\\\\\b(?<!')(?:where|deriving)\\\\\\\\b(?!')|\\\\\\\\{-).)*)\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"keyword.other.$2.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.other.$3.haskell\\\"},\\\"4\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type_signature\\\"}]}},\\\"end\\\":\\\"(?=[};])|^(?!\\\\\\\\1\\\\\\\\s+\\\\\\\\S|\\\\\\\\s*(?:$|\\\\\\\\{-[^@]|--+(?![[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]{}`_\\\\\\\"']]).*$))\\\",\\\"name\\\":\\\"meta.declaration.$2.algebraic.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"},{\\\"include\\\":\\\"#deriving\\\"},{\\\"include\\\":\\\"#forall\\\"},{\\\"include\\\":\\\"#adt_constructor\\\"},{\\\"include\\\":\\\"#context\\\"},{\\\"include\\\":\\\"#record_decl\\\"},{\\\"include\\\":\\\"#type_signature\\\"}]},{\\\"begin\\\":\\\"^(\\\\\\\\s*)(type)\\\\\\\\s+(family)\\\\\\\\b(?!')(((?!(?<![[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']])(?:=|--+)(?![[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']])|\\\\\\\\b(?<!')where\\\\\\\\b(?!')|\\\\\\\\{-).)*)\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"keyword.other.type.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.other.family.haskell\\\"},\\\"4\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"},{\\\"include\\\":\\\"#where\\\"},{\\\"include\\\":\\\"#type_signature\\\"}]}},\\\"end\\\":\\\"(?=[};])|^(?!\\\\\\\\1\\\\\\\\s+\\\\\\\\S|\\\\\\\\s*(?:$|\\\\\\\\{-[^@]|--+(?![[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]{}`_\\\\\\\"']]).*$))\\\",\\\"name\\\":\\\"meta.declaration.type.family.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"},{\\\"include\\\":\\\"#where\\\"},{\\\"include\\\":\\\"#type_signature\\\"}]},{\\\"begin\\\":\\\"^(\\\\\\\\s*)(type)(?:\\\\\\\\s+(instance))?\\\\\\\\s+(((?!(?<![[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']])(?:=|--+|::|∷)(?![[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']])|\\\\\\\\{-).)*)\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"keyword.other.type.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.other.instance.haskell\\\"},\\\"4\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type_signature\\\"}]}},\\\"end\\\":\\\"(?=[};])|^(?!\\\\\\\\1\\\\\\\\s+\\\\\\\\S|\\\\\\\\s*(?:$|\\\\\\\\{-[^@]|--+(?![[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]{}`_\\\\\\\"']]).*$))\\\",\\\"name\\\":\\\"meta.declaration.type.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type_signature\\\"}]},{\\\"begin\\\":\\\"^(\\\\\\\\s*)(instance)(\\\\\\\\b(?!'))\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"keyword.other.instance.haskell\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\b(?<!')(where)\\\\\\\\b(?!'))|(?=[};])|^(?!\\\\\\\\1\\\\\\\\s+\\\\\\\\S|\\\\\\\\s*(?:$|\\\\\\\\{-[^@]|--+(?![[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]{}`_\\\\\\\"']]).*$))\\\",\\\"name\\\":\\\"meta.declaration.instance.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"},{\\\"include\\\":\\\"#where\\\"},{\\\"include\\\":\\\"#type_signature\\\"}]},{\\\"begin\\\":\\\"^(\\\\\\\\s*)(import)(\\\\\\\\b(?!'))\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"keyword.other.import.haskell\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\b(?<!')(where)\\\\\\\\b(?!'))|(?=[};])|^(?!\\\\\\\\1\\\\\\\\s+\\\\\\\\S|\\\\\\\\s*(?:$|\\\\\\\\{-[^@]|--+(?![[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]{}`_\\\\\\\"']]).*$))\\\",\\\"name\\\":\\\"meta.import.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"},{\\\"include\\\":\\\"#where\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.$1.haskell\\\"}},\\\"match\\\":\\\"(qualified|as|hiding)\\\"},{\\\"include\\\":\\\"#module_name\\\"},{\\\"include\\\":\\\"#module_exports\\\"}]},{\\\"include\\\":\\\"#deriving\\\"},{\\\"include\\\":\\\"#layout_herald\\\"},{\\\"include\\\":\\\"#keyword\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.$1.haskell\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"},{\\\"include\\\":\\\"#integer_literals\\\"},{\\\"include\\\":\\\"#infix_op\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\s*(infix[lr]?)\\\\\\\\s+(.*)\\\",\\\"name\\\":\\\"meta.fixity-declaration.haskell\\\"},{\\\"include\\\":\\\"#overloaded_label\\\"},{\\\"include\\\":\\\"#type_application\\\"},{\\\"include\\\":\\\"#reserved_symbol\\\"},{\\\"include\\\":\\\"#fun_decl\\\"},{\\\"include\\\":\\\"#qualifier\\\"},{\\\"include\\\":\\\"#data_constructor\\\"},{\\\"include\\\":\\\"#start_type_signature\\\"},{\\\"include\\\":\\\"#prefix_op\\\"},{\\\"include\\\":\\\"#infix_op\\\"},{\\\"begin\\\":\\\"(\\\\\\\\()(#)\\\\\\\\s\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.hash.haskell\\\"}},\\\"end\\\":\\\"(#)(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.hash.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comma\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comma\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"include\\\":\\\"#quasi_quote\\\"},{\\\"begin\\\":\\\"(\\\\\\\\[)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.bracket.haskell\\\"}},\\\"end\\\":\\\"(])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.bracket.haskell\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comma\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"include\\\":\\\"#record\\\"}],\\\"repository\\\":{\\\"adt_constructor\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"},{\\\"begin\\\":\\\"(?<![[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']])(?:(=)|(\\\\\\\\|))(?![[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']])\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.eq.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.pipe.haskell\\\"}},\\\"end\\\":\\\"(?:\\\\\\\\G|^)\\\\\\\\s*(?:(?<!')\\\\\\\\b([\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d'.]+)|('?(?<paren>\\\\\\\\((?:[^()]?|\\\\\\\\g<paren>)*\\\\\\\\)))|('?(?<brac>\\\\\\\\((?:[^\\\\\\\\[\\\\\\\\]]?|\\\\\\\\g<brac>)*])))\\\\\\\\s*(?:(?<![[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']])(:[[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']]*)|(`)([\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)(`))|(?<!')\\\\\\\\b([\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)|(\\\\\\\\()\\\\\\\\s*(:[[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']]*)\\\\\\\\s*(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type_signature\\\"}]},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type_signature\\\"}]},\\\"4\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type_signature\\\"}]},\\\"6\\\":{\\\"name\\\":\\\"constant.other.operator.haskell\\\"},\\\"7\\\":{\\\"name\\\":\\\"punctuation.backtick.haskell\\\"},\\\"8\\\":{\\\"name\\\":\\\"constant.other.haskell\\\"},\\\"9\\\":{\\\"name\\\":\\\"punctuation.backtick.haskell\\\"},\\\"10\\\":{\\\"name\\\":\\\"constant.other.haskell\\\"},\\\"11\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"},\\\"12\\\":{\\\"name\\\":\\\"constant.other.operator.haskell\\\"},\\\"13\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"},{\\\"include\\\":\\\"#deriving\\\"},{\\\"include\\\":\\\"#record_decl\\\"},{\\\"include\\\":\\\"#forall\\\"},{\\\"include\\\":\\\"#context\\\"}]}]},\\\"block_comment\\\":{\\\"applyEndPatternLast\\\":1,\\\"begin\\\":\\\"\\\\\\\\{-\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.haskell\\\"}},\\\"end\\\":\\\"-}\\\",\\\"name\\\":\\\"comment.block.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block_comment\\\"}]},\\\"char_literal\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.character.escape.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.character.escape.octal.haskell\\\"},\\\"4\\\":{\\\"name\\\":\\\"constant.character.escape.hexadecimal.haskell\\\"},\\\"5\\\":{\\\"name\\\":\\\"constant.character.escape.control.haskell\\\"},\\\"6\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.haskell\\\"}},\\\"match\\\":\\\"(?<![\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d'])(')(?:[ -\\\\\\\\[\\\\\\\\]-~]|(\\\\\\\\\\\\\\\\(?:NUL|SOH|STX|ETX|EOT|ENQ|ACK|BEL|BS|HT|LF|VT|FF|CR|SO|SI|DLE|DC1|DC2|DC3|DC4|NAK|SYN|ETB|CAN|EM|SUB|ESC|FS|GS|RS|US|SP|DEL|[abfnrtv\\\\\\\\\\\\\\\\\\\\\\\"'\\\\\\\\&]))|(\\\\\\\\\\\\\\\\o[0-7]+)|(\\\\\\\\\\\\\\\\x\\\\\\\\h+)|(\\\\\\\\\\\\\\\\\\\\\\\\^[A-Z@\\\\\\\\[\\\\\\\\]\\\\\\\\\\\\\\\\^_]))(')\\\",\\\"name\\\":\\\"string.quoted.single.haskell\\\"},\\\"comma\\\":{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.separator.comma.haskell\\\"},\\\"comment_like\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#cpp\\\"},{\\\"include\\\":\\\"#pragma\\\"},{\\\"include\\\":\\\"#comments\\\"}]},\\\"comments\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^(\\\\\\\\s*)(--\\\\\\\\s[|$])\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"punctuation.whitespace.comment.leading.haskell\\\"}},\\\"end\\\":\\\"(?=^(?!\\\\\\\\1--+(?![[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']])))\\\",\\\"name\\\":\\\"comment.block.documentation.haskell\\\"},{\\\"begin\\\":\\\"(^[ \\\\\\\\t]+)?(--\\\\\\\\s[\\\\\\\\^*])\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.comment.leading.haskell\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.line.documentation.haskell\\\"},{\\\"applyEndPatternLast\\\":1,\\\"begin\\\":\\\"\\\\\\\\{-\\\\\\\\s?[|$*^]\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.haskell\\\"}},\\\"end\\\":\\\"-}\\\",\\\"name\\\":\\\"comment.block.documentation.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block_comment\\\"}]},{\\\"begin\\\":\\\"(^[ \\\\\\\\t]+)?(?=--+(?![[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']]))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.comment.leading.haskell\\\"}},\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"--\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.haskell\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.line.double-dash.haskell\\\"}]},{\\\"include\\\":\\\"#block_comment\\\"}]},\\\"context\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"},{\\\"include\\\":\\\"#type_signature\\\"}]},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.big-arrow.haskell\\\"}},\\\"match\\\":\\\"(.*)(?<![[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']])(=>|⇒)(?![[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']])\\\"},\\\"cpp\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.preprocessor.c\\\"}},\\\"match\\\":\\\"^(#).*$\\\",\\\"name\\\":\\\"meta.preprocessor.c\\\"},\\\"data_constructor\\\":{\\\"match\\\":\\\"\\\\\\\\b(?<!')[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*(?![.'\\\\\\\\w])\\\",\\\"name\\\":\\\"constant.other.haskell\\\"},\\\"deriving\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^(\\\\\\\\s*)(deriving)\\\\\\\\s+(?:(via|stock|newtype|anyclass)\\\\\\\\s+)?\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"keyword.other.deriving.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.other.deriving.strategy.$3.haskell\\\"}},\\\"end\\\":\\\"(?=[};])|^(?!\\\\\\\\1\\\\\\\\s+\\\\\\\\S|\\\\\\\\s*(?:$|\\\\\\\\{-[^@]|--+(?![[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]{}`_\\\\\\\"']]).*$))\\\",\\\"name\\\":\\\"meta.deriving.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"},{\\\"match\\\":\\\"(?<!')\\\\\\\\b(instance)\\\\\\\\b(?!')\\\",\\\"name\\\":\\\"keyword.other.instance.haskell\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.deriving.strategy.$1.haskell\\\"}},\\\"match\\\":\\\"(?<!')\\\\\\\\b(via|stock|newtype|anyclass)\\\\\\\\b(?!')\\\"},{\\\"include\\\":\\\"#type_signature\\\"}]},{\\\"begin\\\":\\\"(deriving)(?:\\\\\\\\s+(stock|newtype|anyclass))?\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.deriving.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.deriving.strategy.$2.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"name\\\":\\\"meta.deriving.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type_signature\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.deriving.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.deriving.strategy.$2.haskell\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type_signature\\\"}]},\\\"5\\\":{\\\"name\\\":\\\"keyword.other.deriving.strategy.via.haskell\\\"},\\\"6\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type_signature\\\"}]}},\\\"match\\\":\\\"(deriving)(?:\\\\\\\\s+(stock|newtype|anyclass))?\\\\\\\\s+([\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)(\\\\\\\\s+(via)\\\\\\\\s+(.*)$)?\\\",\\\"name\\\":\\\"meta.deriving.haskell\\\"},{\\\"match\\\":\\\"(?<!')\\\\\\\\b(via)\\\\\\\\b(?!')\\\",\\\"name\\\":\\\"keyword.other.deriving.strategy.via.haskell\\\"}]},\\\"double_colon\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.double-colon.haskell\\\"}},\\\"match\\\":\\\"\\\\\\\\s*(::|∷)(?![[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']])\\\\\\\\s*\\\"},\\\"export_constructs\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"},{\\\"begin\\\":\\\"\\\\\\\\b(?<!')(pattern)\\\\\\\\b(?!')\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.pattern.haskell\\\"}},\\\"end\\\":\\\"([\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)|(\\\\\\\\()\\\\\\\\s*(:[[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']]+)\\\\\\\\s*(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.other.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.other.operator.haskell\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\b(?<!')(type)\\\\\\\\b(?!')\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.type.haskell\\\"}},\\\"end\\\":\\\"([\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)|(\\\\\\\\()\\\\\\\\s*([[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']]+)\\\\\\\\s*(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"storage.type.operator.haskell\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"}]},{\\\"match\\\":\\\"(?<!')\\\\\\\\b[\\\\\\\\p{Ll}_][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*\\\",\\\"name\\\":\\\"entity.name.function.haskell\\\"},{\\\"match\\\":\\\"(?<!')\\\\\\\\b[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*\\\",\\\"name\\\":\\\"storage.type.haskell\\\"},{\\\"include\\\":\\\"#record_wildcard\\\"},{\\\"include\\\":\\\"#reserved_symbol\\\"},{\\\"include\\\":\\\"#prefix_op\\\"}]},\\\"ffi\\\":{\\\"begin\\\":\\\"^(\\\\\\\\s*)(foreign)\\\\\\\\s+(import|export)\\\\\\\\s+\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"keyword.other.foreign.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.other.$3.haskell\\\"}},\\\"end\\\":\\\"(?=[};])|^(?!\\\\\\\\1\\\\\\\\s+\\\\\\\\S|\\\\\\\\s*(?:$|\\\\\\\\{-[^@]|--+(?![[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]{}`_\\\\\\\"']]).*$))\\\",\\\"name\\\":\\\"meta.$3.foreign.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.calling-convention.$1.haskell\\\"}},\\\"match\\\":\\\"\\\\\\\\b(?<!')(ccall|cplusplus|dotnet|jvm|stdcall|prim|capi)\\\\\\\\s+\\\"},{\\\"begin\\\":\\\"(?=\\\\\\\")|(?=\\\\\\\\b(?<!')([\\\\\\\\p{Ll}_][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)\\\\\\\\b(?!'))\\\",\\\"end\\\":\\\"(?=(::|∷)(?![[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']]))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.safety.$1.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.foreign.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_literal\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"entity.name.function.haskell\\\"},\\\"4\\\":{\\\"name\\\":\\\"entity.name.function.infix.haskell\\\"}},\\\"match\\\":\\\"\\\\\\\\b(?<!')(safe|unsafe|interruptible)\\\\\\\\b(?!')\\\\\\\\s*(\\\\\\\"(?:\\\\\\\\\\\\\\\\\\\\\\\"|[^\\\\\\\"])*\\\\\\\")?\\\\\\\\s*(?:\\\\\\\\b(?<!'')([\\\\\\\\p{Ll}_][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)\\\\\\\\b(?!')|\\\\\\\\(\\\\\\\\s*(?!--+\\\\\\\\))([[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']]+)\\\\\\\\s*\\\\\\\\))\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.safety.$1.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.foreign.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_literal\\\"}]}},\\\"match\\\":\\\"\\\\\\\\b(?<!')(safe|unsafe|interruptible)\\\\\\\\b(?!')\\\\\\\\s*(\\\\\\\"(?:\\\\\\\\\\\\\\\\\\\\\\\"|[^\\\\\\\"])*\\\\\\\")?\\\\\\\\s*$\\\"},{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"entity.name.foreign.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_literal\\\"}]}},\\\"match\\\":\\\"\\\\\\\"(?:\\\\\\\\\\\\\\\\\\\\\\\"|[^\\\\\\\"])*\\\\\\\"\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.function.infix.haskell\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"match\\\":\\\"\\\\\\\\b(?<!'')([\\\\\\\\p{Ll}_][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)\\\\\\\\b(?!')|(\\\\\\\\()\\\\\\\\s*(?!--+\\\\\\\\))([[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']]+)\\\\\\\\s*(\\\\\\\\))\\\"}]},{\\\"include\\\":\\\"#double_colon\\\"},{\\\"include\\\":\\\"#type_signature\\\"}]},\\\"float_literals\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.numeric.floating.decimal.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.numeric.floating.hexadecimal.haskell\\\"}},\\\"match\\\":\\\"\\\\\\\\b(?<!')(?:([0-9][_0-9]*\\\\\\\\.[0-9][_0-9]*(?:[eE][-+]?[0-9][_0-9]*)?|[0-9][_0-9]*[eE][-+]?[0-9][_0-9]*)|(0(?:[xX]_*\\\\\\\\h[_\\\\\\\\h]*\\\\\\\\.\\\\\\\\h[_\\\\\\\\h]*(?:[pP][-+]?[0-9][_0-9]*)?|[xX]_*\\\\\\\\h[_\\\\\\\\h]*[pP][-+]?[0-9][_0-9]*)))\\\\\\\\b(?!')\\\"},\\\"forall\\\":{\\\"begin\\\":\\\"\\\\\\\\b(?<!')(forall|∀)\\\\\\\\b(?!')\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.forall.haskell\\\"}},\\\"end\\\":\\\"(\\\\\\\\.)|(->|→)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.period.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.arrow.haskell\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"},{\\\"include\\\":\\\"#type_variable\\\"},{\\\"include\\\":\\\"#type_signature\\\"}]},\\\"fun_decl\\\":{\\\"begin\\\":\\\"^(\\\\\\\\s*)(?<fn>(?:[\\\\\\\\p{Ll}_][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*#*|\\\\\\\\(\\\\\\\\s*(?!--+\\\\\\\\))[[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),:;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']][[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']]*\\\\\\\\s*\\\\\\\\))(?:\\\\\\\\s*,\\\\\\\\s*\\\\\\\\g<fn>)?)\\\\\\\\s*(?<![[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^),;\\\\\\\\]`}_\\\\\\\"']])(::|∷)(?![[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(,;\\\\\\\\[`{_\\\\\\\"']])\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#reserved_symbol\\\"},{\\\"include\\\":\\\"#prefix_op\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"keyword.operator.double-colon.haskell\\\"}},\\\"end\\\":\\\"(?=(?<![[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']])((<-|←)|(=)|(-<|↢)|(-<<|⤛))([(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"'[^\\\\\\\\p{S}\\\\\\\\p{P}]]))|(?=[};])|^(?!\\\\\\\\1\\\\\\\\s+\\\\\\\\S|\\\\\\\\s*(?:$|\\\\\\\\{-[^@]|--+(?![[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]{}`_\\\\\\\"']]).*$))\\\",\\\"name\\\":\\\"meta.function.type-declaration.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type_signature\\\"}]},\\\"gadt_constructor\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^(\\\\\\\\s*)(?:(\\\\\\\\b(?<!')[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)|(\\\\\\\\()\\\\\\\\s*(:[[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']]*)\\\\\\\\s*(\\\\\\\\)))\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"constant.other.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"},\\\"4\\\":{\\\"name\\\":\\\"constant.other.operator.haskell\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\b(?<!'')deriving\\\\\\\\b(?!'))|(?=[};])|^(?!\\\\\\\\1\\\\\\\\s+\\\\\\\\S|\\\\\\\\s*(?:$|\\\\\\\\{-[^@]|--+(?![[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]{}`_\\\\\\\"']]).*$))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"},{\\\"include\\\":\\\"#deriving\\\"},{\\\"include\\\":\\\"#double_colon\\\"},{\\\"include\\\":\\\"#record_decl\\\"},{\\\"include\\\":\\\"#type_signature\\\"}]},{\\\"begin\\\":\\\"(\\\\\\\\b(?<!')[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d]*)|(\\\\\\\\()\\\\\\\\s*(:[[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']]*)\\\\\\\\s*(\\\\\\\\))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.other.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.other.operator.haskell\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"end\\\":\\\"$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"},{\\\"include\\\":\\\"#deriving\\\"},{\\\"include\\\":\\\"#double_colon\\\"},{\\\"include\\\":\\\"#record_decl\\\"},{\\\"include\\\":\\\"#type_signature\\\"}]}]},\\\"infix_op\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.promotion.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.namespace.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.operator.infix.haskell\\\"}},\\\"match\\\":\\\"((?:(?<!'')('')?[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*\\\\\\\\.)*)(#+|[[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']]+(?<!#))\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.backtick.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.namespace.haskell\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#data_constructor\\\"}]},\\\"4\\\":{\\\"name\\\":\\\"punctuation.backtick.haskell\\\"}},\\\"match\\\":\\\"(`)((?:[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*\\\\\\\\.)*)([\\\\\\\\p{Ll}\\\\\\\\p{Lu}_][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)(`)\\\",\\\"name\\\":\\\"keyword.operator.function.infix.haskell\\\"}]},\\\"inline_phase\\\":{\\\"begin\\\":\\\"\\\\\\\\[\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.bracket.haskell\\\"}},\\\"end\\\":\\\"]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.bracket.haskell\\\"}},\\\"name\\\":\\\"meta.inlining-phase.haskell\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"~\\\",\\\"name\\\":\\\"punctuation.tilde.haskell\\\"},{\\\"include\\\":\\\"#integer_literals\\\"},{\\\"match\\\":\\\"\\\\\\\\w*\\\",\\\"name\\\":\\\"invalid\\\"}]},\\\"integer_literals\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.numeric.integral.decimal.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.numeric.integral.hexadecimal.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.numeric.integral.octal.haskell\\\"},\\\"4\\\":{\\\"name\\\":\\\"constant.numeric.integral.binary.haskell\\\"}},\\\"match\\\":\\\"\\\\\\\\b(?<!')(?:([0-9][_0-9]*)|(0[xX]_*\\\\\\\\h[_\\\\\\\\h]*)|(0[oO]_*[0-7][_0-7]*)|(0[bB]_*[01][_01]*))\\\\\\\\b(?!')\\\"},\\\"keyword\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.$1.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.$2.haskell\\\"}},\\\"match\\\":\\\"\\\\\\\\b(?<!')(?:(where|let|in|default)|(m?do|if|then|else|case|of|proc|rec))\\\\\\\\b(?!')\\\"},\\\"layout_herald\\\":{\\\"begin\\\":\\\"(?<!')\\\\\\\\b(?:(where|let|m?do)|(of))\\\\\\\\s*(\\\\\\\\{)(?!-)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.$1.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.of.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.brace.haskell\\\"}},\\\"end\\\":\\\"(})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.brace.haskell\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"},{\\\"match\\\":\\\";\\\",\\\"name\\\":\\\"punctuation.semicolon.haskell\\\"}]},\\\"liquid_haskell\\\":{\\\"begin\\\":\\\"\\\\\\\\{-@\\\",\\\"end\\\":\\\"@-}\\\",\\\"name\\\":\\\"block.liquidhaskell.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},\\\"module_exports\\\":{\\\"applyEndPatternLast\\\":1,\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"name\\\":\\\"meta.declaration.exports.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.module.haskell\\\"}},\\\"match\\\":\\\"\\\\\\\\b(?<!')(module)\\\\\\\\b(?!')\\\"},{\\\"include\\\":\\\"#comma\\\"},{\\\"include\\\":\\\"#export_constructs\\\"},{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"},{\\\"include\\\":\\\"#record_wildcard\\\"},{\\\"include\\\":\\\"#export_constructs\\\"},{\\\"include\\\":\\\"#comma\\\"}]}]},\\\"module_name\\\":{\\\"match\\\":\\\"(?<conid>[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*(\\\\\\\\.\\\\\\\\g<conid>)?)\\\",\\\"name\\\":\\\"entity.name.namespace.haskell\\\"},\\\"numeric_literals\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#float_literals\\\"},{\\\"include\\\":\\\"#integer_literals\\\"}]},\\\"overloaded_label\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.prefix.hash.haskell\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#string_literal\\\"}]}},\\\"match\\\":\\\"(?<![[\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(,;\\\\\\\\[`{]])(#)(?:(\\\\\\\"(?:\\\\\\\\\\\\\\\\\\\\\\\"|[^\\\\\\\"])*\\\\\\\")|[\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d'.]+)\\\",\\\"name\\\":\\\"entity.name.label.haskell\\\"}]},\\\"pragma\\\":{\\\"begin\\\":\\\"\\\\\\\\{-#\\\",\\\"end\\\":\\\"#-}\\\",\\\"name\\\":\\\"meta.preprocessor.haskell\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\b(?<!')(LANGUAGE)\\\\\\\\b(?!')\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.preprocessor.pragma.haskell\\\"}},\\\"end\\\":\\\"(?=#-})\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?:No)?(?:AutoDeriveTypeable|DatatypeContexts|DoRec|IncoherentInstances|MonadFailDesugaring|MonoPatBinds|NullaryTypeClasses|OverlappingInstances|PatternSignatures|RecordPuns|RelaxedPolyRec)\\\",\\\"name\\\":\\\"invalid.deprecated\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.preprocessor.extension.haskell\\\"}},\\\"match\\\":\\\"((?:No)?(?:AllowAmbiguousTypes|AlternativeLayoutRule|AlternativeLayoutRuleTransitional|Arrows|BangPatterns|BinaryLiterals|CApiFFI|CPP|CUSKs|ConstrainedClassMethods|ConstraintKinds|DataKinds|DefaultSignatures|DeriveAnyClass|DeriveDataTypeable|DeriveFoldable|DeriveFunctor|DeriveGeneric|DeriveLift|DeriveTraversable|DerivingStrategies|DerivingVia|DisambiguateRecordFields|DoAndIfThenElse|BlockArguments|DuplicateRecordFields|EmptyCase|EmptyDataDecls|EmptyDataDeriving|ExistentialQuantification|ExplicitForAll|ExplicitNamespaces|ExtendedDefaultRules|FlexibleContexts|FlexibleInstances|ForeignFunctionInterface|FunctionalDependencies|GADTSyntax|GADTs|GHCForeignImportPrim|Generali[sz]edNewtypeDeriving|ImplicitParams|ImplicitPrelude|ImportQualifiedPost|ImpredicativeTypes|TypeFamilyDependencies|InstanceSigs|ApplicativeDo|InterruptibleFFI|JavaScriptFFI|KindSignatures|LambdaCase|LiberalTypeSynonyms|MagicHash|MonadComprehensions|MonoLocalBinds|MonomorphismRestriction|MultiParamTypeClasses|MultiWayIf|NumericUnderscores|NPlusKPatterns|NamedFieldPuns|NamedWildCards|NegativeLiterals|HexFloatLiterals|NondecreasingIndentation|NumDecimals|OverloadedLabels|OverloadedLists|OverloadedStrings|PackageImports|ParallelArrays|ParallelListComp|PartialTypeSignatures|PatternGuards|PatternSynonyms|PolyKinds|PolymorphicComponents|QuantifiedConstraints|PostfixOperators|QuasiQuotes|Rank2Types|RankNTypes|RebindableSyntax|RecordWildCards|RecursiveDo|RelaxedLayout|RoleAnnotations|ScopedTypeVariables|StandaloneDeriving|StarIsType|StaticPointers|Strict|StrictData|TemplateHaskell|TemplateHaskellQuotes|StandaloneKindSignatures|TraditionalRecordSyntax|TransformListComp|TupleSections|TypeApplications|TypeInType|TypeFamilies|TypeOperators|TypeSynonymInstances|UnboxedTuples|UnboxedSums|UndecidableInstances|UndecidableSuperClasses|UnicodeSyntax|UnliftedFFITypes|UnliftedNewtypes|ViewPatterns))\\\"},{\\\"include\\\":\\\"#comma\\\"}]},{\\\"begin\\\":\\\"(?i)\\\\\\\\b(?<!')(SPECIALI[SZ]E)(?:\\\\\\\\s*(\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*])?\\\\\\\\s*|\\\\\\\\s+)(instance)\\\\\\\\b(?!')\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.preprocessor.pragma.haskell\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#inline_phase\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"keyword.other.instance.haskell\\\"}},\\\"end\\\":\\\"(?=#-})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type_signature\\\"}]},{\\\"begin\\\":\\\"(?i)\\\\\\\\b(?<!')(SPECIALI[SZ]E)\\\\\\\\b(?!')(?:\\\\\\\\s+(INLINE)\\\\\\\\b(?!'))?\\\\\\\\s*(\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*])?\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.preprocessor.pragma.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.preprocessor.pragma.haskell\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#inline_phase\\\"}]}},\\\"end\\\":\\\"(?=#-})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"match\\\":\\\"(?i)\\\\\\\\b(?<!')(LANGUAGE|OPTIONS_GHC|INCLUDE|MINIMAL|UNPACK|OVERLAPS|INCOHERENT|NOUNPACK|SOURCE|OVERLAPPING|OVERLAPPABLE|INLINE|NOINLINE|INLINE?ABLE|CONLIKE|LINE|COLUMN|RULES|COMPLETE)\\\\\\\\b(?!')\\\",\\\"name\\\":\\\"keyword.other.preprocessor.haskell\\\"},{\\\"begin\\\":\\\"(?i)\\\\\\\\b(DEPRECATED|WARNING)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.preprocessor.pragma.haskell\\\"}},\\\"end\\\":\\\"(?=#-})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_literal\\\"}]}]},\\\"prefix_op\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.infix.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"match\\\":\\\"(\\\\\\\\()\\\\\\\\s*(?!(?:--+|\\\\\\\\.\\\\\\\\.)\\\\\\\\))(#+|[[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']]+(?<!#))\\\\\\\\s*(\\\\\\\\))\\\"}]},\\\"qualifier\\\":{\\\"match\\\":\\\"\\\\\\\\b(?<!')[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*\\\\\\\\.\\\",\\\"name\\\":\\\"entity.name.namespace.haskell\\\"},\\\"quasi_quote\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(\\\\\\\\[)([edp])?(\\\\\\\\|\\\\\\\\|?)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.quasi-quotation.begin.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.quasi-quoter.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.operator.quasi-quotation.begin.haskell\\\"}},\\\"end\\\":\\\"\\\\\\\\3]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.quasi-quotation.end.haskell\\\"}},\\\"name\\\":\\\"meta.quasi-quotation.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"(\\\\\\\\[)(t)(\\\\\\\\|\\\\\\\\|?)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.quasi-quotation.begin.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.quasi-quoter.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.operator.quasi-quotation.begin.haskell\\\"}},\\\"end\\\":\\\"\\\\\\\\3]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.quasi-quotation.end.haskell\\\"}},\\\"name\\\":\\\"meta.quasi-quotation.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type_signature\\\"}]},{\\\"begin\\\":\\\"(\\\\\\\\[)(?:(\\\\\\\\$\\\\\\\\$)|(\\\\\\\\$))?([[^\\\\\\\\s\\\\\\\\p{S}\\\\\\\\p{P}].'_]*)(\\\\\\\\|\\\\\\\\|?)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.quasi-quotation.begin.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.prefix.double-dollar.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.operator.prefix.dollar.haskell\\\"},\\\"4\\\":{\\\"name\\\":\\\"entity.name.quasi-quoter.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#qualifier\\\"}]},\\\"5\\\":{\\\"name\\\":\\\"keyword.operator.quasi-quotation.begin.haskell\\\"}},\\\"end\\\":\\\"\\\\\\\\5]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.quasi-quotation.end.haskell\\\"}},\\\"name\\\":\\\"meta.quasi-quotation.haskell\\\"}]},\\\"record\\\":{\\\"begin\\\":\\\"(\\\\\\\\{)(?!-)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.brace.haskell\\\"}},\\\"end\\\":\\\"(?<!-)(})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.brace.haskell\\\"}},\\\"name\\\":\\\"meta.record.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"},{\\\"include\\\":\\\"#record_field\\\"}]},\\\"record_decl\\\":{\\\"begin\\\":\\\"(\\\\\\\\{)(?!-)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.brace.haskell\\\"}},\\\"end\\\":\\\"(?<!-)(})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.brace.haskell\\\"}},\\\"name\\\":\\\"meta.record.definition.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"},{\\\"include\\\":\\\"#record_decl_field\\\"}]},\\\"record_decl_field\\\":{\\\"begin\\\":\\\"(?:([\\\\\\\\p{Ll}_][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)|(\\\\\\\\()\\\\\\\\s*([[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']]+)\\\\\\\\s*(\\\\\\\\)))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.member.definition.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"variable.other.member.definition.haskell\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"end\\\":\\\"(,)|(?=})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.comma.haskell\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"},{\\\"include\\\":\\\"#comma\\\"},{\\\"include\\\":\\\"#double_colon\\\"},{\\\"include\\\":\\\"#type_signature\\\"},{\\\"include\\\":\\\"#record_decl_field\\\"}]},\\\"record_field\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?:([\\\\\\\\p{Ll}\\\\\\\\p{Lu}_][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d.']*)|(\\\\\\\\()\\\\\\\\s*([[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']]+)\\\\\\\\s*(\\\\\\\\)))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.member.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#qualifier\\\"}]},\\\"2\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"variable.other.member.haskell\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"end\\\":\\\"(,)|(?=})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.comma.haskell\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"},{\\\"include\\\":\\\"#comma\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"include\\\":\\\"#record_wildcard\\\"}]},\\\"record_wildcard\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.member.wildcard.haskell\\\"}},\\\"match\\\":\\\"(?<![[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']])(\\\\\\\\.\\\\\\\\.)(?![[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']])\\\"},\\\"reserved_symbol\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.double-dot.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.colon.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.operator.eq.haskell\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.operator.lambda.haskell\\\"},\\\"5\\\":{\\\"name\\\":\\\"keyword.operator.pipe.haskell\\\"},\\\"6\\\":{\\\"name\\\":\\\"keyword.operator.arrow.left.haskell\\\"},\\\"7\\\":{\\\"name\\\":\\\"keyword.operator.arrow.haskell\\\"},\\\"8\\\":{\\\"name\\\":\\\"keyword.operator.arrow.left.tail.haskell\\\"},\\\"9\\\":{\\\"name\\\":\\\"keyword.operator.arrow.left.tail.double.haskell\\\"},\\\"10\\\":{\\\"name\\\":\\\"keyword.operator.arrow.tail.haskell\\\"},\\\"11\\\":{\\\"name\\\":\\\"keyword.operator.arrow.tail.double.haskell\\\"},\\\"12\\\":{\\\"name\\\":\\\"keyword.other.forall.haskell\\\"}},\\\"match\\\":\\\"(?<![[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']])(?:(\\\\\\\\.\\\\\\\\.)|(:)|(=)|(\\\\\\\\\\\\\\\\)|(\\\\\\\\|)|(<-|←)|(->|→)|(-<|↢)|(-<<|⤛)|(>-|⤚)|(>>-|⤜)|(∀))(?![[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']])\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.postfix.hash.haskell\\\"}},\\\"match\\\":\\\"(?<=[[\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d\\\\\\\\p{S}\\\\\\\\p{P}]&&[^#,;\\\\\\\\[`{]])(#+)(?![[\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d\\\\\\\\p{S}\\\\\\\\p{P}]&&[^),;\\\\\\\\]`}]])\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.infix.tight.at.haskell\\\"}},\\\"match\\\":\\\"(?<=[\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d)}\\\\\\\\]])(@)(?=[\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d(\\\\\\\\[{])\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.prefix.tilde.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.prefix.bang.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.operator.prefix.minus.haskell\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.operator.prefix.dollar.haskell\\\"},\\\"5\\\":{\\\"name\\\":\\\"keyword.operator.prefix.double-dollar.haskell\\\"}},\\\"match\\\":\\\"(?<![[\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(,;\\\\\\\\[`{]])(?:(~)|(!)|(-)|(\\\\\\\\$)|(\\\\\\\\$\\\\\\\\$))(?=[\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d({\\\\\\\\[])\\\"}]},\\\"role_annotation\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^(\\\\\\\\s*)(type)\\\\\\\\s+(role)\\\\\\\\b(?!')\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"keyword.other.type.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.other.role.haskell\\\"}},\\\"end\\\":\\\"(?=[};])|^(?!\\\\\\\\1\\\\\\\\s+\\\\\\\\S|\\\\\\\\s*(?:$|\\\\\\\\{-[^@]|--+(?![[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]{}`_\\\\\\\"']]).*$))\\\",\\\"name\\\":\\\"meta.role-annotation.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"},{\\\"include\\\":\\\"#type_constructor\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.role.$1.haskell\\\"}},\\\"match\\\":\\\"\\\\\\\\b(?<!')(nominal|representational|phantom)\\\\\\\\b(?!')\\\"}]}]},\\\"start_type_signature\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^(\\\\\\\\s*)(::|∷)(?![[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(,;\\\\\\\\[`{_\\\\\\\"']])\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.double-colon.haskell\\\"}},\\\"end\\\":\\\"(?=#?\\\\\\\\)|[\\\\\\\\],]|(?<!')\\\\\\\\b(in|then|else|of)\\\\\\\\b(?!')|(?<![[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']])(?:([\\\\\\\\\\\\\\\\λ])|(<-|←)|(=)|(-<|↢)|(-<<|⤛))([(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"'[^\\\\\\\\p{S}\\\\\\\\p{P}]])|([#@])-}|(?=[};])|^(?!\\\\\\\\1\\\\\\\\s*\\\\\\\\S|\\\\\\\\s*(?:$|\\\\\\\\{-[^@]|--+(?![[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]{}`_\\\\\\\"']]).*$)))\\\",\\\"name\\\":\\\"meta.type-declaration.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type_signature\\\"}]},{\\\"begin\\\":\\\"(?<![[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(,;\\\\\\\\[`{_\\\\\\\"']])(::|∷)(?![[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(,;\\\\\\\\[`{_\\\\\\\"']])\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.double-colon.haskell\\\"}},\\\"end\\\":\\\"(?=#?\\\\\\\\)|[\\\\\\\\],]|\\\\\\\\b(?<!')(in|then|else|of)\\\\\\\\b(?!')|([#@])-}|(?<![[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']])(?:([\\\\\\\\\\\\\\\\λ])|(<-|←)|(=)|(-<|↢)|(-<<|⤛))([(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"'[^\\\\\\\\p{S}\\\\\\\\p{P}]])|(?=[};])|$)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type_signature\\\"}]}]},\\\"string_literal\\\":{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.haskell\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.haskell\\\"}},\\\"name\\\":\\\"string.quoted.double.haskell\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\(NUL|SOH|STX|ETX|EOT|ENQ|ACK|BEL|BS|HT|LF|VT|FF|CR|SO|SI|DLE|DC1|DC2|DC3|DC4|NAK|SYN|ETB|CAN|EM|SUB|ESC|FS|GS|RS|US|SP|DEL|[abfnrtv\\\\\\\\\\\\\\\\\\\\\\\"'\\\\\\\\&])\\\",\\\"name\\\":\\\"constant.character.escape.haskell\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\(?:o[0-7]+|x\\\\\\\\h+|[0-9]+)\\\",\\\"name\\\":\\\"constant.character.escape.octal.haskell\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\\\\\\\\\^[A-Z@\\\\\\\\[\\\\\\\\]\\\\\\\\\\\\\\\\^_]\\\",\\\"name\\\":\\\"constant.character.escape.control.haskell\\\"},{\\\"begin\\\":\\\"\\\\\\\\\\\\\\\\\\\\\\\\s\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"constant.character.escape.begin.haskell\\\"}},\\\"end\\\":\\\"\\\\\\\\\\\\\\\\\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"constant.character.escape.end.haskell\\\"}},\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\S+\\\",\\\"name\\\":\\\"invalid.illegal.character-not-allowed-here.haskell\\\"}]}]},\\\"type_application\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=[\\\\\\\\s,;\\\\\\\\[\\\\\\\\]{}\\\\\\\"])(@)(')?(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.prefix.at.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.promotion.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"name\\\":\\\"meta.type-application.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type_signature\\\"}]},{\\\"begin\\\":\\\"(?<=[\\\\\\\\s,;\\\\\\\\[\\\\\\\\]{}\\\\\\\"])(@)(')?(\\\\\\\\[)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.prefix.at.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.promotion.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.bracket.haskell\\\"}},\\\"end\\\":\\\"]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.bracket.haskell\\\"}},\\\"name\\\":\\\"meta.type-application.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type_signature\\\"}]},{\\\"begin\\\":\\\"(?<=[\\\\\\\\s,;\\\\\\\\[\\\\\\\\]{}\\\\\\\"])(@)(?=\\\\\\\")\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.prefix.at.haskell\\\"}},\\\"end\\\":\\\"(?<=\\\\\\\")\\\",\\\"name\\\":\\\"meta.type-application.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_literal\\\"}]},{\\\"begin\\\":\\\"(?<=[\\\\\\\\s,;\\\\\\\\[\\\\\\\\]{}\\\\\\\"])(@)(?=[\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d'])\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.prefix.at.haskell\\\"}},\\\"end\\\":\\\"(?![\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d'])\\\",\\\"name\\\":\\\"meta.type-application.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type_signature\\\"}]}]},\\\"type_constructor\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.promotion.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.namespace.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"storage.type.haskell\\\"}},\\\"match\\\":\\\"(')?((?:\\\\\\\\b[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*\\\\\\\\.)*)(\\\\\\\\b[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.promotion.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.namespace.haskell\\\"},\\\"4\\\":{\\\"name\\\":\\\"storage.type.operator.haskell\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"match\\\":\\\"(')?(\\\\\\\\()\\\\\\\\s*((?:[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*\\\\\\\\.)*)([[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']]+)\\\\\\\\s*(\\\\\\\\))\\\"}]},\\\"type_operator\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.promotion.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.namespace.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"storage.type.operator.infix.haskell\\\"}},\\\"match\\\":\\\"(?:(?<!')('))?((?:\\\\\\\\b[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*\\\\\\\\.)*)(?![#@]?-})(#+|[[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']]+(?<!#))\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.promotion.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.backtick.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.namespace.haskell\\\"},\\\"4\\\":{\\\"name\\\":\\\"storage.type.infix.haskell\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.backtick.haskell\\\"}},\\\"match\\\":\\\"(')?(`)((?:[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*\\\\\\\\.)*)([\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)(`)\\\"}]},\\\"type_signature\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.promotion.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"match\\\":\\\"(')?(\\\\\\\\()\\\\\\\\s*(\\\\\\\\))\\\",\\\"name\\\":\\\"support.constant.unit.haskell\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.hash.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.operator.hash.haskell\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"match\\\":\\\"(\\\\\\\\()(#)\\\\\\\\s*(#)(\\\\\\\\))\\\",\\\"name\\\":\\\"support.constant.unit.unboxed.haskell\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.promotion.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"match\\\":\\\"(')?(\\\\\\\\()\\\\\\\\s*,[\\\\\\\\s,]*(\\\\\\\\))\\\",\\\"name\\\":\\\"support.constant.tuple.haskell\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.hash.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.operator.hash.haskell\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"match\\\":\\\"(\\\\\\\\()(#)\\\\\\\\s*(#)(\\\\\\\\))\\\",\\\"name\\\":\\\"support.constant.unit.unboxed.haskell\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.hash.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.operator.hash.haskell\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"match\\\":\\\"(\\\\\\\\()(#)\\\\\\\\s*,[\\\\\\\\s,]*(#)(\\\\\\\\))\\\",\\\"name\\\":\\\"support.constant.tuple.unboxed.haskell\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.promotion.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.bracket.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.bracket.haskell\\\"}},\\\"match\\\":\\\"(')?(\\\\\\\\[)\\\\\\\\s*(])\\\",\\\"name\\\":\\\"support.constant.empty-list.haskell\\\"},{\\\"include\\\":\\\"#integer_literals\\\"},{\\\"match\\\":\\\"(::|∷)(?![[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']])\\\",\\\"name\\\":\\\"keyword.operator.double-colon.haskell\\\"},{\\\"include\\\":\\\"#forall\\\"},{\\\"match\\\":\\\"=>|⇒\\\",\\\"name\\\":\\\"keyword.operator.big-arrow.haskell\\\"},{\\\"include\\\":\\\"#string_literal\\\"},{\\\"match\\\":\\\"'[^']'\\\",\\\"name\\\":\\\"invalid\\\"},{\\\"include\\\":\\\"#type_application\\\"},{\\\"include\\\":\\\"#reserved_symbol\\\"},{\\\"include\\\":\\\"#type_operator\\\"},{\\\"include\\\":\\\"#type_constructor\\\"},{\\\"begin\\\":\\\"(\\\\\\\\()(#)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.hash.haskell\\\"}},\\\"end\\\":\\\"(#)(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.hash.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comma\\\"},{\\\"include\\\":\\\"#type_signature\\\"}]},{\\\"begin\\\":\\\"(')?(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.promotion.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comma\\\"},{\\\"include\\\":\\\"#type_signature\\\"}]},{\\\"begin\\\":\\\"(')?(\\\\\\\\[)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.promotion.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.bracket.haskell\\\"}},\\\"end\\\":\\\"(])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.bracket.haskell\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comma\\\"},{\\\"include\\\":\\\"#type_signature\\\"}]},{\\\"include\\\":\\\"#type_variable\\\"}]},\\\"type_variable\\\":{\\\"match\\\":\\\"\\\\\\\\b(?<!')(?!(?:forall|deriving)\\\\\\\\b(?!'))[\\\\\\\\p{Ll}_][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*\\\",\\\"name\\\":\\\"variable.other.generic-type.haskell\\\"},\\\"where\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<!')\\\\\\\\b(where)\\\\\\\\s*(\\\\\\\\{)(?!-)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.where.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.brace.haskell\\\"}},\\\"end\\\":\\\"(})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.brace.haskell\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"},{\\\"match\\\":\\\";\\\",\\\"name\\\":\\\"punctuation.semicolon.haskell\\\"}]},{\\\"match\\\":\\\"\\\\\\\\b(?<!')(where)\\\\\\\\b(?!')\\\",\\\"name\\\":\\\"keyword.other.where.haskell\\\"}]}},\\\"scopeName\\\":\\\"source.haskell\\\",\\\"aliases\\\":[\\\"hs\\\"]}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/haskell.mjs\n"));

/***/ })

}]);