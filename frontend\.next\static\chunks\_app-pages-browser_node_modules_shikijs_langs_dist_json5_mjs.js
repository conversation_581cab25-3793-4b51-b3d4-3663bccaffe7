"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_json5_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/json5.mjs":
/*!****************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/json5.mjs ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"JSON5\\\",\\\"fileTypes\\\":[\\\"json5\\\"],\\\"name\\\":\\\"json5\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#value\\\"}],\\\"repository\\\":{\\\"array\\\":{\\\"begin\\\":\\\"\\\\\\\\[\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.array.begin.json5\\\"}},\\\"end\\\":\\\"]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.array.end.json5\\\"}},\\\"name\\\":\\\"meta.structure.array.json5\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#value\\\"},{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.separator.array.json5\\\"},{\\\"match\\\":\\\"[^\\\\\\\\s\\\\\\\\]]\\\",\\\"name\\\":\\\"invalid.illegal.expected-array-separator.json5\\\"}]},\\\"comments\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"/{2}.*\\\",\\\"name\\\":\\\"comment.single.json5\\\"},{\\\"begin\\\":\\\"/\\\\\\\\*\\\\\\\\*(?!/)\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.json5\\\"}},\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"name\\\":\\\"comment.block.documentation.json5\\\"},{\\\"begin\\\":\\\"/\\\\\\\\*\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.json5\\\"}},\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"name\\\":\\\"comment.block.json5\\\"}]},\\\"constant\\\":{\\\"match\\\":\\\"\\\\\\\\b(?:true|false|null|Infinity|NaN)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.json5\\\"},\\\"infinity\\\":{\\\"match\\\":\\\"(-)*\\\\\\\\b(?:Infinity|NaN)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.json5\\\"},\\\"key\\\":{\\\"name\\\":\\\"string.key.json5\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#stringSingle\\\"},{\\\"include\\\":\\\"#stringDouble\\\"},{\\\"match\\\":\\\"[a-zA-Z0-9_-]\\\",\\\"name\\\":\\\"string.key.json5\\\"}]},\\\"number\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(0x)[0-9a-fA-f]*\\\",\\\"name\\\":\\\"constant.hex.numeric.json5\\\"},{\\\"match\\\":\\\"[+-.]?(?=[1-9]|0(?!\\\\\\\\d))\\\\\\\\d+(\\\\\\\\.\\\\\\\\d+)?([eE][+-]?\\\\\\\\d+)?\\\",\\\"name\\\":\\\"constant.dec.numeric.json5\\\"}]},\\\"object\\\":{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.dictionary.begin.json5\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.dictionary.end.json5\\\"}},\\\"name\\\":\\\"meta.structure.dictionary.json5\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#key\\\"},{\\\"begin\\\":\\\":\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.separator.dictionary.key-value.json5\\\"}},\\\"end\\\":\\\"(,)|(?=})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.dictionary.pair.json5\\\"}},\\\"name\\\":\\\"meta.structure.dictionary.value.json5\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#value\\\"},{\\\"match\\\":\\\"[^\\\\\\\\s,]\\\",\\\"name\\\":\\\"invalid.illegal.expected-dictionary-separator.json5\\\"}]},{\\\"match\\\":\\\"[^\\\\\\\\s}]\\\",\\\"name\\\":\\\"invalid.illegal.expected-dictionary-separator.json5\\\"}]},\\\"stringDouble\\\":{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.json5\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.json5\\\"}},\\\"name\\\":\\\"string.quoted.json5\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\(?:[\\\\\\\"\\\\\\\\\\\\\\\\/bfnrt]|u\\\\\\\\h{4})\\\",\\\"name\\\":\\\"constant.character.escape.json5\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"invalid.illegal.unrecognized-string-escape.json5\\\"}]},\\\"stringSingle\\\":{\\\"begin\\\":\\\"'\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.json5\\\"}},\\\"end\\\":\\\"'\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.json5\\\"}},\\\"name\\\":\\\"string.quoted.json5\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\(?:[\\\\\\\"\\\\\\\\\\\\\\\\/bfnrt]|u\\\\\\\\h{4})\\\",\\\"name\\\":\\\"constant.character.escape.json5\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"invalid.illegal.unrecognized-string-escape.json5\\\"}]},\\\"value\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#constant\\\"},{\\\"include\\\":\\\"#infinity\\\"},{\\\"include\\\":\\\"#number\\\"},{\\\"include\\\":\\\"#stringSingle\\\"},{\\\"include\\\":\\\"#stringDouble\\\"},{\\\"include\\\":\\\"#array\\\"},{\\\"include\\\":\\\"#object\\\"}]}},\\\"scopeName\\\":\\\"source.json5\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/json5.mjs\n"));

/***/ })

}]);