"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_lean_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/lean.mjs":
/*!***************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/lean.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Lean 4\\\",\\\"fileTypes\\\":[],\\\"name\\\":\\\"lean\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"match\\\":\\\"\\\\\\\\b(Prop|Type|Sort)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.lean4\\\"},{\\\"match\\\":\\\"\\\\\\\\battribute\\\\\\\\b\\\\\\\\s*\\\\\\\\[[^\\\\\\\\]]*]\\\",\\\"name\\\":\\\"storage.modifier.lean4\\\"},{\\\"match\\\":\\\"@\\\\\\\\[[^\\\\\\\\]]*]\\\",\\\"name\\\":\\\"storage.modifier.lean4\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?<!\\\\\\\\.)(global|local|scoped|partial|unsafe|private|protected|noncomputable)(?!\\\\\\\\.)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.modifier.lean4\\\"},{\\\"match\\\":\\\"\\\\\\\\b(sorry|admit|stop)\\\\\\\\b\\\",\\\"name\\\":\\\"invalid.illegal.lean4\\\"},{\\\"match\\\":\\\"#(print|eval|reduce|check|check_failure)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.lean4\\\"},{\\\"match\\\":\\\"\\\\\\\\bderiving\\\\\\\\s+instance\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.command.lean4\\\"},{\\\"begin\\\":\\\"\\\\\\\\b(?<!\\\\\\\\.)(inductive|coinductive|structure|theorem|axiom|abbrev|lemma|def|instance|class|constant)\\\\\\\\b\\\\\\\\s+(\\\\\\\\{[^}]*})?\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.definitioncommand.lean4\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\bwith\\\\\\\\b|\\\\\\\\bextends\\\\\\\\b|\\\\\\\\bwhere\\\\\\\\b|[:|(\\\\\\\\[{⦃<>])\\\",\\\"name\\\":\\\"meta.definitioncommand.lean4\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#definitionName\\\"},{\\\"match\\\":\\\",\\\"}]},{\\\"match\\\":\\\"\\\\\\\\b(?<!\\\\\\\\.)(theorem|show|have|from|suffices|nomatch|def|class|structure|instance|set_option|initialize|builtin_initialize|example|inductive|coinductive|axiom|constant|universe|universes|variable|variables|import|open|export|theory|prelude|renaming|hiding|exposing|do|by|let|extends|mutual|mut|where|rec|syntax|macro_rules|macro|deriving|fun|section|namespace|end|infix|infixl|infixr|postfix|prefix|notation|abbrev|if|then|else|calc|match|with|for|in|unless|try|catch|finally|return|continue|break)(?!\\\\\\\\.)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.lean4\\\"},{\\\"begin\\\":\\\"«\\\",\\\"contentName\\\":\\\"entity.name.lean4\\\",\\\"end\\\":\\\"»\\\"},{\\\"begin\\\":\\\"(s!)\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.lean4\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"name\\\":\\\"string.interpolated.lean4\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.lean4\\\"}},\\\"end\\\":\\\"(})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.lean4\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\[\\\\\\\\\\\\\\\\\\\\\\\"ntr']\\\",\\\"name\\\":\\\"constant.character.escape.lean4\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\x\\\\\\\\h\\\\\\\\h\\\",\\\"name\\\":\\\"constant.character.escape.lean4\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\u\\\\\\\\h\\\\\\\\h\\\\\\\\h\\\\\\\\h\\\",\\\"name\\\":\\\"constant.character.escape.lean4\\\"}]},{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"end\\\":\\\"\\\\\\\"\\\",\\\"name\\\":\\\"string.quoted.double.lean4\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\[\\\\\\\\\\\\\\\\\\\\\\\"ntr']\\\",\\\"name\\\":\\\"constant.character.escape.lean4\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\x\\\\\\\\h\\\\\\\\h\\\",\\\"name\\\":\\\"constant.character.escape.lean4\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\u\\\\\\\\h\\\\\\\\h\\\\\\\\h\\\\\\\\h\\\",\\\"name\\\":\\\"constant.character.escape.lean4\\\"}]},{\\\"match\\\":\\\"\\\\\\\\b(true|false)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.lean4\\\"},{\\\"match\\\":\\\"'[^\\\\\\\\\\\\\\\\']'\\\",\\\"name\\\":\\\"string.quoted.single.lean4\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.character.escape.lean4\\\"}},\\\"match\\\":\\\"'(\\\\\\\\\\\\\\\\(x\\\\\\\\h\\\\\\\\h|u\\\\\\\\h\\\\\\\\h\\\\\\\\h\\\\\\\\h|.))'\\\",\\\"name\\\":\\\"string.quoted.single.lean4\\\"},{\\\"match\\\":\\\"`+[^\\\\\\\\[(]\\\\\\\\S+\\\",\\\"name\\\":\\\"entity.name.lean4\\\"},{\\\"match\\\":\\\"\\\\\\\\b([0-9]+|0([xX]\\\\\\\\h+)|-?(0|[1-9][0-9]*)(\\\\\\\\.[0-9]+)?([eE][+-]?[0-9]+)?)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.lean4\\\"}],\\\"repository\\\":{\\\"blockComment\\\":{\\\"begin\\\":\\\"/-\\\",\\\"end\\\":\\\"-/\\\",\\\"name\\\":\\\"comment.block.lean4\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.lean4.markdown\\\"},{\\\"include\\\":\\\"#blockComment\\\"}]},\\\"comments\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#dashComment\\\"},{\\\"include\\\":\\\"#docComment\\\"},{\\\"include\\\":\\\"#stringBlock\\\"},{\\\"include\\\":\\\"#modDocComment\\\"},{\\\"include\\\":\\\"#blockComment\\\"}]},\\\"dashComment\\\":{\\\"begin\\\":\\\"--\\\",\\\"end\\\":\\\"$\\\",\\\"name\\\":\\\"comment.line.double-dash.lean4\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.lean4.markdown\\\"}]},\\\"definitionName\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b[^:«»(){}\\\\\\\\s=→λ∀?][^:«»(){}\\\\\\\\s]*\\\",\\\"name\\\":\\\"entity.name.function.lean4\\\"},{\\\"begin\\\":\\\"«\\\",\\\"contentName\\\":\\\"entity.name.function.lean4\\\",\\\"end\\\":\\\"»\\\"}]},\\\"docComment\\\":{\\\"begin\\\":\\\"/--\\\",\\\"end\\\":\\\"-/\\\",\\\"name\\\":\\\"comment.block.documentation.lean4\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.lean4.markdown\\\"},{\\\"include\\\":\\\"#blockComment\\\"}]},\\\"modDocComment\\\":{\\\"begin\\\":\\\"/-!\\\",\\\"end\\\":\\\"-/\\\",\\\"name\\\":\\\"comment.block.documentation.lean4\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.lean4.markdown\\\"},{\\\"include\\\":\\\"#blockComment\\\"}]}},\\\"scopeName\\\":\\\"source.lean4\\\",\\\"aliases\\\":[\\\"lean4\\\"]}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ac2hpa2lqcy9sYW5ncy9kaXN0L2xlYW4ubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSx3Q0FBd0MsNkVBQTZFLDBCQUEwQixFQUFFLHlFQUF5RSxFQUFFLDJGQUEyRixFQUFFLG1FQUFtRSxFQUFFLG9KQUFvSixFQUFFLDhFQUE4RSxFQUFFLDZGQUE2RixFQUFFLHdGQUF3RixFQUFFLG9JQUFvSSxHQUFHLEdBQUcsd0JBQXdCLE9BQU8sb0RBQW9ELHlFQUF5RSxpRUFBaUUsMEJBQTBCLEVBQUUsZ0NBQWdDLEVBQUUsZ0JBQWdCLEVBQUUsRUFBRSxtakJBQW1qQixFQUFFLG9FQUFvRSxFQUFFLDBDQUEwQyxPQUFPLGtDQUFrQyx3RUFBd0Usa0JBQWtCLHVCQUF1QixPQUFPLGtDQUFrQyxhQUFhLHFCQUFxQixPQUFPLGtDQUFrQyxnQkFBZ0Isc0JBQXNCLEVBQUUsRUFBRSxzRkFBc0YsRUFBRSwrRUFBK0UsRUFBRSx5RkFBeUYsRUFBRSxFQUFFLDJGQUEyRixzRkFBc0YsRUFBRSwrRUFBK0UsRUFBRSx5RkFBeUYsRUFBRSxFQUFFLDBFQUEwRSxFQUFFLHFFQUFxRSxFQUFFLGNBQWMsT0FBTyw4Q0FBOEMseUdBQXlHLEVBQUUsK0RBQStELEVBQUUscUlBQXFJLGtCQUFrQixrQkFBa0IsZ0ZBQWdGLHNDQUFzQyxFQUFFLDhCQUE4QixFQUFFLGVBQWUsZUFBZSw2QkFBNkIsRUFBRSw0QkFBNEIsRUFBRSw2QkFBNkIsRUFBRSwrQkFBK0IsRUFBRSw4QkFBOEIsRUFBRSxrQkFBa0IsMEZBQTBGLHNDQUFzQyxFQUFFLHFCQUFxQixlQUFlLDBCQUEwQixvQkFBb0Isa0RBQWtELEVBQUUsNkVBQTZFLEVBQUUsaUJBQWlCLCtGQUErRixzQ0FBc0MsRUFBRSw4QkFBOEIsRUFBRSxvQkFBb0IsK0ZBQStGLHNDQUFzQyxFQUFFLDhCQUE4QixHQUFHLHdEQUF3RDs7QUFFNzZJLGlFQUFlO0FBQ2Y7QUFDQSxDQUFDIiwic291cmNlcyI6WyJEOlxcc29mdFxcU2VhUHJvamVjdFxcZ2l0aHViX29wZW5fcHJvamVjdF9zdWNjZXNzXFxzdW5hXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxAc2hpa2lqc1xcbGFuZ3NcXGRpc3RcXGxlYW4ubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGxhbmcgPSBPYmplY3QuZnJlZXplKEpTT04ucGFyc2UoXCJ7XFxcImRpc3BsYXlOYW1lXFxcIjpcXFwiTGVhbiA0XFxcIixcXFwiZmlsZVR5cGVzXFxcIjpbXSxcXFwibmFtZVxcXCI6XFxcImxlYW5cXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNjb21tZW50c1xcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCJcXFxcXFxcXGIoUHJvcHxUeXBlfFNvcnQpXFxcXFxcXFxiXFxcIixcXFwibmFtZVxcXCI6XFxcInN0b3JhZ2UudHlwZS5sZWFuNFxcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCJcXFxcXFxcXGJhdHRyaWJ1dGVcXFxcXFxcXGJcXFxcXFxcXHMqXFxcXFxcXFxbW15cXFxcXFxcXF1dKl1cXFwiLFxcXCJuYW1lXFxcIjpcXFwic3RvcmFnZS5tb2RpZmllci5sZWFuNFxcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCJAXFxcXFxcXFxbW15cXFxcXFxcXF1dKl1cXFwiLFxcXCJuYW1lXFxcIjpcXFwic3RvcmFnZS5tb2RpZmllci5sZWFuNFxcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCJcXFxcXFxcXGIoPzwhXFxcXFxcXFwuKShnbG9iYWx8bG9jYWx8c2NvcGVkfHBhcnRpYWx8dW5zYWZlfHByaXZhdGV8cHJvdGVjdGVkfG5vbmNvbXB1dGFibGUpKD8hXFxcXFxcXFwuKVxcXFxcXFxcYlxcXCIsXFxcIm5hbWVcXFwiOlxcXCJzdG9yYWdlLm1vZGlmaWVyLmxlYW40XFxcIn0se1xcXCJtYXRjaFxcXCI6XFxcIlxcXFxcXFxcYihzb3JyeXxhZG1pdHxzdG9wKVxcXFxcXFxcYlxcXCIsXFxcIm5hbWVcXFwiOlxcXCJpbnZhbGlkLmlsbGVnYWwubGVhbjRcXFwifSx7XFxcIm1hdGNoXFxcIjpcXFwiIyhwcmludHxldmFsfHJlZHVjZXxjaGVja3xjaGVja19mYWlsdXJlKVxcXFxcXFxcYlxcXCIsXFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLm90aGVyLmxlYW40XFxcIn0se1xcXCJtYXRjaFxcXCI6XFxcIlxcXFxcXFxcYmRlcml2aW5nXFxcXFxcXFxzK2luc3RhbmNlXFxcXFxcXFxiXFxcIixcXFwibmFtZVxcXCI6XFxcImtleXdvcmQub3RoZXIuY29tbWFuZC5sZWFuNFxcXCJ9LHtcXFwiYmVnaW5cXFwiOlxcXCJcXFxcXFxcXGIoPzwhXFxcXFxcXFwuKShpbmR1Y3RpdmV8Y29pbmR1Y3RpdmV8c3RydWN0dXJlfHRoZW9yZW18YXhpb218YWJicmV2fGxlbW1hfGRlZnxpbnN0YW5jZXxjbGFzc3xjb25zdGFudClcXFxcXFxcXGJcXFxcXFxcXHMrKFxcXFxcXFxce1tefV0qfSk/XFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLm90aGVyLmRlZmluaXRpb25jb21tYW5kLmxlYW40XFxcIn19LFxcXCJlbmRcXFwiOlxcXCIoPz1cXFxcXFxcXGJ3aXRoXFxcXFxcXFxifFxcXFxcXFxcYmV4dGVuZHNcXFxcXFxcXGJ8XFxcXFxcXFxid2hlcmVcXFxcXFxcXGJ8Wzp8KFxcXFxcXFxcW3vipoM8Pl0pXFxcIixcXFwibmFtZVxcXCI6XFxcIm1ldGEuZGVmaW5pdGlvbmNvbW1hbmQubGVhbjRcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNjb21tZW50c1xcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNkZWZpbml0aW9uTmFtZVxcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCIsXFxcIn1dfSx7XFxcIm1hdGNoXFxcIjpcXFwiXFxcXFxcXFxiKD88IVxcXFxcXFxcLikodGhlb3JlbXxzaG93fGhhdmV8ZnJvbXxzdWZmaWNlc3xub21hdGNofGRlZnxjbGFzc3xzdHJ1Y3R1cmV8aW5zdGFuY2V8c2V0X29wdGlvbnxpbml0aWFsaXplfGJ1aWx0aW5faW5pdGlhbGl6ZXxleGFtcGxlfGluZHVjdGl2ZXxjb2luZHVjdGl2ZXxheGlvbXxjb25zdGFudHx1bml2ZXJzZXx1bml2ZXJzZXN8dmFyaWFibGV8dmFyaWFibGVzfGltcG9ydHxvcGVufGV4cG9ydHx0aGVvcnl8cHJlbHVkZXxyZW5hbWluZ3xoaWRpbmd8ZXhwb3Npbmd8ZG98Ynl8bGV0fGV4dGVuZHN8bXV0dWFsfG11dHx3aGVyZXxyZWN8c3ludGF4fG1hY3JvX3J1bGVzfG1hY3JvfGRlcml2aW5nfGZ1bnxzZWN0aW9ufG5hbWVzcGFjZXxlbmR8aW5maXh8aW5maXhsfGluZml4cnxwb3N0Zml4fHByZWZpeHxub3RhdGlvbnxhYmJyZXZ8aWZ8dGhlbnxlbHNlfGNhbGN8bWF0Y2h8d2l0aHxmb3J8aW58dW5sZXNzfHRyeXxjYXRjaHxmaW5hbGx5fHJldHVybnxjb250aW51ZXxicmVhaykoPyFcXFxcXFxcXC4pXFxcXFxcXFxiXFxcIixcXFwibmFtZVxcXCI6XFxcImtleXdvcmQub3RoZXIubGVhbjRcXFwifSx7XFxcImJlZ2luXFxcIjpcXFwiwqtcXFwiLFxcXCJjb250ZW50TmFtZVxcXCI6XFxcImVudGl0eS5uYW1lLmxlYW40XFxcIixcXFwiZW5kXFxcIjpcXFwiwrtcXFwifSx7XFxcImJlZ2luXFxcIjpcXFwiKHMhKVxcXFxcXFwiXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLm90aGVyLmxlYW40XFxcIn19LFxcXCJlbmRcXFwiOlxcXCJcXFxcXFxcIlxcXCIsXFxcIm5hbWVcXFwiOlxcXCJzdHJpbmcuaW50ZXJwb2xhdGVkLmxlYW40XFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImJlZ2luXFxcIjpcXFwiKFxcXFxcXFxceylcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQub3RoZXIubGVhbjRcXFwifX0sXFxcImVuZFxcXCI6XFxcIih9KVxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQub3RoZXIubGVhbjRcXFwifX0sXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiJHNlbGZcXFwifV19LHtcXFwibWF0Y2hcXFwiOlxcXCJcXFxcXFxcXFxcXFxcXFxcW1xcXFxcXFxcXFxcXFxcXFxcXFxcXFxcIm50ciddXFxcIixcXFwibmFtZVxcXCI6XFxcImNvbnN0YW50LmNoYXJhY3Rlci5lc2NhcGUubGVhbjRcXFwifSx7XFxcIm1hdGNoXFxcIjpcXFwiXFxcXFxcXFxcXFxcXFxcXHhcXFxcXFxcXGhcXFxcXFxcXGhcXFwiLFxcXCJuYW1lXFxcIjpcXFwiY29uc3RhbnQuY2hhcmFjdGVyLmVzY2FwZS5sZWFuNFxcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCJcXFxcXFxcXFxcXFxcXFxcdVxcXFxcXFxcaFxcXFxcXFxcaFxcXFxcXFxcaFxcXFxcXFxcaFxcXCIsXFxcIm5hbWVcXFwiOlxcXCJjb25zdGFudC5jaGFyYWN0ZXIuZXNjYXBlLmxlYW40XFxcIn1dfSx7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXCJcXFwiLFxcXCJlbmRcXFwiOlxcXCJcXFxcXFxcIlxcXCIsXFxcIm5hbWVcXFwiOlxcXCJzdHJpbmcucXVvdGVkLmRvdWJsZS5sZWFuNFxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJtYXRjaFxcXCI6XFxcIlxcXFxcXFxcXFxcXFxcXFxbXFxcXFxcXFxcXFxcXFxcXFxcXFxcXFwibnRyJ11cXFwiLFxcXCJuYW1lXFxcIjpcXFwiY29uc3RhbnQuY2hhcmFjdGVyLmVzY2FwZS5sZWFuNFxcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCJcXFxcXFxcXFxcXFxcXFxceFxcXFxcXFxcaFxcXFxcXFxcaFxcXCIsXFxcIm5hbWVcXFwiOlxcXCJjb25zdGFudC5jaGFyYWN0ZXIuZXNjYXBlLmxlYW40XFxcIn0se1xcXCJtYXRjaFxcXCI6XFxcIlxcXFxcXFxcXFxcXFxcXFx1XFxcXFxcXFxoXFxcXFxcXFxoXFxcXFxcXFxoXFxcXFxcXFxoXFxcIixcXFwibmFtZVxcXCI6XFxcImNvbnN0YW50LmNoYXJhY3Rlci5lc2NhcGUubGVhbjRcXFwifV19LHtcXFwibWF0Y2hcXFwiOlxcXCJcXFxcXFxcXGIodHJ1ZXxmYWxzZSlcXFxcXFxcXGJcXFwiLFxcXCJuYW1lXFxcIjpcXFwiY29uc3RhbnQubGFuZ3VhZ2UubGVhbjRcXFwifSx7XFxcIm1hdGNoXFxcIjpcXFwiJ1teXFxcXFxcXFxcXFxcXFxcXCddJ1xcXCIsXFxcIm5hbWVcXFwiOlxcXCJzdHJpbmcucXVvdGVkLnNpbmdsZS5sZWFuNFxcXCJ9LHtcXFwiY2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwiY29uc3RhbnQuY2hhcmFjdGVyLmVzY2FwZS5sZWFuNFxcXCJ9fSxcXFwibWF0Y2hcXFwiOlxcXCInKFxcXFxcXFxcXFxcXFxcXFwoeFxcXFxcXFxcaFxcXFxcXFxcaHx1XFxcXFxcXFxoXFxcXFxcXFxoXFxcXFxcXFxoXFxcXFxcXFxofC4pKSdcXFwiLFxcXCJuYW1lXFxcIjpcXFwic3RyaW5nLnF1b3RlZC5zaW5nbGUubGVhbjRcXFwifSx7XFxcIm1hdGNoXFxcIjpcXFwiYCtbXlxcXFxcXFxcWyhdXFxcXFxcXFxTK1xcXCIsXFxcIm5hbWVcXFwiOlxcXCJlbnRpdHkubmFtZS5sZWFuNFxcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCJcXFxcXFxcXGIoWzAtOV0rfDAoW3hYXVxcXFxcXFxcaCspfC0/KDB8WzEtOV1bMC05XSopKFxcXFxcXFxcLlswLTldKyk/KFtlRV1bKy1dP1swLTldKyk/KVxcXFxcXFxcYlxcXCIsXFxcIm5hbWVcXFwiOlxcXCJjb25zdGFudC5udW1lcmljLmxlYW40XFxcIn1dLFxcXCJyZXBvc2l0b3J5XFxcIjp7XFxcImJsb2NrQ29tbWVudFxcXCI6e1xcXCJiZWdpblxcXCI6XFxcIi8tXFxcIixcXFwiZW5kXFxcIjpcXFwiLS9cXFwiLFxcXCJuYW1lXFxcIjpcXFwiY29tbWVudC5ibG9jay5sZWFuNFxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwic291cmNlLmxlYW40Lm1hcmtkb3duXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2Jsb2NrQ29tbWVudFxcXCJ9XX0sXFxcImNvbW1lbnRzXFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2Rhc2hDb21tZW50XFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2RvY0NvbW1lbnRcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjc3RyaW5nQmxvY2tcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjbW9kRG9jQ29tbWVudFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNibG9ja0NvbW1lbnRcXFwifV19LFxcXCJkYXNoQ29tbWVudFxcXCI6e1xcXCJiZWdpblxcXCI6XFxcIi0tXFxcIixcXFwiZW5kXFxcIjpcXFwiJFxcXCIsXFxcIm5hbWVcXFwiOlxcXCJjb21tZW50LmxpbmUuZG91YmxlLWRhc2gubGVhbjRcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcInNvdXJjZS5sZWFuNC5tYXJrZG93blxcXCJ9XX0sXFxcImRlZmluaXRpb25OYW1lXFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJtYXRjaFxcXCI6XFxcIlxcXFxcXFxcYlteOsKrwrsoKXt9XFxcXFxcXFxzPeKGks674oiAP11bXjrCq8K7KCl7fVxcXFxcXFxcc10qXFxcIixcXFwibmFtZVxcXCI6XFxcImVudGl0eS5uYW1lLmZ1bmN0aW9uLmxlYW40XFxcIn0se1xcXCJiZWdpblxcXCI6XFxcIsKrXFxcIixcXFwiY29udGVudE5hbWVcXFwiOlxcXCJlbnRpdHkubmFtZS5mdW5jdGlvbi5sZWFuNFxcXCIsXFxcImVuZFxcXCI6XFxcIsK7XFxcIn1dfSxcXFwiZG9jQ29tbWVudFxcXCI6e1xcXCJiZWdpblxcXCI6XFxcIi8tLVxcXCIsXFxcImVuZFxcXCI6XFxcIi0vXFxcIixcXFwibmFtZVxcXCI6XFxcImNvbW1lbnQuYmxvY2suZG9jdW1lbnRhdGlvbi5sZWFuNFxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwic291cmNlLmxlYW40Lm1hcmtkb3duXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2Jsb2NrQ29tbWVudFxcXCJ9XX0sXFxcIm1vZERvY0NvbW1lbnRcXFwiOntcXFwiYmVnaW5cXFwiOlxcXCIvLSFcXFwiLFxcXCJlbmRcXFwiOlxcXCItL1xcXCIsXFxcIm5hbWVcXFwiOlxcXCJjb21tZW50LmJsb2NrLmRvY3VtZW50YXRpb24ubGVhbjRcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcInNvdXJjZS5sZWFuNC5tYXJrZG93blxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNibG9ja0NvbW1lbnRcXFwifV19fSxcXFwic2NvcGVOYW1lXFxcIjpcXFwic291cmNlLmxlYW40XFxcIixcXFwiYWxpYXNlc1xcXCI6W1xcXCJsZWFuNFxcXCJdfVwiKSlcblxuZXhwb3J0IGRlZmF1bHQgW1xubGFuZ1xuXVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/lean.mjs\n"));

/***/ })

}]);