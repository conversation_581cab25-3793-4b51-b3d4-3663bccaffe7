"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_fluent_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/fluent.mjs":
/*!*****************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/fluent.mjs ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Fluent\\\",\\\"name\\\":\\\"fluent\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#message\\\"},{\\\"include\\\":\\\"#wrong-line\\\"}],\\\"repository\\\":{\\\"attributes\\\":{\\\"begin\\\":\\\"\\\\\\\\s*(\\\\\\\\.[a-zA-Z][a-zA-Z0-9_-]*\\\\\\\\s*=\\\\\\\\s*)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.class.attribute-begin.fluent\\\"}},\\\"end\\\":\\\"^(?=\\\\\\\\s*[^.])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#placeable\\\"}]},\\\"comment\\\":{\\\"match\\\":\\\"^##?#?\\\\\\\\s.*$\\\",\\\"name\\\":\\\"comment.fluent\\\"},\\\"function-comma\\\":{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"support.function.function-comma.fluent\\\"},\\\"function-named-argument\\\":{\\\"begin\\\":\\\"([a-zA-Z0-9]+:)\\\\\\\\s*([\\\\\\\"a-zA-Z0-9]+)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.named-argument.name.fluent\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.other.named-argument.value.fluent\\\"}},\\\"end\\\":\\\"(?=[),\\\\\\\\s])\\\",\\\"name\\\":\\\"variable.other.named-argument.fluent\\\"},\\\"function-positional-argument\\\":{\\\"match\\\":\\\"\\\\\\\\$[a-zA-Z0-9_-]+\\\",\\\"name\\\":\\\"variable.other.function.positional-argument.fluent\\\"},\\\"invalid-placeable-string-missing-end-quote\\\":{\\\"match\\\":\\\"\\\\\\\"[^\\\\\\\"]+$\\\",\\\"name\\\":\\\"invalid.illegal.wrong-placeable-missing-end-quote.fluent\\\"},\\\"invalid-placeable-wrong-placeable-missing-end\\\":{\\\"match\\\":\\\"([^}A-Z]*$|[^-][^>]$)\\\\\\\\b\\\",\\\"name\\\":\\\"invalid.illegal.wrong-placeable-missing-end.fluent\\\"},\\\"message\\\":{\\\"begin\\\":\\\"^(-?[a-zA-Z][a-zA-Z0-9_-]*\\\\\\\\s*=\\\\\\\\s*)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.class.message-identifier.fluent\\\"}},\\\"contentName\\\":\\\"string.fluent\\\",\\\"end\\\":\\\"^(?=\\\\\\\\S)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#attributes\\\"},{\\\"include\\\":\\\"#placeable\\\"}]},\\\"placeable\\\":{\\\"begin\\\":\\\"(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.placeable.begin.fluent\\\"}},\\\"contentName\\\":\\\"variable.other.placeable.content.fluent\\\",\\\"end\\\":\\\"(})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.placeable.end.fluent\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#placeable-string\\\"},{\\\"include\\\":\\\"#placeable-function\\\"},{\\\"include\\\":\\\"#placeable-reference-or-number\\\"},{\\\"include\\\":\\\"#selector\\\"},{\\\"include\\\":\\\"#invalid-placeable-wrong-placeable-missing-end\\\"},{\\\"include\\\":\\\"#invalid-placeable-string-missing-end-quote\\\"},{\\\"include\\\":\\\"#invalid-placeable-wrong-function-name\\\"}]},\\\"placeable-function\\\":{\\\"begin\\\":\\\"([A-Z][A-Z0-9_-]*\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.placeable-function.call.begin.fluent\\\"}},\\\"contentName\\\":\\\"string.placeable-function.fluent\\\",\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.placeable-function.call.end.fluent\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#function-comma\\\"},{\\\"include\\\":\\\"#function-positional-argument\\\"},{\\\"include\\\":\\\"#function-named-argument\\\"}]},\\\"placeable-reference-or-number\\\":{\\\"match\\\":\\\"(([-$])[a-zA-Z0-9_-]+|[a-zA-Z][a-zA-Z0-9_-]*|[0-9]+)\\\",\\\"name\\\":\\\"variable.other.placeable.reference-or-number.fluent\\\"},\\\"placeable-string\\\":{\\\"begin\\\":\\\"(\\\\\\\")(?=[^\\\\\\\\n]*\\\\\\\")\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.placeable-string-begin.fluent\\\"}},\\\"contentName\\\":\\\"string.placeable-string-content.fluent\\\",\\\"end\\\":\\\"(\\\\\\\")\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.placeable-string-end.fluent\\\"}}},\\\"selector\\\":{\\\"begin\\\":\\\"(->)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.selector.begin.fluent\\\"}},\\\"contentName\\\":\\\"string.selector.content.fluent\\\",\\\"end\\\":\\\"^(?=\\\\\\\\s*})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#selector-item\\\"}]},\\\"selector-item\\\":{\\\"begin\\\":\\\"(\\\\\\\\s*\\\\\\\\*?\\\\\\\\[)([a-zA-Z0-9_-]+)(]\\\\\\\\s*)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.selector-item.begin.fluent\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.other.selector-item.begin.fluent\\\"},\\\"3\\\":{\\\"name\\\":\\\"support.function.selector-item.begin.fluent\\\"}},\\\"contentName\\\":\\\"string.selector-item.content.fluent\\\",\\\"end\\\":\\\"^(?=(\\\\\\\\s*})|(\\\\\\\\s*\\\\\\\\[)|(\\\\\\\\s*\\\\\\\\*))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#placeable\\\"}]},\\\"wrong-line\\\":{\\\"match\\\":\\\".*\\\",\\\"name\\\":\\\"invalid.illegal.wrong-line.fluent\\\"}},\\\"scopeName\\\":\\\"source.ftl\\\",\\\"aliases\\\":[\\\"ftl\\\"]}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ac2hpa2lqcy9sYW5ncy9kaXN0L2ZsdWVudC5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLHdDQUF3Qyw4REFBOEQseUJBQXlCLEVBQUUseUJBQXlCLEVBQUUsNEJBQTRCLGtCQUFrQixnQkFBZ0Isa0ZBQWtGLE9BQU8sbURBQW1ELDRDQUE0QywyQkFBMkIsRUFBRSxjQUFjLHlEQUF5RCxxQkFBcUIsb0VBQW9FLDhCQUE4Qix5RUFBeUUsT0FBTyx5REFBeUQsUUFBUSx5REFBeUQsNkVBQTZFLG1DQUFtQyxrR0FBa0csaURBQWlELGtHQUFrRyxvREFBb0QsZ0JBQWdCLHlGQUF5RixjQUFjLDBFQUEwRSxPQUFPLHNEQUFzRCx5RUFBeUUsNEJBQTRCLEVBQUUsMkJBQTJCLEVBQUUsZ0JBQWdCLGtCQUFrQix1QkFBdUIsT0FBTyw2Q0FBNkMseUVBQXlFLHFCQUFxQixPQUFPLDJDQUEyQyxnQkFBZ0Isa0NBQWtDLEVBQUUsb0NBQW9DLEVBQUUsK0NBQStDLEVBQUUsMEJBQTBCLEVBQUUsK0RBQStELEVBQUUsNERBQTRELEVBQUUsdURBQXVELEVBQUUseUJBQXlCLHlEQUF5RCxPQUFPLG9FQUFvRSwyRkFBMkYsT0FBTyxrRUFBa0UsZ0JBQWdCLGdDQUFnQyxFQUFFLDhDQUE4QyxFQUFFLHlDQUF5QyxFQUFFLG9DQUFvQyxvSUFBb0ksdUJBQXVCLHlEQUF5RCxPQUFPLDJEQUEyRCxnR0FBZ0csT0FBTywwREFBMEQsZUFBZSxzQ0FBc0MsT0FBTyxxREFBcUQseUVBQXlFLG1CQUFtQiwrQkFBK0IsRUFBRSxvQkFBb0IsOEVBQThFLE9BQU8seURBQXlELFFBQVEsdURBQXVELFFBQVEsMERBQTBELCtFQUErRSxnREFBZ0QsMkJBQTJCLEVBQUUsaUJBQWlCLGlFQUFpRSxvREFBb0Q7O0FBRTE0SCxpRUFBZTtBQUNmO0FBQ0EsQ0FBQyIsInNvdXJjZXMiOlsiRDpcXHNvZnRcXFNlYVByb2plY3RcXGdpdGh1Yl9vcGVuX3Byb2plY3Rfc3VjY2Vzc1xcc3VuYVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcQHNoaWtpanNcXGxhbmdzXFxkaXN0XFxmbHVlbnQubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGxhbmcgPSBPYmplY3QuZnJlZXplKEpTT04ucGFyc2UoXCJ7XFxcImRpc3BsYXlOYW1lXFxcIjpcXFwiRmx1ZW50XFxcIixcXFwibmFtZVxcXCI6XFxcImZsdWVudFxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2NvbW1lbnRcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjbWVzc2FnZVxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiN3cm9uZy1saW5lXFxcIn1dLFxcXCJyZXBvc2l0b3J5XFxcIjp7XFxcImF0dHJpYnV0ZXNcXFwiOntcXFwiYmVnaW5cXFwiOlxcXCJcXFxcXFxcXHMqKFxcXFxcXFxcLlthLXpBLVpdW2EtekEtWjAtOV8tXSpcXFxcXFxcXHMqPVxcXFxcXFxccyopXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJzdXBwb3J0LmNsYXNzLmF0dHJpYnV0ZS1iZWdpbi5mbHVlbnRcXFwifX0sXFxcImVuZFxcXCI6XFxcIl4oPz1cXFxcXFxcXHMqW14uXSlcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNwbGFjZWFibGVcXFwifV19LFxcXCJjb21tZW50XFxcIjp7XFxcIm1hdGNoXFxcIjpcXFwiXiMjPyM/XFxcXFxcXFxzLiokXFxcIixcXFwibmFtZVxcXCI6XFxcImNvbW1lbnQuZmx1ZW50XFxcIn0sXFxcImZ1bmN0aW9uLWNvbW1hXFxcIjp7XFxcIm1hdGNoXFxcIjpcXFwiLFxcXCIsXFxcIm5hbWVcXFwiOlxcXCJzdXBwb3J0LmZ1bmN0aW9uLmZ1bmN0aW9uLWNvbW1hLmZsdWVudFxcXCJ9LFxcXCJmdW5jdGlvbi1uYW1lZC1hcmd1bWVudFxcXCI6e1xcXCJiZWdpblxcXCI6XFxcIihbYS16QS1aMC05XSs6KVxcXFxcXFxccyooW1xcXFxcXFwiYS16QS1aMC05XSspXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJzdXBwb3J0LmZ1bmN0aW9uLm5hbWVkLWFyZ3VtZW50Lm5hbWUuZmx1ZW50XFxcIn0sXFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcInZhcmlhYmxlLm90aGVyLm5hbWVkLWFyZ3VtZW50LnZhbHVlLmZsdWVudFxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiKD89WyksXFxcXFxcXFxzXSlcXFwiLFxcXCJuYW1lXFxcIjpcXFwidmFyaWFibGUub3RoZXIubmFtZWQtYXJndW1lbnQuZmx1ZW50XFxcIn0sXFxcImZ1bmN0aW9uLXBvc2l0aW9uYWwtYXJndW1lbnRcXFwiOntcXFwibWF0Y2hcXFwiOlxcXCJcXFxcXFxcXCRbYS16QS1aMC05Xy1dK1xcXCIsXFxcIm5hbWVcXFwiOlxcXCJ2YXJpYWJsZS5vdGhlci5mdW5jdGlvbi5wb3NpdGlvbmFsLWFyZ3VtZW50LmZsdWVudFxcXCJ9LFxcXCJpbnZhbGlkLXBsYWNlYWJsZS1zdHJpbmctbWlzc2luZy1lbmQtcXVvdGVcXFwiOntcXFwibWF0Y2hcXFwiOlxcXCJcXFxcXFxcIlteXFxcXFxcXCJdKyRcXFwiLFxcXCJuYW1lXFxcIjpcXFwiaW52YWxpZC5pbGxlZ2FsLndyb25nLXBsYWNlYWJsZS1taXNzaW5nLWVuZC1xdW90ZS5mbHVlbnRcXFwifSxcXFwiaW52YWxpZC1wbGFjZWFibGUtd3JvbmctcGxhY2VhYmxlLW1pc3NpbmctZW5kXFxcIjp7XFxcIm1hdGNoXFxcIjpcXFwiKFtefUEtWl0qJHxbXi1dW14+XSQpXFxcXFxcXFxiXFxcIixcXFwibmFtZVxcXCI6XFxcImludmFsaWQuaWxsZWdhbC53cm9uZy1wbGFjZWFibGUtbWlzc2luZy1lbmQuZmx1ZW50XFxcIn0sXFxcIm1lc3NhZ2VcXFwiOntcXFwiYmVnaW5cXFwiOlxcXCJeKC0/W2EtekEtWl1bYS16QS1aMC05Xy1dKlxcXFxcXFxccyo9XFxcXFxcXFxzKilcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcInN1cHBvcnQuY2xhc3MubWVzc2FnZS1pZGVudGlmaWVyLmZsdWVudFxcXCJ9fSxcXFwiY29udGVudE5hbWVcXFwiOlxcXCJzdHJpbmcuZmx1ZW50XFxcIixcXFwiZW5kXFxcIjpcXFwiXig/PVxcXFxcXFxcUylcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNhdHRyaWJ1dGVzXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3BsYWNlYWJsZVxcXCJ9XX0sXFxcInBsYWNlYWJsZVxcXCI6e1xcXCJiZWdpblxcXCI6XFxcIihcXFxcXFxcXHspXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLnBsYWNlYWJsZS5iZWdpbi5mbHVlbnRcXFwifX0sXFxcImNvbnRlbnROYW1lXFxcIjpcXFwidmFyaWFibGUub3RoZXIucGxhY2VhYmxlLmNvbnRlbnQuZmx1ZW50XFxcIixcXFwiZW5kXFxcIjpcXFwiKH0pXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5wbGFjZWFibGUuZW5kLmZsdWVudFxcXCJ9fSxcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjcGxhY2VhYmxlLXN0cmluZ1xcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNwbGFjZWFibGUtZnVuY3Rpb25cXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjcGxhY2VhYmxlLXJlZmVyZW5jZS1vci1udW1iZXJcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjc2VsZWN0b3JcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjaW52YWxpZC1wbGFjZWFibGUtd3JvbmctcGxhY2VhYmxlLW1pc3NpbmctZW5kXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2ludmFsaWQtcGxhY2VhYmxlLXN0cmluZy1taXNzaW5nLWVuZC1xdW90ZVxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNpbnZhbGlkLXBsYWNlYWJsZS13cm9uZy1mdW5jdGlvbi1uYW1lXFxcIn1dfSxcXFwicGxhY2VhYmxlLWZ1bmN0aW9uXFxcIjp7XFxcImJlZ2luXFxcIjpcXFwiKFtBLVpdW0EtWjAtOV8tXSpcXFxcXFxcXCgpXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJzdXBwb3J0LmZ1bmN0aW9uLnBsYWNlYWJsZS1mdW5jdGlvbi5jYWxsLmJlZ2luLmZsdWVudFxcXCJ9fSxcXFwiY29udGVudE5hbWVcXFwiOlxcXCJzdHJpbmcucGxhY2VhYmxlLWZ1bmN0aW9uLmZsdWVudFxcXCIsXFxcImVuZFxcXCI6XFxcIihcXFxcXFxcXCkpXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwic3VwcG9ydC5mdW5jdGlvbi5wbGFjZWFibGUtZnVuY3Rpb24uY2FsbC5lbmQuZmx1ZW50XFxcIn19LFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNmdW5jdGlvbi1jb21tYVxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNmdW5jdGlvbi1wb3NpdGlvbmFsLWFyZ3VtZW50XFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2Z1bmN0aW9uLW5hbWVkLWFyZ3VtZW50XFxcIn1dfSxcXFwicGxhY2VhYmxlLXJlZmVyZW5jZS1vci1udW1iZXJcXFwiOntcXFwibWF0Y2hcXFwiOlxcXCIoKFstJF0pW2EtekEtWjAtOV8tXSt8W2EtekEtWl1bYS16QS1aMC05Xy1dKnxbMC05XSspXFxcIixcXFwibmFtZVxcXCI6XFxcInZhcmlhYmxlLm90aGVyLnBsYWNlYWJsZS5yZWZlcmVuY2Utb3ItbnVtYmVyLmZsdWVudFxcXCJ9LFxcXCJwbGFjZWFibGUtc3RyaW5nXFxcIjp7XFxcImJlZ2luXFxcIjpcXFwiKFxcXFxcXFwiKSg/PVteXFxcXFxcXFxuXSpcXFxcXFxcIilcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcInZhcmlhYmxlLm90aGVyLnBsYWNlYWJsZS1zdHJpbmctYmVnaW4uZmx1ZW50XFxcIn19LFxcXCJjb250ZW50TmFtZVxcXCI6XFxcInN0cmluZy5wbGFjZWFibGUtc3RyaW5nLWNvbnRlbnQuZmx1ZW50XFxcIixcXFwiZW5kXFxcIjpcXFwiKFxcXFxcXFwiKVxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcInZhcmlhYmxlLm90aGVyLnBsYWNlYWJsZS1zdHJpbmctZW5kLmZsdWVudFxcXCJ9fX0sXFxcInNlbGVjdG9yXFxcIjp7XFxcImJlZ2luXFxcIjpcXFwiKC0+KVxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwic3VwcG9ydC5mdW5jdGlvbi5zZWxlY3Rvci5iZWdpbi5mbHVlbnRcXFwifX0sXFxcImNvbnRlbnROYW1lXFxcIjpcXFwic3RyaW5nLnNlbGVjdG9yLmNvbnRlbnQuZmx1ZW50XFxcIixcXFwiZW5kXFxcIjpcXFwiXig/PVxcXFxcXFxccyp9KVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3NlbGVjdG9yLWl0ZW1cXFwifV19LFxcXCJzZWxlY3Rvci1pdGVtXFxcIjp7XFxcImJlZ2luXFxcIjpcXFwiKFxcXFxcXFxccypcXFxcXFxcXCo/XFxcXFxcXFxbKShbYS16QS1aMC05Xy1dKykoXVxcXFxcXFxccyopXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJzdXBwb3J0LmZ1bmN0aW9uLnNlbGVjdG9yLWl0ZW0uYmVnaW4uZmx1ZW50XFxcIn0sXFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcInZhcmlhYmxlLm90aGVyLnNlbGVjdG9yLWl0ZW0uYmVnaW4uZmx1ZW50XFxcIn0sXFxcIjNcXFwiOntcXFwibmFtZVxcXCI6XFxcInN1cHBvcnQuZnVuY3Rpb24uc2VsZWN0b3ItaXRlbS5iZWdpbi5mbHVlbnRcXFwifX0sXFxcImNvbnRlbnROYW1lXFxcIjpcXFwic3RyaW5nLnNlbGVjdG9yLWl0ZW0uY29udGVudC5mbHVlbnRcXFwiLFxcXCJlbmRcXFwiOlxcXCJeKD89KFxcXFxcXFxccyp9KXwoXFxcXFxcXFxzKlxcXFxcXFxcWyl8KFxcXFxcXFxccypcXFxcXFxcXCopKVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3BsYWNlYWJsZVxcXCJ9XX0sXFxcIndyb25nLWxpbmVcXFwiOntcXFwibWF0Y2hcXFwiOlxcXCIuKlxcXCIsXFxcIm5hbWVcXFwiOlxcXCJpbnZhbGlkLmlsbGVnYWwud3JvbmctbGluZS5mbHVlbnRcXFwifX0sXFxcInNjb3BlTmFtZVxcXCI6XFxcInNvdXJjZS5mdGxcXFwiLFxcXCJhbGlhc2VzXFxcIjpbXFxcImZ0bFxcXCJdfVwiKSlcblxuZXhwb3J0IGRlZmF1bHQgW1xubGFuZ1xuXVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/fluent.mjs\n"));

/***/ })

}]);