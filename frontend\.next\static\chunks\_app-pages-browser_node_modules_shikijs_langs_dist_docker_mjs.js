"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_docker_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/docker.mjs":
/*!*****************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/docker.mjs ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Dockerfile\\\",\\\"name\\\":\\\"docker\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.special-method.dockerfile\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.special-method.dockerfile\\\"}},\\\"match\\\":\\\"^\\\\\\\\s*\\\\\\\\b(?i:(FROM))\\\\\\\\b.*?\\\\\\\\b(?i:(AS))\\\\\\\\b\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.dockerfile\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.special-method.dockerfile\\\"}},\\\"match\\\":\\\"^\\\\\\\\s*(?i:(ONBUILD)\\\\\\\\s+)?(?i:(ADD|ARG|CMD|COPY|ENTRYPOINT|ENV|EXPOSE|FROM|HEALTHCHECK|LABEL|MAINTAINER|RUN|SHELL|STOPSIGNAL|USER|VOLUME|WORKDIR))\\\\\\\\s\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.dockerfile\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.special-method.dockerfile\\\"}},\\\"match\\\":\\\"^\\\\\\\\s*(?i:(ONBUILD)\\\\\\\\s+)?(?i:(CMD|ENTRYPOINT))\\\\\\\\s\\\"},{\\\"include\\\":\\\"#string-character-escape\\\"},{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.dockerfile\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.dockerfile\\\"}},\\\"name\\\":\\\"string.quoted.double.dockerfile\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string-character-escape\\\"}]},{\\\"begin\\\":\\\"'\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.dockerfile\\\"}},\\\"end\\\":\\\"'\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.dockerfile\\\"}},\\\"name\\\":\\\"string.quoted.single.dockerfile\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string-character-escape\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.comment.leading.dockerfile\\\"},\\\"2\\\":{\\\"name\\\":\\\"comment.line.number-sign.dockerfile\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.comment.dockerfile\\\"}},\\\"match\\\":\\\"^(\\\\\\\\s*)((#).*$\\\\\\\\n?)\\\"}],\\\"repository\\\":{\\\"string-character-escape\\\":{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"constant.character.escaped.dockerfile\\\"}},\\\"scopeName\\\":\\\"source.dockerfile\\\",\\\"aliases\\\":[\\\"dockerfile\\\"]}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ac2hpa2lqcy9sYW5ncy9kaXN0L2RvY2tlci5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLHdDQUF3QyxrRUFBa0UsY0FBYyxPQUFPLHFEQUFxRCxRQUFRLHNEQUFzRCxrRUFBa0UsRUFBRSxjQUFjLE9BQU8sd0NBQXdDLFFBQVEsc0RBQXNELHlLQUF5SyxFQUFFLGNBQWMsT0FBTyx5Q0FBeUMsUUFBUSxzREFBc0Qsc0VBQXNFLEVBQUUseUNBQXlDLEVBQUUsc0NBQXNDLE9BQU8sNkRBQTZELG1DQUFtQyxPQUFPLDJEQUEyRCw2REFBNkQseUNBQXlDLEVBQUUsRUFBRSxtQ0FBbUMsT0FBTyw2REFBNkQsZ0NBQWdDLE9BQU8sMkRBQTJELDZEQUE2RCx5Q0FBeUMsRUFBRSxFQUFFLGNBQWMsT0FBTywrREFBK0QsUUFBUSxpREFBaUQsUUFBUSx3REFBd0QsdUNBQXVDLGtCQUFrQiw2QkFBNkIsNEVBQTRFLGtFQUFrRTs7QUFFeDJELGlFQUFlO0FBQ2Y7QUFDQSxDQUFDIiwic291cmNlcyI6WyJEOlxcc29mdFxcU2VhUHJvamVjdFxcZ2l0aHViX29wZW5fcHJvamVjdF9zdWNjZXNzXFxzdW5hXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxAc2hpa2lqc1xcbGFuZ3NcXGRpc3RcXGRvY2tlci5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgbGFuZyA9IE9iamVjdC5mcmVlemUoSlNPTi5wYXJzZShcIntcXFwiZGlzcGxheU5hbWVcXFwiOlxcXCJEb2NrZXJmaWxlXFxcIixcXFwibmFtZVxcXCI6XFxcImRvY2tlclxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJjYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLm90aGVyLnNwZWNpYWwtbWV0aG9kLmRvY2tlcmZpbGVcXFwifSxcXFwiMlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5vdGhlci5zcGVjaWFsLW1ldGhvZC5kb2NrZXJmaWxlXFxcIn19LFxcXCJtYXRjaFxcXCI6XFxcIl5cXFxcXFxcXHMqXFxcXFxcXFxiKD9pOihGUk9NKSlcXFxcXFxcXGIuKj9cXFxcXFxcXGIoP2k6KEFTKSlcXFxcXFxcXGJcXFwifSx7XFxcImNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQuY29udHJvbC5kb2NrZXJmaWxlXFxcIn0sXFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQub3RoZXIuc3BlY2lhbC1tZXRob2QuZG9ja2VyZmlsZVxcXCJ9fSxcXFwibWF0Y2hcXFwiOlxcXCJeXFxcXFxcXFxzKig/aTooT05CVUlMRClcXFxcXFxcXHMrKT8oP2k6KEFERHxBUkd8Q01EfENPUFl8RU5UUllQT0lOVHxFTlZ8RVhQT1NFfEZST018SEVBTFRIQ0hFQ0t8TEFCRUx8TUFJTlRBSU5FUnxSVU58U0hFTEx8U1RPUFNJR05BTHxVU0VSfFZPTFVNRXxXT1JLRElSKSlcXFxcXFxcXHNcXFwifSx7XFxcImNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQub3BlcmF0b3IuZG9ja2VyZmlsZVxcXCJ9LFxcXCIyXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLm90aGVyLnNwZWNpYWwtbWV0aG9kLmRvY2tlcmZpbGVcXFwifX0sXFxcIm1hdGNoXFxcIjpcXFwiXlxcXFxcXFxccyooP2k6KE9OQlVJTEQpXFxcXFxcXFxzKyk/KD9pOihDTUR8RU5UUllQT0lOVCkpXFxcXFxcXFxzXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3N0cmluZy1jaGFyYWN0ZXItZXNjYXBlXFxcIn0se1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFwiXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLnN0cmluZy5iZWdpbi5kb2NrZXJmaWxlXFxcIn19LFxcXCJlbmRcXFwiOlxcXCJcXFxcXFxcIlxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24uc3RyaW5nLmVuZC5kb2NrZXJmaWxlXFxcIn19LFxcXCJuYW1lXFxcIjpcXFwic3RyaW5nLnF1b3RlZC5kb3VibGUuZG9ja2VyZmlsZVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3N0cmluZy1jaGFyYWN0ZXItZXNjYXBlXFxcIn1dfSx7XFxcImJlZ2luXFxcIjpcXFwiJ1xcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uZGVmaW5pdGlvbi5zdHJpbmcuYmVnaW4uZG9ja2VyZmlsZVxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiJ1xcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24uc3RyaW5nLmVuZC5kb2NrZXJmaWxlXFxcIn19LFxcXCJuYW1lXFxcIjpcXFwic3RyaW5nLnF1b3RlZC5zaW5nbGUuZG9ja2VyZmlsZVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3N0cmluZy1jaGFyYWN0ZXItZXNjYXBlXFxcIn1dfSx7XFxcImNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLndoaXRlc3BhY2UuY29tbWVudC5sZWFkaW5nLmRvY2tlcmZpbGVcXFwifSxcXFwiMlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwiY29tbWVudC5saW5lLm51bWJlci1zaWduLmRvY2tlcmZpbGVcXFwifSxcXFwiM1xcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uZGVmaW5pdGlvbi5jb21tZW50LmRvY2tlcmZpbGVcXFwifX0sXFxcIm1hdGNoXFxcIjpcXFwiXihcXFxcXFxcXHMqKSgoIykuKiRcXFxcXFxcXG4/KVxcXCJ9XSxcXFwicmVwb3NpdG9yeVxcXCI6e1xcXCJzdHJpbmctY2hhcmFjdGVyLWVzY2FwZVxcXCI6e1xcXCJtYXRjaFxcXCI6XFxcIlxcXFxcXFxcXFxcXFxcXFwuXFxcIixcXFwibmFtZVxcXCI6XFxcImNvbnN0YW50LmNoYXJhY3Rlci5lc2NhcGVkLmRvY2tlcmZpbGVcXFwifX0sXFxcInNjb3BlTmFtZVxcXCI6XFxcInNvdXJjZS5kb2NrZXJmaWxlXFxcIixcXFwiYWxpYXNlc1xcXCI6W1xcXCJkb2NrZXJmaWxlXFxcIl19XCIpKVxuXG5leHBvcnQgZGVmYXVsdCBbXG5sYW5nXG5dXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/docker.mjs\n"));

/***/ })

}]);