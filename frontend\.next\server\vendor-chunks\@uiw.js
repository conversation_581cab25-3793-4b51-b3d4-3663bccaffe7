"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@uiw";
exports.ids = ["vendor-chunks/@uiw"];
exports.modules = {

/***/ "(ssr)/./node_modules/@uiw/codemirror-extensions-basic-setup/esm/index.js":
/*!**************************************************************************!*\
  !*** ./node_modules/@uiw/codemirror-extensions-basic-setup/esm/index.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   basicSetup: () => (/* binding */ basicSetup),\n/* harmony export */   minimalSetup: () => (/* binding */ minimalSetup)\n/* harmony export */ });\n/* harmony import */ var _codemirror_view__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @codemirror/view */ \"(ssr)/./node_modules/@codemirror/view/dist/index.js\");\n/* harmony import */ var _codemirror_state__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @codemirror/state */ \"(ssr)/./node_modules/@codemirror/state/dist/index.js\");\n/* harmony import */ var _codemirror_commands__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @codemirror/commands */ \"(ssr)/./node_modules/@codemirror/commands/dist/index.js\");\n/* harmony import */ var _codemirror_search__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @codemirror/search */ \"(ssr)/./node_modules/@codemirror/search/dist/index.js\");\n/* harmony import */ var _codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @codemirror/autocomplete */ \"(ssr)/./node_modules/@codemirror/autocomplete/dist/index.js\");\n/* harmony import */ var _codemirror_language__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @codemirror/language */ \"(ssr)/./node_modules/@codemirror/language/dist/index.js\");\n/* harmony import */ var _codemirror_lint__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @codemirror/lint */ \"(ssr)/./node_modules/@codemirror/lint/dist/index.js\");\n\n\n\n\n\n\n\n/**\nThis is an extension value that just pulls together a number of\nextensions that you might want in a basic editor. It is meant as a\nconvenient helper to quickly set up CodeMirror without installing\nand importing a lot of separate packages.\n\nSpecifically, it includes...\n\n - [the default command bindings](https://codemirror.net/6/docs/ref/#commands.defaultKeymap)\n - [line numbers](https://codemirror.net/6/docs/ref/#view.lineNumbers)\n - [special character highlighting](https://codemirror.net/6/docs/ref/#view.highlightSpecialChars)\n - [the undo history](https://codemirror.net/6/docs/ref/#commands.history)\n - [a fold gutter](https://codemirror.net/6/docs/ref/#language.foldGutter)\n - [custom selection drawing](https://codemirror.net/6/docs/ref/#view.drawSelection)\n - [drop cursor](https://codemirror.net/6/docs/ref/#view.dropCursor)\n - [multiple selections](https://codemirror.net/6/docs/ref/#state.EditorState^allowMultipleSelections)\n - [reindentation on input](https://codemirror.net/6/docs/ref/#language.indentOnInput)\n - [the default highlight style](https://codemirror.net/6/docs/ref/#language.defaultHighlightStyle) (as fallback)\n - [bracket matching](https://codemirror.net/6/docs/ref/#language.bracketMatching)\n - [bracket closing](https://codemirror.net/6/docs/ref/#autocomplete.closeBrackets)\n - [autocompletion](https://codemirror.net/6/docs/ref/#autocomplete.autocompletion)\n - [rectangular selection](https://codemirror.net/6/docs/ref/#view.rectangularSelection) and [crosshair cursor](https://codemirror.net/6/docs/ref/#view.crosshairCursor)\n - [active line highlighting](https://codemirror.net/6/docs/ref/#view.highlightActiveLine)\n - [active line gutter highlighting](https://codemirror.net/6/docs/ref/#view.highlightActiveLineGutter)\n - [selection match highlighting](https://codemirror.net/6/docs/ref/#search.highlightSelectionMatches)\n - [search](https://codemirror.net/6/docs/ref/#search.searchKeymap)\n - [linting](https://codemirror.net/6/docs/ref/#lint.lintKeymap)\n\n(You'll probably want to add some language package to your setup\ntoo.)\n\nThis extension does not allow customization. The idea is that,\nonce you decide you want to configure your editor more precisely,\nyou take this package's source (which is just a bunch of imports\nand an array literal), copy it into your own code, and adjust it\nas desired.\n*/\nvar basicSetup = function basicSetup(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var {\n    crosshairCursor: initCrosshairCursor = false\n  } = options;\n  var keymaps = [];\n  if (options.closeBracketsKeymap !== false) {\n    keymaps = keymaps.concat(_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_0__.closeBracketsKeymap);\n  }\n  if (options.defaultKeymap !== false) {\n    keymaps = keymaps.concat(_codemirror_commands__WEBPACK_IMPORTED_MODULE_1__.defaultKeymap);\n  }\n  if (options.searchKeymap !== false) {\n    keymaps = keymaps.concat(_codemirror_search__WEBPACK_IMPORTED_MODULE_2__.searchKeymap);\n  }\n  if (options.historyKeymap !== false) {\n    keymaps = keymaps.concat(_codemirror_commands__WEBPACK_IMPORTED_MODULE_1__.historyKeymap);\n  }\n  if (options.foldKeymap !== false) {\n    keymaps = keymaps.concat(_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.foldKeymap);\n  }\n  if (options.completionKeymap !== false) {\n    keymaps = keymaps.concat(_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_0__.completionKeymap);\n  }\n  if (options.lintKeymap !== false) {\n    keymaps = keymaps.concat(_codemirror_lint__WEBPACK_IMPORTED_MODULE_4__.lintKeymap);\n  }\n  var extensions = [];\n  if (options.lineNumbers !== false) extensions.push((0,_codemirror_view__WEBPACK_IMPORTED_MODULE_5__.lineNumbers)());\n  if (options.highlightActiveLineGutter !== false) extensions.push((0,_codemirror_view__WEBPACK_IMPORTED_MODULE_5__.highlightActiveLineGutter)());\n  if (options.highlightSpecialChars !== false) extensions.push((0,_codemirror_view__WEBPACK_IMPORTED_MODULE_5__.highlightSpecialChars)());\n  if (options.history !== false) extensions.push((0,_codemirror_commands__WEBPACK_IMPORTED_MODULE_1__.history)());\n  if (options.foldGutter !== false) extensions.push((0,_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.foldGutter)());\n  if (options.drawSelection !== false) extensions.push((0,_codemirror_view__WEBPACK_IMPORTED_MODULE_5__.drawSelection)());\n  if (options.dropCursor !== false) extensions.push((0,_codemirror_view__WEBPACK_IMPORTED_MODULE_5__.dropCursor)());\n  if (options.allowMultipleSelections !== false) extensions.push(_codemirror_state__WEBPACK_IMPORTED_MODULE_6__.EditorState.allowMultipleSelections.of(true));\n  if (options.indentOnInput !== false) extensions.push((0,_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.indentOnInput)());\n  if (options.syntaxHighlighting !== false) extensions.push((0,_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.syntaxHighlighting)(_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.defaultHighlightStyle, {\n    fallback: true\n  }));\n  if (options.bracketMatching !== false) extensions.push((0,_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.bracketMatching)());\n  if (options.closeBrackets !== false) extensions.push((0,_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_0__.closeBrackets)());\n  if (options.autocompletion !== false) extensions.push((0,_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_0__.autocompletion)());\n  if (options.rectangularSelection !== false) extensions.push((0,_codemirror_view__WEBPACK_IMPORTED_MODULE_5__.rectangularSelection)());\n  if (initCrosshairCursor !== false) extensions.push((0,_codemirror_view__WEBPACK_IMPORTED_MODULE_5__.crosshairCursor)());\n  if (options.highlightActiveLine !== false) extensions.push((0,_codemirror_view__WEBPACK_IMPORTED_MODULE_5__.highlightActiveLine)());\n  if (options.highlightSelectionMatches !== false) extensions.push((0,_codemirror_search__WEBPACK_IMPORTED_MODULE_2__.highlightSelectionMatches)());\n  if (options.tabSize && typeof options.tabSize === 'number') extensions.push(_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.indentUnit.of(' '.repeat(options.tabSize)));\n  return extensions.concat([_codemirror_view__WEBPACK_IMPORTED_MODULE_5__.keymap.of(keymaps.flat())]).filter(Boolean);\n};\n/**\nA minimal set of extensions to create a functional editor. Only\nincludes [the default keymap](https://codemirror.net/6/docs/ref/#commands.defaultKeymap), [undo\nhistory](https://codemirror.net/6/docs/ref/#commands.history), [special character\nhighlighting](https://codemirror.net/6/docs/ref/#view.highlightSpecialChars), [custom selection\ndrawing](https://codemirror.net/6/docs/ref/#view.drawSelection), and [default highlight\nstyle](https://codemirror.net/6/docs/ref/#language.defaultHighlightStyle).\n*/\nvar minimalSetup = function minimalSetup(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var keymaps = [];\n  if (options.defaultKeymap !== false) {\n    keymaps = keymaps.concat(_codemirror_commands__WEBPACK_IMPORTED_MODULE_1__.defaultKeymap);\n  }\n  if (options.historyKeymap !== false) {\n    keymaps = keymaps.concat(_codemirror_commands__WEBPACK_IMPORTED_MODULE_1__.historyKeymap);\n  }\n  var extensions = [];\n  if (options.highlightSpecialChars !== false) extensions.push((0,_codemirror_view__WEBPACK_IMPORTED_MODULE_5__.highlightSpecialChars)());\n  if (options.history !== false) extensions.push((0,_codemirror_commands__WEBPACK_IMPORTED_MODULE_1__.history)());\n  if (options.drawSelection !== false) extensions.push((0,_codemirror_view__WEBPACK_IMPORTED_MODULE_5__.drawSelection)());\n  if (options.syntaxHighlighting !== false) extensions.push((0,_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.syntaxHighlighting)(_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.defaultHighlightStyle, {\n    fallback: true\n  }));\n  return extensions.concat([_codemirror_view__WEBPACK_IMPORTED_MODULE_5__.keymap.of(keymaps.flat())]).filter(Boolean);\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/codemirror-extensions-basic-setup/esm/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/codemirror-extensions-langs/esm/index.js":
/*!********************************************************************!*\
  !*** ./node_modules/@uiw/codemirror-extensions-langs/esm/index.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   langNames: () => (/* binding */ langNames),\n/* harmony export */   langs: () => (/* binding */ langs),\n/* harmony export */   loadLanguage: () => (/* binding */ loadLanguage)\n/* harmony export */ });\n/* harmony import */ var _codemirror_language_data__WEBPACK_IMPORTED_MODULE_81__ = __webpack_require__(/*! @codemirror/language-data */ \"(ssr)/./node_modules/@codemirror/language-data/dist/index.js\");\n/* harmony import */ var _codemirror_language__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @codemirror/language */ \"(ssr)/./node_modules/@codemirror/language/dist/index.js\");\n/* harmony import */ var _codemirror_lang_markdown__WEBPACK_IMPORTED_MODULE_80__ = __webpack_require__(/*! @codemirror/lang-markdown */ \"(ssr)/./node_modules/@codemirror/lang-markdown/dist/index.js\");\n/* harmony import */ var _codemirror_lang_javascript__WEBPACK_IMPORTED_MODULE_73__ = __webpack_require__(/*! @codemirror/lang-javascript */ \"(ssr)/./node_modules/@codemirror/lang-javascript/dist/index.js\");\n/* harmony import */ var _codemirror_lang_html__WEBPACK_IMPORTED_MODULE_77__ = __webpack_require__(/*! @codemirror/lang-html */ \"(ssr)/./node_modules/@codemirror/lang-html/dist/index.js\");\n/* harmony import */ var _codemirror_lang_css__WEBPACK_IMPORTED_MODULE_78__ = __webpack_require__(/*! @codemirror/lang-css */ \"(ssr)/./node_modules/@codemirror/lang-css/dist/index.js\");\n/* harmony import */ var _codemirror_lang_less__WEBPACK_IMPORTED_MODULE_105__ = __webpack_require__(/*! @codemirror/lang-less */ \"(ssr)/./node_modules/@codemirror/lang-less/dist/index.js\");\n/* harmony import */ var _codemirror_lang_sass__WEBPACK_IMPORTED_MODULE_48__ = __webpack_require__(/*! @codemirror/lang-sass */ \"(ssr)/./node_modules/@codemirror/lang-sass/dist/index.js\");\n/* harmony import */ var _codemirror_lang_json__WEBPACK_IMPORTED_MODULE_76__ = __webpack_require__(/*! @codemirror/lang-json */ \"(ssr)/./node_modules/@codemirror/lang-json/dist/index.js\");\n/* harmony import */ var codemirror_lang_mermaid__WEBPACK_IMPORTED_MODULE_50__ = __webpack_require__(/*! codemirror-lang-mermaid */ \"(ssr)/./node_modules/codemirror-lang-mermaid/dist/index.js\");\n/* harmony import */ var _codemirror_lang_python__WEBPACK_IMPORTED_MODULE_79__ = __webpack_require__(/*! @codemirror/lang-python */ \"(ssr)/./node_modules/@codemirror/lang-python/dist/index.js\");\n/* harmony import */ var _codemirror_lang_xml__WEBPACK_IMPORTED_MODULE_82__ = __webpack_require__(/*! @codemirror/lang-xml */ \"(ssr)/./node_modules/@codemirror/lang-xml/dist/index.js\");\n/* harmony import */ var _codemirror_lang_sql__WEBPACK_IMPORTED_MODULE_83__ = __webpack_require__(/*! @codemirror/lang-sql */ \"(ssr)/./node_modules/@codemirror/lang-sql/dist/index.js\");\n/* harmony import */ var _codemirror_lang_java__WEBPACK_IMPORTED_MODULE_84__ = __webpack_require__(/*! @codemirror/lang-java */ \"(ssr)/./node_modules/@codemirror/lang-java/dist/index.js\");\n/* harmony import */ var _codemirror_lang_rust__WEBPACK_IMPORTED_MODULE_85__ = __webpack_require__(/*! @codemirror/lang-rust */ \"(ssr)/./node_modules/@codemirror/lang-rust/dist/index.js\");\n/* harmony import */ var _codemirror_lang_cpp__WEBPACK_IMPORTED_MODULE_86__ = __webpack_require__(/*! @codemirror/lang-cpp */ \"(ssr)/./node_modules/@codemirror/lang-cpp/dist/index.js\");\n/* harmony import */ var _codemirror_lang_lezer__WEBPACK_IMPORTED_MODULE_87__ = __webpack_require__(/*! @codemirror/lang-lezer */ \"(ssr)/./node_modules/@codemirror/lang-lezer/dist/index.js\");\n/* harmony import */ var _codemirror_lang_php__WEBPACK_IMPORTED_MODULE_88__ = __webpack_require__(/*! @codemirror/lang-php */ \"(ssr)/./node_modules/@codemirror/lang-php/dist/index.js\");\n/* harmony import */ var _codemirror_lang_liquid__WEBPACK_IMPORTED_MODULE_49__ = __webpack_require__(/*! @codemirror/lang-liquid */ \"(ssr)/./node_modules/@codemirror/lang-liquid/dist/index.js\");\n/* harmony import */ var _codemirror_lang_wast__WEBPACK_IMPORTED_MODULE_72__ = __webpack_require__(/*! @codemirror/lang-wast */ \"(ssr)/./node_modules/@codemirror/lang-wast/dist/index.js\");\n/* harmony import */ var _codemirror_lang_vue__WEBPACK_IMPORTED_MODULE_74__ = __webpack_require__(/*! @codemirror/lang-vue */ \"(ssr)/./node_modules/@codemirror/lang-vue/dist/index.js\");\n/* harmony import */ var _codemirror_lang_angular__WEBPACK_IMPORTED_MODULE_75__ = __webpack_require__(/*! @codemirror/lang-angular */ \"(ssr)/./node_modules/@codemirror/lang-angular/dist/index.js\");\n/* harmony import */ var _replit_codemirror_lang_nix__WEBPACK_IMPORTED_MODULE_51__ = __webpack_require__(/*! @replit/codemirror-lang-nix */ \"(ssr)/./node_modules/@replit/codemirror-lang-nix/dist/index.js\");\n/* harmony import */ var _replit_codemirror_lang_svelte__WEBPACK_IMPORTED_MODULE_52__ = __webpack_require__(/*! @replit/codemirror-lang-svelte */ \"(ssr)/./node_modules/@replit/codemirror-lang-svelte/dist/index.js\");\n/* harmony import */ var _replit_codemirror_lang_csharp__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @replit/codemirror-lang-csharp */ \"(ssr)/./node_modules/@replit/codemirror-lang-csharp/dist/index.js\");\n/* harmony import */ var _replit_codemirror_lang_solidity__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @replit/codemirror-lang-solidity */ \"(ssr)/./node_modules/@replit/codemirror-lang-solidity/dist/index.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_apl__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/apl */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/apl.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_asciiarmor__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/asciiarmor */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/asciiarmor.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_asterisk__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/asterisk */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/asterisk.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_brainfuck__WEBPACK_IMPORTED_MODULE_97__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/brainfuck */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/brainfuck.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_clike__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/clike */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/clike.js\");\n/* harmony import */ var _nextjournal_lang_clojure__WEBPACK_IMPORTED_MODULE_109__ = __webpack_require__(/*! @nextjournal/lang-clojure */ \"(ssr)/./node_modules/@nextjournal/lang-clojure/dist/index.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_cmake__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/cmake */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/cmake.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_cobol__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/cobol */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/cobol.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_coffeescript__WEBPACK_IMPORTED_MODULE_110__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/coffeescript */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/coffeescript.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_commonlisp__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/commonlisp */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/commonlisp.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_crystal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/crystal */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/crystal.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_cypher__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/cypher */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/cypher.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_d__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/d */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/d.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_diff__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/diff */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/diff.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_dockerfile__WEBPACK_IMPORTED_MODULE_112__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/dockerfile */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/dockerfile.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_dtd__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/dtd */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/dtd.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_dylan__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/dylan */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/dylan.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_ebnf__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/ebnf */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/ebnf.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_ecl__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/ecl */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/ecl.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_eiffel__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/eiffel */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/eiffel.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_elm__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/elm */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/elm.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_erlang__WEBPACK_IMPORTED_MODULE_99__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/erlang */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/erlang.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_factor__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/factor */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/factor.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_fcl__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/fcl */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/fcl.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_forth__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/forth */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/forth.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_fortran__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/fortran */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/fortran.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_gas__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/gas */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/gas.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_gherkin__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/gherkin */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/gherkin.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_go__WEBPACK_IMPORTED_MODULE_89__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/go */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/go.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_groovy__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/groovy */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/groovy.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_haskell__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/haskell */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/haskell.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_haxe__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/haxe */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/haxe.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_http__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/http */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/http.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_idl__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/idl */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/idl.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_jinja2__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/jinja2 */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/jinja2.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_julia__WEBPACK_IMPORTED_MODULE_111__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/julia */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/julia.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_livescript__WEBPACK_IMPORTED_MODULE_104__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/livescript */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/livescript.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_lua__WEBPACK_IMPORTED_MODULE_91__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/lua */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/lua.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_mathematica__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/mathematica */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/mathematica.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_mbox__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/mbox */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/mbox.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_mirc__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/mirc */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/mirc.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_modelica__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/modelica */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/modelica.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_mscgen__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/mscgen */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/mscgen.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_mumps__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/mumps */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/mumps.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_nginx__WEBPACK_IMPORTED_MODULE_100__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/nginx */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/nginx.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_nsis__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/nsis */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/nsis.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_ntriples__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/ntriples */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/ntriples.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_octave__WEBPACK_IMPORTED_MODULE_40__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/octave */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/octave.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_oz__WEBPACK_IMPORTED_MODULE_41__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/oz */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/oz.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_pascal__WEBPACK_IMPORTED_MODULE_103__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/pascal */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/pascal.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_perl__WEBPACK_IMPORTED_MODULE_101__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/perl */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/perl.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_pig__WEBPACK_IMPORTED_MODULE_42__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/pig */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/pig.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_powershell__WEBPACK_IMPORTED_MODULE_96__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/powershell */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/powershell.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_properties__WEBPACK_IMPORTED_MODULE_43__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/properties */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/properties.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_protobuf__WEBPACK_IMPORTED_MODULE_44__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/protobuf */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/protobuf.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_puppet__WEBPACK_IMPORTED_MODULE_45__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/puppet */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/puppet.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_q__WEBPACK_IMPORTED_MODULE_46__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/q */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/q.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_r__WEBPACK_IMPORTED_MODULE_113__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/r */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/r.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_ruby__WEBPACK_IMPORTED_MODULE_102__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/ruby */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/ruby.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_sas__WEBPACK_IMPORTED_MODULE_47__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/sas */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/sas.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_scheme__WEBPACK_IMPORTED_MODULE_106__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/scheme */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/scheme.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_shell__WEBPACK_IMPORTED_MODULE_90__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/shell */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/shell.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_sieve__WEBPACK_IMPORTED_MODULE_53__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/sieve */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/sieve.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_smalltalk__WEBPACK_IMPORTED_MODULE_54__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/smalltalk */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/smalltalk.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_solr__WEBPACK_IMPORTED_MODULE_55__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/solr */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/solr.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_sparql__WEBPACK_IMPORTED_MODULE_56__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/sparql */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/sparql.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_spreadsheet__WEBPACK_IMPORTED_MODULE_57__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/spreadsheet */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/spreadsheet.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_stex__WEBPACK_IMPORTED_MODULE_58__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/stex */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/stex.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_stylus__WEBPACK_IMPORTED_MODULE_98__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/stylus */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/stylus.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_swift__WEBPACK_IMPORTED_MODULE_92__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/swift */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/swift.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_tcl__WEBPACK_IMPORTED_MODULE_93__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/tcl */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/tcl.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_textile__WEBPACK_IMPORTED_MODULE_59__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/textile */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/textile.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_tiddlywiki__WEBPACK_IMPORTED_MODULE_60__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/tiddlywiki */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/tiddlywiki.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_tiki__WEBPACK_IMPORTED_MODULE_61__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/tiki */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/tiki.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_toml__WEBPACK_IMPORTED_MODULE_107__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/toml */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/toml.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_troff__WEBPACK_IMPORTED_MODULE_62__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/troff */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/troff.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_ttcn__WEBPACK_IMPORTED_MODULE_63__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/ttcn */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/ttcn.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_turtle__WEBPACK_IMPORTED_MODULE_64__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/turtle */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/turtle.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_vb__WEBPACK_IMPORTED_MODULE_95__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/vb */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/vb.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_vbscript__WEBPACK_IMPORTED_MODULE_108__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/vbscript */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/vbscript.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_velocity__WEBPACK_IMPORTED_MODULE_65__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/velocity */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/velocity.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_verilog__WEBPACK_IMPORTED_MODULE_66__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/verilog */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/verilog.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_vhdl__WEBPACK_IMPORTED_MODULE_67__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/vhdl */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/vhdl.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_webidl__WEBPACK_IMPORTED_MODULE_68__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/webidl */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/webidl.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_xquery__WEBPACK_IMPORTED_MODULE_69__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/xquery */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/xquery.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_yacas__WEBPACK_IMPORTED_MODULE_70__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/yacas */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/yacas.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_yaml__WEBPACK_IMPORTED_MODULE_94__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/yaml */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/yaml.js\");\n/* harmony import */ var _codemirror_legacy_modes_mode_z80__WEBPACK_IMPORTED_MODULE_71__ = __webpack_require__(/*! @codemirror/legacy-modes/mode/z80 */ \"(ssr)/./node_modules/@codemirror/legacy-modes/mode/z80.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// import { asn1 } from '@codemirror/legacy-modes/mode/asn1';\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// import { mllike } from '@codemirror/legacy-modes/mode/mllike';\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// import { rpm } from '@codemirror/legacy-modes/mode/rpm';\n\n\n\n\n\n// import { mode } from '@codemirror/legacy-modes/mode/simple-mode';\n\n\n\n\n\n\n\n\n\n\n\n\n\n// import { cfg } from '@codemirror/legacy-modes/mode/ttcn-cfg';\n\n\n\n\n\n\n\n\n\n\n\n\nvar langs = {\n  apl: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_apl__WEBPACK_IMPORTED_MODULE_1__.apl),\n  asciiArmor: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_asciiarmor__WEBPACK_IMPORTED_MODULE_2__.asciiArmor),\n  // asn1: () => StreamLanguage.define(asn1),\n  asterisk: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_asterisk__WEBPACK_IMPORTED_MODULE_3__.asterisk),\n  // clike: () => StreamLanguage.define(clike),\n  c: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_clike__WEBPACK_IMPORTED_MODULE_4__.c),\n  csharp: () => (0,_replit_codemirror_lang_csharp__WEBPACK_IMPORTED_MODULE_5__.csharp)(),\n  scala: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_clike__WEBPACK_IMPORTED_MODULE_4__.scala),\n  solidity: () => _replit_codemirror_lang_solidity__WEBPACK_IMPORTED_MODULE_6__.solidity,\n  kotlin: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_clike__WEBPACK_IMPORTED_MODULE_4__.kotlin),\n  shader: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_clike__WEBPACK_IMPORTED_MODULE_4__.shader),\n  nesC: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_clike__WEBPACK_IMPORTED_MODULE_4__.nesC),\n  objectiveC: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_clike__WEBPACK_IMPORTED_MODULE_4__.objectiveC),\n  objectiveCpp: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_clike__WEBPACK_IMPORTED_MODULE_4__.objectiveCpp),\n  squirrel: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_clike__WEBPACK_IMPORTED_MODULE_4__.squirrel),\n  ceylon: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_clike__WEBPACK_IMPORTED_MODULE_4__.ceylon),\n  dart: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_clike__WEBPACK_IMPORTED_MODULE_4__.dart),\n  cmake: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_cmake__WEBPACK_IMPORTED_MODULE_7__.cmake),\n  cobol: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_cobol__WEBPACK_IMPORTED_MODULE_8__.cobol),\n  commonLisp: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_commonlisp__WEBPACK_IMPORTED_MODULE_9__.commonLisp),\n  crystal: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_crystal__WEBPACK_IMPORTED_MODULE_10__.crystal),\n  cypher: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_cypher__WEBPACK_IMPORTED_MODULE_11__.cypher),\n  d: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_d__WEBPACK_IMPORTED_MODULE_12__.d),\n  diff: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_diff__WEBPACK_IMPORTED_MODULE_13__.diff),\n  dtd: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_dtd__WEBPACK_IMPORTED_MODULE_14__.dtd),\n  dylan: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_dylan__WEBPACK_IMPORTED_MODULE_15__.dylan),\n  ebnf: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_ebnf__WEBPACK_IMPORTED_MODULE_16__.ebnf),\n  ecl: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_ecl__WEBPACK_IMPORTED_MODULE_17__.ecl),\n  eiffel: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_eiffel__WEBPACK_IMPORTED_MODULE_18__.eiffel),\n  elm: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_elm__WEBPACK_IMPORTED_MODULE_19__.elm),\n  factor: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_factor__WEBPACK_IMPORTED_MODULE_20__.factor),\n  fcl: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_fcl__WEBPACK_IMPORTED_MODULE_21__.fcl),\n  forth: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_forth__WEBPACK_IMPORTED_MODULE_22__.forth),\n  fortran: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_fortran__WEBPACK_IMPORTED_MODULE_23__.fortran),\n  gas: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_gas__WEBPACK_IMPORTED_MODULE_24__.gas),\n  gherkin: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_gherkin__WEBPACK_IMPORTED_MODULE_25__.gherkin),\n  groovy: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_groovy__WEBPACK_IMPORTED_MODULE_26__.groovy),\n  haskell: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_haskell__WEBPACK_IMPORTED_MODULE_27__.haskell),\n  haxe: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_haxe__WEBPACK_IMPORTED_MODULE_28__.haxe),\n  http: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_http__WEBPACK_IMPORTED_MODULE_29__.http),\n  idl: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_idl__WEBPACK_IMPORTED_MODULE_30__.idl),\n  jinja2: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_jinja2__WEBPACK_IMPORTED_MODULE_31__.jinja2),\n  mathematica: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_mathematica__WEBPACK_IMPORTED_MODULE_32__.mathematica),\n  mbox: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_mbox__WEBPACK_IMPORTED_MODULE_33__.mbox),\n  mirc: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_mirc__WEBPACK_IMPORTED_MODULE_34__.mirc),\n  modelica: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_modelica__WEBPACK_IMPORTED_MODULE_35__.modelica),\n  mscgen: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_mscgen__WEBPACK_IMPORTED_MODULE_36__.mscgen),\n  mumps: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_mumps__WEBPACK_IMPORTED_MODULE_37__.mumps),\n  nsis: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_nsis__WEBPACK_IMPORTED_MODULE_38__.nsis),\n  ntriples: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_ntriples__WEBPACK_IMPORTED_MODULE_39__.ntriples),\n  octave: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_octave__WEBPACK_IMPORTED_MODULE_40__.octave),\n  oz: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_oz__WEBPACK_IMPORTED_MODULE_41__.oz),\n  pig: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_pig__WEBPACK_IMPORTED_MODULE_42__.pig),\n  properties: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_properties__WEBPACK_IMPORTED_MODULE_43__.properties),\n  protobuf: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_protobuf__WEBPACK_IMPORTED_MODULE_44__.protobuf),\n  puppet: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_puppet__WEBPACK_IMPORTED_MODULE_45__.puppet),\n  q: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_q__WEBPACK_IMPORTED_MODULE_46__.q),\n  sas: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_sas__WEBPACK_IMPORTED_MODULE_47__.sas),\n  sass: () => (0,_codemirror_lang_sass__WEBPACK_IMPORTED_MODULE_48__.sass)(),\n  liquid: () => (0,_codemirror_lang_liquid__WEBPACK_IMPORTED_MODULE_49__.liquid)(),\n  mermaid: () => (0,codemirror_lang_mermaid__WEBPACK_IMPORTED_MODULE_50__.mermaid)(),\n  nix: () => (0,_replit_codemirror_lang_nix__WEBPACK_IMPORTED_MODULE_51__.nix)(),\n  svelte: () => (0,_replit_codemirror_lang_svelte__WEBPACK_IMPORTED_MODULE_52__.svelte)(),\n  sieve: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_sieve__WEBPACK_IMPORTED_MODULE_53__.sieve),\n  smalltalk: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_smalltalk__WEBPACK_IMPORTED_MODULE_54__.smalltalk),\n  solr: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_solr__WEBPACK_IMPORTED_MODULE_55__.solr),\n  sparql: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_sparql__WEBPACK_IMPORTED_MODULE_56__.sparql),\n  spreadsheet: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_spreadsheet__WEBPACK_IMPORTED_MODULE_57__.spreadsheet),\n  stex: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_stex__WEBPACK_IMPORTED_MODULE_58__.stex),\n  textile: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_textile__WEBPACK_IMPORTED_MODULE_59__.textile),\n  tiddlyWiki: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_tiddlywiki__WEBPACK_IMPORTED_MODULE_60__.tiddlyWiki),\n  tiki: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_tiki__WEBPACK_IMPORTED_MODULE_61__.tiki),\n  troff: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_troff__WEBPACK_IMPORTED_MODULE_62__.troff),\n  ttcn: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_ttcn__WEBPACK_IMPORTED_MODULE_63__.ttcn),\n  turtle: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_turtle__WEBPACK_IMPORTED_MODULE_64__.turtle),\n  velocity: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_velocity__WEBPACK_IMPORTED_MODULE_65__.velocity),\n  verilog: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_verilog__WEBPACK_IMPORTED_MODULE_66__.verilog),\n  vhdl: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_vhdl__WEBPACK_IMPORTED_MODULE_67__.vhdl),\n  webIDL: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_webidl__WEBPACK_IMPORTED_MODULE_68__.webIDL),\n  xQuery: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_xquery__WEBPACK_IMPORTED_MODULE_69__.xQuery),\n  yacas: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_yacas__WEBPACK_IMPORTED_MODULE_70__.yacas),\n  z80: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_z80__WEBPACK_IMPORTED_MODULE_71__.z80),\n  wast: _codemirror_lang_wast__WEBPACK_IMPORTED_MODULE_72__.wast,\n  javascript: _codemirror_lang_javascript__WEBPACK_IMPORTED_MODULE_73__.javascript,\n  jsx: () => (0,_codemirror_lang_javascript__WEBPACK_IMPORTED_MODULE_73__.javascript)({\n    jsx: true\n  }),\n  typescript: () => (0,_codemirror_lang_javascript__WEBPACK_IMPORTED_MODULE_73__.javascript)({\n    typescript: true\n  }),\n  tsx: () => (0,_codemirror_lang_javascript__WEBPACK_IMPORTED_MODULE_73__.javascript)({\n    jsx: true,\n    typescript: true\n  }),\n  vue: () => (0,_codemirror_lang_vue__WEBPACK_IMPORTED_MODULE_74__.vue)(),\n  angular: () => (0,_codemirror_lang_angular__WEBPACK_IMPORTED_MODULE_75__.angular)(),\n  json: _codemirror_lang_json__WEBPACK_IMPORTED_MODULE_76__.json,\n  html: _codemirror_lang_html__WEBPACK_IMPORTED_MODULE_77__.html,\n  css: _codemirror_lang_css__WEBPACK_IMPORTED_MODULE_78__.css,\n  python: _codemirror_lang_python__WEBPACK_IMPORTED_MODULE_79__.python,\n  markdown: () => (0,_codemirror_lang_markdown__WEBPACK_IMPORTED_MODULE_80__.markdown)({\n    base: _codemirror_lang_markdown__WEBPACK_IMPORTED_MODULE_80__.markdownLanguage,\n    codeLanguages: _codemirror_language_data__WEBPACK_IMPORTED_MODULE_81__.languages\n  }),\n  xml: _codemirror_lang_xml__WEBPACK_IMPORTED_MODULE_82__.xml,\n  sql: _codemirror_lang_sql__WEBPACK_IMPORTED_MODULE_83__.sql,\n  mysql: () => (0,_codemirror_lang_sql__WEBPACK_IMPORTED_MODULE_83__.sql)({\n    dialect: _codemirror_lang_sql__WEBPACK_IMPORTED_MODULE_83__.MySQL\n  }),\n  pgsql: () => (0,_codemirror_lang_sql__WEBPACK_IMPORTED_MODULE_83__.sql)({\n    dialect: _codemirror_lang_sql__WEBPACK_IMPORTED_MODULE_83__.PostgreSQL\n  }),\n  java: _codemirror_lang_java__WEBPACK_IMPORTED_MODULE_84__.java,\n  rust: _codemirror_lang_rust__WEBPACK_IMPORTED_MODULE_85__.rust,\n  cpp: _codemirror_lang_cpp__WEBPACK_IMPORTED_MODULE_86__.cpp,\n  // clike: () => StreamLanguage.define(clike),\n  // clike: () => clike({ }),\n  lezer: _codemirror_lang_lezer__WEBPACK_IMPORTED_MODULE_87__.lezer,\n  php: _codemirror_lang_php__WEBPACK_IMPORTED_MODULE_88__.php,\n  go: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_go__WEBPACK_IMPORTED_MODULE_89__.go),\n  shell: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_shell__WEBPACK_IMPORTED_MODULE_90__.shell),\n  lua: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_lua__WEBPACK_IMPORTED_MODULE_91__.lua),\n  swift: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_swift__WEBPACK_IMPORTED_MODULE_92__.swift),\n  tcl: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_tcl__WEBPACK_IMPORTED_MODULE_93__.tcl),\n  yaml: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_yaml__WEBPACK_IMPORTED_MODULE_94__.yaml),\n  vb: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_vb__WEBPACK_IMPORTED_MODULE_95__.vb),\n  powershell: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_powershell__WEBPACK_IMPORTED_MODULE_96__.powerShell),\n  brainfuck: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_brainfuck__WEBPACK_IMPORTED_MODULE_97__.brainfuck),\n  stylus: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_stylus__WEBPACK_IMPORTED_MODULE_98__.stylus),\n  erlang: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_erlang__WEBPACK_IMPORTED_MODULE_99__.erlang),\n  nginx: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_nginx__WEBPACK_IMPORTED_MODULE_100__.nginx),\n  perl: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_perl__WEBPACK_IMPORTED_MODULE_101__.perl),\n  ruby: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_ruby__WEBPACK_IMPORTED_MODULE_102__.ruby),\n  pascal: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_pascal__WEBPACK_IMPORTED_MODULE_103__.pascal),\n  livescript: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_livescript__WEBPACK_IMPORTED_MODULE_104__.liveScript),\n  less: () => (0,_codemirror_lang_less__WEBPACK_IMPORTED_MODULE_105__.less)(),\n  scheme: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_scheme__WEBPACK_IMPORTED_MODULE_106__.scheme),\n  toml: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_toml__WEBPACK_IMPORTED_MODULE_107__.toml),\n  vbscript: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_vbscript__WEBPACK_IMPORTED_MODULE_108__.vbScript),\n  clojure: () => (0,_nextjournal_lang_clojure__WEBPACK_IMPORTED_MODULE_109__.clojure)(),\n  coffeescript: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_coffeescript__WEBPACK_IMPORTED_MODULE_110__.coffeeScript),\n  julia: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_julia__WEBPACK_IMPORTED_MODULE_111__.julia),\n  dockerfile: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_dockerfile__WEBPACK_IMPORTED_MODULE_112__.dockerFile),\n  r: () => _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(_codemirror_legacy_modes_mode_r__WEBPACK_IMPORTED_MODULE_113__.r)\n};\n\n/** Language list */\nvar langNames = Object.keys(langs);\nfunction loadLanguage(name) {\n  return langs[name] ? langs[name]() : null;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/codemirror-extensions-langs/esm/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/codemirror-theme-vscode/esm/dark.js":
/*!***************************************************************!*\
  !*** ./node_modules/@uiw/codemirror-theme-vscode/esm/dark.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultSettingsVscodeDark: () => (/* binding */ defaultSettingsVscodeDark),\n/* harmony export */   vscodeDark: () => (/* binding */ vscodeDark),\n/* harmony export */   vscodeDarkInit: () => (/* binding */ vscodeDarkInit),\n/* harmony export */   vscodeDarkStyle: () => (/* binding */ vscodeDarkStyle)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @lezer/highlight */ \"(ssr)/./node_modules/@lezer/highlight/dist/index.js\");\n/* harmony import */ var _uiw_codemirror_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @uiw/codemirror-themes */ \"(ssr)/./node_modules/@uiw/codemirror-themes/esm/index.js\");\n\n/**\n * https://github.com/uiwjs/react-codemirror/issues/409\n */\n\n\nvar defaultSettingsVscodeDark = {\n  background: '#1e1e1e',\n  foreground: '#9cdcfe',\n  caret: '#c6c6c6',\n  selection: '#6199ff2f',\n  selectionMatch: '#72a1ff59',\n  lineHighlight: '#ffffff0f',\n  gutterBackground: '#1e1e1e',\n  gutterForeground: '#838383',\n  gutterActiveForeground: '#fff',\n  fontFamily: 'Menlo, Monaco, Consolas, \"Andale Mono\", \"Ubuntu Mono\", \"Courier New\", monospace'\n};\nvar vscodeDarkStyle = [{\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.keyword, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.operatorKeyword, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.modifier, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.color, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.constant(_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.name), _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.standard(_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.name), _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.standard(_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.tagName), _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.special(_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.brace), _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.atom, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.bool, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.special(_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.variableName)],\n  color: '#569cd6'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.controlKeyword, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.moduleKeyword],\n  color: '#c586c0'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.name, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.deleted, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.character, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.macroName, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.propertyName, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.variableName, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.labelName, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.definition(_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.name)],\n  color: '#9cdcfe'\n}, {\n  tag: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.heading,\n  fontWeight: 'bold',\n  color: '#9cdcfe'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.typeName, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.className, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.tagName, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.number, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.changed, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.annotation, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.self, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.namespace],\n  color: '#4ec9b0'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.function(_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.variableName), _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.function(_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.propertyName)],\n  color: '#dcdcaa'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.number],\n  color: '#b5cea8'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.operator, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.punctuation, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.separator, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.url, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.escape, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.regexp],\n  color: '#d4d4d4'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.regexp],\n  color: '#d16969'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.special(_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.string), _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.processingInstruction, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.string, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.inserted],\n  color: '#ce9178'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.angleBracket],\n  color: '#808080'\n}, {\n  tag: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.strong,\n  fontWeight: 'bold'\n}, {\n  tag: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.emphasis,\n  fontStyle: 'italic'\n}, {\n  tag: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.strikethrough,\n  textDecoration: 'line-through'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.meta, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.comment],\n  color: '#6a9955'\n}, {\n  tag: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.link,\n  color: '#6a9955',\n  textDecoration: 'underline'\n}, {\n  tag: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.invalid,\n  color: '#ff0000'\n}];\nfunction vscodeDarkInit(options) {\n  var {\n    theme = 'dark',\n    settings = {},\n    styles = []\n  } = options || {};\n  return (0,_uiw_codemirror_themes__WEBPACK_IMPORTED_MODULE_2__.createTheme)({\n    theme: theme,\n    settings: _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default()({}, defaultSettingsVscodeDark, settings),\n    styles: [...vscodeDarkStyle, ...styles]\n  });\n}\nvar vscodeDark = vscodeDarkInit();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/codemirror-theme-vscode/esm/dark.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/codemirror-theme-vscode/esm/index.js":
/*!****************************************************************!*\
  !*** ./node_modules/@uiw/codemirror-theme-vscode/esm/index.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultSettingsVscodeDark: () => (/* reexport safe */ _dark__WEBPACK_IMPORTED_MODULE_1__.defaultSettingsVscodeDark),\n/* harmony export */   defaultSettingsVscodeLight: () => (/* reexport safe */ _light__WEBPACK_IMPORTED_MODULE_0__.defaultSettingsVscodeLight),\n/* harmony export */   vscodeDark: () => (/* reexport safe */ _dark__WEBPACK_IMPORTED_MODULE_1__.vscodeDark),\n/* harmony export */   vscodeDarkInit: () => (/* reexport safe */ _dark__WEBPACK_IMPORTED_MODULE_1__.vscodeDarkInit),\n/* harmony export */   vscodeDarkStyle: () => (/* reexport safe */ _dark__WEBPACK_IMPORTED_MODULE_1__.vscodeDarkStyle),\n/* harmony export */   vscodeLight: () => (/* reexport safe */ _light__WEBPACK_IMPORTED_MODULE_0__.vscodeLight),\n/* harmony export */   vscodeLightInit: () => (/* reexport safe */ _light__WEBPACK_IMPORTED_MODULE_0__.vscodeLightInit),\n/* harmony export */   vscodeLightStyle: () => (/* reexport safe */ _light__WEBPACK_IMPORTED_MODULE_0__.vscodeLightStyle)\n/* harmony export */ });\n/* harmony import */ var _light__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./light */ \"(ssr)/./node_modules/@uiw/codemirror-theme-vscode/esm/light.js\");\n/* harmony import */ var _dark__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./dark */ \"(ssr)/./node_modules/@uiw/codemirror-theme-vscode/esm/dark.js\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHVpdy9jb2RlbWlycm9yLXRoZW1lLXZzY29kZS9lc20vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUF3QiIsInNvdXJjZXMiOlsiRDpcXHNvZnRcXFNlYVByb2plY3RcXGdpdGh1Yl9vcGVuX3Byb2plY3Rfc3VjY2Vzc1xcc3VuYVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcQHVpd1xcY29kZW1pcnJvci10aGVtZS12c2NvZGVcXGVzbVxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSAnLi9saWdodCc7XG5leHBvcnQgKiBmcm9tICcuL2RhcmsnOyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/codemirror-theme-vscode/esm/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/codemirror-theme-vscode/esm/light.js":
/*!****************************************************************!*\
  !*** ./node_modules/@uiw/codemirror-theme-vscode/esm/light.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultSettingsVscodeLight: () => (/* binding */ defaultSettingsVscodeLight),\n/* harmony export */   vscodeLight: () => (/* binding */ vscodeLight),\n/* harmony export */   vscodeLightInit: () => (/* binding */ vscodeLightInit),\n/* harmony export */   vscodeLightStyle: () => (/* binding */ vscodeLightStyle)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @lezer/highlight */ \"(ssr)/./node_modules/@lezer/highlight/dist/index.js\");\n/* harmony import */ var _uiw_codemirror_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @uiw/codemirror-themes */ \"(ssr)/./node_modules/@uiw/codemirror-themes/esm/index.js\");\n\n/**\n * https://github.com/uiwjs/react-codemirror/issues/409\n */\n\n\nvar defaultSettingsVscodeLight = {\n  background: '#ffffff',\n  foreground: '#383a42',\n  caret: '#000',\n  selection: '#add6ff',\n  selectionMatch: '#a8ac94',\n  lineHighlight: '#99999926',\n  gutterBackground: '#fff',\n  gutterForeground: '#237893',\n  gutterActiveForeground: '#0b216f',\n  fontFamily: 'Menlo, Monaco, Consolas, \"Andale Mono\", \"Ubuntu Mono\", \"Courier New\", monospace'\n};\nvar vscodeLightStyle = [{\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.keyword, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.operatorKeyword, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.modifier, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.color, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.constant(_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.name), _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.standard(_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.name), _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.standard(_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.tagName), _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.special(_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.brace), _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.atom, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.bool, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.special(_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.variableName)],\n  color: '#0000ff'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.moduleKeyword, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.controlKeyword],\n  color: '#af00db'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.name, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.deleted, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.character, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.macroName, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.propertyName, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.variableName, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.labelName, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.definition(_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.name)],\n  color: '#0070c1'\n}, {\n  tag: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.heading,\n  fontWeight: 'bold',\n  color: '#0070c1'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.typeName, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.className, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.tagName, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.number, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.changed, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.annotation, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.self, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.namespace],\n  color: '#267f99'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.function(_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.variableName), _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.function(_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.propertyName)],\n  color: '#795e26'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.number],\n  color: '#098658'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.operator, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.punctuation, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.separator, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.url, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.escape, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.regexp],\n  color: '#383a42'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.regexp],\n  color: '#af00db'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.special(_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.string), _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.processingInstruction, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.string, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.inserted],\n  color: '#a31515'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.angleBracket],\n  color: '#383a42'\n}, {\n  tag: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.strong,\n  fontWeight: 'bold'\n}, {\n  tag: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.emphasis,\n  fontStyle: 'italic'\n}, {\n  tag: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.strikethrough,\n  textDecoration: 'line-through'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.meta, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.comment],\n  color: '#008000'\n}, {\n  tag: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.link,\n  color: '#4078f2',\n  textDecoration: 'underline'\n}, {\n  tag: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.invalid,\n  color: '#e45649'\n}];\nfunction vscodeLightInit(options) {\n  var {\n    theme = 'light',\n    settings = {},\n    styles = []\n  } = options || {};\n  return (0,_uiw_codemirror_themes__WEBPACK_IMPORTED_MODULE_2__.createTheme)({\n    theme: theme,\n    settings: _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default()({}, defaultSettingsVscodeLight, settings),\n    styles: [...vscodeLightStyle, ...styles]\n  });\n}\nvar vscodeLight = vscodeLightInit();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/codemirror-theme-vscode/esm/light.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/codemirror-theme-xcode/esm/index.js":
/*!***************************************************************!*\
  !*** ./node_modules/@uiw/codemirror-theme-xcode/esm/index.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultSettingsXcodeDark: () => (/* binding */ defaultSettingsXcodeDark),\n/* harmony export */   defaultSettingsXcodeLight: () => (/* binding */ defaultSettingsXcodeLight),\n/* harmony export */   xcodeDark: () => (/* binding */ xcodeDark),\n/* harmony export */   xcodeDarkInit: () => (/* binding */ xcodeDarkInit),\n/* harmony export */   xcodeDarkStyle: () => (/* binding */ xcodeDarkStyle),\n/* harmony export */   xcodeLight: () => (/* binding */ xcodeLight),\n/* harmony export */   xcodeLightInit: () => (/* binding */ xcodeLightInit),\n/* harmony export */   xcodeLightStyle: () => (/* binding */ xcodeLightStyle)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @lezer/highlight */ \"(ssr)/./node_modules/@lezer/highlight/dist/index.js\");\n/* harmony import */ var _uiw_codemirror_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @uiw/codemirror-themes */ \"(ssr)/./node_modules/@uiw/codemirror-themes/esm/index.js\");\n\n/**\n * @name Xcode\n */\n\n\nvar defaultSettingsXcodeLight = {\n  background: '#fff',\n  foreground: '#3D3D3D',\n  selection: '#BBDFFF',\n  selectionMatch: '#BBDFFF',\n  gutterBackground: '#fff',\n  gutterForeground: '#AFAFAF',\n  lineHighlight: '#d5e6ff69'\n};\nvar xcodeLightStyle = [{\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.comment, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.quote],\n  color: '#707F8D'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.typeName, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.typeOperator],\n  color: '#aa0d91'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.keyword],\n  color: '#aa0d91',\n  fontWeight: 'bold'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.string, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.meta],\n  color: '#D23423'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.name],\n  color: '#032f62'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.typeName],\n  color: '#522BB2'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.variableName],\n  color: '#23575C'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.definition(_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.variableName)],\n  color: '#327A9E'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.regexp, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.link],\n  color: '#0e0eff'\n}];\nfunction xcodeLightInit(options) {\n  var {\n    theme = 'light',\n    settings = {},\n    styles = []\n  } = options || {};\n  return (0,_uiw_codemirror_themes__WEBPACK_IMPORTED_MODULE_2__.createTheme)({\n    theme: theme,\n    settings: _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default()({}, defaultSettingsXcodeLight, settings),\n    styles: [...xcodeLightStyle, ...styles]\n  });\n}\nvar xcodeLight = xcodeLightInit();\nvar defaultSettingsXcodeDark = {\n  background: '#292A30',\n  foreground: '#CECFD0',\n  caret: '#fff',\n  selection: '#727377',\n  selectionMatch: '#727377',\n  lineHighlight: '#ffffff0f'\n};\nvar xcodeDarkStyle = [{\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.comment, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.quote],\n  color: '#7F8C98'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.keyword],\n  color: '#FF7AB2',\n  fontWeight: 'bold'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.string, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.meta],\n  color: '#FF8170'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.typeName],\n  color: '#DABAFF'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.definition(_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.variableName)],\n  color: '#6BDFFF'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.name],\n  color: '#6BAA9F'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.variableName],\n  color: '#ACF2E4'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.regexp, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.link],\n  color: '#FF8170'\n}];\nvar xcodeDarkInit = options => {\n  var {\n    theme = 'dark',\n    settings = {},\n    styles = []\n  } = options || {};\n  return (0,_uiw_codemirror_themes__WEBPACK_IMPORTED_MODULE_2__.createTheme)({\n    theme: theme,\n    settings: _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default()({}, defaultSettingsXcodeDark, settings),\n    styles: [...xcodeDarkStyle, ...styles]\n  });\n};\nvar xcodeDark = xcodeDarkInit();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/codemirror-theme-xcode/esm/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/codemirror-themes/esm/index.js":
/*!**********************************************************!*\
  !*** ./node_modules/@uiw/codemirror-themes/esm/index.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createTheme: () => (/* binding */ createTheme),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _codemirror_view__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @codemirror/view */ \"(ssr)/./node_modules/@codemirror/view/dist/index.js\");\n/* harmony import */ var _codemirror_language__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @codemirror/language */ \"(ssr)/./node_modules/@codemirror/language/dist/index.js\");\n\n\nvar createTheme = _ref => {\n  var {\n    theme,\n    settings = {},\n    styles = []\n  } = _ref;\n  var themeOptions = {\n    '.cm-gutters': {}\n  };\n  var baseStyle = {};\n  if (settings.background) {\n    baseStyle.backgroundColor = settings.background;\n  }\n  if (settings.backgroundImage) {\n    baseStyle.backgroundImage = settings.backgroundImage;\n  }\n  if (settings.foreground) {\n    baseStyle.color = settings.foreground;\n  }\n  if (settings.fontSize) {\n    baseStyle.fontSize = settings.fontSize;\n  }\n  if (settings.background || settings.foreground) {\n    themeOptions['&'] = baseStyle;\n  }\n  if (settings.fontFamily) {\n    themeOptions['&.cm-editor .cm-scroller'] = {\n      fontFamily: settings.fontFamily\n    };\n  }\n  if (settings.gutterBackground) {\n    themeOptions['.cm-gutters'].backgroundColor = settings.gutterBackground;\n  }\n  if (settings.gutterForeground) {\n    themeOptions['.cm-gutters'].color = settings.gutterForeground;\n  }\n  if (settings.gutterBorder) {\n    themeOptions['.cm-gutters'].borderRightColor = settings.gutterBorder;\n  }\n  if (settings.caret) {\n    themeOptions['.cm-content'] = {\n      caretColor: settings.caret\n    };\n    themeOptions['.cm-cursor, .cm-dropCursor'] = {\n      borderLeftColor: settings.caret\n    };\n  }\n  var activeLineGutterStyle = {};\n  if (settings.gutterActiveForeground) {\n    activeLineGutterStyle.color = settings.gutterActiveForeground;\n  }\n  if (settings.lineHighlight) {\n    themeOptions['.cm-activeLine'] = {\n      backgroundColor: settings.lineHighlight\n    };\n    activeLineGutterStyle.backgroundColor = settings.lineHighlight;\n  }\n  themeOptions['.cm-activeLineGutter'] = activeLineGutterStyle;\n  if (settings.selection) {\n    themeOptions['&.cm-focused .cm-selectionBackground, & .cm-line::selection, & .cm-selectionLayer .cm-selectionBackground, .cm-content ::selection'] = {\n      background: settings.selection + ' !important'\n    };\n  }\n  if (settings.selectionMatch) {\n    themeOptions['& .cm-selectionMatch'] = {\n      backgroundColor: settings.selectionMatch\n    };\n  }\n  var themeExtension = _codemirror_view__WEBPACK_IMPORTED_MODULE_0__.EditorView.theme(themeOptions, {\n    dark: theme === 'dark'\n  });\n  var highlightStyle = _codemirror_language__WEBPACK_IMPORTED_MODULE_1__.HighlightStyle.define(styles);\n  var extension = [themeExtension, (0,_codemirror_language__WEBPACK_IMPORTED_MODULE_1__.syntaxHighlighting)(highlightStyle)];\n  return extension;\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (createTheme);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/codemirror-themes/esm/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-codemirror/esm/getDefaultExtensions.js":
/*!************************************************************************!*\
  !*** ./node_modules/@uiw/react-codemirror/esm/getDefaultExtensions.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   color: () => (/* reexport safe */ _codemirror_theme_one_dark__WEBPACK_IMPORTED_MODULE_2__.color),\n/* harmony export */   defaultLightThemeOption: () => (/* reexport safe */ _theme_light__WEBPACK_IMPORTED_MODULE_1__.defaultLightThemeOption),\n/* harmony export */   getDefaultExtensions: () => (/* binding */ getDefaultExtensions),\n/* harmony export */   oneDark: () => (/* reexport safe */ _codemirror_theme_one_dark__WEBPACK_IMPORTED_MODULE_2__.oneDark),\n/* harmony export */   oneDarkHighlightStyle: () => (/* reexport safe */ _codemirror_theme_one_dark__WEBPACK_IMPORTED_MODULE_2__.oneDarkHighlightStyle),\n/* harmony export */   oneDarkTheme: () => (/* reexport safe */ _codemirror_theme_one_dark__WEBPACK_IMPORTED_MODULE_2__.oneDarkTheme)\n/* harmony export */ });\n/* harmony import */ var _codemirror_commands__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @codemirror/commands */ \"(ssr)/./node_modules/@codemirror/commands/dist/index.js\");\n/* harmony import */ var _uiw_codemirror_extensions_basic_setup__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @uiw/codemirror-extensions-basic-setup */ \"(ssr)/./node_modules/@uiw/codemirror-extensions-basic-setup/esm/index.js\");\n/* harmony import */ var _codemirror_view__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @codemirror/view */ \"(ssr)/./node_modules/@codemirror/view/dist/index.js\");\n/* harmony import */ var _codemirror_theme_one_dark__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @codemirror/theme-one-dark */ \"(ssr)/./node_modules/@codemirror/theme-one-dark/dist/index.js\");\n/* harmony import */ var _codemirror_state__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @codemirror/state */ \"(ssr)/./node_modules/@codemirror/state/dist/index.js\");\n/* harmony import */ var _theme_light__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./theme/light */ \"(ssr)/./node_modules/@uiw/react-codemirror/esm/theme/light.js\");\n\n\n\n\n\n\n\n\nvar getDefaultExtensions = function getDefaultExtensions(optios) {\n  if (optios === void 0) {\n    optios = {};\n  }\n  var {\n    indentWithTab: defaultIndentWithTab = true,\n    editable = true,\n    readOnly = false,\n    theme = 'light',\n    placeholder: placeholderStr = '',\n    basicSetup: defaultBasicSetup = true\n  } = optios;\n  var getExtensions = [];\n  if (defaultIndentWithTab) {\n    getExtensions.unshift(_codemirror_view__WEBPACK_IMPORTED_MODULE_3__.keymap.of([_codemirror_commands__WEBPACK_IMPORTED_MODULE_4__.indentWithTab]));\n  }\n  if (defaultBasicSetup) {\n    if (typeof defaultBasicSetup === 'boolean') {\n      getExtensions.unshift((0,_uiw_codemirror_extensions_basic_setup__WEBPACK_IMPORTED_MODULE_0__.basicSetup)());\n    } else {\n      getExtensions.unshift((0,_uiw_codemirror_extensions_basic_setup__WEBPACK_IMPORTED_MODULE_0__.basicSetup)(defaultBasicSetup));\n    }\n  }\n  if (placeholderStr) {\n    getExtensions.unshift((0,_codemirror_view__WEBPACK_IMPORTED_MODULE_3__.placeholder)(placeholderStr));\n  }\n  switch (theme) {\n    case 'light':\n      getExtensions.push(_theme_light__WEBPACK_IMPORTED_MODULE_1__.defaultLightThemeOption);\n      break;\n    case 'dark':\n      getExtensions.push(_codemirror_theme_one_dark__WEBPACK_IMPORTED_MODULE_2__.oneDark);\n      break;\n    case 'none':\n      break;\n    default:\n      getExtensions.push(theme);\n      break;\n  }\n  if (editable === false) {\n    getExtensions.push(_codemirror_view__WEBPACK_IMPORTED_MODULE_3__.EditorView.editable.of(false));\n  }\n  if (readOnly) {\n    getExtensions.push(_codemirror_state__WEBPACK_IMPORTED_MODULE_5__.EditorState.readOnly.of(true));\n  }\n  return [...getExtensions];\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-codemirror/esm/getDefaultExtensions.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-codemirror/esm/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/@uiw/react-codemirror/esm/index.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Annotation: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.Annotation),\n/* harmony export */   AnnotationType: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.AnnotationType),\n/* harmony export */   BidiSpan: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.BidiSpan),\n/* harmony export */   BlockInfo: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.BlockInfo),\n/* harmony export */   BlockType: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.BlockType),\n/* harmony export */   ChangeDesc: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.ChangeDesc),\n/* harmony export */   ChangeSet: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.ChangeSet),\n/* harmony export */   CharCategory: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.CharCategory),\n/* harmony export */   Compartment: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.Compartment),\n/* harmony export */   Decoration: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.Decoration),\n/* harmony export */   Direction: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.Direction),\n/* harmony export */   EditorSelection: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.EditorSelection),\n/* harmony export */   EditorState: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.EditorState),\n/* harmony export */   EditorView: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.EditorView),\n/* harmony export */   Facet: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.Facet),\n/* harmony export */   GutterMarker: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.GutterMarker),\n/* harmony export */   Line: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.Line),\n/* harmony export */   MapMode: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.MapMode),\n/* harmony export */   MatchDecorator: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.MatchDecorator),\n/* harmony export */   Prec: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.Prec),\n/* harmony export */   Range: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.Range),\n/* harmony export */   RangeSet: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.RangeSet),\n/* harmony export */   RangeSetBuilder: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.RangeSetBuilder),\n/* harmony export */   RangeValue: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.RangeValue),\n/* harmony export */   RectangleMarker: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.RectangleMarker),\n/* harmony export */   SelectionRange: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.SelectionRange),\n/* harmony export */   StateEffect: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.StateEffect),\n/* harmony export */   StateEffectType: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.StateEffectType),\n/* harmony export */   StateField: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.StateField),\n/* harmony export */   Text: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.Text),\n/* harmony export */   Transaction: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.Transaction),\n/* harmony export */   ViewPlugin: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.ViewPlugin),\n/* harmony export */   ViewUpdate: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.ViewUpdate),\n/* harmony export */   WidgetType: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.WidgetType),\n/* harmony export */   __test: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.__test),\n/* harmony export */   basicSetup: () => (/* reexport safe */ _uiw_codemirror_extensions_basic_setup__WEBPACK_IMPORTED_MODULE_7__.basicSetup),\n/* harmony export */   closeHoverTooltips: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.closeHoverTooltips),\n/* harmony export */   codePointAt: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.codePointAt),\n/* harmony export */   codePointSize: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.codePointSize),\n/* harmony export */   color: () => (/* reexport safe */ _getDefaultExtensions__WEBPACK_IMPORTED_MODULE_8__.color),\n/* harmony export */   combineConfig: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.combineConfig),\n/* harmony export */   countColumn: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.countColumn),\n/* harmony export */   crosshairCursor: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.crosshairCursor),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   defaultLightThemeOption: () => (/* reexport safe */ _getDefaultExtensions__WEBPACK_IMPORTED_MODULE_8__.defaultLightThemeOption),\n/* harmony export */   drawSelection: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.drawSelection),\n/* harmony export */   dropCursor: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.dropCursor),\n/* harmony export */   findClusterBreak: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.findClusterBreak),\n/* harmony export */   findColumn: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.findColumn),\n/* harmony export */   fromCodePoint: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.fromCodePoint),\n/* harmony export */   getDefaultExtensions: () => (/* reexport safe */ _getDefaultExtensions__WEBPACK_IMPORTED_MODULE_8__.getDefaultExtensions),\n/* harmony export */   getDrawSelectionConfig: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.getDrawSelectionConfig),\n/* harmony export */   getPanel: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.getPanel),\n/* harmony export */   getStatistics: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_9__.getStatistics),\n/* harmony export */   getTooltip: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.getTooltip),\n/* harmony export */   gutter: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.gutter),\n/* harmony export */   gutterLineClass: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.gutterLineClass),\n/* harmony export */   gutterWidgetClass: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.gutterWidgetClass),\n/* harmony export */   gutters: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.gutters),\n/* harmony export */   hasHoverTooltips: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.hasHoverTooltips),\n/* harmony export */   highlightActiveLine: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.highlightActiveLine),\n/* harmony export */   highlightActiveLineGutter: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.highlightActiveLineGutter),\n/* harmony export */   highlightSpecialChars: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.highlightSpecialChars),\n/* harmony export */   highlightTrailingWhitespace: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.highlightTrailingWhitespace),\n/* harmony export */   highlightWhitespace: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.highlightWhitespace),\n/* harmony export */   hoverTooltip: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.hoverTooltip),\n/* harmony export */   keymap: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.keymap),\n/* harmony export */   layer: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.layer),\n/* harmony export */   lineNumberMarkers: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.lineNumberMarkers),\n/* harmony export */   lineNumberWidgetMarker: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.lineNumberWidgetMarker),\n/* harmony export */   lineNumbers: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.lineNumbers),\n/* harmony export */   logException: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.logException),\n/* harmony export */   minimalSetup: () => (/* reexport safe */ _uiw_codemirror_extensions_basic_setup__WEBPACK_IMPORTED_MODULE_7__.minimalSetup),\n/* harmony export */   oneDark: () => (/* reexport safe */ _getDefaultExtensions__WEBPACK_IMPORTED_MODULE_8__.oneDark),\n/* harmony export */   oneDarkHighlightStyle: () => (/* reexport safe */ _getDefaultExtensions__WEBPACK_IMPORTED_MODULE_8__.oneDarkHighlightStyle),\n/* harmony export */   oneDarkTheme: () => (/* reexport safe */ _getDefaultExtensions__WEBPACK_IMPORTED_MODULE_8__.oneDarkTheme),\n/* harmony export */   panels: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.panels),\n/* harmony export */   placeholder: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.placeholder),\n/* harmony export */   rectangularSelection: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.rectangularSelection),\n/* harmony export */   repositionTooltips: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.repositionTooltips),\n/* harmony export */   runScopeHandlers: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.runScopeHandlers),\n/* harmony export */   scrollPastEnd: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.scrollPastEnd),\n/* harmony export */   showPanel: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.showPanel),\n/* harmony export */   showTooltip: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.showTooltip),\n/* harmony export */   tooltips: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.tooltips),\n/* harmony export */   useCodeMirror: () => (/* reexport safe */ _useCodeMirror__WEBPACK_IMPORTED_MODULE_3__.useCodeMirror)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _babel_runtime_helpers_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/objectWithoutPropertiesLoose */ \"(ssr)/./node_modules/@babel/runtime/helpers/objectWithoutPropertiesLoose.js\");\n/* harmony import */ var _babel_runtime_helpers_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _useCodeMirror__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./useCodeMirror */ \"(ssr)/./node_modules/@uiw/react-codemirror/esm/useCodeMirror.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _codemirror_view__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @codemirror/view */ \"(ssr)/./node_modules/@codemirror/view/dist/index.js\");\n/* harmony import */ var _codemirror_state__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @codemirror/state */ \"(ssr)/./node_modules/@codemirror/state/dist/index.js\");\n/* harmony import */ var _uiw_codemirror_extensions_basic_setup__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @uiw/codemirror-extensions-basic-setup */ \"(ssr)/./node_modules/@uiw/codemirror-extensions-basic-setup/esm/index.js\");\n/* harmony import */ var _getDefaultExtensions__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./getDefaultExtensions */ \"(ssr)/./node_modules/@uiw/react-codemirror/esm/getDefaultExtensions.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/@uiw/react-codemirror/esm/utils.js\");\n\n\nvar _excluded = [\"className\", \"value\", \"selection\", \"extensions\", \"onChange\", \"onStatistics\", \"onCreateEditor\", \"onUpdate\", \"autoFocus\", \"theme\", \"height\", \"minHeight\", \"maxHeight\", \"width\", \"minWidth\", \"maxWidth\", \"basicSetup\", \"placeholder\", \"indentWithTab\", \"editable\", \"readOnly\", \"root\", \"initialState\"];\n\n\n\n\n\n\n\n\n\nvar ReactCodeMirror = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_2__.forwardRef)((props, ref) => {\n  var {\n      className,\n      value = '',\n      selection,\n      extensions = [],\n      onChange,\n      onStatistics,\n      onCreateEditor,\n      onUpdate,\n      autoFocus,\n      theme = 'light',\n      height,\n      minHeight,\n      maxHeight,\n      width,\n      minWidth,\n      maxWidth,\n      basicSetup,\n      placeholder,\n      indentWithTab,\n      editable,\n      readOnly,\n      root,\n      initialState\n    } = props,\n    other = _babel_runtime_helpers_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1___default()(props, _excluded);\n  var editor = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n  var {\n    state,\n    view,\n    container,\n    setContainer\n  } = (0,_useCodeMirror__WEBPACK_IMPORTED_MODULE_3__.useCodeMirror)({\n    root,\n    value,\n    autoFocus,\n    theme,\n    height,\n    minHeight,\n    maxHeight,\n    width,\n    minWidth,\n    maxWidth,\n    basicSetup,\n    placeholder,\n    indentWithTab,\n    editable,\n    readOnly,\n    selection,\n    onChange,\n    onStatistics,\n    onCreateEditor,\n    onUpdate,\n    extensions,\n    initialState\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_2__.useImperativeHandle)(ref, () => ({\n    editor: editor.current,\n    state: state,\n    view: view\n  }), [editor, container, state, view]);\n  var setEditorRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(el => {\n    editor.current = el;\n    setContainer(el);\n  }, [setContainer]);\n\n  // check type of value\n  if (typeof value !== 'string') {\n    throw new Error(\"value must be typeof string but got \" + typeof value);\n  }\n  var defaultClassNames = typeof theme === 'string' ? \"cm-theme-\" + theme : 'cm-theme';\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(\"div\", _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default()({\n    ref: setEditorRef,\n    className: \"\" + defaultClassNames + (className ? \" \" + className : '')\n  }, other));\n});\nReactCodeMirror.displayName = 'CodeMirror';\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ReactCodeMirror);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-codemirror/esm/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-codemirror/esm/theme/light.js":
/*!***************************************************************!*\
  !*** ./node_modules/@uiw/react-codemirror/esm/theme/light.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultLightThemeOption: () => (/* binding */ defaultLightThemeOption)\n/* harmony export */ });\n/* harmony import */ var _codemirror_view__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @codemirror/view */ \"(ssr)/./node_modules/@codemirror/view/dist/index.js\");\n\nvar defaultLightThemeOption = _codemirror_view__WEBPACK_IMPORTED_MODULE_0__.EditorView.theme({\n  '&': {\n    backgroundColor: '#fff'\n  }\n}, {\n  dark: false\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHVpdy9yZWFjdC1jb2RlbWlycm9yL2VzbS90aGVtZS9saWdodC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUE4QztBQUN2Qyw4QkFBOEIsd0RBQVU7QUFDL0M7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0EsQ0FBQyIsInNvdXJjZXMiOlsiRDpcXHNvZnRcXFNlYVByb2plY3RcXGdpdGh1Yl9vcGVuX3Byb2plY3Rfc3VjY2Vzc1xcc3VuYVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcQHVpd1xccmVhY3QtY29kZW1pcnJvclxcZXNtXFx0aGVtZVxcbGlnaHQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgRWRpdG9yVmlldyB9IGZyb20gJ0Bjb2RlbWlycm9yL3ZpZXcnO1xuZXhwb3J0IHZhciBkZWZhdWx0TGlnaHRUaGVtZU9wdGlvbiA9IEVkaXRvclZpZXcudGhlbWUoe1xuICAnJic6IHtcbiAgICBiYWNrZ3JvdW5kQ29sb3I6ICcjZmZmJ1xuICB9XG59LCB7XG4gIGRhcms6IGZhbHNlXG59KTsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-codemirror/esm/theme/light.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-codemirror/esm/useCodeMirror.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@uiw/react-codemirror/esm/useCodeMirror.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCodeMirror: () => (/* binding */ useCodeMirror)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _codemirror_state__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @codemirror/state */ \"(ssr)/./node_modules/@codemirror/state/dist/index.js\");\n/* harmony import */ var _codemirror_view__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @codemirror/view */ \"(ssr)/./node_modules/@codemirror/view/dist/index.js\");\n/* harmony import */ var _getDefaultExtensions__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getDefaultExtensions */ \"(ssr)/./node_modules/@uiw/react-codemirror/esm/getDefaultExtensions.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/@uiw/react-codemirror/esm/utils.js\");\n\n\n\n\n\nvar External = _codemirror_state__WEBPACK_IMPORTED_MODULE_3__.Annotation.define();\nvar emptyExtensions = [];\nfunction useCodeMirror(props) {\n  var {\n    value,\n    selection,\n    onChange,\n    onStatistics,\n    onCreateEditor,\n    onUpdate,\n    extensions = emptyExtensions,\n    autoFocus,\n    theme = 'light',\n    height = null,\n    minHeight = null,\n    maxHeight = null,\n    width = null,\n    minWidth = null,\n    maxWidth = null,\n    placeholder: placeholderStr = '',\n    editable = true,\n    readOnly = false,\n    indentWithTab: defaultIndentWithTab = true,\n    basicSetup: defaultBasicSetup = true,\n    root,\n    initialState\n  } = props;\n  var [container, setContainer] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  var [view, setView] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  var [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  var defaultThemeOption = _codemirror_view__WEBPACK_IMPORTED_MODULE_4__.EditorView.theme({\n    '&': {\n      height,\n      minHeight,\n      maxHeight,\n      width,\n      minWidth,\n      maxWidth\n    },\n    '& .cm-scroller': {\n      height: '100% !important'\n    }\n  });\n  var updateListener = _codemirror_view__WEBPACK_IMPORTED_MODULE_4__.EditorView.updateListener.of(vu => {\n    if (vu.docChanged && typeof onChange === 'function' &&\n    // Fix echoing of the remote changes:\n    // If transaction is market as remote we don't have to call `onChange` handler again\n    !vu.transactions.some(tr => tr.annotation(External))) {\n      var doc = vu.state.doc;\n      var _value = doc.toString();\n      onChange(_value, vu);\n    }\n    onStatistics && onStatistics((0,_utils__WEBPACK_IMPORTED_MODULE_2__.getStatistics)(vu));\n  });\n  var defaultExtensions = (0,_getDefaultExtensions__WEBPACK_IMPORTED_MODULE_1__.getDefaultExtensions)({\n    theme,\n    editable,\n    readOnly,\n    placeholder: placeholderStr,\n    indentWithTab: defaultIndentWithTab,\n    basicSetup: defaultBasicSetup\n  });\n  var getExtensions = [updateListener, defaultThemeOption, ...defaultExtensions];\n  if (onUpdate && typeof onUpdate === 'function') {\n    getExtensions.push(_codemirror_view__WEBPACK_IMPORTED_MODULE_4__.EditorView.updateListener.of(onUpdate));\n  }\n  getExtensions = getExtensions.concat(extensions);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(() => {\n    if (container && !state) {\n      var config = {\n        doc: value,\n        selection,\n        extensions: getExtensions\n      };\n      var stateCurrent = initialState ? _codemirror_state__WEBPACK_IMPORTED_MODULE_3__.EditorState.fromJSON(initialState.json, config, initialState.fields) : _codemirror_state__WEBPACK_IMPORTED_MODULE_3__.EditorState.create(config);\n      setState(stateCurrent);\n      if (!view) {\n        var viewCurrent = new _codemirror_view__WEBPACK_IMPORTED_MODULE_4__.EditorView({\n          state: stateCurrent,\n          parent: container,\n          root\n        });\n        setView(viewCurrent);\n        onCreateEditor && onCreateEditor(viewCurrent, stateCurrent);\n      }\n    }\n    return () => {\n      if (view) {\n        setState(undefined);\n        setView(undefined);\n      }\n    };\n  }, [container, state]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (props.container) {\n      setContainer(props.container);\n    }\n  }, [props.container]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => () => {\n    if (view) {\n      view.destroy();\n      setView(undefined);\n    }\n  }, [view]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (autoFocus && view) {\n      view.focus();\n    }\n  }, [autoFocus, view]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (view) {\n      view.dispatch({\n        effects: _codemirror_state__WEBPACK_IMPORTED_MODULE_3__.StateEffect.reconfigure.of(getExtensions)\n      });\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [theme, extensions, height, minHeight, maxHeight, width, minWidth, maxWidth, placeholderStr, editable, readOnly, defaultIndentWithTab, defaultBasicSetup, onChange, onUpdate]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (value === undefined) {\n      return;\n    }\n    var currentValue = view ? view.state.doc.toString() : '';\n    if (view && value !== currentValue) {\n      view.dispatch({\n        changes: {\n          from: 0,\n          to: currentValue.length,\n          insert: value || ''\n        },\n        annotations: [External.of(true)]\n      });\n    }\n  }, [value, view]);\n  return {\n    state,\n    setState,\n    view,\n    setView,\n    container,\n    setContainer\n  };\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-codemirror/esm/useCodeMirror.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-codemirror/esm/utils.js":
/*!*********************************************************!*\
  !*** ./node_modules/@uiw/react-codemirror/esm/utils.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getStatistics: () => (/* binding */ getStatistics)\n/* harmony export */ });\nvar getStatistics = view => {\n  return {\n    line: view.state.doc.lineAt(view.state.selection.main.from),\n    lineCount: view.state.doc.lines,\n    lineBreak: view.state.lineBreak,\n    length: view.state.doc.length,\n    readOnly: view.state.readOnly,\n    tabSize: view.state.tabSize,\n    selection: view.state.selection,\n    selectionAsSingle: view.state.selection.asSingle().main,\n    ranges: view.state.selection.ranges,\n    selectionCode: view.state.sliceDoc(view.state.selection.main.from, view.state.selection.main.to),\n    selections: view.state.selection.ranges.map(r => view.state.sliceDoc(r.from, r.to)),\n    selectedText: view.state.selection.ranges.some(r => !r.empty)\n  };\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHVpdy9yZWFjdC1jb2RlbWlycm9yL2VzbS91dGlscy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxzb2Z0XFxTZWFQcm9qZWN0XFxnaXRodWJfb3Blbl9wcm9qZWN0X3N1Y2Nlc3NcXHN1bmFcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXEB1aXdcXHJlYWN0LWNvZGVtaXJyb3JcXGVzbVxcdXRpbHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHZhciBnZXRTdGF0aXN0aWNzID0gdmlldyA9PiB7XG4gIHJldHVybiB7XG4gICAgbGluZTogdmlldy5zdGF0ZS5kb2MubGluZUF0KHZpZXcuc3RhdGUuc2VsZWN0aW9uLm1haW4uZnJvbSksXG4gICAgbGluZUNvdW50OiB2aWV3LnN0YXRlLmRvYy5saW5lcyxcbiAgICBsaW5lQnJlYWs6IHZpZXcuc3RhdGUubGluZUJyZWFrLFxuICAgIGxlbmd0aDogdmlldy5zdGF0ZS5kb2MubGVuZ3RoLFxuICAgIHJlYWRPbmx5OiB2aWV3LnN0YXRlLnJlYWRPbmx5LFxuICAgIHRhYlNpemU6IHZpZXcuc3RhdGUudGFiU2l6ZSxcbiAgICBzZWxlY3Rpb246IHZpZXcuc3RhdGUuc2VsZWN0aW9uLFxuICAgIHNlbGVjdGlvbkFzU2luZ2xlOiB2aWV3LnN0YXRlLnNlbGVjdGlvbi5hc1NpbmdsZSgpLm1haW4sXG4gICAgcmFuZ2VzOiB2aWV3LnN0YXRlLnNlbGVjdGlvbi5yYW5nZXMsXG4gICAgc2VsZWN0aW9uQ29kZTogdmlldy5zdGF0ZS5zbGljZURvYyh2aWV3LnN0YXRlLnNlbGVjdGlvbi5tYWluLmZyb20sIHZpZXcuc3RhdGUuc2VsZWN0aW9uLm1haW4udG8pLFxuICAgIHNlbGVjdGlvbnM6IHZpZXcuc3RhdGUuc2VsZWN0aW9uLnJhbmdlcy5tYXAociA9PiB2aWV3LnN0YXRlLnNsaWNlRG9jKHIuZnJvbSwgci50bykpLFxuICAgIHNlbGVjdGVkVGV4dDogdmlldy5zdGF0ZS5zZWxlY3Rpb24ucmFuZ2VzLnNvbWUociA9PiAhci5lbXB0eSlcbiAgfTtcbn07Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-codemirror/esm/utils.js\n");

/***/ })

};
;