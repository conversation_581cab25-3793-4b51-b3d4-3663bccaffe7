"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_purescript_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/purescript.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/purescript.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"PureScript\\\",\\\"fileTypes\\\":[\\\"purs\\\"],\\\"name\\\":\\\"purescript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#module_declaration\\\"},{\\\"include\\\":\\\"#module_import\\\"},{\\\"include\\\":\\\"#type_synonym_declaration\\\"},{\\\"include\\\":\\\"#data_type_declaration\\\"},{\\\"include\\\":\\\"#typeclass_declaration\\\"},{\\\"include\\\":\\\"#instance_declaration\\\"},{\\\"include\\\":\\\"#derive_declaration\\\"},{\\\"include\\\":\\\"#infix_op_declaration\\\"},{\\\"include\\\":\\\"#foreign_import_data\\\"},{\\\"include\\\":\\\"#foreign_import\\\"},{\\\"include\\\":\\\"#function_type_declaration\\\"},{\\\"include\\\":\\\"#typed_hole\\\"},{\\\"include\\\":\\\"#keywords_orphan\\\"},{\\\"include\\\":\\\"#control_keywords\\\"},{\\\"include\\\":\\\"#function_infix\\\"},{\\\"include\\\":\\\"#data_ctor\\\"},{\\\"include\\\":\\\"#infix_op\\\"},{\\\"include\\\":\\\"#constants_numeric_decimal\\\"},{\\\"include\\\":\\\"#constant_numeric\\\"},{\\\"include\\\":\\\"#constant_boolean\\\"},{\\\"include\\\":\\\"#string_triple_quoted\\\"},{\\\"include\\\":\\\"#string_single_quoted\\\"},{\\\"include\\\":\\\"#string_double_quoted\\\"},{\\\"include\\\":\\\"#markup_newline\\\"},{\\\"include\\\":\\\"#double_colon_parens\\\"},{\\\"include\\\":\\\"#double_colon_inlined\\\"},{\\\"include\\\":\\\"#double_colon_orphan\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"match\\\":\\\"<-|->\\\",\\\"name\\\":\\\"keyword.other.arrow.purescript\\\"},{\\\"match\\\":\\\"[[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']]+\\\",\\\"name\\\":\\\"keyword.operator.purescript\\\"},{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.separator.comma.purescript\\\"}],\\\"repository\\\":{\\\"block_comment\\\":{\\\"patterns\\\":[{\\\"applyEndPatternLast\\\":1,\\\"begin\\\":\\\"\\\\\\\\{-\\\\\\\\s*\\\\\\\\|\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.documentation.purescript\\\"}},\\\"end\\\":\\\"-}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.documentation.purescript\\\"}},\\\"name\\\":\\\"comment.block.documentation.purescript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block_comment\\\"}]},{\\\"applyEndPatternLast\\\":1,\\\"begin\\\":\\\"\\\\\\\\{-\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.purescript\\\"}},\\\"end\\\":\\\"-}\\\",\\\"name\\\":\\\"comment.block.purescript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block_comment\\\"}]}]},\\\"characters\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.character.escape.purescript\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.character.escape.octal.purescript\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.character.escape.hexadecimal.purescript\\\"},\\\"4\\\":{\\\"name\\\":\\\"constant.character.escape.control.purescript\\\"}},\\\"match\\\":\\\"(?:[ -\\\\\\\\[\\\\\\\\]-~]|(\\\\\\\\\\\\\\\\(?:NUL|SOH|STX|ETX|EOT|ENQ|ACK|BEL|BS|HT|LF|VT|FF|CR|SO|SI|DLE|DC1|DC2|DC3|DC4|NAK|SYN|ETB|CAN|EM|SUB|ESC|FS|GS|RS|US|SP|DEL|[abfnrtv\\\\\\\\\\\\\\\\\\\\\\\"'\\\\\\\\&]))|(\\\\\\\\\\\\\\\\o[0-7]+)|(\\\\\\\\\\\\\\\\x\\\\\\\\h+)|(\\\\\\\\^[A-Z@\\\\\\\\[\\\\\\\\]\\\\\\\\\\\\\\\\^_]))\\\"}]},\\\"class_constraint\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*(?:\\\\\\\\.[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)*\\\",\\\"name\\\":\\\"entity.name.type.purescript\\\"}]},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type_name\\\"},{\\\"include\\\":\\\"#generic_type\\\"}]}},\\\"match\\\":\\\"([\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*(?:\\\\\\\\.[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)*)\\\\\\\\s+(?<classConstraint>(?:[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*(?:\\\\\\\\.[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)*|(?:[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*(?:\\\\\\\\.[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)*\\\\\\\\.)?[\\\\\\\\p{Ll}_][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)(?:\\\\\\\\s*\\\\\\\\s+\\\\\\\\s*(?:[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*(?:\\\\\\\\.[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)*|(?:[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*(?:\\\\\\\\.[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)*\\\\\\\\.)?[\\\\\\\\p{Ll}_][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*))*)\\\",\\\"name\\\":\\\"meta.class-constraint.purescript\\\"}]},\\\"comments\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(^[ \\\\\\\\t]+)?(?=--+)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.comment.leading.purescript\\\"}},\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"--\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.purescript\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.line.double-dash.purescript\\\"}]},{\\\"include\\\":\\\"#block_comment\\\"}]},\\\"constant_boolean\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(true|false)(?!')\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.boolean.purescript\\\"}]},\\\"constant_numeric\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(([0-9]+_?)*[0-9]+|0([xX]\\\\\\\\h+|[oO][0-7]+))\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.purescript\\\"}]},\\\"constants_numeric_decimal\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"constant.numeric.decimal.purescript\\\"},\\\"1\\\":{\\\"name\\\":\\\"meta.delimiter.decimal.period.purescript\\\"},\\\"2\\\":{\\\"name\\\":\\\"meta.delimiter.decimal.period.purescript\\\"},\\\"3\\\":{\\\"name\\\":\\\"meta.delimiter.decimal.period.purescript\\\"},\\\"4\\\":{\\\"name\\\":\\\"meta.delimiter.decimal.period.purescript\\\"},\\\"5\\\":{\\\"name\\\":\\\"meta.delimiter.decimal.period.purescript\\\"},\\\"6\\\":{\\\"name\\\":\\\"meta.delimiter.decimal.period.purescript\\\"}},\\\"match\\\":\\\"(?<!\\\\\\\\$)\\\\\\\\b(?:[0-9]+(\\\\\\\\.)[0-9]+[eE][+-]?[0-9]+\\\\\\\\b|[0-9]+[eE][+-]?[0-9]+\\\\\\\\b|[0-9]+(\\\\\\\\.)[0-9]+\\\\\\\\b|[0-9]+\\\\\\\\b(?!\\\\\\\\.))(?!\\\\\\\\$)\\\",\\\"name\\\":\\\"constant.numeric.decimal.purescript\\\"}]},\\\"control_keywords\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(do|ado|if|then|else|case|of|let|in)(?!('|\\\\\\\\s*([:=])))\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.purescript\\\"}]},\\\"data_ctor\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*(?:\\\\\\\\.[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)*\\\",\\\"name\\\":\\\"entity.name.tag.purescript\\\"}]},\\\"data_type_declaration\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^(\\\\\\\\s)*(data|newtype)\\\\\\\\s+(.+?)\\\\\\\\s*(?==|$)\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"storage.type.data.purescript\\\"},\\\"3\\\":{\\\"name\\\":\\\"meta.type-signature.purescript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type_signature\\\"}]}},\\\"end\\\":\\\"^(?!\\\\\\\\1[ \\\\\\\\t]|[ \\\\\\\\t]*$)\\\",\\\"name\\\":\\\"meta.declaration.type.data.purescript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"captures\\\":{\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#data_ctor\\\"}]}},\\\"match\\\":\\\"(?<=([|=])\\\\\\\\s*)([\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)\\\"},{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.pipe.purescript\\\"}},\\\"match\\\":\\\"\\\\\\\\|\\\"},{\\\"include\\\":\\\"#record_types\\\"},{\\\"include\\\":\\\"#type_signature\\\"}]}]},\\\"derive_declaration\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*\\\\\\\\b(derive)(\\\\\\\\s+newtype)?(\\\\\\\\s+instance)?(?!')\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.purescript\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.purescript\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.other.purescript\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.other.purescript\\\"}},\\\"contentName\\\":\\\"meta.type-signature.purescript\\\",\\\"end\\\":\\\"^(?=\\\\\\\\S)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.purescript\\\"}},\\\"name\\\":\\\"meta.declaration.derive.purescript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type_signature\\\"}]}]},\\\"double_colon\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?:::|∷)\\\",\\\"name\\\":\\\"keyword.other.double-colon.purescript\\\"}]},\\\"double_colon_inlined\\\":{\\\"patterns\\\":[{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.double-colon.purescript\\\"},\\\"2\\\":{\\\"name\\\":\\\"meta.type-signature.purescript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type_signature\\\"}]}},\\\"match\\\":\\\"((?:::|∷))(.*)(?=<-|\\\\\\\"\\\\\\\"\\\\\\\")\\\"}]},{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.double-colon.purescript\\\"},\\\"2\\\":{\\\"name\\\":\\\"meta.type-signature.purescript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type_signature\\\"}]}},\\\"match\\\":\\\"((?:::|∷))(.*)\\\"}]}]},\\\"double_colon_orphan\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(\\\\\\\\s*)(::|∷)(\\\\\\\\s*)$\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"keyword.other.double-colon.purescript\\\"}},\\\"end\\\":\\\"^(?!\\\\\\\\1[ \\\\\\\\t]*|[ \\\\\\\\t]*$)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type_signature\\\"}]}]},\\\"double_colon_parens\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.double-colon.purescript\\\"},\\\"3\\\":{\\\"name\\\":\\\"meta.type-signature.purescript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type_signature\\\"}]}},\\\"match\\\":\\\"\\\\\\\\((?<paren>(?:[^()]|\\\\\\\\(\\\\\\\\g<paren>\\\\\\\\))*)(::|∷)(?<paren2>(?:[^()]|\\\\\\\\(\\\\\\\\g<paren2>\\\\\\\\))*)\\\\\\\\)\\\"}]},\\\"foreign_import\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^(\\\\\\\\s*)(foreign)\\\\\\\\s+(import)\\\\\\\\s+([\\\\\\\\p{Ll}_][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"keyword.other.purescript\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.other.purescript\\\"},\\\"4\\\":{\\\"name\\\":\\\"entity.name.function.purescript\\\"}},\\\"contentName\\\":\\\"meta.type-signature.purescript\\\",\\\"end\\\":\\\"^(?!\\\\\\\\1[ \\\\\\\\t]|[ \\\\\\\\t]*$)\\\",\\\"name\\\":\\\"meta.foreign.purescript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#double_colon\\\"},{\\\"include\\\":\\\"#type_signature\\\"},{\\\"include\\\":\\\"#record_types\\\"}]}]},\\\"foreign_import_data\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^(\\\\\\\\s*)(foreign)\\\\\\\\s+(import)\\\\\\\\s+(data)(?:\\\\\\\\s+([\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)\\\\\\\\s*((?:::|∷)))?\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"keyword.other.purescript\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.other.purescript\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.other.purescript\\\"},\\\"5\\\":{\\\"name\\\":\\\"entity.name.type.purescript\\\"},\\\"6\\\":{\\\"name\\\":\\\"keyword.other.double-colon.purescript\\\"}},\\\"contentName\\\":\\\"meta.kind-signature.purescript\\\",\\\"end\\\":\\\"^(?!\\\\\\\\1[ \\\\\\\\t]|[ \\\\\\\\t]*$)\\\",\\\"name\\\":\\\"meta.foreign.data.purescript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#type_signature\\\"},{\\\"include\\\":\\\"#record_types\\\"}]}]},\\\"function_infix\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.entity.purescript\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.entity.purescript\\\"}},\\\"match\\\":\\\"(`)(?:[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*(?:\\\\\\\\.[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)*\\\\\\\\.)?[\\\\\\\\p{Ll}_][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*(`)\\\",\\\"name\\\":\\\"keyword.operator.function.infix.purescript\\\"}]},\\\"function_type_declaration\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^(\\\\\\\\s*)([\\\\\\\\p{Ll}_][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)\\\\\\\\s*(::|∷)(?!.*<-)\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.purescript\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.other.double-colon.purescript\\\"}},\\\"contentName\\\":\\\"meta.type-signature.purescript\\\",\\\"end\\\":\\\"^(?!\\\\\\\\1[ \\\\\\\\t]|[ \\\\\\\\t]*$)\\\",\\\"name\\\":\\\"meta.function.type-declaration.purescript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#double_colon\\\"},{\\\"include\\\":\\\"#type_signature\\\"},{\\\"include\\\":\\\"#record_types\\\"},{\\\"include\\\":\\\"#row_types\\\"}]}]},\\\"generic_type\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(?:[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*(?:\\\\\\\\.[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)*\\\\\\\\.)?[\\\\\\\\p{Ll}_][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*\\\",\\\"name\\\":\\\"variable.other.generic-type.purescript\\\"}]},\\\"infix_op\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\((?!--+\\\\\\\\))[[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']]+\\\\\\\\)\\\",\\\"name\\\":\\\"entity.name.function.infix.purescript\\\"}]},\\\"infix_op_declaration\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\b(infix[l|r]?)(?!')\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.purescript\\\"}},\\\"end\\\":\\\"($)\\\",\\\"name\\\":\\\"meta.infix.declaration.purescript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#data_ctor\\\"},{\\\"match\\\":\\\"\\\\\\\\d+\\\",\\\"name\\\":\\\"constant.numeric.purescript\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.purescript\\\"}},\\\"match\\\":\\\"([[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']]+)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.purescript\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.type.purescript\\\"}},\\\"match\\\":\\\"\\\\\\\\b(type)\\\\\\\\s+([\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*(?:\\\\\\\\.[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)*)\\\\\\\\b\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.purescript\\\"}},\\\"match\\\":\\\"\\\\\\\\b(as|type)\\\\\\\\b\\\"}]}]},\\\"instance_declaration\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*\\\\\\\\b(else\\\\\\\\s+)?(newtype\\\\\\\\s+)?(instance)(?!')\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.purescript\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.purescript\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.other.purescript\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.other.purescript\\\"}},\\\"contentName\\\":\\\"meta.type-signature.purescript\\\",\\\"end\\\":\\\"(\\\\\\\\bwhere\\\\\\\\b|(?=^\\\\\\\\S))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.purescript\\\"}},\\\"name\\\":\\\"meta.declaration.instance.purescript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type_signature\\\"}]}]},\\\"keywords_orphan\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"^\\\\\\\\s*\\\\\\\\b(derive|where|data|type|newtype|foreign(\\\\\\\\s+import)?(\\\\\\\\s+data)?)(?!')\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.purescript\\\"}]},\\\"kind_signature\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\*\\\",\\\"name\\\":\\\"keyword.other.star.purescript\\\"},{\\\"match\\\":\\\"!\\\",\\\"name\\\":\\\"keyword.other.exclaimation-point.purescript\\\"},{\\\"match\\\":\\\"#\\\",\\\"name\\\":\\\"keyword.other.pound-sign.purescript\\\"},{\\\"match\\\":\\\"->|→\\\",\\\"name\\\":\\\"keyword.other.arrow.purescript\\\"}]},\\\"markup_newline\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\$\\\",\\\"name\\\":\\\"markup.other.escape.newline.purescript\\\"}]},\\\"module_declaration\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*\\\\\\\\b(module)(?!')\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.purescript\\\"}},\\\"end\\\":\\\"(\\\\\\\\bwhere\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.purescript\\\"}},\\\"name\\\":\\\"meta.declaration.module.purescript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#module_name\\\"},{\\\"include\\\":\\\"#module_exports\\\"},{\\\"match\\\":\\\"[a-z]+\\\",\\\"name\\\":\\\"invalid.purescript\\\"}]}]},\\\"module_exports\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"name\\\":\\\"meta.declaration.exports.purescript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?:[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*(?:\\\\\\\\.[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)*\\\\\\\\.)?[\\\\\\\\p{Ll}_][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*\\\",\\\"name\\\":\\\"entity.name.function.purescript\\\"},{\\\"include\\\":\\\"#type_name\\\"},{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.separator.comma.purescript\\\"},{\\\"include\\\":\\\"#infix_op\\\"},{\\\"match\\\":\\\"\\\\\\\\(.*?\\\\\\\\)\\\",\\\"name\\\":\\\"meta.other.constructor-list.purescript\\\"}]}]},\\\"module_import\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*\\\\\\\\b(import)(?!')\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.purescript\\\"}},\\\"end\\\":\\\"^(?=\\\\\\\\S)\\\",\\\"name\\\":\\\"meta.import.purescript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#module_name\\\"},{\\\"include\\\":\\\"#string_double_quoted\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#module_exports\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.purescript\\\"}},\\\"match\\\":\\\"\\\\\\\\b(as|hiding)\\\\\\\\b\\\"}]}]},\\\"module_name\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?:[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*(?:\\\\\\\\.[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)*\\\\\\\\.)*[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*(?:\\\\\\\\.[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)*\\\\\\\\.?\\\",\\\"name\\\":\\\"support.other.module.purescript\\\"}]},\\\"record_field_declaration\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"([ ,]\\\\\\\"(?:[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*(?:\\\\\\\\.[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)*|[\\\\\\\\p{Ll}_][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)\\\\\\\"|[\\\\\\\\p{Ll}_][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)\\\\\\\\s*(::|∷)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?:[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*(?:\\\\\\\\.[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)*\\\\\\\\.)?[\\\\\\\\p{Ll}_][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*\\\",\\\"name\\\":\\\"entity.other.attribute-name.purescript\\\"},{\\\"match\\\":\\\"\\\\\\\"([\\\\\\\\p{Ll}_][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*|[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)\\\\\\\"\\\",\\\"name\\\":\\\"string.quoted.double.purescript\\\"}]},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.double-colon.purescript\\\"}},\\\"contentName\\\":\\\"meta.type-signature.purescript\\\",\\\"end\\\":\\\"(?=([ ,]\\\\\\\"(?:[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*(?:\\\\\\\\.[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)*|[\\\\\\\\p{Ll}_][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)\\\\\\\"|[\\\\\\\\p{Ll}_][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)\\\\\\\\s*(::|∷)|}| \\\\\\\\)|^(?!\\\\\\\\1[ \\\\\\\\t]|[ \\\\\\\\t]*$))\\\",\\\"name\\\":\\\"meta.record-field.type-declaration.purescript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#record_types\\\"},{\\\"include\\\":\\\"#type_signature\\\"},{\\\"include\\\":\\\"#comments\\\"}]}]},\\\"record_types\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\{(?!-)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.type.record.begin.purescript\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.type.record.end.purescript\\\"}},\\\"name\\\":\\\"meta.type.record.purescript\\\",\\\"patterns\\\":[{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.separator.comma.purescript\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#record_field_declaration\\\"},{\\\"include\\\":\\\"#type_signature\\\"}]}]},\\\"row_types\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\((?=\\\\\\\\s*([\\\\\\\\p{Ll}_][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*|\\\\\\\"[\\\\\\\\p{Ll}_][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*\\\\\\\"|\\\\\\\"[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*\\\\\\\")\\\\\\\\s*(::|∷))\\\",\\\"end\\\":\\\"(?=^\\\\\\\\S)\\\",\\\"name\\\":\\\"meta.type.row.purescript\\\",\\\"patterns\\\":[{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.separator.comma.purescript\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#record_field_declaration\\\"},{\\\"include\\\":\\\"#type_signature\\\"}]}]},\\\"string_double_quoted\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.purescript\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.purescript\\\"}},\\\"name\\\":\\\"string.quoted.double.purescript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#characters\\\"},{\\\"begin\\\":\\\"\\\\\\\\\\\\\\\\\\\\\\\\s\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.other.escape.newline.begin.purescript\\\"}},\\\"end\\\":\\\"\\\\\\\\\\\\\\\\\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.other.escape.newline.end.purescript\\\"}},\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\S+\\\",\\\"name\\\":\\\"invalid.illegal.character-not-allowed-here.purescript\\\"}]}]}]},\\\"string_single_quoted\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.purescript\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#characters\\\"}]},\\\"7\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.purescript\\\"}},\\\"match\\\":\\\"(')((?:[ -\\\\\\\\[\\\\\\\\]-~]|(\\\\\\\\\\\\\\\\(?:NUL|SOH|STX|ETX|EOT|ENQ|ACK|BEL|BS|HT|LF|VT|FF|CR|SO|SI|DLE|DC1|DC2|DC3|DC4|NAK|SYN|ETB|CAN|EM|SUB|ESC|FS|GS|RS|US|SP|DEL|[abfnrtv\\\\\\\\\\\\\\\\\\\\\\\"'\\\\\\\\&]))|(\\\\\\\\\\\\\\\\o[0-7]+)|(\\\\\\\\\\\\\\\\x\\\\\\\\h+)|(\\\\\\\\^[A-Z@\\\\\\\\[\\\\\\\\]\\\\\\\\\\\\\\\\^_])))(')\\\",\\\"name\\\":\\\"string.quoted.single.purescript\\\"}]},\\\"string_triple_quoted\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\"\\\\\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.purescript\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\\\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.purescript\\\"}},\\\"name\\\":\\\"string.quoted.triple.purescript\\\"}]},\\\"type_kind_signature\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^(data|newtype)\\\\\\\\s+([\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)\\\\\\\\s*((?:::|∷))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.data.purescript\\\"},\\\"2\\\":{\\\"name\\\":\\\"meta.type-signature.purescript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type_signature\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"keyword.other.double-colon.purescript\\\"}},\\\"end\\\":\\\"(?=^\\\\\\\\S)\\\",\\\"name\\\":\\\"meta.declaration.type.data.signature.purescript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type_signature\\\"},{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.assignment.purescript\\\"}},\\\"match\\\":\\\"=\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#data_ctor\\\"}]},\\\"2\\\":{\\\"name\\\":\\\"meta.type-signature.purescript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type_signature\\\"}]}},\\\"match\\\":\\\"\\\\\\\\b([\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*(?:\\\\\\\\.[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)*)\\\\\\\\s+(?<ctorArgs>(?:[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*(?:\\\\\\\\.[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)*|(?:[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*(?:\\\\\\\\.[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)*\\\\\\\\.)?[\\\\\\\\p{Ll}_][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*|(?:(?:[\\\\\\\\w()'→⇒\\\\\\\\[\\\\\\\\],]|->|=>)+\\\\\\\\s*)+)(?:\\\\\\\\s*\\\\\\\\s+\\\\\\\\s*(?:[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*(?:\\\\\\\\.[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)*|(?:[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*(?:\\\\\\\\.[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)*\\\\\\\\.)?[\\\\\\\\p{Ll}_][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*|(?:(?:[\\\\\\\\w()'→⇒\\\\\\\\[\\\\\\\\],]|->|=>)+\\\\\\\\s*)+))*)?\\\"},{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.pipe.purescript\\\"}},\\\"match\\\":\\\"\\\\\\\\|\\\"},{\\\"include\\\":\\\"#record_types\\\"}]}]},\\\"type_name\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*(?:\\\\\\\\.[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)*\\\",\\\"name\\\":\\\"entity.name.type.purescript\\\"}]},\\\"type_signature\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#record_types\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#class_constraint\\\"}]},\\\"6\\\":{\\\"name\\\":\\\"keyword.other.big-arrow.purescript\\\"}},\\\"match\\\":\\\"\\\\\\\\((?<classConstraints>([\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*(?:\\\\\\\\.[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)*)\\\\\\\\s+(?<classConstraint>(?:[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*(?:\\\\\\\\.[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)*|(?:[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*(?:\\\\\\\\.[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)*\\\\\\\\.)?[\\\\\\\\p{Ll}_][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)(?:\\\\\\\\s*\\\\\\\\s+\\\\\\\\s*(?:[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*(?:\\\\\\\\.[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)*|(?:[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*(?:\\\\\\\\.[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)*\\\\\\\\.)?[\\\\\\\\p{Ll}_][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*))*)(?:\\\\\\\\s*,\\\\\\\\s*([\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*(?:\\\\\\\\.[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)*)\\\\\\\\s+(?<classConstraint>(?:[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*(?:\\\\\\\\.[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)*|(?:[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*(?:\\\\\\\\.[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)*\\\\\\\\.)?[\\\\\\\\p{Ll}_][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)(?:\\\\\\\\s*\\\\\\\\s+\\\\\\\\s*(?:[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*(?:\\\\\\\\.[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)*|(?:[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*(?:\\\\\\\\.[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)*\\\\\\\\.)?[\\\\\\\\p{Ll}_][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*))*))*)\\\\\\\\)\\\\\\\\s*(=>|<=|[⇐⇒])\\\",\\\"name\\\":\\\"meta.class-constraints.purescript\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#class_constraint\\\"}]},\\\"4\\\":{\\\"name\\\":\\\"keyword.other.big-arrow.purescript\\\"}},\\\"match\\\":\\\"(([\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*(?:\\\\\\\\.[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)*)\\\\\\\\s+(?<classConstraint>(?:[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*(?:\\\\\\\\.[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)*|(?:[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*(?:\\\\\\\\.[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)*\\\\\\\\.)?[\\\\\\\\p{Ll}_][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)(?:\\\\\\\\s*\\\\\\\\s+\\\\\\\\s*(?:[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*(?:\\\\\\\\.[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)*|(?:[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*(?:\\\\\\\\.[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)*\\\\\\\\.)?[\\\\\\\\p{Ll}_][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*))*))\\\\\\\\s*(=>|<=|[⇐⇒])\\\",\\\"name\\\":\\\"meta.class-constraints.purescript\\\"},{\\\"match\\\":\\\"(?<![[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']])(->|→)\\\",\\\"name\\\":\\\"keyword.other.arrow.purescript\\\"},{\\\"match\\\":\\\"(?<![[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']])(=>|⇒)\\\",\\\"name\\\":\\\"keyword.other.big-arrow.purescript\\\"},{\\\"match\\\":\\\"<=|⇐\\\",\\\"name\\\":\\\"keyword.other.big-arrow-left.purescript\\\"},{\\\"match\\\":\\\"forall|∀\\\",\\\"name\\\":\\\"keyword.other.forall.purescript\\\"},{\\\"include\\\":\\\"#string_double_quoted\\\"},{\\\"include\\\":\\\"#generic_type\\\"},{\\\"include\\\":\\\"#type_name\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"match\\\":\\\"[[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']]+\\\",\\\"name\\\":\\\"keyword.other.purescript\\\"}]},\\\"type_synonym_declaration\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^(\\\\\\\\s)*(type)\\\\\\\\s+(.+?)\\\\\\\\s*(?==|$)\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"storage.type.data.purescript\\\"},\\\"3\\\":{\\\"name\\\":\\\"meta.type-signature.purescript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type_signature\\\"}]}},\\\"contentName\\\":\\\"meta.type-signature.purescript\\\",\\\"end\\\":\\\"^(?!\\\\\\\\1[ \\\\\\\\t]|[ \\\\\\\\t]*$)\\\",\\\"name\\\":\\\"meta.declaration.type.type.purescript\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.assignment.purescript\\\"}},\\\"match\\\":\\\"=\\\"},{\\\"include\\\":\\\"#type_signature\\\"},{\\\"include\\\":\\\"#record_types\\\"},{\\\"include\\\":\\\"#row_types\\\"},{\\\"include\\\":\\\"#comments\\\"}]}]},\\\"typeclass_declaration\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*\\\\\\\\b(class)(?!')\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.class.purescript\\\"}},\\\"end\\\":\\\"(\\\\\\\\bwhere\\\\\\\\b|(?=^\\\\\\\\S))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.purescript\\\"}},\\\"name\\\":\\\"meta.declaration.typeclass.purescript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type_signature\\\"}]}]},\\\"typed_hole\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\?(?:[\\\\\\\\p{Ll}_][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*|[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\d']*)\\\",\\\"name\\\":\\\"entity.name.function.typed-hole.purescript\\\"}]}},\\\"scopeName\\\":\\\"source.purescript\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/purescript.mjs\n"));

/***/ })

}]);