"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_jsonc_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/jsonc.mjs":
/*!****************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/jsonc.mjs ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"JSON with Comments\\\",\\\"name\\\":\\\"jsonc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#value\\\"}],\\\"repository\\\":{\\\"array\\\":{\\\"begin\\\":\\\"\\\\\\\\[\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.array.begin.json.comments\\\"}},\\\"end\\\":\\\"]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.array.end.json.comments\\\"}},\\\"name\\\":\\\"meta.structure.array.json.comments\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#value\\\"},{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.separator.array.json.comments\\\"},{\\\"match\\\":\\\"[^\\\\\\\\s\\\\\\\\]]\\\",\\\"name\\\":\\\"invalid.illegal.expected-array-separator.json.comments\\\"}]},\\\"comments\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"/\\\\\\\\*\\\\\\\\*(?!/)\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.json.comments\\\"}},\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"name\\\":\\\"comment.block.documentation.json.comments\\\"},{\\\"begin\\\":\\\"/\\\\\\\\*\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.json.comments\\\"}},\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"name\\\":\\\"comment.block.json.comments\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.json.comments\\\"}},\\\"match\\\":\\\"(//).*$\\\\\\\\n?\\\",\\\"name\\\":\\\"comment.line.double-slash.js\\\"}]},\\\"constant\\\":{\\\"match\\\":\\\"\\\\\\\\b(?:true|false|null)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.json.comments\\\"},\\\"number\\\":{\\\"match\\\":\\\"-?(?:0|[1-9]\\\\\\\\d*)(?:(?:\\\\\\\\.\\\\\\\\d+)?(?:[eE][+-]?\\\\\\\\d+)?)?\\\",\\\"name\\\":\\\"constant.numeric.json.comments\\\"},\\\"object\\\":{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.dictionary.begin.json.comments\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.dictionary.end.json.comments\\\"}},\\\"name\\\":\\\"meta.structure.dictionary.json.comments\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#objectkey\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"begin\\\":\\\":\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.separator.dictionary.key-value.json.comments\\\"}},\\\"end\\\":\\\"(,)|(?=})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.dictionary.pair.json.comments\\\"}},\\\"name\\\":\\\"meta.structure.dictionary.value.json.comments\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#value\\\"},{\\\"match\\\":\\\"[^\\\\\\\\s,]\\\",\\\"name\\\":\\\"invalid.illegal.expected-dictionary-separator.json.comments\\\"}]},{\\\"match\\\":\\\"[^\\\\\\\\s}]\\\",\\\"name\\\":\\\"invalid.illegal.expected-dictionary-separator.json.comments\\\"}]},\\\"objectkey\\\":{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.support.type.property-name.begin.json.comments\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.support.type.property-name.end.json.comments\\\"}},\\\"name\\\":\\\"string.json.comments support.type.property-name.json.comments\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#stringcontent\\\"}]},\\\"string\\\":{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.json.comments\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.json.comments\\\"}},\\\"name\\\":\\\"string.quoted.double.json.comments\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#stringcontent\\\"}]},\\\"stringcontent\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\(?:[\\\\\\\"\\\\\\\\\\\\\\\\/bfnrt]|u\\\\\\\\h{4})\\\",\\\"name\\\":\\\"constant.character.escape.json.comments\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"invalid.illegal.unrecognized-string-escape.json.comments\\\"}]},\\\"value\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#constant\\\"},{\\\"include\\\":\\\"#number\\\"},{\\\"include\\\":\\\"#string\\\"},{\\\"include\\\":\\\"#array\\\"},{\\\"include\\\":\\\"#object\\\"},{\\\"include\\\":\\\"#comments\\\"}]}},\\\"scopeName\\\":\\\"source.json.comments\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ac2hpa2lqcy9sYW5ncy9kaXN0L2pzb25jLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsd0NBQXdDLHlFQUF5RSx1QkFBdUIsa0JBQWtCLFdBQVcsdUNBQXVDLE9BQU8sK0RBQStELGdDQUFnQyxPQUFPLDZEQUE2RCxnRUFBZ0UsdUJBQXVCLEVBQUUsdUVBQXVFLEVBQUUsZ0dBQWdHLEVBQUUsZUFBZSxlQUFlLDZDQUE2QyxPQUFPLDJEQUEyRCwyRUFBMkUsRUFBRSxtQ0FBbUMsT0FBTywyREFBMkQsNkRBQTZELEVBQUUsY0FBYyxPQUFPLDJEQUEyRCx1RUFBdUUsRUFBRSxlQUFlLHlGQUF5RixhQUFhLHVIQUF1SCxhQUFhLGlCQUFpQixzQkFBc0IsT0FBTyxvRUFBb0UsWUFBWSxvQkFBb0IsT0FBTyxrRUFBa0UscUVBQXFFLDJCQUEyQixFQUFFLDBCQUEwQixFQUFFLG1DQUFtQyxPQUFPLHVFQUF1RSxtQkFBbUIscUJBQXFCLE9BQU8sa0VBQWtFLDJFQUEyRSx1QkFBdUIsRUFBRSxpR0FBaUcsRUFBRSxFQUFFLG9CQUFvQiw2RUFBNkUsRUFBRSxnQkFBZ0Isc0NBQXNDLE9BQU8seUVBQXlFLG1DQUFtQyxPQUFPLHVFQUF1RSwyRkFBMkYsK0JBQStCLEVBQUUsYUFBYSxzQ0FBc0MsT0FBTyxnRUFBZ0UsbUNBQW1DLE9BQU8sOERBQThELGdFQUFnRSwrQkFBK0IsRUFBRSxvQkFBb0IsZUFBZSxtREFBbUQsRUFBRSx5REFBeUQsRUFBRSw4RkFBOEYsRUFBRSxZQUFZLGVBQWUsMEJBQTBCLEVBQUUsd0JBQXdCLEVBQUUsd0JBQXdCLEVBQUUsdUJBQXVCLEVBQUUsd0JBQXdCLEVBQUUsMEJBQTBCLEdBQUcsd0NBQXdDOztBQUU3M0csaUVBQWU7QUFDZjtBQUNBLENBQUMiLCJzb3VyY2VzIjpbIkQ6XFxzb2Z0XFxTZWFQcm9qZWN0XFxnaXRodWJfb3Blbl9wcm9qZWN0X3N1Y2Nlc3NcXHN1bmFcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXEBzaGlraWpzXFxsYW5nc1xcZGlzdFxcanNvbmMubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGxhbmcgPSBPYmplY3QuZnJlZXplKEpTT04ucGFyc2UoXCJ7XFxcImRpc3BsYXlOYW1lXFxcIjpcXFwiSlNPTiB3aXRoIENvbW1lbnRzXFxcIixcXFwibmFtZVxcXCI6XFxcImpzb25jXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjdmFsdWVcXFwifV0sXFxcInJlcG9zaXRvcnlcXFwiOntcXFwiYXJyYXlcXFwiOntcXFwiYmVnaW5cXFwiOlxcXCJcXFxcXFxcXFtcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24uYXJyYXkuYmVnaW4uanNvbi5jb21tZW50c1xcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiXVxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24uYXJyYXkuZW5kLmpzb24uY29tbWVudHNcXFwifX0sXFxcIm5hbWVcXFwiOlxcXCJtZXRhLnN0cnVjdHVyZS5hcnJheS5qc29uLmNvbW1lbnRzXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjdmFsdWVcXFwifSx7XFxcIm1hdGNoXFxcIjpcXFwiLFxcXCIsXFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5zZXBhcmF0b3IuYXJyYXkuanNvbi5jb21tZW50c1xcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCJbXlxcXFxcXFxcc1xcXFxcXFxcXV1cXFwiLFxcXCJuYW1lXFxcIjpcXFwiaW52YWxpZC5pbGxlZ2FsLmV4cGVjdGVkLWFycmF5LXNlcGFyYXRvci5qc29uLmNvbW1lbnRzXFxcIn1dfSxcXFwiY29tbWVudHNcXFwiOntcXFwicGF0dGVybnNcXFwiOlt7XFxcImJlZ2luXFxcIjpcXFwiL1xcXFxcXFxcKlxcXFxcXFxcKig/IS8pXFxcIixcXFwiY2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uZGVmaW5pdGlvbi5jb21tZW50Lmpzb24uY29tbWVudHNcXFwifX0sXFxcImVuZFxcXCI6XFxcIlxcXFxcXFxcKi9cXFwiLFxcXCJuYW1lXFxcIjpcXFwiY29tbWVudC5ibG9jay5kb2N1bWVudGF0aW9uLmpzb24uY29tbWVudHNcXFwifSx7XFxcImJlZ2luXFxcIjpcXFwiL1xcXFxcXFxcKlxcXCIsXFxcImNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24uY29tbWVudC5qc29uLmNvbW1lbnRzXFxcIn19LFxcXCJlbmRcXFwiOlxcXCJcXFxcXFxcXCovXFxcIixcXFwibmFtZVxcXCI6XFxcImNvbW1lbnQuYmxvY2suanNvbi5jb21tZW50c1xcXCJ9LHtcXFwiY2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uZGVmaW5pdGlvbi5jb21tZW50Lmpzb24uY29tbWVudHNcXFwifX0sXFxcIm1hdGNoXFxcIjpcXFwiKC8vKS4qJFxcXFxcXFxcbj9cXFwiLFxcXCJuYW1lXFxcIjpcXFwiY29tbWVudC5saW5lLmRvdWJsZS1zbGFzaC5qc1xcXCJ9XX0sXFxcImNvbnN0YW50XFxcIjp7XFxcIm1hdGNoXFxcIjpcXFwiXFxcXFxcXFxiKD86dHJ1ZXxmYWxzZXxudWxsKVxcXFxcXFxcYlxcXCIsXFxcIm5hbWVcXFwiOlxcXCJjb25zdGFudC5sYW5ndWFnZS5qc29uLmNvbW1lbnRzXFxcIn0sXFxcIm51bWJlclxcXCI6e1xcXCJtYXRjaFxcXCI6XFxcIi0/KD86MHxbMS05XVxcXFxcXFxcZCopKD86KD86XFxcXFxcXFwuXFxcXFxcXFxkKyk/KD86W2VFXVsrLV0/XFxcXFxcXFxkKyk/KT9cXFwiLFxcXCJuYW1lXFxcIjpcXFwiY29uc3RhbnQubnVtZXJpYy5qc29uLmNvbW1lbnRzXFxcIn0sXFxcIm9iamVjdFxcXCI6e1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxce1xcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uZGVmaW5pdGlvbi5kaWN0aW9uYXJ5LmJlZ2luLmpzb24uY29tbWVudHNcXFwifX0sXFxcImVuZFxcXCI6XFxcIn1cXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLmRpY3Rpb25hcnkuZW5kLmpzb24uY29tbWVudHNcXFwifX0sXFxcIm5hbWVcXFwiOlxcXCJtZXRhLnN0cnVjdHVyZS5kaWN0aW9uYXJ5Lmpzb24uY29tbWVudHNcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNvYmplY3RrZXlcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjY29tbWVudHNcXFwifSx7XFxcImJlZ2luXFxcIjpcXFwiOlxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uc2VwYXJhdG9yLmRpY3Rpb25hcnkua2V5LXZhbHVlLmpzb24uY29tbWVudHNcXFwifX0sXFxcImVuZFxcXCI6XFxcIigsKXwoPz19KVxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLnNlcGFyYXRvci5kaWN0aW9uYXJ5LnBhaXIuanNvbi5jb21tZW50c1xcXCJ9fSxcXFwibmFtZVxcXCI6XFxcIm1ldGEuc3RydWN0dXJlLmRpY3Rpb25hcnkudmFsdWUuanNvbi5jb21tZW50c1xcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3ZhbHVlXFxcIn0se1xcXCJtYXRjaFxcXCI6XFxcIlteXFxcXFxcXFxzLF1cXFwiLFxcXCJuYW1lXFxcIjpcXFwiaW52YWxpZC5pbGxlZ2FsLmV4cGVjdGVkLWRpY3Rpb25hcnktc2VwYXJhdG9yLmpzb24uY29tbWVudHNcXFwifV19LHtcXFwibWF0Y2hcXFwiOlxcXCJbXlxcXFxcXFxcc31dXFxcIixcXFwibmFtZVxcXCI6XFxcImludmFsaWQuaWxsZWdhbC5leHBlY3RlZC1kaWN0aW9uYXJ5LXNlcGFyYXRvci5qc29uLmNvbW1lbnRzXFxcIn1dfSxcXFwib2JqZWN0a2V5XFxcIjp7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXCJcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLnN1cHBvcnQudHlwZS5wcm9wZXJ0eS1uYW1lLmJlZ2luLmpzb24uY29tbWVudHNcXFwifX0sXFxcImVuZFxcXCI6XFxcIlxcXFxcXFwiXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uc3VwcG9ydC50eXBlLnByb3BlcnR5LW5hbWUuZW5kLmpzb24uY29tbWVudHNcXFwifX0sXFxcIm5hbWVcXFwiOlxcXCJzdHJpbmcuanNvbi5jb21tZW50cyBzdXBwb3J0LnR5cGUucHJvcGVydHktbmFtZS5qc29uLmNvbW1lbnRzXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjc3RyaW5nY29udGVudFxcXCJ9XX0sXFxcInN0cmluZ1xcXCI6e1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFwiXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLnN0cmluZy5iZWdpbi5qc29uLmNvbW1lbnRzXFxcIn19LFxcXCJlbmRcXFwiOlxcXCJcXFxcXFxcIlxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24uc3RyaW5nLmVuZC5qc29uLmNvbW1lbnRzXFxcIn19LFxcXCJuYW1lXFxcIjpcXFwic3RyaW5nLnF1b3RlZC5kb3VibGUuanNvbi5jb21tZW50c1xcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3N0cmluZ2NvbnRlbnRcXFwifV19LFxcXCJzdHJpbmdjb250ZW50XFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJtYXRjaFxcXCI6XFxcIlxcXFxcXFxcXFxcXFxcXFwoPzpbXFxcXFxcXCJcXFxcXFxcXFxcXFxcXFxcL2JmbnJ0XXx1XFxcXFxcXFxoezR9KVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJjb25zdGFudC5jaGFyYWN0ZXIuZXNjYXBlLmpzb24uY29tbWVudHNcXFwifSx7XFxcIm1hdGNoXFxcIjpcXFwiXFxcXFxcXFxcXFxcXFxcXC5cXFwiLFxcXCJuYW1lXFxcIjpcXFwiaW52YWxpZC5pbGxlZ2FsLnVucmVjb2duaXplZC1zdHJpbmctZXNjYXBlLmpzb24uY29tbWVudHNcXFwifV19LFxcXCJ2YWx1ZVxcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNjb25zdGFudFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNudW1iZXJcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjc3RyaW5nXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2FycmF5XFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI29iamVjdFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNjb21tZW50c1xcXCJ9XX19LFxcXCJzY29wZU5hbWVcXFwiOlxcXCJzb3VyY2UuanNvbi5jb21tZW50c1xcXCJ9XCIpKVxuXG5leHBvcnQgZGVmYXVsdCBbXG5sYW5nXG5dXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/jsonc.mjs\n"));

/***/ })

}]);