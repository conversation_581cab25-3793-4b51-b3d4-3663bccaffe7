"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_codemirror_legacy-modes_mode_mllike_js"],{

/***/ "(app-pages-browser)/./node_modules/@codemirror/legacy-modes/mode/mllike.js":
/*!**************************************************************!*\
  !*** ./node_modules/@codemirror/legacy-modes/mode/mllike.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fSharp: () => (/* binding */ fSharp),\n/* harmony export */   oCaml: () => (/* binding */ oCaml),\n/* harmony export */   sml: () => (/* binding */ sml)\n/* harmony export */ });\nfunction mlLike(parserConfig) {\n  var words = {\n    'as': 'keyword',\n    'do': 'keyword',\n    'else': 'keyword',\n    'end': 'keyword',\n    'exception': 'keyword',\n    'fun': 'keyword',\n    'functor': 'keyword',\n    'if': 'keyword',\n    'in': 'keyword',\n    'include': 'keyword',\n    'let': 'keyword',\n    'of': 'keyword',\n    'open': 'keyword',\n    'rec': 'keyword',\n    'struct': 'keyword',\n    'then': 'keyword',\n    'type': 'keyword',\n    'val': 'keyword',\n    'while': 'keyword',\n    'with': 'keyword'\n  };\n\n  var extraWords = parserConfig.extraWords || {};\n  for (var prop in extraWords) {\n    if (extraWords.hasOwnProperty(prop)) {\n      words[prop] = parserConfig.extraWords[prop];\n    }\n  }\n  var hintWords = [];\n  for (var k in words) { hintWords.push(k); }\n\n  function tokenBase(stream, state) {\n    var ch = stream.next();\n\n    if (ch === '\"') {\n      state.tokenize = tokenString;\n      return state.tokenize(stream, state);\n    }\n    if (ch === '{') {\n      if (stream.eat('|')) {\n        state.longString = true;\n        state.tokenize = tokenLongString;\n        return state.tokenize(stream, state);\n      }\n    }\n    if (ch === '(') {\n      if (stream.match(/^\\*(?!\\))/)) {\n        state.commentLevel++;\n        state.tokenize = tokenComment;\n        return state.tokenize(stream, state);\n      }\n    }\n    if (ch === '~' || ch === '?') {\n      stream.eatWhile(/\\w/);\n      return 'variableName.special';\n    }\n    if (ch === '`') {\n      stream.eatWhile(/\\w/);\n      return 'quote';\n    }\n    if (ch === '/' && parserConfig.slashComments && stream.eat('/')) {\n      stream.skipToEnd();\n      return 'comment';\n    }\n    if (/\\d/.test(ch)) {\n      if (ch === '0' && stream.eat(/[bB]/)) {\n        stream.eatWhile(/[01]/);\n      } if (ch === '0' && stream.eat(/[xX]/)) {\n        stream.eatWhile(/[0-9a-fA-F]/)\n      } if (ch === '0' && stream.eat(/[oO]/)) {\n        stream.eatWhile(/[0-7]/);\n      } else {\n        stream.eatWhile(/[\\d_]/);\n        if (stream.eat('.')) {\n          stream.eatWhile(/[\\d]/);\n        }\n        if (stream.eat(/[eE]/)) {\n          stream.eatWhile(/[\\d\\-+]/);\n        }\n      }\n      return 'number';\n    }\n    if ( /[+\\-*&%=<>!?|@\\.~:]/.test(ch)) {\n      return 'operator';\n    }\n    if (/[\\w\\xa1-\\uffff]/.test(ch)) {\n      stream.eatWhile(/[\\w\\xa1-\\uffff]/);\n      var cur = stream.current();\n      return words.hasOwnProperty(cur) ? words[cur] : 'variable';\n    }\n    return null\n  }\n\n  function tokenString(stream, state) {\n    var next, end = false, escaped = false;\n    while ((next = stream.next()) != null) {\n      if (next === '\"' && !escaped) {\n        end = true;\n        break;\n      }\n      escaped = !escaped && next === '\\\\';\n    }\n    if (end && !escaped) {\n      state.tokenize = tokenBase;\n    }\n    return 'string';\n  };\n\n  function tokenComment(stream, state) {\n    var prev, next;\n    while(state.commentLevel > 0 && (next = stream.next()) != null) {\n      if (prev === '(' && next === '*') state.commentLevel++;\n      if (prev === '*' && next === ')') state.commentLevel--;\n      prev = next;\n    }\n    if (state.commentLevel <= 0) {\n      state.tokenize = tokenBase;\n    }\n    return 'comment';\n  }\n\n  function tokenLongString(stream, state) {\n    var prev, next;\n    while (state.longString && (next = stream.next()) != null) {\n      if (prev === '|' && next === '}') state.longString = false;\n      prev = next;\n    }\n    if (!state.longString) {\n      state.tokenize = tokenBase;\n    }\n    return 'string';\n  }\n\n  return {\n    startState: function() {return {tokenize: tokenBase, commentLevel: 0, longString: false};},\n    token: function(stream, state) {\n      if (stream.eatSpace()) return null;\n      return state.tokenize(stream, state);\n    },\n\n    languageData: {\n      autocomplete: hintWords,\n      commentTokens: {\n        line: parserConfig.slashComments ? \"//\" : undefined,\n        block: {open: \"(*\", close: \"*)\"}\n      }\n    }\n  };\n};\n\nconst oCaml = mlLike({\n  name: \"ocaml\",\n  extraWords: {\n    'and': 'keyword',\n    'assert': 'keyword',\n    'begin': 'keyword',\n    'class': 'keyword',\n    'constraint': 'keyword',\n    'done': 'keyword',\n    'downto': 'keyword',\n    'external': 'keyword',\n    'function': 'keyword',\n    'initializer': 'keyword',\n    'lazy': 'keyword',\n    'match': 'keyword',\n    'method': 'keyword',\n    'module': 'keyword',\n    'mutable': 'keyword',\n    'new': 'keyword',\n    'nonrec': 'keyword',\n    'object': 'keyword',\n    'private': 'keyword',\n    'sig': 'keyword',\n    'to': 'keyword',\n    'try': 'keyword',\n    'value': 'keyword',\n    'virtual': 'keyword',\n    'when': 'keyword',\n\n    // builtins\n    'raise': 'builtin',\n    'failwith': 'builtin',\n    'true': 'builtin',\n    'false': 'builtin',\n\n    // Pervasives builtins\n    'asr': 'builtin',\n    'land': 'builtin',\n    'lor': 'builtin',\n    'lsl': 'builtin',\n    'lsr': 'builtin',\n    'lxor': 'builtin',\n    'mod': 'builtin',\n    'or': 'builtin',\n\n    // More Pervasives\n    'raise_notrace': 'builtin',\n    'trace': 'builtin',\n    'exit': 'builtin',\n    'print_string': 'builtin',\n    'print_endline': 'builtin',\n\n     'int': 'type',\n     'float': 'type',\n     'bool': 'type',\n     'char': 'type',\n     'string': 'type',\n     'unit': 'type',\n\n     // Modules\n     'List': 'builtin'\n  }\n});\n\nconst fSharp = mlLike({\n  name: \"fsharp\",\n  extraWords: {\n    'abstract': 'keyword',\n    'assert': 'keyword',\n    'base': 'keyword',\n    'begin': 'keyword',\n    'class': 'keyword',\n    'default': 'keyword',\n    'delegate': 'keyword',\n    'do!': 'keyword',\n    'done': 'keyword',\n    'downcast': 'keyword',\n    'downto': 'keyword',\n    'elif': 'keyword',\n    'extern': 'keyword',\n    'finally': 'keyword',\n    'for': 'keyword',\n    'function': 'keyword',\n    'global': 'keyword',\n    'inherit': 'keyword',\n    'inline': 'keyword',\n    'interface': 'keyword',\n    'internal': 'keyword',\n    'lazy': 'keyword',\n    'let!': 'keyword',\n    'match': 'keyword',\n    'member': 'keyword',\n    'module': 'keyword',\n    'mutable': 'keyword',\n    'namespace': 'keyword',\n    'new': 'keyword',\n    'null': 'keyword',\n    'override': 'keyword',\n    'private': 'keyword',\n    'public': 'keyword',\n    'return!': 'keyword',\n    'return': 'keyword',\n    'select': 'keyword',\n    'static': 'keyword',\n    'to': 'keyword',\n    'try': 'keyword',\n    'upcast': 'keyword',\n    'use!': 'keyword',\n    'use': 'keyword',\n    'void': 'keyword',\n    'when': 'keyword',\n    'yield!': 'keyword',\n    'yield': 'keyword',\n\n    // Reserved words\n    'atomic': 'keyword',\n    'break': 'keyword',\n    'checked': 'keyword',\n    'component': 'keyword',\n    'const': 'keyword',\n    'constraint': 'keyword',\n    'constructor': 'keyword',\n    'continue': 'keyword',\n    'eager': 'keyword',\n    'event': 'keyword',\n    'external': 'keyword',\n    'fixed': 'keyword',\n    'method': 'keyword',\n    'mixin': 'keyword',\n    'object': 'keyword',\n    'parallel': 'keyword',\n    'process': 'keyword',\n    'protected': 'keyword',\n    'pure': 'keyword',\n    'sealed': 'keyword',\n    'tailcall': 'keyword',\n    'trait': 'keyword',\n    'virtual': 'keyword',\n    'volatile': 'keyword',\n\n    // builtins\n    'List': 'builtin',\n    'Seq': 'builtin',\n    'Map': 'builtin',\n    'Set': 'builtin',\n    'Option': 'builtin',\n    'int': 'builtin',\n    'string': 'builtin',\n    'not': 'builtin',\n    'true': 'builtin',\n    'false': 'builtin',\n\n    'raise': 'builtin',\n    'failwith': 'builtin'\n  },\n  slashComments: true\n});\n\nconst sml = mlLike({\n  name: \"sml\",\n  extraWords: {\n    'abstype': 'keyword',\n    'and': 'keyword',\n    'andalso': 'keyword',\n    'case': 'keyword',\n    'datatype': 'keyword',\n    'fn': 'keyword',\n    'handle': 'keyword',\n    'infix': 'keyword',\n    'infixr': 'keyword',\n    'local': 'keyword',\n    'nonfix': 'keyword',\n    'op': 'keyword',\n    'orelse': 'keyword',\n    'raise': 'keyword',\n    'withtype': 'keyword',\n    'eqtype': 'keyword',\n    'sharing': 'keyword',\n    'sig': 'keyword',\n    'signature': 'keyword',\n    'structure': 'keyword',\n    'where': 'keyword',\n    'true': 'keyword',\n    'false': 'keyword',\n\n    // types\n    'int': 'builtin',\n    'real': 'builtin',\n    'string': 'builtin',\n    'char': 'builtin',\n    'bool': 'builtin'\n  },\n  slashComments: true\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@codemirror/legacy-modes/mode/mllike.js\n"));

/***/ })

}]);