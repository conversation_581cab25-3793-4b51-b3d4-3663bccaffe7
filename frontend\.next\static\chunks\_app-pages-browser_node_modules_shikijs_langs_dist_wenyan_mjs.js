"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_wenyan_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/wenyan.mjs":
/*!*****************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/wenyan.mjs ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Wenyan\\\",\\\"name\\\":\\\"wenyan\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#keywords\\\"},{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"include\\\":\\\"#symbols\\\"},{\\\"include\\\":\\\"#expression\\\"},{\\\"include\\\":\\\"#comment-blocks\\\"},{\\\"include\\\":\\\"#comment-lines\\\"}],\\\"repository\\\":{\\\"comment-blocks\\\":{\\\"begin\\\":\\\"(注曰|疏曰|批曰)。?(「「|『)\\\",\\\"end\\\":\\\"(」」|』)\\\",\\\"name\\\":\\\"comment.block\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"constant.character\\\"}]},\\\"comment-lines\\\":{\\\"begin\\\":\\\"注曰|疏曰|批曰\\\",\\\"end\\\":\\\"$\\\",\\\"name\\\":\\\"comment.line\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"constant.character\\\"}]},\\\"constants\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"[負·又零〇一二三四五六七八九十百千萬億兆京垓秭穰溝澗正載極分釐毫絲忽微纖沙塵埃渺漠]\\\",\\\"name\\\":\\\"constant.numeric\\\"},{\\\"match\\\":\\\"[其陰陽]\\\",\\\"name\\\":\\\"constant.language\\\"},{\\\"begin\\\":\\\"「「|『\\\",\\\"end\\\":\\\"」」|』\\\",\\\"name\\\":\\\"string.quoted\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"constant.character\\\"}]}]},\\\"expression\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#variables\\\"}]},\\\"keywords\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"[數列言術爻物元]\\\",\\\"name\\\":\\\"storage.type\\\"},{\\\"match\\\":\\\"乃行是術曰|若其不然者|乃歸空無|欲行是術|乃止是遍|若其然者|其物如是|乃得矣|之術也|必先得|是術曰|恆為是|之物也|乃得|是謂|云云|中之|為是|乃止|若非|或若|之長|其餘\\\",\\\"name\\\":\\\"keyword.control\\\"},{\\\"match\\\":\\\"或云|蓋謂\\\",\\\"name\\\":\\\"keyword.control\\\"},{\\\"match\\\":\\\"中有陽乎|中無陰乎|所餘幾何|不等於|不大於|不小於|等於|大於|小於|[加減乘除變以於]\\\",\\\"name\\\":\\\"keyword.operator\\\"},{\\\"match\\\":\\\"不知何禍歟|不復存矣|姑妄行此|如事不諧|名之曰|吾嘗觀|之禍歟|乃作罷|吾有|今有|物之|書之|以施|昔之|是矣|之書|方悟|之義|嗚呼|之禍|[有施曰噫取今夫中豈]\\\",\\\"name\\\":\\\"keyword.other\\\"},{\\\"match\\\":\\\"[也凡遍若者之充銜]\\\",\\\"name\\\":\\\"keyword.control\\\"}]},\\\"symbols\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"[。、]\\\",\\\"name\\\":\\\"punctuation.separator\\\"}]},\\\"variables\\\":{\\\"begin\\\":\\\"「\\\",\\\"end\\\":\\\"」\\\",\\\"name\\\":\\\"variable.other\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"constant.character\\\"}]}},\\\"scopeName\\\":\\\"source.wenyan\\\",\\\"aliases\\\":[\\\"文言\\\"]}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/wenyan.mjs\n"));

/***/ })

}]);