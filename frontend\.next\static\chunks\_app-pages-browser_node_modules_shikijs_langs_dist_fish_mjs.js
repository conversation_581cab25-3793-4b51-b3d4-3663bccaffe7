"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_fish_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/fish.mjs":
/*!***************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/fish.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Fish\\\",\\\"fileTypes\\\":[\\\"fish\\\"],\\\"firstLineMatch\\\":\\\"^#!.*\\\\\\\\bfish\\\\\\\\b\\\",\\\"foldingStartMarker\\\":\\\"^\\\\\\\\s*(function|while|if|switch|for|begin)\\\\\\\\s.*$\\\",\\\"foldingStopMarker\\\":\\\"^\\\\\\\\s*end\\\\\\\\s*$\\\",\\\"name\\\":\\\"fish\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.fish\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.fish\\\"}},\\\"name\\\":\\\"string.quoted.double.fish\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#variable\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\([\\\\\\\"$]|$|\\\\\\\\\\\\\\\\)\\\",\\\"name\\\":\\\"constant.character.escape.fish\\\"}]},{\\\"begin\\\":\\\"'\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.fish\\\"}},\\\"end\\\":\\\"'\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.fish\\\"}},\\\"name\\\":\\\"string.quoted.single.fish\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\(['`\\\\\\\\\\\\\\\\])\\\",\\\"name\\\":\\\"constant.character.escape.fish\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.fish\\\"}},\\\"match\\\":\\\"(?<!\\\\\\\\$)(#)(?!\\\\\\\\{).*$\\\\\\\\n?\\\",\\\"name\\\":\\\"comment.line.number-sign.fish\\\"},{\\\"captures\\\":{\\\"2\\\":{\\\"name\\\":\\\"keyword.control.fish\\\"},\\\"4\\\":{\\\"name\\\":\\\"support.function.command.fish\\\"}},\\\"match\\\":\\\"(^\\\\\\\\s*|&&\\\\\\\\s*|\\\\\\\\|\\\\\\\\s*|\\\\\\\\(\\\\\\\\s*|;\\\\\\\\s*|\\\\\\\\b(if|while)\\\\\\\\b\\\\\\\\s+)(?!(?<!\\\\\\\\.)\\\\\\\\b(function|while|if|else|switch|case|for|in|begin|end|continue|break|return|source|exit|wait|and|or|not)\\\\\\\\b(?![?!]))([a-zA-Z_\\\\\\\\-0-9\\\\\\\\[\\\\\\\\].]+)\\\"},{\\\"match\\\":\\\"(?<!\\\\\\\\.)\\\\\\\\b(function|while|if|else|switch|case|for|in|begin|end|continue|break|return|source|exit|wait|and|or|not)\\\\\\\\b(?![?!])\\\",\\\"name\\\":\\\"keyword.control.fish\\\"},{\\\"match\\\":\\\"(?<!\\\\\\\\.)\\\\\\\\bfunction\\\\\\\\b(?![?!])\\\",\\\"name\\\":\\\"storage.type.fish\\\"},{\\\"match\\\":\\\"\\\\\\\\|\\\",\\\"name\\\":\\\"keyword.operator.pipe.fish\\\"},{\\\"match\\\":\\\"(?:<|([>^]|>>|\\\\\\\\^\\\\\\\\^)(&[012-])?|[012]([<>]|>>)(&[012-])?)\\\",\\\"name\\\":\\\"keyword.operator.redirect.fish\\\"},{\\\"match\\\":\\\"&\\\",\\\"name\\\":\\\"keyword.operator.background.fish\\\"},{\\\"match\\\":\\\"\\\\\\\\*\\\\\\\\*|[*?]\\\",\\\"name\\\":\\\"keyword.operator.glob.fish\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"source.option.fish\\\"}},\\\"match\\\":\\\"\\\\\\\\s(-{1,2}[a-zA-Z_\\\\\\\\-0-9]+|-\\\\\\\\w)\\\\\\\\b\\\"},{\\\"include\\\":\\\"#variable\\\"},{\\\"include\\\":\\\"#escape\\\"}],\\\"repository\\\":{\\\"escape\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\[abefnrtv $*?~#(){}\\\\\\\\[\\\\\\\\]<>^\\\\\\\\&|;\\\\\\\"']\\\",\\\"name\\\":\\\"constant.character.escape.single.fish\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\x\\\\\\\\h{1,2}\\\",\\\"name\\\":\\\"constant.character.escape.hex-ascii.fish\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\X\\\\\\\\h{1,2}\\\",\\\"name\\\":\\\"constant.character.escape.hex-byte.fish\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\[0-7]{1,3}\\\",\\\"name\\\":\\\"constant.character.escape.octal.fish\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\u\\\\\\\\h{1,4}\\\",\\\"name\\\":\\\"constant.character.escape.unicode-16-bit.fish\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\U\\\\\\\\h{1,8}\\\",\\\"name\\\":\\\"constant.character.escape.unicode-32-bit.fish\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\c[a-zA-Z]\\\",\\\"name\\\":\\\"constant.character.escape.control.fish\\\"}]},\\\"variable\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.variable.fish\\\"}},\\\"match\\\":\\\"(\\\\\\\\$)(argv|CMD_DURATION|COLUMNS|fish_bind_mode|fish_color_autosuggestion|fish_color_cancel|fish_color_command|fish_color_comment|fish_color_cwd|fish_color_cwd_root|fish_color_end|fish_color_error|fish_color_escape|fish_color_hg_added|fish_color_hg_clean|fish_color_hg_copied|fish_color_hg_deleted|fish_color_hg_dirty|fish_color_hg_modified|fish_color_hg_renamed|fish_color_hg_unmerged|fish_color_hg_untracked|fish_color_history_current|fish_color_host|fish_color_host_remote|fish_color_match|fish_color_normal|fish_color_operator|fish_color_param|fish_color_quote|fish_color_redirection|fish_color_search_match|fish_color_selection|fish_color_status|fish_color_user|fish_color_valid_path|fish_complete_path|fish_function_path|fish_greeting|fish_key_bindings|fish_pager_color_completion|fish_pager_color_description|fish_pager_color_prefix|fish_pager_color_progress|fish_pid|fish_prompt_hg_status_added|fish_prompt_hg_status_copied|fish_prompt_hg_status_deleted|fish_prompt_hg_status_modified|fish_prompt_hg_status_order|fish_prompt_hg_status_unmerged|fish_prompt_hg_status_untracked|FISH_VERSION|history|hostname|IFS|LINES|pipestatus|status|umask|version)\\\\\\\\b\\\",\\\"name\\\":\\\"variable.language.fish\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.variable.fish\\\"}},\\\"match\\\":\\\"(\\\\\\\\$)[a-zA-Z_][a-zA-Z0-9_]*\\\",\\\"name\\\":\\\"variable.other.normal.fish\\\"}]}},\\\"scopeName\\\":\\\"source.fish\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/fish.mjs\n"));

/***/ })

}]);