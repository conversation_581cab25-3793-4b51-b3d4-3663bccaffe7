"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_berry_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/berry.mjs":
/*!****************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/berry.mjs ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Berry\\\",\\\"name\\\":\\\"berry\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#controls\\\"},{\\\"include\\\":\\\"#strings\\\"},{\\\"include\\\":\\\"#comment-block\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#keywords\\\"},{\\\"include\\\":\\\"#function\\\"},{\\\"include\\\":\\\"#member\\\"},{\\\"include\\\":\\\"#identifier\\\"},{\\\"include\\\":\\\"#number\\\"},{\\\"include\\\":\\\"#operator\\\"}],\\\"repository\\\":{\\\"comment-block\\\":{\\\"begin\\\":\\\"#-\\\",\\\"end\\\":\\\"-#\\\",\\\"name\\\":\\\"comment.berry\\\",\\\"patterns\\\":[{}]},\\\"comments\\\":{\\\"begin\\\":\\\"#\\\",\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.line.berry\\\",\\\"patterns\\\":[{}]},\\\"controls\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(if|elif|else|for|while|do|end|break|continue|return|try|except|raise)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.berry\\\"}]},\\\"function\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b([a-zA-Z_][a-zA-Z0-9_]*(?=\\\\\\\\s*\\\\\\\\())\\\",\\\"name\\\":\\\"entity.name.function.berry\\\"}]},\\\"identifier\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b[_A-Za-z]\\\\\\\\w+\\\\\\\\b\\\",\\\"name\\\":\\\"identifier.berry\\\"}]},\\\"keywords\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(var|static|def|class|true|false|nil|self|super|import|as|_class)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.berry\\\"}]},\\\"member\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"entity.other.attribute-name.berry\\\"}},\\\"match\\\":\\\"\\\\\\\\.([a-zA-Z_][a-zA-Z0-9_]*)\\\"}]},\\\"number\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"0x\\\\\\\\h+|\\\\\\\\d+|(\\\\\\\\d+\\\\\\\\.?|\\\\\\\\.\\\\\\\\d)\\\\\\\\d*([eE][+-]?\\\\\\\\d+)?\\\",\\\"name\\\":\\\"constant.numeric.berry\\\"}]},\\\"operator\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"[()\\\\\\\\[\\\\\\\\].\\\\\\\\-!~*/%+\\\\\\\\&^|<>=:]\\\",\\\"name\\\":\\\"keyword.operator.berry\\\"}]},\\\"strings\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"([\\\\\\\"'])\\\",\\\"end\\\":\\\"\\\\\\\\1\\\",\\\"name\\\":\\\"string.quoted.double.berry\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(\\\\\\\\\\\\\\\\x\\\\\\\\h{2})|(\\\\\\\\\\\\\\\\[0-7]{3})|(\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\)|(\\\\\\\\\\\\\\\\\\\\\\\")|(\\\\\\\\\\\\\\\\')|(\\\\\\\\\\\\\\\\a)|(\\\\\\\\\\\\\\\\b)|(\\\\\\\\\\\\\\\\f)|(\\\\\\\\\\\\\\\\n)|(\\\\\\\\\\\\\\\\r)|(\\\\\\\\\\\\\\\\t)|(\\\\\\\\\\\\\\\\v)\\\",\\\"name\\\":\\\"constant.character.escape.berry\\\"}]},{\\\"begin\\\":\\\"f([\\\\\\\"'])\\\",\\\"end\\\":\\\"\\\\\\\\1\\\",\\\"name\\\":\\\"string.quoted.other.berry\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(\\\\\\\\\\\\\\\\x\\\\\\\\h{2})|(\\\\\\\\\\\\\\\\[0-7]{3})|(\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\)|(\\\\\\\\\\\\\\\\\\\\\\\")|(\\\\\\\\\\\\\\\\')|(\\\\\\\\\\\\\\\\a)|(\\\\\\\\\\\\\\\\b)|(\\\\\\\\\\\\\\\\f)|(\\\\\\\\\\\\\\\\n)|(\\\\\\\\\\\\\\\\r)|(\\\\\\\\\\\\\\\\t)|(\\\\\\\\\\\\\\\\v)\\\",\\\"name\\\":\\\"constant.character.escape.berry\\\"},{\\\"match\\\":\\\"\\\\\\\\{\\\\\\\\{[^}]*}}\\\",\\\"name\\\":\\\"string.quoted.other.berry\\\"},{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"end\\\":\\\"}\\\",\\\"name\\\":\\\"keyword.other.unit.berry\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#keywords\\\"},{\\\"include\\\":\\\"#numbers\\\"},{\\\"include\\\":\\\"#identifier\\\"},{\\\"include\\\":\\\"#operator\\\"},{\\\"include\\\":\\\"#member\\\"},{\\\"include\\\":\\\"#function\\\"}]}]}]}},\\\"scopeName\\\":\\\"source.berry\\\",\\\"aliases\\\":[\\\"be\\\"]}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/berry.mjs\n"));

/***/ })

}]);