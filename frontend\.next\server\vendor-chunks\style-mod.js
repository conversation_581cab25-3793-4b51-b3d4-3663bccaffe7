"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/style-mod";
exports.ids = ["vendor-chunks/style-mod"];
exports.modules = {

/***/ "(ssr)/./node_modules/style-mod/src/style-mod.js":
/*!*************************************************!*\
  !*** ./node_modules/style-mod/src/style-mod.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StyleModule: () => (/* binding */ StyleModule)\n/* harmony export */ });\nconst C = \"\\u037c\"\nconst COUNT = typeof Symbol == \"undefined\" ? \"__\" + C : Symbol.for(C)\nconst SET = typeof Symbol == \"undefined\" ? \"__styleSet\" + Math.floor(Math.random() * 1e8) : Symbol(\"styleSet\")\nconst top = typeof globalThis != \"undefined\" ? globalThis : typeof window != \"undefined\" ? window : {}\n\n// :: - Style modules encapsulate a set of CSS rules defined from\n// JavaScript. Their definitions are only available in a given DOM\n// root after it has been _mounted_ there with `StyleModule.mount`.\n//\n// Style modules should be created once and stored somewhere, as\n// opposed to re-creating them every time you need them. The amount of\n// CSS rules generated for a given DOM root is bounded by the amount\n// of style modules that were used. So to avoid leaking rules, don't\n// create these dynamically, but treat them as one-time allocations.\nclass StyleModule {\n  // :: (Object<Style>, ?{finish: ?(string) → string})\n  // Create a style module from the given spec.\n  //\n  // When `finish` is given, it is called on regular (non-`@`)\n  // selectors (after `&` expansion) to compute the final selector.\n  constructor(spec, options) {\n    this.rules = []\n    let {finish} = options || {}\n\n    function splitSelector(selector) {\n      return /^@/.test(selector) ? [selector] : selector.split(/,\\s*/)\n    }\n\n    function render(selectors, spec, target, isKeyframes) {\n      let local = [], isAt = /^@(\\w+)\\b/.exec(selectors[0]), keyframes = isAt && isAt[1] == \"keyframes\"\n      if (isAt && spec == null) return target.push(selectors[0] + \";\")\n      for (let prop in spec) {\n        let value = spec[prop]\n        if (/&/.test(prop)) {\n          render(prop.split(/,\\s*/).map(part => selectors.map(sel => part.replace(/&/, sel))).reduce((a, b) => a.concat(b)),\n                 value, target)\n        } else if (value && typeof value == \"object\") {\n          if (!isAt) throw new RangeError(\"The value of a property (\" + prop + \") should be a primitive value.\")\n          render(splitSelector(prop), value, local, keyframes)\n        } else if (value != null) {\n          local.push(prop.replace(/_.*/, \"\").replace(/[A-Z]/g, l => \"-\" + l.toLowerCase()) + \": \" + value + \";\")\n        }\n      }\n      if (local.length || keyframes) {\n        target.push((finish && !isAt && !isKeyframes ? selectors.map(finish) : selectors).join(\", \") +\n                    \" {\" + local.join(\" \") + \"}\")\n      }\n    }\n\n    for (let prop in spec) render(splitSelector(prop), spec[prop], this.rules)\n  }\n\n  // :: () → string\n  // Returns a string containing the module's CSS rules.\n  getRules() { return this.rules.join(\"\\n\") }\n\n  // :: () → string\n  // Generate a new unique CSS class name.\n  static newName() {\n    let id = top[COUNT] || 1\n    top[COUNT] = id + 1\n    return C + id.toString(36)\n  }\n\n  // :: (union<Document, ShadowRoot>, union<[StyleModule], StyleModule>, ?{nonce: ?string})\n  //\n  // Mount the given set of modules in the given DOM root, which ensures\n  // that the CSS rules defined by the module are available in that\n  // context.\n  //\n  // Rules are only added to the document once per root.\n  //\n  // Rule order will follow the order of the modules, so that rules from\n  // modules later in the array take precedence of those from earlier\n  // modules. If you call this function multiple times for the same root\n  // in a way that changes the order of already mounted modules, the old\n  // order will be changed.\n  //\n  // If a Content Security Policy nonce is provided, it is added to\n  // the `<style>` tag generated by the library.\n  static mount(root, modules, options) {\n    let set = root[SET], nonce = options && options.nonce\n    if (!set) set = new StyleSet(root, nonce)\n    else if (nonce) set.setNonce(nonce)\n    set.mount(Array.isArray(modules) ? modules : [modules], root)\n  }\n}\n\nlet adoptedSet = new Map //<Document, StyleSet>\n\nclass StyleSet {\n  constructor(root, nonce) {\n    let doc = root.ownerDocument || root, win = doc.defaultView\n    if (!root.head && root.adoptedStyleSheets && win.CSSStyleSheet) {\n      let adopted = adoptedSet.get(doc)\n      if (adopted) return root[SET] = adopted\n      this.sheet = new win.CSSStyleSheet\n      adoptedSet.set(doc, this)\n    } else {\n      this.styleTag = doc.createElement(\"style\")\n      if (nonce) this.styleTag.setAttribute(\"nonce\", nonce)\n    }\n    this.modules = []\n    root[SET] = this\n  }\n\n  mount(modules, root) {\n    let sheet = this.sheet\n    let pos = 0 /* Current rule offset */, j = 0 /* Index into this.modules */\n    for (let i = 0; i < modules.length; i++) {\n      let mod = modules[i], index = this.modules.indexOf(mod)\n      if (index < j && index > -1) { // Ordering conflict\n        this.modules.splice(index, 1)\n        j--\n        index = -1\n      }\n      if (index == -1) {\n        this.modules.splice(j++, 0, mod)\n        if (sheet) for (let k = 0; k < mod.rules.length; k++)\n          sheet.insertRule(mod.rules[k], pos++)\n      } else {\n        while (j < index) pos += this.modules[j++].rules.length\n        pos += mod.rules.length\n        j++\n      }\n    }\n\n    if (sheet) {\n      if (root.adoptedStyleSheets.indexOf(this.sheet) < 0)\n        root.adoptedStyleSheets = [this.sheet, ...root.adoptedStyleSheets]\n    } else {\n      let text = \"\"\n      for (let i = 0; i < this.modules.length; i++)\n        text += this.modules[i].getRules() + \"\\n\"\n      this.styleTag.textContent = text\n      let target = root.head || root\n      if (this.styleTag.parentNode != target)\n        target.insertBefore(this.styleTag, target.firstChild)\n    }\n  }\n\n  setNonce(nonce) {\n    if (this.styleTag && this.styleTag.getAttribute(\"nonce\") != nonce)\n      this.styleTag.setAttribute(\"nonce\", nonce)\n  }\n}\n\n// Style::Object<union<Style,string>>\n//\n// A style is an object that, in the simple case, maps CSS property\n// names to strings holding their values, as in `{color: \"red\",\n// fontWeight: \"bold\"}`. The property names can be given in\n// camel-case—the library will insert a dash before capital letters\n// when converting them to CSS.\n//\n// If you include an underscore in a property name, it and everything\n// after it will be removed from the output, which can be useful when\n// providing a property multiple times, for browser compatibility\n// reasons.\n//\n// A property in a style object can also be a sub-selector, which\n// extends the current context to add a pseudo-selector or a child\n// selector. Such a property should contain a `&` character, which\n// will be replaced by the current selector. For example `{\"&:before\":\n// {content: '\"hi\"'}}`. Sub-selectors and regular properties can\n// freely be mixed in a given object. Any property containing a `&` is\n// assumed to be a sub-selector.\n//\n// Finally, a property can specify an @-block to be wrapped around the\n// styles defined inside the object that's the property's value. For\n// example to create a media query you can do `{\"@media screen and\n// (min-width: 400px)\": {...}}`.\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/style-mod/src/style-mod.js\n");

/***/ })

};
;