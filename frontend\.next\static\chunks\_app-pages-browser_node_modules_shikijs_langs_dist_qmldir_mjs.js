"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_qmldir_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/qmldir.mjs":
/*!*****************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/qmldir.mjs ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"QML Directory\\\",\\\"name\\\":\\\"qmldir\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#keywords\\\"},{\\\"include\\\":\\\"#version\\\"},{\\\"include\\\":\\\"#names\\\"}],\\\"repository\\\":{\\\"comment\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"#\\\",\\\"end\\\":\\\"$\\\",\\\"name\\\":\\\"comment.line.number-sign.qmldir\\\"}]},\\\"file-name\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b\\\\\\\\w+\\\\\\\\.(qmltypes|qml|js)\\\\\\\\b\\\",\\\"name\\\":\\\"string.unquoted.qmldir\\\"}]},\\\"identifier\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b\\\\\\\\w+\\\\\\\\b\\\",\\\"name\\\":\\\"variable.parameter.qmldir\\\"}]},\\\"keywords\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(module|singleton|internal|plugin|classname|typeinfo|depends|designersupported)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.qmldir\\\"}]},\\\"module-name\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b[A-Z]\\\\\\\\w*\\\\\\\\b\\\",\\\"name\\\":\\\"entity.name.type.qmldir\\\"}]},\\\"names\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#file-name\\\"},{\\\"include\\\":\\\"#module-name\\\"},{\\\"include\\\":\\\"#identifier\\\"}]},\\\"version\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b\\\\\\\\d+\\\\\\\\.\\\\\\\\d+\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.qml\\\"}]}},\\\"scopeName\\\":\\\"source.qmldir\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/qmldir.mjs\n"));

/***/ })

}]);