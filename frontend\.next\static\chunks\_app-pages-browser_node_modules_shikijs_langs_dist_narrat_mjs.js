"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_narrat_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/narrat.mjs":
/*!*****************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/narrat.mjs ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Narrat Language\\\",\\\"name\\\":\\\"narrat\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#expression\\\"}],\\\"repository\\\":{\\\"commands\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(set|var)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.commands.variables.narrat\\\"},{\\\"match\\\":\\\"\\\\\\\\b(t(?:alk|hink))\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.commands.text.narrat\\\"},{\\\"match\\\":\\\"\\\\\\\\b(jump|run|wait|return|save|save_prompt)\\\",\\\"name\\\":\\\"keyword.commands.flow.narrat\\\"},{\\\"match\\\":\\\"\\\\\\\\b(log|clear_dialog)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.commands.helpers.narrat\\\"},{\\\"match\\\":\\\"\\\\\\\\b(set_screen|empty_layer|set_button)\\\",\\\"name\\\":\\\"keyword.commands.screens.narrat\\\"},{\\\"match\\\":\\\"\\\\\\\\b(play|pause|stop)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.commands.audio.narrat\\\"},{\\\"match\\\":\\\"\\\\\\\\b(notify|enable_notifications|disable_notifications)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.commands.notifications.narrat\\\"},{\\\"match\\\":\\\"\\\\\\\\b(set_stat|get_stat_value|add_stat)\\\",\\\"name\\\":\\\"keyword.commands.stats.narrat\\\"},{\\\"match\\\":\\\"\\\\\\\\b(neg|abs|random|random_float|random_from_args|min|max|clamp|floor|round|ceil|sqrt|^)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.commands.math.narrat\\\"},{\\\"match\\\":\\\"\\\\\\\\b(concat|join)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.commands.string.narrat\\\"},{\\\"match\\\":\\\"\\\\\\\\b(text_field)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.commands.text_field.narrat\\\"},{\\\"match\\\":\\\"\\\\\\\\b(add_level|set_level|add_xp|roll|get_level|get_xp)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.commands.skills.narrat\\\"},{\\\"match\\\":\\\"\\\\\\\\b(add_item|remove_item|enable_interaction|disable_interaction|has_item?|item_amount?)\\\",\\\"name\\\":\\\"keyword.commands.inventory.narrat\\\"},{\\\"match\\\":\\\"\\\\\\\\b(start_quest|start_objective|complete_objective|complete_quest|quest_started?|objective_started?|quest_completed?|objective_completed?)\\\",\\\"name\\\":\\\"keyword.commands.quests.narrat\\\"}]},\\\"comments\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"//.*$\\\",\\\"name\\\":\\\"comment.line.narrat\\\"}]},\\\"expression\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#keywords\\\"},{\\\"include\\\":\\\"#commands\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"include\\\":\\\"#primitives\\\"},{\\\"include\\\":\\\"#strings\\\"},{\\\"include\\\":\\\"#paren-expression\\\"}]},\\\"interpolation\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"([\\\\\\\\w.])+\\\",\\\"name\\\":\\\"variable.interpolation.narrat\\\"}]},\\\"keywords\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(if|else|choice)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.narrat\\\"},{\\\"match\\\":\\\"\\\\\\\\$[\\\\\\\\w|.]+\\\\\\\\b\\\",\\\"name\\\":\\\"variable.value.narrat\\\"},{\\\"match\\\":\\\"^\\\\\\\\w+(?=([\\\\\\\\s\\\\\\\\w])*:)\\\",\\\"name\\\":\\\"entity.name.function.narrat\\\"},{\\\"match\\\":\\\"^\\\\\\\\w+(?!([\\\\\\\\s\\\\\\\\w])*:)\\\",\\\"name\\\":\\\"invalid.label.narrat\\\"},{\\\"match\\\":\\\"(?<=\\\\\\\\w)[^^](\\\\\\\\b\\\\\\\\w+\\\\\\\\b)(?=([\\\\\\\\s\\\\\\\\w])*:)\\\",\\\"name\\\":\\\"entity.other.attribute-name\\\"}]},\\\"operators\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(&&|\\\\\\\\|\\\\\\\\||!=|==|>=|<=|[<>!?])\\\\\\\\s\\\",\\\"name\\\":\\\"keyword.operator.logic.narrat\\\"},{\\\"match\\\":\\\"([+\\\\\\\\-*/])\\\\\\\\s\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.narrat\\\"}]},\\\"paren-expression\\\":{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.paren.open\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.paren.close\\\"}},\\\"name\\\":\\\"expression.group\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},\\\"primitives\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b\\\\\\\\d+\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.narrat\\\"},{\\\"match\\\":\\\"\\\\\\\\btrue\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.true.narrat\\\"},{\\\"match\\\":\\\"\\\\\\\\bfalse\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.false.narrat\\\"},{\\\"match\\\":\\\"\\\\\\\\bnull\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.null.narrat\\\"},{\\\"match\\\":\\\"\\\\\\\\bundefined\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.undefined.narrat\\\"}]},\\\"strings\\\":{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"end\\\":\\\"\\\\\\\"\\\",\\\"name\\\":\\\"string.quoted.double.narrat\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"constant.character.escape.narrat\\\"},{\\\"begin\\\":\\\"%\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.template.open\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.template.close.narrat\\\"}},\\\"name\\\":\\\"expression.template\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"},{\\\"include\\\":\\\"#interpolation\\\"}]}]}},\\\"scopeName\\\":\\\"source.narrat\\\",\\\"aliases\\\":[\\\"nar\\\"]}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/narrat.mjs\n"));

/***/ })

}]);