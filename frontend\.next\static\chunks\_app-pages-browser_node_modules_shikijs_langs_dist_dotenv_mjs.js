"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_dotenv_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/dotenv.mjs":
/*!*****************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/dotenv.mjs ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"dotEnv\\\",\\\"name\\\":\\\"dotenv\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#line-comment\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\s?(#.*$)\\\\\\\\n\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#key\\\"}]},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.assignment.dotenv\\\"},\\\"3\\\":{\\\"name\\\":\\\"property.value.dotenv\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#line-comment\\\"},{\\\"include\\\":\\\"#double-quoted-string\\\"},{\\\"include\\\":\\\"#single-quoted-string\\\"},{\\\"include\\\":\\\"#interpolation\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\s?(.*?)\\\\\\\\s?(=)(.*)$\\\"}],\\\"repository\\\":{\\\"double-quoted-string\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#interpolation\\\"},{\\\"include\\\":\\\"#escape-characters\\\"}]}},\\\"match\\\":\\\"\\\\\\\"(.*)\\\\\\\"\\\",\\\"name\\\":\\\"string.quoted.double.dotenv\\\"},\\\"escape-characters\\\":{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\(?:[nrtfb\\\\\\\"'\\\\\\\\\\\\\\\\]|u[0123456789ABCDEF]{4})\\\",\\\"name\\\":\\\"constant.character.escape.dotenv\\\"},\\\"interpolation\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.interpolation.begin.dotenv\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.interpolation.dotenv\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.interpolation.end.dotenv\\\"}},\\\"match\\\":\\\"(\\\\\\\\$\\\\\\\\{)(.*)(})\\\"},\\\"key\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.key.export.dotenv\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.key.dotenv\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#variable\\\"}]}},\\\"match\\\":\\\"(export\\\\\\\\s)?(.*)\\\"},\\\"line-comment\\\":{\\\"match\\\":\\\"#.*$\\\",\\\"name\\\":\\\"comment.line.dotenv\\\"},\\\"single-quoted-string\\\":{\\\"match\\\":\\\"'(.*)'\\\",\\\"name\\\":\\\"string.quoted.single.dotenv\\\"},\\\"variable\\\":{\\\"match\\\":\\\"[a-zA-Z_]+[a-zA-Z0-9_]*\\\"}},\\\"scopeName\\\":\\\"source.dotenv\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/dotenv.mjs\n"));

/***/ })

}]);