"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/color-bits";
exports.ids = ["vendor-chunks/color-bits"];
exports.modules = {

/***/ "(ssr)/./node_modules/color-bits/build/bit.js":
/*!**********************************************!*\
  !*** ./node_modules/color-bits/build/bit.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n// Bitwise functions\n//\n// The color representation would ideally be 32-bits unsigned, but JS bitwise\n// operators only work as 32-bits signed. The range of Smi values on V8 is also\n// 32-bits signed. Those two factors make it that it's much more efficient to just\n// use signed integers to represent the data.\n//\n// Colors with a R channel >= 0x80 will be a negative number, but that's not really\n// an issue at any point because the bits for signed and unsigned integers are always\n// the same, only their interpretation changes.\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.cast = cast;\nexports.get = get;\nexports.set = set;\nconst INT32_TO_UINT32_OFFSET = 2 ** 32;\nfunction cast(n) {\n    if (n < 0) {\n        return n + INT32_TO_UINT32_OFFSET;\n    }\n    return n;\n}\nfunction get(n, offset) {\n    return (n >> offset) & 0xff;\n}\nfunction set(n, offset, byte) {\n    return n ^ ((n ^ (byte << offset)) & (0xff << offset));\n}\n//# sourceMappingURL=bit.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvY29sb3ItYml0cy9idWlsZC9iaXQuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxZQUFZO0FBQ1osV0FBVztBQUNYLFdBQVc7QUFDWDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcc29mdFxcU2VhUHJvamVjdFxcZ2l0aHViX29wZW5fcHJvamVjdF9zdWNjZXNzXFxzdW5hXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxjb2xvci1iaXRzXFxidWlsZFxcYml0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuLy8gQml0d2lzZSBmdW5jdGlvbnNcbi8vXG4vLyBUaGUgY29sb3IgcmVwcmVzZW50YXRpb24gd291bGQgaWRlYWxseSBiZSAzMi1iaXRzIHVuc2lnbmVkLCBidXQgSlMgYml0d2lzZVxuLy8gb3BlcmF0b3JzIG9ubHkgd29yayBhcyAzMi1iaXRzIHNpZ25lZC4gVGhlIHJhbmdlIG9mIFNtaSB2YWx1ZXMgb24gVjggaXMgYWxzb1xuLy8gMzItYml0cyBzaWduZWQuIFRob3NlIHR3byBmYWN0b3JzIG1ha2UgaXQgdGhhdCBpdCdzIG11Y2ggbW9yZSBlZmZpY2llbnQgdG8ganVzdFxuLy8gdXNlIHNpZ25lZCBpbnRlZ2VycyB0byByZXByZXNlbnQgdGhlIGRhdGEuXG4vL1xuLy8gQ29sb3JzIHdpdGggYSBSIGNoYW5uZWwgPj0gMHg4MCB3aWxsIGJlIGEgbmVnYXRpdmUgbnVtYmVyLCBidXQgdGhhdCdzIG5vdCByZWFsbHlcbi8vIGFuIGlzc3VlIGF0IGFueSBwb2ludCBiZWNhdXNlIHRoZSBiaXRzIGZvciBzaWduZWQgYW5kIHVuc2lnbmVkIGludGVnZXJzIGFyZSBhbHdheXNcbi8vIHRoZSBzYW1lLCBvbmx5IHRoZWlyIGludGVycHJldGF0aW9uIGNoYW5nZXMuXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLmNhc3QgPSBjYXN0O1xuZXhwb3J0cy5nZXQgPSBnZXQ7XG5leHBvcnRzLnNldCA9IHNldDtcbmNvbnN0IElOVDMyX1RPX1VJTlQzMl9PRkZTRVQgPSAyICoqIDMyO1xuZnVuY3Rpb24gY2FzdChuKSB7XG4gICAgaWYgKG4gPCAwKSB7XG4gICAgICAgIHJldHVybiBuICsgSU5UMzJfVE9fVUlOVDMyX09GRlNFVDtcbiAgICB9XG4gICAgcmV0dXJuIG47XG59XG5mdW5jdGlvbiBnZXQobiwgb2Zmc2V0KSB7XG4gICAgcmV0dXJuIChuID4+IG9mZnNldCkgJiAweGZmO1xufVxuZnVuY3Rpb24gc2V0KG4sIG9mZnNldCwgYnl0ZSkge1xuICAgIHJldHVybiBuIF4gKChuIF4gKGJ5dGUgPDwgb2Zmc2V0KSkgJiAoMHhmZiA8PCBvZmZzZXQpKTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWJpdC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/color-bits/build/bit.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/color-bits/build/convert.js":
/*!**************************************************!*\
  !*** ./node_modules/color-bits/build/convert.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n// Copyright 2022 The Chromium Authors. All rights reserved.\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n//\n// Source: https://github.com/ChromeDevTools/devtools-frontend/blob/c51201e6ee70370f7f1ac8a1a49dca7d4561aeaa/front_end/core/common/ColorConverter.ts\n// License: https://github.com/ChromeDevTools/devtools-frontend/blob/c51201e6ee70370f7f1ac8a1a49dca7d4561aeaa/LICENSE\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.labToXyzd50 = labToXyzd50;\nexports.xyzd50ToLab = xyzd50ToLab;\nexports.oklabToXyzd65 = oklabToXyzd65;\nexports.xyzd65ToOklab = xyzd65ToOklab;\nexports.lchToLab = lchToLab;\nexports.labToLch = labToLch;\nexports.displayP3ToXyzd50 = displayP3ToXyzd50;\nexports.xyzd50ToDisplayP3 = xyzd50ToDisplayP3;\nexports.proPhotoToXyzd50 = proPhotoToXyzd50;\nexports.xyzd50ToProPhoto = xyzd50ToProPhoto;\nexports.adobeRGBToXyzd50 = adobeRGBToXyzd50;\nexports.xyzd50ToAdobeRGB = xyzd50ToAdobeRGB;\nexports.rec2020ToXyzd50 = rec2020ToXyzd50;\nexports.xyzd50ToRec2020 = xyzd50ToRec2020;\nexports.xyzd50ToD65 = xyzd50ToD65;\nexports.xyzd65ToD50 = xyzd65ToD50;\nexports.xyzd65TosRGBLinear = xyzd65TosRGBLinear;\nexports.xyzd50TosRGBLinear = xyzd50TosRGBLinear;\nexports.srgbLinearToXyzd50 = srgbLinearToXyzd50;\nexports.srgbToXyzd50 = srgbToXyzd50;\nexports.xyzd50ToSrgb = xyzd50ToSrgb;\nexports.oklchToXyzd50 = oklchToXyzd50;\nexports.xyzd50ToOklch = xyzd50ToOklch;\n/**\n * Implementation of this module and all the tests are heavily influenced by\n * https://source.chromium.org/chromium/chromium/src/+/main:ui/gfx/color_conversions.cc\n */\n// https://en.wikipedia.org/wiki/CIELAB_color_space#Converting_between_CIELAB_and_CIEXYZ_coordinates\nconst D50_X = 0.9642;\nconst D50_Y = 1.0;\nconst D50_Z = 0.8251;\nfunction multiply(matrix, other) {\n    const dst = [0, 0, 0];\n    for (let row = 0; row < 3; ++row) {\n        dst[row] = matrix[row][0] * other[0] + matrix[row][1] * other[1] +\n            matrix[row][2] * other[2];\n    }\n    return dst;\n}\n// A transfer function mapping encoded values to linear values,\n// represented by this 7-parameter piecewise function:\n//\n//   linear = sign(encoded) *  (c*|encoded| + f)       , 0 <= |encoded| < d\n//          = sign(encoded) * ((a*|encoded| + b)^g + e), d <= |encoded|\n//\n// (A simple gamma transfer function sets g to gamma and a to 1.)\nclass TransferFunction {\n    g;\n    a;\n    b;\n    c;\n    d;\n    e;\n    f;\n    constructor(g, a, b = 0, c = 0, d = 0, e = 0, f = 0) {\n        this.g = g;\n        this.a = a;\n        this.b = b;\n        this.c = c;\n        this.d = d;\n        this.e = e;\n        this.f = f;\n    }\n    eval(val) {\n        const sign = val < 0 ? -1.0 : 1.0;\n        const abs = val * sign;\n        // 0 <= |encoded| < d path\n        if (abs < this.d) {\n            return sign * (this.c * abs + this.f);\n        }\n        // d <= |encoded| path\n        return sign * (Math.pow(this.a * abs + this.b, this.g) + this.e);\n    }\n}\nconst NAMED_TRANSFER_FN = {\n    sRGB: new TransferFunction(2.4, (1 / 1.055), (0.055 / 1.055), (1 / 12.92), 0.04045, 0.0, 0.0),\n    sRGB_INVERSE: new TransferFunction(0.416667, 1.13728, -0, 12.92, 0.0031308, -0.0549698, -0),\n    proPhotoRGB: new TransferFunction(1.8, 1),\n    proPhotoRGB_INVERSE: new TransferFunction(0.555556, 1, -0, 0, 0, 0, 0),\n    k2Dot2: new TransferFunction(2.2, 1.0),\n    k2Dot2_INVERSE: new TransferFunction(0.454545, 1),\n    rec2020: new TransferFunction(2.22222, 0.909672, 0.0903276, 0.222222, 0.0812429, 0, 0),\n    rec2020_INVERSE: new TransferFunction(0.45, 1.23439, -0, 4.5, 0.018054, -0.0993195, -0),\n};\nconst NAMED_GAMUTS = {\n    sRGB: [\n        [0.436065674, 0.385147095, 0.143066406],\n        [0.222488403, 0.716873169, 0.060607910],\n        [0.013916016, 0.097076416, 0.714096069],\n    ],\n    sRGB_INVERSE: [\n        [3.134112151374599, -1.6173924597114966, -0.4906334036481285],\n        [-0.9787872938826594, 1.9162795854799963, 0.0334547139520088],\n        [0.07198304248352326, -0.2289858493321844, 1.4053851325241447],\n    ],\n    displayP3: [\n        [0.515102, 0.291965, 0.157153],\n        [0.241182, 0.692236, 0.0665819],\n        [-0.00104941, 0.0418818, 0.784378],\n    ],\n    displayP3_INVERSE: [\n        [2.404045155982687, -0.9898986932663839, -0.3976317191366333],\n        [-0.8422283799266768, 1.7988505115115485, 0.016048170293157416],\n        [0.04818705979712955, -0.09737385156228891, 1.2735066448052303],\n    ],\n    adobeRGB: [\n        [0.60974, 0.20528, 0.14919],\n        [0.31111, 0.62567, 0.06322],\n        [0.01947, 0.06087, 0.74457],\n    ],\n    adobeRGB_INVERSE: [\n        [1.9625385510109137, -0.6106892546501431, -0.3413827467482388],\n        [-0.9787580455521, 1.9161624707082339, 0.03341676594241408],\n        [0.028696263137883395, -0.1406807819331586, 1.349252109991369],\n    ],\n    rec2020: [\n        [0.673459, 0.165661, 0.125100],\n        [0.279033, 0.675338, 0.0456288],\n        [-0.00193139, 0.0299794, 0.797162],\n    ],\n    rec2020_INVERSE: [\n        [1.647275201661012, -0.3936024771460771, -0.23598028884792507],\n        [-0.6826176165196962, 1.647617775014935, 0.01281626807852422],\n        [0.029662725298529837, -0.06291668721366285, 1.2533964313435522],\n    ],\n    xyz: [\n        [1.0, 0.0, 0.0],\n        [0.0, 1.0, 0.0],\n        [0.0, 0.0, 1.0],\n    ],\n};\nfunction degToRad(deg) {\n    return deg * (Math.PI / 180);\n}\nfunction radToDeg(rad) {\n    return rad * (180 / Math.PI);\n}\nfunction applyTransferFns(fn, r, g, b) {\n    return [fn.eval(r), fn.eval(g), fn.eval(b)];\n}\nconst OKLAB_TO_LMS_MATRIX = [\n    [0.99999999845051981432, 0.39633779217376785678, 0.21580375806075880339],\n    [1.0000000088817607767, -0.1055613423236563494, -0.063854174771705903402],\n    [1.0000000546724109177, -0.089484182094965759684, -1.2914855378640917399],\n];\n// Inverse of the OKLAB_TO_LMS_MATRIX\nconst LMS_TO_OKLAB_MATRIX = [\n    [0.2104542553, 0.7936177849999999, -0.0040720468],\n    [1.9779984951000003, -2.4285922049999997, 0.4505937099000001],\n    [0.025904037099999982, 0.7827717662, -0.8086757660000001],\n];\nconst XYZ_TO_LMS_MATRIX = [\n    [0.8190224432164319, 0.3619062562801221, -0.12887378261216414],\n    [0.0329836671980271, 0.9292868468965546, 0.03614466816999844],\n    [0.048177199566046255, 0.26423952494422764, 0.6335478258136937],\n];\n// Inverse of XYZ_TO_LMS_MATRIX\nconst LMS_TO_XYZ_MATRIX = [\n    [1.226879873374156, -0.5578149965554814, 0.2813910501772159],\n    [-0.040575762624313734, 1.1122868293970596, -0.07171106666151703],\n    [-0.07637294974672144, -0.4214933239627915, 1.586924024427242],\n];\nconst PRO_PHOTO_TO_XYZD50_MATRIX = [\n    [0.7976700747153241, 0.13519395152800417, 0.03135596341127167],\n    [0.28803902352472205, 0.7118744007923554, 0.00008661179538844252],\n    [2.739876695467402e-7, -0.0000014405226518969991, 0.825211112593861],\n];\n// Inverse of PRO_PHOTO_TO_XYZD50_MATRIX\nconst XYZD50_TO_PRO_PHOTO_MATRIX = [\n    [1.3459533710138858, -0.25561367037652133, -0.051116041522131374],\n    [-0.544600415668951, 1.5081687311475767, 0.020535163968720935],\n    [-0.0000013975622054109725, 0.000002717590904589903, 1.2118111696814942],\n];\nconst XYZD65_TO_XYZD50_MATRIX = [\n    [1.0478573189120088, 0.022907374491829943, -0.050162247377152525],\n    [0.029570500050499514, 0.9904755577034089, -0.017061518194840468],\n    [-0.00924047197558879, 0.015052921526981566, 0.7519708530777581],\n];\n// Inverse of XYZD65_TO_XYZD50_MATRIX\nconst XYZD50_TO_XYZD65_MATRIX = [\n    [0.9555366447632887, -0.02306009252137888, 0.06321844147263304],\n    [-0.028315378228764922, 1.009951351591575, 0.021026001591792402],\n    [0.012308773293784308, -0.02050053471777469, 1.3301947294775631],\n];\nconst XYZD65_TO_SRGB_MATRIX = [\n    [3.2408089365140573, -1.5375788839307314, -0.4985609572551541],\n    [-0.9692732213205414, 1.876110235238969, 0.041560501141251774],\n    [0.05567030990267439, -0.2040007921971802, 1.0571046720577026],\n];\nfunction labToXyzd50(l, a, b) {\n    let y = (l + 16.0) / 116.0;\n    let x = y + a / 500.0;\n    let z = y - b / 200.0;\n    function labInverseTransferFunction(t) {\n        const delta = (24.0 / 116.0);\n        if (t <= delta) {\n            return (108.0 / 841.0) * (t - (16.0 / 116.0));\n        }\n        return t * t * t;\n    }\n    x = labInverseTransferFunction(x) * D50_X;\n    y = labInverseTransferFunction(y) * D50_Y;\n    z = labInverseTransferFunction(z) * D50_Z;\n    return [x, y, z];\n}\nfunction xyzd50ToLab(x, y, z) {\n    function labTransferFunction(t) {\n        const deltaLimit = (24.0 / 116.0) * (24.0 / 116.0) * (24.0 / 116.0);\n        if (t <= deltaLimit) {\n            return (841.0 / 108.0) * t + (16.0 / 116.0);\n        }\n        return Math.pow(t, 1.0 / 3.0);\n    }\n    x = labTransferFunction(x / D50_X);\n    y = labTransferFunction(y / D50_Y);\n    z = labTransferFunction(z / D50_Z);\n    const l = 116.0 * y - 16.0;\n    const a = 500.0 * (x - y);\n    const b = 200.0 * (y - z);\n    return [l, a, b];\n}\nfunction oklabToXyzd65(l, a, b) {\n    const labInput = [l, a, b];\n    const lmsIntermediate = multiply(OKLAB_TO_LMS_MATRIX, labInput);\n    lmsIntermediate[0] = lmsIntermediate[0] * lmsIntermediate[0] * lmsIntermediate[0];\n    lmsIntermediate[1] = lmsIntermediate[1] * lmsIntermediate[1] * lmsIntermediate[1];\n    lmsIntermediate[2] = lmsIntermediate[2] * lmsIntermediate[2] * lmsIntermediate[2];\n    const xyzOutput = multiply(LMS_TO_XYZ_MATRIX, lmsIntermediate);\n    return xyzOutput;\n}\nfunction xyzd65ToOklab(x, y, z) {\n    const xyzInput = [x, y, z];\n    const lmsIntermediate = multiply(XYZ_TO_LMS_MATRIX, xyzInput);\n    lmsIntermediate[0] = Math.pow(lmsIntermediate[0], 1.0 / 3.0);\n    lmsIntermediate[1] = Math.pow(lmsIntermediate[1], 1.0 / 3.0);\n    lmsIntermediate[2] = Math.pow(lmsIntermediate[2], 1.0 / 3.0);\n    const labOutput = multiply(LMS_TO_OKLAB_MATRIX, lmsIntermediate);\n    return [labOutput[0], labOutput[1], labOutput[2]];\n}\nfunction lchToLab(l, c, h) {\n    if (h === undefined) {\n        return [l, 0, 0];\n    }\n    return [l, c * Math.cos(degToRad(h)), c * Math.sin(degToRad(h))];\n}\nfunction labToLch(l, a, b) {\n    return [l, Math.sqrt(a * a + b * b), radToDeg(Math.atan2(b, a))];\n}\nfunction displayP3ToXyzd50(r, g, b) {\n    const [mappedR, mappedG, mappedB] = applyTransferFns(NAMED_TRANSFER_FN.sRGB, r, g, b);\n    const rgbInput = [mappedR, mappedG, mappedB];\n    const xyzOutput = multiply(NAMED_GAMUTS.displayP3, rgbInput);\n    return xyzOutput;\n}\nfunction xyzd50ToDisplayP3(x, y, z) {\n    const xyzInput = [x, y, z];\n    const rgbOutput = multiply(NAMED_GAMUTS.displayP3_INVERSE, xyzInput);\n    return applyTransferFns(NAMED_TRANSFER_FN.sRGB_INVERSE, rgbOutput[0], rgbOutput[1], rgbOutput[2]);\n}\nfunction proPhotoToXyzd50(r, g, b) {\n    const [mappedR, mappedG, mappedB] = applyTransferFns(NAMED_TRANSFER_FN.proPhotoRGB, r, g, b);\n    const rgbInput = [mappedR, mappedG, mappedB];\n    const xyzOutput = multiply(PRO_PHOTO_TO_XYZD50_MATRIX, rgbInput);\n    return xyzOutput;\n}\nfunction xyzd50ToProPhoto(x, y, z) {\n    const xyzInput = [x, y, z];\n    const rgbOutput = multiply(XYZD50_TO_PRO_PHOTO_MATRIX, xyzInput);\n    return applyTransferFns(NAMED_TRANSFER_FN.proPhotoRGB_INVERSE, rgbOutput[0], rgbOutput[1], rgbOutput[2]);\n}\nfunction adobeRGBToXyzd50(r, g, b) {\n    const [mappedR, mappedG, mappedB] = applyTransferFns(NAMED_TRANSFER_FN.k2Dot2, r, g, b);\n    const rgbInput = [mappedR, mappedG, mappedB];\n    const xyzOutput = multiply(NAMED_GAMUTS.adobeRGB, rgbInput);\n    return xyzOutput;\n}\nfunction xyzd50ToAdobeRGB(x, y, z) {\n    const xyzInput = [x, y, z];\n    const rgbOutput = multiply(NAMED_GAMUTS.adobeRGB_INVERSE, xyzInput);\n    return applyTransferFns(NAMED_TRANSFER_FN.k2Dot2_INVERSE, rgbOutput[0], rgbOutput[1], rgbOutput[2]);\n}\nfunction rec2020ToXyzd50(r, g, b) {\n    const [mappedR, mappedG, mappedB] = applyTransferFns(NAMED_TRANSFER_FN.rec2020, r, g, b);\n    const rgbInput = [mappedR, mappedG, mappedB];\n    const xyzOutput = multiply(NAMED_GAMUTS.rec2020, rgbInput);\n    return xyzOutput;\n}\nfunction xyzd50ToRec2020(x, y, z) {\n    const xyzInput = [x, y, z];\n    const rgbOutput = multiply(NAMED_GAMUTS.rec2020_INVERSE, xyzInput);\n    return applyTransferFns(NAMED_TRANSFER_FN.rec2020_INVERSE, rgbOutput[0], rgbOutput[1], rgbOutput[2]);\n}\nfunction xyzd50ToD65(x, y, z) {\n    const xyzInput = [x, y, z];\n    const xyzOutput = multiply(XYZD50_TO_XYZD65_MATRIX, xyzInput);\n    return xyzOutput;\n}\nfunction xyzd65ToD50(x, y, z) {\n    const xyzInput = [x, y, z];\n    const xyzOutput = multiply(XYZD65_TO_XYZD50_MATRIX, xyzInput);\n    return xyzOutput;\n}\nfunction xyzd65TosRGBLinear(x, y, z) {\n    const xyzInput = [x, y, z];\n    const rgbResult = multiply(XYZD65_TO_SRGB_MATRIX, xyzInput);\n    return rgbResult;\n}\nfunction xyzd50TosRGBLinear(x, y, z) {\n    const xyzInput = [x, y, z];\n    const rgbResult = multiply(NAMED_GAMUTS.sRGB_INVERSE, xyzInput);\n    return rgbResult;\n}\nfunction srgbLinearToXyzd50(r, g, b) {\n    const rgbInput = [r, g, b];\n    const xyzOutput = multiply(NAMED_GAMUTS.sRGB, rgbInput);\n    return xyzOutput;\n}\nfunction srgbToXyzd50(r, g, b) {\n    const [mappedR, mappedG, mappedB] = applyTransferFns(NAMED_TRANSFER_FN.sRGB, r, g, b);\n    const rgbInput = [mappedR, mappedG, mappedB];\n    const xyzOutput = multiply(NAMED_GAMUTS.sRGB, rgbInput);\n    return xyzOutput;\n}\nfunction xyzd50ToSrgb(x, y, z) {\n    const xyzInput = [x, y, z];\n    const rgbOutput = multiply(NAMED_GAMUTS.sRGB_INVERSE, xyzInput);\n    return applyTransferFns(NAMED_TRANSFER_FN.sRGB_INVERSE, rgbOutput[0], rgbOutput[1], rgbOutput[2]);\n}\nfunction oklchToXyzd50(lInput, c, h) {\n    const [l, a, b] = lchToLab(lInput, c, h);\n    const [x65, y65, z65] = oklabToXyzd65(l, a, b);\n    return xyzd65ToD50(x65, y65, z65);\n}\nfunction xyzd50ToOklch(x, y, z) {\n    const [x65, y65, z65] = xyzd50ToD65(x, y, z);\n    const [l, a, b] = xyzd65ToOklab(x65, y65, z65);\n    return labToLch(l, a, b);\n}\n//# sourceMappingURL=convert.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/color-bits/build/convert.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/color-bits/build/core.js":
/*!***********************************************!*\
  !*** ./node_modules/color-bits/build/core.js ***!
  \***********************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.OFFSET_A = exports.OFFSET_B = exports.OFFSET_G = exports.OFFSET_R = void 0;\nexports.newColor = newColor;\nexports.from = from;\nexports.toNumber = toNumber;\nexports.getRed = getRed;\nexports.getGreen = getGreen;\nexports.getBlue = getBlue;\nexports.getAlpha = getAlpha;\nexports.setRed = setRed;\nexports.setGreen = setGreen;\nexports.setBlue = setBlue;\nexports.setAlpha = setAlpha;\nconst bit = __importStar(__webpack_require__(/*! ./bit */ \"(ssr)/./node_modules/color-bits/build/bit.js\"));\nconst { cast, get, set } = bit;\nexports.OFFSET_R = 24;\nexports.OFFSET_G = 16;\nexports.OFFSET_B = 8;\nexports.OFFSET_A = 0;\n/**\n * Creates a new color from the given RGBA components.\n * Every component should be in the [0, 255] range.\n */\nfunction newColor(r, g, b, a) {\n    return ((r << exports.OFFSET_R) +\n        (g << exports.OFFSET_G) +\n        (b << exports.OFFSET_B) +\n        (a << exports.OFFSET_A));\n}\n/**\n * Creates a new color from the given number value, e.g. 0x599eff.\n */\nfunction from(color) {\n    return newColor(get(color, exports.OFFSET_R), get(color, exports.OFFSET_G), get(color, exports.OFFSET_B), get(color, exports.OFFSET_A));\n}\n/**\n * Turns the color into its equivalent number representation.\n * This is essentially a cast from int32 to uint32.\n */\nfunction toNumber(color) {\n    return cast(color);\n}\nfunction getRed(c) { return get(c, exports.OFFSET_R); }\nfunction getGreen(c) { return get(c, exports.OFFSET_G); }\nfunction getBlue(c) { return get(c, exports.OFFSET_B); }\nfunction getAlpha(c) { return get(c, exports.OFFSET_A); }\nfunction setRed(c, value) { return set(c, exports.OFFSET_R, value); }\nfunction setGreen(c, value) { return set(c, exports.OFFSET_G, value); }\nfunction setBlue(c, value) { return set(c, exports.OFFSET_B, value); }\nfunction setAlpha(c, value) { return set(c, exports.OFFSET_A, value); }\n//# sourceMappingURL=core.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/color-bits/build/core.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/color-bits/build/format.js":
/*!*************************************************!*\
  !*** ./node_modules/color-bits/build/format.js ***!
  \*************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.format = void 0;\nexports.formatHEXA = formatHEXA;\nexports.formatHEX = formatHEX;\nexports.formatRGBA = formatRGBA;\nexports.toRGBA = toRGBA;\nexports.formatHSLA = formatHSLA;\nexports.toHSLA = toHSLA;\nexports.formatHWBA = formatHWBA;\nexports.toHWBA = toHWBA;\nconst core = __importStar(__webpack_require__(/*! ./core */ \"(ssr)/./node_modules/color-bits/build/core.js\"));\nconst { getRed, getGreen, getBlue, getAlpha } = core;\n// Return buffer, avoid allocations\nconst buffer = [0, 0, 0];\n/**\n * Map 8-bits value to its hexadecimal representation\n * ['00', '01', '02', ..., 'fe', 'ff']\n */\nconst FORMAT_HEX = Array.from({ length: 256 })\n    .map((_, byte) => byte.toString(16).padStart(2, '0'));\n/** Format to a #RRGGBBAA string */\nexports.format = formatHEXA;\n/** Format to a #RRGGBBAA string */\nfunction formatHEXA(color) {\n    return ('#' +\n        FORMAT_HEX[getRed(color)] +\n        FORMAT_HEX[getGreen(color)] +\n        FORMAT_HEX[getBlue(color)] +\n        FORMAT_HEX[getAlpha(color)]);\n}\nfunction formatHEX(color) {\n    return ('#' +\n        FORMAT_HEX[getRed(color)] +\n        FORMAT_HEX[getGreen(color)] +\n        FORMAT_HEX[getBlue(color)]);\n}\nfunction formatRGBA(color) {\n    return `rgba(${getRed(color)} ${getGreen(color)} ${getBlue(color)} / ${getAlpha(color) / 255})`;\n}\nfunction toRGBA(color) {\n    return {\n        r: getRed(color),\n        g: getGreen(color),\n        b: getBlue(color),\n        a: getAlpha(color),\n    };\n}\nfunction formatHSLA(color) {\n    rgbToHSL(getRed(color), getGreen(color), getBlue(color));\n    const h = buffer[0];\n    const s = buffer[1];\n    const l = buffer[2];\n    const a = getAlpha(color) / 255;\n    return `hsla(${h} ${s}% ${l}% / ${a})`;\n}\nfunction toHSLA(color) {\n    rgbToHSL(getRed(color), getGreen(color), getBlue(color));\n    const h = buffer[0];\n    const s = buffer[1];\n    const l = buffer[2];\n    const a = getAlpha(color) / 255;\n    return { h, s, l, a };\n}\nfunction formatHWBA(color) {\n    rgbToHWB(getRed(color), getGreen(color), getBlue(color));\n    const h = buffer[0];\n    const w = buffer[1];\n    const b = buffer[2];\n    const a = getAlpha(color) / 255;\n    return `hsla(${h} ${w}% ${b}% / ${a})`;\n}\nfunction toHWBA(color) {\n    rgbToHWB(getRed(color), getGreen(color), getBlue(color));\n    const h = buffer[0];\n    const w = buffer[1];\n    const b = buffer[2];\n    const a = getAlpha(color) / 255;\n    return { h, w, b, a };\n}\n// Conversion functions\n// https://www.30secondsofcode.org/js/s/rgb-hex-hsl-hsb-color-format-conversion/\nfunction rgbToHSL(r, g, b) {\n    r /= 255;\n    g /= 255;\n    b /= 255;\n    const l = Math.max(r, g, b);\n    const s = l - Math.min(r, g, b);\n    const h = s\n        ? l === r\n            ? (g - b) / s\n            : l === g\n                ? 2 + (b - r) / s\n                : 4 + (r - g) / s\n        : 0;\n    buffer[0] = 60 * h < 0 ? 60 * h + 360 : 60 * h;\n    buffer[1] = 100 * (s ? (l <= 0.5 ? s / (2 * l - s) : s / (2 - (2 * l - s))) : 0);\n    buffer[2] = (100 * (2 * l - s)) / 2;\n}\n// https://stackoverflow.com/a/29463581/3112706\nfunction rgbToHWB(r, g, b) {\n    r /= 255;\n    g /= 255;\n    b /= 255;\n    const w = Math.min(r, g, b);\n    const v = Math.max(r, g, b);\n    const black = 1 - v;\n    if (v === w) {\n        buffer[0] = 0;\n        buffer[1] = w;\n        buffer[2] = black;\n        return;\n    }\n    let f = r === w ? g - b : (g === w ? b - r : r - g);\n    let i = r === w ? 3 : (g === w ? 5 : 1);\n    buffer[0] = (i - f / (v - w)) / 6;\n    buffer[1] = w;\n    buffer[2] = black;\n}\n//# sourceMappingURL=format.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/color-bits/build/format.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/color-bits/build/functions.js":
/*!****************************************************!*\
  !*** ./node_modules/color-bits/build/functions.js ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.alpha = alpha;\nexports.darken = darken;\nexports.lighten = lighten;\nexports.blend = blend;\nexports.getLuminance = getLuminance;\nconst core = __importStar(__webpack_require__(/*! ./core */ \"(ssr)/./node_modules/color-bits/build/core.js\"));\nconst { getRed, getGreen, getBlue, getAlpha, setAlpha, newColor } = core;\n/**\n * Modifies color alpha channel.\n * @param color - Color\n * @param value - Value in the range [0, 1]\n */\nfunction alpha(color, value) {\n    return setAlpha(color, Math.round(value * 255));\n}\n/**\n * Darkens a color.\n * @param color - Color\n * @param coefficient - Multiplier in the range [0, 1]\n */\nfunction darken(color, coefficient) {\n    const r = getRed(color);\n    const g = getGreen(color);\n    const b = getBlue(color);\n    const a = getAlpha(color);\n    const factor = 1 - coefficient;\n    return newColor(r * factor, g * factor, b * factor, a);\n}\n/**\n * Lighten a color.\n * @param color - Color\n * @param coefficient - Multiplier in the range [0, 1]\n */\nfunction lighten(color, coefficient) {\n    const r = getRed(color);\n    const g = getGreen(color);\n    const b = getBlue(color);\n    const a = getAlpha(color);\n    return newColor(r + (255 - r) * coefficient, g + (255 - g) * coefficient, b + (255 - b) * coefficient, a);\n}\n/**\n * Blend (aka mix) two colors together.\n * @param background The background color\n * @param overlay The overlay color that is affected by @opacity\n * @param opacity Opacity (alpha) for @overlay\n * @param [gamma=1.0] Gamma correction coefficient. `1.0` to match browser behavior, `2.2` for gamma-corrected blending.\n */\nfunction blend(background, overlay, opacity, gamma = 1.0) {\n    const blendChannel = (b, o) => Math.round((b ** (1 / gamma) * (1 - opacity) + o ** (1 / gamma) * opacity) ** gamma);\n    const r = blendChannel(getRed(background), getRed(overlay));\n    const g = blendChannel(getGreen(background), getGreen(overlay));\n    const b = blendChannel(getBlue(background), getBlue(overlay));\n    return newColor(r, g, b, 255);\n}\n/**\n * The relative brightness of any point in a color space, normalized to 0 for\n * darkest black and 1 for lightest white.\n * @returns The relative brightness of the color in the range 0 - 1, with 3 digits precision\n */\nfunction getLuminance(color) {\n    const r = getRed(color) / 255;\n    const g = getGreen(color) / 255;\n    const b = getBlue(color) / 255;\n    const apply = (v) => v <= 0.03928 ? v / 12.92 : ((v + 0.055) / 1.055) ** 2.4;\n    const r1 = apply(r);\n    const g1 = apply(g);\n    const b1 = apply(b);\n    return Math.round((0.2126 * r1 + 0.7152 * g1 + 0.0722 * b1) * 1000) / 1000;\n}\n//# sourceMappingURL=functions.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/color-bits/build/functions.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/color-bits/build/index.js":
/*!************************************************!*\
  !*** ./node_modules/color-bits/build/index.js ***!
  \************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./core */ \"(ssr)/./node_modules/color-bits/build/core.js\"), exports);\n__exportStar(__webpack_require__(/*! ./parse */ \"(ssr)/./node_modules/color-bits/build/parse.js\"), exports);\n__exportStar(__webpack_require__(/*! ./format */ \"(ssr)/./node_modules/color-bits/build/format.js\"), exports);\n__exportStar(__webpack_require__(/*! ./functions */ \"(ssr)/./node_modules/color-bits/build/functions.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/color-bits/build/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/color-bits/build/parse.js":
/*!************************************************!*\
  !*** ./node_modules/color-bits/build/parse.js ***!
  \************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.parse = parse;\nexports.parseHex = parseHex;\nexports.parseColor = parseColor;\nconst core_1 = __webpack_require__(/*! ./core */ \"(ssr)/./node_modules/color-bits/build/core.js\");\nconst convert = __importStar(__webpack_require__(/*! ./convert */ \"(ssr)/./node_modules/color-bits/build/convert.js\"));\nconst HASH = '#'.charCodeAt(0);\nconst PERCENT = '%'.charCodeAt(0);\nconst G = 'g'.charCodeAt(0);\nconst N = 'n'.charCodeAt(0);\nconst D = 'd'.charCodeAt(0);\nconst E = 'e'.charCodeAt(0);\n/**\n * Approximative CSS colorspace string pattern, e.g. rgb(), color()\n */\nconst PATTERN = (() => {\n    const NAME = '(\\\\w+)';\n    const SEPARATOR = '[\\\\s,\\\\/]';\n    const VALUE = '([^\\\\s,\\\\/]+)';\n    const SEPARATOR_THEN_VALUE = `(?:${SEPARATOR}+${VALUE})`;\n    return new RegExp(`${NAME}\\\\(\n      ${SEPARATOR}*\n      ${VALUE}\n      ${SEPARATOR_THEN_VALUE}\n      ${SEPARATOR_THEN_VALUE}\n      ${SEPARATOR_THEN_VALUE}?\n      ${SEPARATOR_THEN_VALUE}?\n      ${SEPARATOR}*\n    \\\\)`.replace(/\\s/g, ''));\n})();\n/**\n * Parse CSS color\n * @param color CSS color string: #xxx, #xxxxxx, #xxxxxxxx, rgb(), rgba(), hsl(), hsla(), color()\n */\nfunction parse(color) {\n    if (color.charCodeAt(0) === HASH) {\n        return parseHex(color);\n    }\n    else {\n        return parseColor(color);\n    }\n}\n/**\n * Parse hexadecimal CSS color\n * @param color Hex color string: #xxx, #xxxxxx, #xxxxxxxx\n */\nfunction parseHex(color) {\n    let r = 0x00;\n    let g = 0x00;\n    let b = 0x00;\n    let a = 0xff;\n    switch (color.length) {\n        // #59f\n        case 4: {\n            r = (hexValue(color.charCodeAt(1)) << 4) + hexValue(color.charCodeAt(1));\n            g = (hexValue(color.charCodeAt(2)) << 4) + hexValue(color.charCodeAt(2));\n            b = (hexValue(color.charCodeAt(3)) << 4) + hexValue(color.charCodeAt(3));\n            break;\n        }\n        // #5599ff\n        case 7: {\n            r = (hexValue(color.charCodeAt(1)) << 4) + hexValue(color.charCodeAt(2));\n            g = (hexValue(color.charCodeAt(3)) << 4) + hexValue(color.charCodeAt(4));\n            b = (hexValue(color.charCodeAt(5)) << 4) + hexValue(color.charCodeAt(6));\n            break;\n        }\n        // #5599ff88\n        case 9: {\n            r = (hexValue(color.charCodeAt(1)) << 4) + hexValue(color.charCodeAt(2));\n            g = (hexValue(color.charCodeAt(3)) << 4) + hexValue(color.charCodeAt(4));\n            b = (hexValue(color.charCodeAt(5)) << 4) + hexValue(color.charCodeAt(6));\n            a = (hexValue(color.charCodeAt(7)) << 4) + hexValue(color.charCodeAt(8));\n            break;\n        }\n        default: {\n            break;\n        }\n    }\n    return (0, core_1.newColor)(r, g, b, a);\n}\n// https://lemire.me/blog/2019/04/17/parsing-short-hexadecimal-strings-efficiently/\nfunction hexValue(c) {\n    return (c & 0xF) + 9 * (c >> 6);\n}\n/**\n * Parse CSS color\n * https://developer.mozilla.org/en-US/docs/Web/CSS/color_value\n * @param color CSS color string: rgb(), rgba(), hsl(), hsla(), color()\n */\nfunction parseColor(color) {\n    const match = PATTERN.exec(color);\n    if (match === null) {\n        throw new Error(`Color.parse(): invalid CSS color: \"${color}\"`);\n    }\n    const format = match[1];\n    const p1 = match[2];\n    const p2 = match[3];\n    const p3 = match[4];\n    const p4 = match[5];\n    const p5 = match[6];\n    switch (format) {\n        case 'rgb':\n        case 'rgba': {\n            const r = parseColorChannel(p1);\n            const g = parseColorChannel(p2);\n            const b = parseColorChannel(p3);\n            const a = p4 ? parseAlphaChannel(p4) : 255;\n            return (0, core_1.newColor)(r, g, b, a);\n        }\n        case 'hsl':\n        case 'hsla': {\n            const h = parseAngle(p1);\n            const s = parsePercentage(p2);\n            const l = parsePercentage(p3);\n            const a = p4 ? parseAlphaChannel(p4) : 255;\n            // https://stackoverflow.com/a/9493060/3112706\n            let r, g, b;\n            if (s === 0) {\n                r = g = b = Math.round(l * 255); // achromatic\n            }\n            else {\n                const q = l < 0.5 ? l * (1 + s) : l + s - l * s;\n                const p = 2 * l - q;\n                r = Math.round(hueToRGB(p, q, h + 1 / 3) * 255);\n                g = Math.round(hueToRGB(p, q, h) * 255);\n                b = Math.round(hueToRGB(p, q, h - 1 / 3) * 255);\n            }\n            return (0, core_1.newColor)(r, g, b, a);\n        }\n        case 'hwb': {\n            const h = parseAngle(p1);\n            const w = parsePercentage(p2);\n            const bl = parsePercentage(p3);\n            const a = p4 ? parseAlphaChannel(p4) : 255;\n            /* https://drafts.csswg.org/css-color/#hwb-to-rgb */\n            const s = 1.0;\n            const l = 0.5;\n            // Same as HSL to RGB\n            const q = l < 0.5 ? l * (1 + s) : l + s - l * s;\n            const p = 2 * l - q;\n            let r = Math.round(hueToRGB(p, q, h + 1 / 3) * 255);\n            let g = Math.round(hueToRGB(p, q, h) * 255);\n            let b = Math.round(hueToRGB(p, q, h - 1 / 3) * 255);\n            // Then HWB\n            r = hwbApply(r, w, bl);\n            g = hwbApply(g, w, bl);\n            b = hwbApply(b, w, bl);\n            return (0, core_1.newColor)(r, g, b, a);\n        }\n        case 'lab': {\n            const l = parsePercentageFor(p1, 100);\n            const aa = parsePercentageFor(p2, 125);\n            const b = parsePercentageFor(p3, 125);\n            const a = p4 ? parseAlphaChannel(p4) : 255;\n            return newColorFromArray(a, convert.xyzd50ToSrgb(...convert.labToXyzd50(l, aa, b)));\n        }\n        case 'lch': {\n            const l = parsePercentageFor(p1, 100);\n            const c = parsePercentageFor(p2, 150);\n            const h = parseAngle(p3) * 360;\n            const a = p4 ? parseAlphaChannel(p4) : 255;\n            return newColorFromArray(a, convert.xyzd50ToSrgb(...convert.labToXyzd50(...convert.lchToLab(l, c, h))));\n        }\n        case 'oklab': {\n            const l = parsePercentageFor(p1, 1);\n            const aa = parsePercentageFor(p2, 0.4);\n            const b = parsePercentageFor(p3, 0.4);\n            const a = p4 ? parseAlphaChannel(p4) : 255;\n            return newColorFromArray(a, convert.xyzd50ToSrgb(...convert.xyzd65ToD50(...convert.oklabToXyzd65(l, aa, b))));\n        }\n        case 'oklch': {\n            const l = parsePercentageOrValue(p1);\n            const c = parsePercentageOrValue(p2);\n            const h = parsePercentageOrValue(p3);\n            const a = p4 ? parseAlphaChannel(p4) : 255;\n            return newColorFromArray(a, convert.xyzd50ToSrgb(...convert.oklchToXyzd50(l, c, h)));\n        }\n        case 'color': {\n            // https://drafts.csswg.org/css-color-4/#color-function\n            const colorspace = p1;\n            const c1 = parsePercentageOrValue(p2);\n            const c2 = parsePercentageOrValue(p3);\n            const c3 = parsePercentageOrValue(p4);\n            const a = p5 ? parseAlphaChannel(p5) : 255;\n            switch (colorspace) {\n                // RGB color spaces\n                case 'srgb': {\n                    return newColorFromArray(a, [c1, c2, c3]);\n                }\n                case 'srgb-linear': {\n                    return newColorFromArray(a, convert.xyzd50ToSrgb(...convert.srgbLinearToXyzd50(c1, c2, c3)));\n                }\n                case 'display-p3': {\n                    return newColorFromArray(a, convert.xyzd50ToSrgb(...convert.displayP3ToXyzd50(c1, c2, c3)));\n                }\n                case 'a98-rgb': {\n                    return newColorFromArray(a, convert.xyzd50ToSrgb(...convert.adobeRGBToXyzd50(c1, c2, c3)));\n                }\n                case 'prophoto-rgb': {\n                    return newColorFromArray(a, convert.xyzd50ToSrgb(...convert.proPhotoToXyzd50(c1, c2, c3)));\n                }\n                case 'rec2020': {\n                    return newColorFromArray(a, convert.xyzd50ToSrgb(...convert.rec2020ToXyzd50(c1, c2, c3)));\n                }\n                // XYZ color spaces\n                case 'xyz':\n                case 'xyz-d65': {\n                    return newColorFromArray(a, convert.xyzd50ToSrgb(...convert.xyzd65ToD50(c1, c2, c3)));\n                }\n                case 'xyz-d50': {\n                    return newColorFromArray(a, convert.xyzd50ToSrgb(c1, c2, c3));\n                }\n                default:\n            }\n        }\n        default:\n    }\n    throw new Error(`Color.parse(): invalid CSS color: \"${color}\"`);\n}\n/**\n * Accepts: \"50%\", \"128\"\n * https://developer.mozilla.org/en-US/docs/Web/CSS/color_value/rgb#values\n * @returns a value in the 0 to 255 range\n */\nfunction parseColorChannel(channel) {\n    if (channel.charCodeAt(channel.length - 1) === PERCENT) {\n        return Math.round((parseFloat(channel) / 100) * 255);\n    }\n    return Math.round(parseFloat(channel));\n}\n/**\n * Accepts: \"50%\", \".5\", \"0.5\"\n * https://developer.mozilla.org/en-US/docs/Web/CSS/alpha-value\n * @returns a value in the [0, 255] range\n */\nfunction parseAlphaChannel(channel) {\n    return Math.round(parseAlphaValue(channel) * 255);\n}\n/**\n * Accepts: \"50%\", \".5\", \"0.5\"\n * https://developer.mozilla.org/en-US/docs/Web/CSS/alpha-value\n * @returns a value in the [0, 1] range\n */\nfunction parseAlphaValue(channel) {\n    if (channel.charCodeAt(0) === N) {\n        return 0;\n    }\n    if (channel.charCodeAt(channel.length - 1) === PERCENT) {\n        return parseFloat(channel) / 100;\n    }\n    return parseFloat(channel);\n}\n/**\n * Accepts: \"360\", \"360deg\", \"400grad\", \"6.28rad\", \"1turn\", \"none\"\n * https://developer.mozilla.org/en-US/docs/Web/CSS/angle\n * @returns a value in the 0.0 to 1.0 range\n */\nfunction parseAngle(angle) {\n    let factor = 1;\n    switch (angle.charCodeAt(angle.length - 1)) {\n        case E: {\n            // 'none'\n            return 0;\n        }\n        case D: {\n            // 'rad', 'grad'\n            if (angle.charCodeAt(Math.max(0, angle.length - 4)) === G) {\n                // 'grad'\n                factor = 400;\n            }\n            else {\n                // 'rad'\n                factor = 2 * Math.PI; // TAU\n            }\n            break;\n        }\n        case N: {\n            // 'turn'\n            factor = 1;\n            break;\n        }\n        // case G: // 'deg', but no need to check as it's also the default\n        default: {\n            factor = 360;\n        }\n    }\n    return parseFloat(angle) / factor;\n}\n/**\n * Accepts: \"100%\", \"none\"\n * @returns a value in the 0.0 to 1.0 range\n */\nfunction parsePercentage(value) {\n    if (value.charCodeAt(0) === N) {\n        return 0;\n    }\n    return parseFloat(value) / 100;\n}\n/**\n * Accepts: \"1.0\", \"100%\", \"none\"\n * @returns a value in the 0.0 to 1.0 range\n */\nfunction parsePercentageOrValue(value) {\n    if (value.charCodeAt(0) === N) {\n        return 0;\n    }\n    if (value.charCodeAt(value.length - 1) === PERCENT) {\n        return parseFloat(value) / 100;\n    }\n    return parseFloat(value);\n}\n/**\n * Accepts: \"100\", \"100%\", \"none\"\n * @returns a value in the -@range to @range range\n */\nfunction parsePercentageFor(value, range) {\n    if (value.charCodeAt(0) === N) {\n        return 0;\n    }\n    if (value.charCodeAt(value.length - 1) === PERCENT) {\n        return parseFloat(value) / 100 * range;\n    }\n    return parseFloat(value);\n}\n// HSL functions\nfunction hueToRGB(p, q, t) {\n    if (t < 0) {\n        t += 1;\n    }\n    ;\n    if (t > 1) {\n        t -= 1;\n    }\n    ;\n    if (t < 1 / 6) {\n        return p + (q - p) * 6 * t;\n    }\n    ;\n    if (t < 1 / 2) {\n        return q;\n    }\n    ;\n    if (t < 2 / 3) {\n        return p + (q - p) * (2 / 3 - t) * 6;\n    }\n    ;\n    {\n        return p;\n    }\n    ;\n}\n// HWB functions\nfunction hwbApply(channel, w, b) {\n    let result = channel / 255;\n    result *= 1 - w - b;\n    result += w;\n    return Math.round(result * 255);\n}\nfunction clamp(value) {\n    return Math.max(0, Math.min(255, value));\n}\nfunction newColorFromArray(a, rgb) {\n    const r = clamp(Math.round(rgb[0] * 255));\n    const g = clamp(Math.round(rgb[1] * 255));\n    const b = clamp(Math.round(rgb[2] * 255));\n    return (0, core_1.newColor)(r, g, b, a);\n}\n//# sourceMappingURL=parse.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/color-bits/build/parse.js\n");

/***/ })

};
;