"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_zig_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/zig.mjs":
/*!**************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/zig.mjs ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Zig\\\",\\\"fileTypes\\\":[\\\"zig\\\",\\\"zon\\\"],\\\"name\\\":\\\"zig\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#strings\\\"},{\\\"include\\\":\\\"#keywords\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"include\\\":\\\"#punctuation\\\"},{\\\"include\\\":\\\"#numbers\\\"},{\\\"include\\\":\\\"#support\\\"},{\\\"include\\\":\\\"#variables\\\"}],\\\"repository\\\":{\\\"commentContents\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(TODO|FIXME|XXX|NOTE)\\\\\\\\b:?\\\",\\\"name\\\":\\\"keyword.todo.zig\\\"}]},\\\"comments\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"//[!/](?=[^/])\\\",\\\"end\\\":\\\"$\\\",\\\"name\\\":\\\"comment.line.documentation.zig\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#commentContents\\\"}]},{\\\"begin\\\":\\\"//\\\",\\\"end\\\":\\\"$\\\",\\\"name\\\":\\\"comment.line.double-slash.zig\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#commentContents\\\"}]}]},\\\"keywords\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\binline\\\\\\\\b(?!\\\\\\\\s*\\\\\\\\bfn\\\\\\\\b)\\\",\\\"name\\\":\\\"keyword.control.repeat.zig\\\"},{\\\"match\\\":\\\"\\\\\\\\b(while|for)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.repeat.zig\\\"},{\\\"match\\\":\\\"\\\\\\\\b(extern|packed|export|pub|noalias|inline|comptime|volatile|align|linksection|threadlocal|allowzero|noinline|callconv)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.storage.zig\\\"},{\\\"match\\\":\\\"\\\\\\\\b(struct|enum|union|opaque)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.structure.zig\\\"},{\\\"match\\\":\\\"\\\\\\\\b(asm|unreachable)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.statement.zig\\\"},{\\\"match\\\":\\\"\\\\\\\\b(break|return|continue|defer|errdefer)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.flow.zig\\\"},{\\\"match\\\":\\\"\\\\\\\\b(await|resume|suspend|async|nosuspend)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.async.zig\\\"},{\\\"match\\\":\\\"\\\\\\\\b(try|catch)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.trycatch.zig\\\"},{\\\"match\\\":\\\"\\\\\\\\b(if|else|switch|orelse)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.conditional.zig\\\"},{\\\"match\\\":\\\"\\\\\\\\b(null|undefined)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.constant.default.zig\\\"},{\\\"match\\\":\\\"\\\\\\\\b(true|false)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.constant.bool.zig\\\"},{\\\"match\\\":\\\"\\\\\\\\b(usingnamespace|test|and|or)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.default.zig\\\"},{\\\"match\\\":\\\"\\\\\\\\b(bool|void|noreturn|type|error|anyerror|anyframe|anytype|anyopaque)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.type.zig\\\"},{\\\"match\\\":\\\"\\\\\\\\b(f16|f32|f64|f80|f128|u\\\\\\\\d+|i\\\\\\\\d+|isize|usize|comptime_int|comptime_float)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.type.integer.zig\\\"},{\\\"match\\\":\\\"\\\\\\\\b(c_(?:char|short|ushort|int|uint|long|ulong|longlong|ulonglong|longdouble))\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.type.c.zig\\\"}]},\\\"numbers\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b0x\\\\\\\\h[_\\\\\\\\h]*(\\\\\\\\.\\\\\\\\h[_\\\\\\\\h]*)?([pP][+-]?[_\\\\\\\\h]+)?\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.hexfloat.zig\\\"},{\\\"match\\\":\\\"\\\\\\\\b[0-9][0-9_]*(\\\\\\\\.[0-9][0-9_]*)?([eE][+-]?[0-9_]+)?\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.float.zig\\\"},{\\\"match\\\":\\\"\\\\\\\\b[0-9][0-9_]*\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.decimal.zig\\\"},{\\\"match\\\":\\\"\\\\\\\\b0x[_\\\\\\\\h]+\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.hexadecimal.zig\\\"},{\\\"match\\\":\\\"\\\\\\\\b0o[0-7_]+\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.octal.zig\\\"},{\\\"match\\\":\\\"\\\\\\\\b0b[01_]+\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.binary.zig\\\"},{\\\"match\\\":\\\"\\\\\\\\b[0-9](([eEpP][+-])|[0-9a-zA-Z_])*(\\\\\\\\.(([eEpP][+-])|[0-9a-zA-Z_])*)?([eEpP][+-])?[0-9a-zA-Z_]*\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.invalid.zig\\\"}]},\\\"operators\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=\\\\\\\\[)\\\\\\\\*c(?=])\\\",\\\"name\\\":\\\"keyword.operator.c-pointer.zig\\\"},{\\\"match\\\":\\\"(\\\\\\\\b(and|or)\\\\\\\\b)|(==|!=|<=|>=|[<>])\\\",\\\"name\\\":\\\"keyword.operator.comparison.zig\\\"},{\\\"match\\\":\\\"(-%?|\\\\\\\\+%?|\\\\\\\\*%?|[/%])=?\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.zig\\\"},{\\\"match\\\":\\\"(<<%?|>>|[!~\\\\\\\\&^|])=?\\\",\\\"name\\\":\\\"keyword.operator.bitwise.zig\\\"},{\\\"match\\\":\\\"(==|\\\\\\\\+\\\\\\\\+|\\\\\\\\*\\\\\\\\*|->)\\\",\\\"name\\\":\\\"keyword.operator.special.zig\\\"},{\\\"match\\\":\\\"=\\\",\\\"name\\\":\\\"keyword.operator.assignment.zig\\\"},{\\\"match\\\":\\\"\\\\\\\\?\\\",\\\"name\\\":\\\"keyword.operator.question.zig\\\"}]},\\\"punctuation\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\.\\\",\\\"name\\\":\\\"punctuation.accessor.zig\\\"},{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.comma.zig\\\"},{\\\"match\\\":\\\":\\\",\\\"name\\\":\\\"punctuation.separator.key-value.zig\\\"},{\\\"match\\\":\\\";\\\",\\\"name\\\":\\\"punctuation.terminator.statement.zig\\\"}]},\\\"stringcontent\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\([nrt'\\\\\\\"\\\\\\\\\\\\\\\\]|(x\\\\\\\\h{2})|(u\\\\\\\\{\\\\\\\\h+}))\\\",\\\"name\\\":\\\"constant.character.escape.zig\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"invalid.illegal.unrecognized-string-escape.zig\\\"}]},\\\"strings\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"end\\\":\\\"\\\\\\\"\\\",\\\"name\\\":\\\"string.quoted.double.zig\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#stringcontent\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\",\\\"end\\\":\\\"$\\\",\\\"name\\\":\\\"string.multiline.zig\\\"},{\\\"match\\\":\\\"'([^'\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\(x\\\\\\\\h{2}|[0-2][0-7]{0,2}|3[0-6][0-7]?|37[0-7]?|[4-7][0-7]?|.))'\\\",\\\"name\\\":\\\"string.quoted.single.zig\\\"}]},\\\"support\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"@[_a-zA-Z][_a-zA-Z0-9]*\\\",\\\"name\\\":\\\"support.function.builtin.zig\\\"}]},\\\"variables\\\":{\\\"patterns\\\":[{\\\"name\\\":\\\"meta.function.declaration.zig\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.function.zig\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.type.zig\\\"}},\\\"match\\\":\\\"\\\\\\\\b(fn)\\\\\\\\s+([A-Z][a-zA-Z0-9]*)\\\\\\\\b\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.function.zig\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.zig\\\"}},\\\"match\\\":\\\"\\\\\\\\b(fn)\\\\\\\\s+([_a-zA-Z][_a-zA-Z0-9]*)\\\\\\\\b\\\"},{\\\"begin\\\":\\\"\\\\\\\\b(fn)\\\\\\\\s+@\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.function.zig\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"name\\\":\\\"entity.name.function.string.zig\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#stringcontent\\\"}]},{\\\"match\\\":\\\"\\\\\\\\b(const|var|fn)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.default.zig\\\"}]},{\\\"name\\\":\\\"meta.function.call.zig\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"([A-Z][a-zA-Z0-9]*)(?=\\\\\\\\s*\\\\\\\\()\\\",\\\"name\\\":\\\"entity.name.type.zig\\\"},{\\\"match\\\":\\\"([_a-zA-Z][_a-zA-Z0-9]*)(?=\\\\\\\\s*\\\\\\\\()\\\",\\\"name\\\":\\\"entity.name.function.zig\\\"}]},{\\\"name\\\":\\\"meta.variable.zig\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b[_a-zA-Z][_a-zA-Z0-9]*\\\\\\\\b\\\",\\\"name\\\":\\\"variable.zig\\\"},{\\\"begin\\\":\\\"@\\\\\\\"\\\",\\\"end\\\":\\\"\\\\\\\"\\\",\\\"name\\\":\\\"variable.string.zig\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#stringcontent\\\"}]}]}]}},\\\"scopeName\\\":\\\"source.zig\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/zig.mjs\n"));

/***/ })

}]);