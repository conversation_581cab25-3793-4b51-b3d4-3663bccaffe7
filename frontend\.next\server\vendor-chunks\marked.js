"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/marked";
exports.ids = ["vendor-chunks/marked"];
exports.modules = {

/***/ "(ssr)/./node_modules/marked/lib/marked.esm.js":
/*!***********************************************!*\
  !*** ./node_modules/marked/lib/marked.esm.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Hooks: () => (/* binding */ _Hooks),\n/* harmony export */   Lexer: () => (/* binding */ _Lexer),\n/* harmony export */   Marked: () => (/* binding */ Marked),\n/* harmony export */   Parser: () => (/* binding */ _Parser),\n/* harmony export */   Renderer: () => (/* binding */ _Renderer),\n/* harmony export */   TextRenderer: () => (/* binding */ _TextRenderer),\n/* harmony export */   Tokenizer: () => (/* binding */ _Tokenizer),\n/* harmony export */   defaults: () => (/* binding */ _defaults),\n/* harmony export */   getDefaults: () => (/* binding */ _getDefaults),\n/* harmony export */   lexer: () => (/* binding */ lexer),\n/* harmony export */   marked: () => (/* binding */ marked),\n/* harmony export */   options: () => (/* binding */ options),\n/* harmony export */   parse: () => (/* binding */ parse),\n/* harmony export */   parseInline: () => (/* binding */ parseInline),\n/* harmony export */   parser: () => (/* binding */ parser),\n/* harmony export */   setOptions: () => (/* binding */ setOptions),\n/* harmony export */   use: () => (/* binding */ use),\n/* harmony export */   walkTokens: () => (/* binding */ walkTokens)\n/* harmony export */ });\n/**\n * marked v15.0.8 - a markdown parser\n * Copyright (c) 2011-2025, Christopher Jeffrey. (MIT Licensed)\n * https://github.com/markedjs/marked\n */\n\n/**\n * DO NOT EDIT THIS FILE\n * The code in this file is generated from files in ./src/\n */\n\n/**\n * Gets the original marked default options.\n */\nfunction _getDefaults() {\n    return {\n        async: false,\n        breaks: false,\n        extensions: null,\n        gfm: true,\n        hooks: null,\n        pedantic: false,\n        renderer: null,\n        silent: false,\n        tokenizer: null,\n        walkTokens: null,\n    };\n}\nlet _defaults = _getDefaults();\nfunction changeDefaults(newDefaults) {\n    _defaults = newDefaults;\n}\n\nconst noopTest = { exec: () => null };\nfunction edit(regex, opt = '') {\n    let source = typeof regex === 'string' ? regex : regex.source;\n    const obj = {\n        replace: (name, val) => {\n            let valSource = typeof val === 'string' ? val : val.source;\n            valSource = valSource.replace(other.caret, '$1');\n            source = source.replace(name, valSource);\n            return obj;\n        },\n        getRegex: () => {\n            return new RegExp(source, opt);\n        },\n    };\n    return obj;\n}\nconst other = {\n    codeRemoveIndent: /^(?: {1,4}| {0,3}\\t)/gm,\n    outputLinkReplace: /\\\\([\\[\\]])/g,\n    indentCodeCompensation: /^(\\s+)(?:```)/,\n    beginningSpace: /^\\s+/,\n    endingHash: /#$/,\n    startingSpaceChar: /^ /,\n    endingSpaceChar: / $/,\n    nonSpaceChar: /[^ ]/,\n    newLineCharGlobal: /\\n/g,\n    tabCharGlobal: /\\t/g,\n    multipleSpaceGlobal: /\\s+/g,\n    blankLine: /^[ \\t]*$/,\n    doubleBlankLine: /\\n[ \\t]*\\n[ \\t]*$/,\n    blockquoteStart: /^ {0,3}>/,\n    blockquoteSetextReplace: /\\n {0,3}((?:=+|-+) *)(?=\\n|$)/g,\n    blockquoteSetextReplace2: /^ {0,3}>[ \\t]?/gm,\n    listReplaceTabs: /^\\t+/,\n    listReplaceNesting: /^ {1,4}(?=( {4})*[^ ])/g,\n    listIsTask: /^\\[[ xX]\\] /,\n    listReplaceTask: /^\\[[ xX]\\] +/,\n    anyLine: /\\n.*\\n/,\n    hrefBrackets: /^<(.*)>$/,\n    tableDelimiter: /[:|]/,\n    tableAlignChars: /^\\||\\| *$/g,\n    tableRowBlankLine: /\\n[ \\t]*$/,\n    tableAlignRight: /^ *-+: *$/,\n    tableAlignCenter: /^ *:-+: *$/,\n    tableAlignLeft: /^ *:-+ *$/,\n    startATag: /^<a /i,\n    endATag: /^<\\/a>/i,\n    startPreScriptTag: /^<(pre|code|kbd|script)(\\s|>)/i,\n    endPreScriptTag: /^<\\/(pre|code|kbd|script)(\\s|>)/i,\n    startAngleBracket: /^</,\n    endAngleBracket: />$/,\n    pedanticHrefTitle: /^([^'\"]*[^\\s])\\s+(['\"])(.*)\\2/,\n    unicodeAlphaNumeric: /[\\p{L}\\p{N}]/u,\n    escapeTest: /[&<>\"']/,\n    escapeReplace: /[&<>\"']/g,\n    escapeTestNoEncode: /[<>\"']|&(?!(#\\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\\w+);)/,\n    escapeReplaceNoEncode: /[<>\"']|&(?!(#\\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\\w+);)/g,\n    unescapeTest: /&(#(?:\\d+)|(?:#x[0-9A-Fa-f]+)|(?:\\w+));?/ig,\n    caret: /(^|[^\\[])\\^/g,\n    percentDecode: /%25/g,\n    findPipe: /\\|/g,\n    splitPipe: / \\|/,\n    slashPipe: /\\\\\\|/g,\n    carriageReturn: /\\r\\n|\\r/g,\n    spaceLine: /^ +$/gm,\n    notSpaceStart: /^\\S*/,\n    endingNewline: /\\n$/,\n    listItemRegex: (bull) => new RegExp(`^( {0,3}${bull})((?:[\\t ][^\\\\n]*)?(?:\\\\n|$))`),\n    nextBulletRegex: (indent) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}(?:[*+-]|\\\\d{1,9}[.)])((?:[ \\t][^\\\\n]*)?(?:\\\\n|$))`),\n    hrRegex: (indent) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\\\* *){3,})(?:\\\\n+|$)`),\n    fencesBeginRegex: (indent) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}(?:\\`\\`\\`|~~~)`),\n    headingBeginRegex: (indent) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}#`),\n    htmlBeginRegex: (indent) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}<(?:[a-z].*>|!--)`, 'i'),\n};\n/**\n * Block-Level Grammar\n */\nconst newline = /^(?:[ \\t]*(?:\\n|$))+/;\nconst blockCode = /^((?: {4}| {0,3}\\t)[^\\n]+(?:\\n(?:[ \\t]*(?:\\n|$))*)?)+/;\nconst fences = /^ {0,3}(`{3,}(?=[^`\\n]*(?:\\n|$))|~{3,})([^\\n]*)(?:\\n|$)(?:|([\\s\\S]*?)(?:\\n|$))(?: {0,3}\\1[~`]* *(?=\\n|$)|$)/;\nconst hr = /^ {0,3}((?:-[\\t ]*){3,}|(?:_[ \\t]*){3,}|(?:\\*[ \\t]*){3,})(?:\\n+|$)/;\nconst heading = /^ {0,3}(#{1,6})(?=\\s|$)(.*)(?:\\n+|$)/;\nconst bullet = /(?:[*+-]|\\d{1,9}[.)])/;\nconst lheadingCore = /^(?!bull |blockCode|fences|blockquote|heading|html|table)((?:.|\\n(?!\\s*?\\n|bull |blockCode|fences|blockquote|heading|html|table))+?)\\n {0,3}(=+|-+) *(?:\\n+|$)/;\nconst lheading = edit(lheadingCore)\n    .replace(/bull/g, bullet) // lists can interrupt\n    .replace(/blockCode/g, /(?: {4}| {0,3}\\t)/) // indented code blocks can interrupt\n    .replace(/fences/g, / {0,3}(?:`{3,}|~{3,})/) // fenced code blocks can interrupt\n    .replace(/blockquote/g, / {0,3}>/) // blockquote can interrupt\n    .replace(/heading/g, / {0,3}#{1,6}/) // ATX heading can interrupt\n    .replace(/html/g, / {0,3}<[^\\n>]+>\\n/) // block html can interrupt\n    .replace(/\\|table/g, '') // table not in commonmark\n    .getRegex();\nconst lheadingGfm = edit(lheadingCore)\n    .replace(/bull/g, bullet) // lists can interrupt\n    .replace(/blockCode/g, /(?: {4}| {0,3}\\t)/) // indented code blocks can interrupt\n    .replace(/fences/g, / {0,3}(?:`{3,}|~{3,})/) // fenced code blocks can interrupt\n    .replace(/blockquote/g, / {0,3}>/) // blockquote can interrupt\n    .replace(/heading/g, / {0,3}#{1,6}/) // ATX heading can interrupt\n    .replace(/html/g, / {0,3}<[^\\n>]+>\\n/) // block html can interrupt\n    .replace(/table/g, / {0,3}\\|?(?:[:\\- ]*\\|)+[\\:\\- ]*\\n/) // table can interrupt\n    .getRegex();\nconst _paragraph = /^([^\\n]+(?:\\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\\n)[^\\n]+)*)/;\nconst blockText = /^[^\\n]+/;\nconst _blockLabel = /(?!\\s*\\])(?:\\\\.|[^\\[\\]\\\\])+/;\nconst def = edit(/^ {0,3}\\[(label)\\]: *(?:\\n[ \\t]*)?([^<\\s][^\\s]*|<.*?>)(?:(?: +(?:\\n[ \\t]*)?| *\\n[ \\t]*)(title))? *(?:\\n+|$)/)\n    .replace('label', _blockLabel)\n    .replace('title', /(?:\"(?:\\\\\"?|[^\"\\\\])*\"|'[^'\\n]*(?:\\n[^'\\n]+)*\\n?'|\\([^()]*\\))/)\n    .getRegex();\nconst list = edit(/^( {0,3}bull)([ \\t][^\\n]+?)?(?:\\n|$)/)\n    .replace(/bull/g, bullet)\n    .getRegex();\nconst _tag = 'address|article|aside|base|basefont|blockquote|body|caption'\n    + '|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption'\n    + '|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe'\n    + '|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option'\n    + '|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title'\n    + '|tr|track|ul';\nconst _comment = /<!--(?:-?>|[\\s\\S]*?(?:-->|$))/;\nconst html = edit('^ {0,3}(?:' // optional indentation\n    + '<(script|pre|style|textarea)[\\\\s>][\\\\s\\\\S]*?(?:</\\\\1>[^\\\\n]*\\\\n+|$)' // (1)\n    + '|comment[^\\\\n]*(\\\\n+|$)' // (2)\n    + '|<\\\\?[\\\\s\\\\S]*?(?:\\\\?>\\\\n*|$)' // (3)\n    + '|<![A-Z][\\\\s\\\\S]*?(?:>\\\\n*|$)' // (4)\n    + '|<!\\\\[CDATA\\\\[[\\\\s\\\\S]*?(?:\\\\]\\\\]>\\\\n*|$)' // (5)\n    + '|</?(tag)(?: +|\\\\n|/?>)[\\\\s\\\\S]*?(?:(?:\\\\n[ \\t]*)+\\\\n|$)' // (6)\n    + '|<(?!script|pre|style|textarea)([a-z][\\\\w-]*)(?:attribute)*? */?>(?=[ \\\\t]*(?:\\\\n|$))[\\\\s\\\\S]*?(?:(?:\\\\n[ \\t]*)+\\\\n|$)' // (7) open tag\n    + '|</(?!script|pre|style|textarea)[a-z][\\\\w-]*\\\\s*>(?=[ \\\\t]*(?:\\\\n|$))[\\\\s\\\\S]*?(?:(?:\\\\n[ \\t]*)+\\\\n|$)' // (7) closing tag\n    + ')', 'i')\n    .replace('comment', _comment)\n    .replace('tag', _tag)\n    .replace('attribute', / +[a-zA-Z:_][\\w.:-]*(?: *= *\"[^\"\\n]*\"| *= *'[^'\\n]*'| *= *[^\\s\"'=<>`]+)?/)\n    .getRegex();\nconst paragraph = edit(_paragraph)\n    .replace('hr', hr)\n    .replace('heading', ' {0,3}#{1,6}(?:\\\\s|$)')\n    .replace('|lheading', '') // setext headings don't interrupt commonmark paragraphs\n    .replace('|table', '')\n    .replace('blockquote', ' {0,3}>')\n    .replace('fences', ' {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n')\n    .replace('list', ' {0,3}(?:[*+-]|1[.)]) ') // only lists starting from 1 can interrupt\n    .replace('html', '</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)')\n    .replace('tag', _tag) // pars can be interrupted by type (6) html blocks\n    .getRegex();\nconst blockquote = edit(/^( {0,3}> ?(paragraph|[^\\n]*)(?:\\n|$))+/)\n    .replace('paragraph', paragraph)\n    .getRegex();\n/**\n * Normal Block Grammar\n */\nconst blockNormal = {\n    blockquote,\n    code: blockCode,\n    def,\n    fences,\n    heading,\n    hr,\n    html,\n    lheading,\n    list,\n    newline,\n    paragraph,\n    table: noopTest,\n    text: blockText,\n};\n/**\n * GFM Block Grammar\n */\nconst gfmTable = edit('^ *([^\\\\n ].*)\\\\n' // Header\n    + ' {0,3}((?:\\\\| *)?:?-+:? *(?:\\\\| *:?-+:? *)*(?:\\\\| *)?)' // Align\n    + '(?:\\\\n((?:(?! *\\\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\\\n|$))*)\\\\n*|$)') // Cells\n    .replace('hr', hr)\n    .replace('heading', ' {0,3}#{1,6}(?:\\\\s|$)')\n    .replace('blockquote', ' {0,3}>')\n    .replace('code', '(?: {4}| {0,3}\\t)[^\\\\n]')\n    .replace('fences', ' {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n')\n    .replace('list', ' {0,3}(?:[*+-]|1[.)]) ') // only lists starting from 1 can interrupt\n    .replace('html', '</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)')\n    .replace('tag', _tag) // tables can be interrupted by type (6) html blocks\n    .getRegex();\nconst blockGfm = {\n    ...blockNormal,\n    lheading: lheadingGfm,\n    table: gfmTable,\n    paragraph: edit(_paragraph)\n        .replace('hr', hr)\n        .replace('heading', ' {0,3}#{1,6}(?:\\\\s|$)')\n        .replace('|lheading', '') // setext headings don't interrupt commonmark paragraphs\n        .replace('table', gfmTable) // interrupt paragraphs with table\n        .replace('blockquote', ' {0,3}>')\n        .replace('fences', ' {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n')\n        .replace('list', ' {0,3}(?:[*+-]|1[.)]) ') // only lists starting from 1 can interrupt\n        .replace('html', '</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)')\n        .replace('tag', _tag) // pars can be interrupted by type (6) html blocks\n        .getRegex(),\n};\n/**\n * Pedantic grammar (original John Gruber's loose markdown specification)\n */\nconst blockPedantic = {\n    ...blockNormal,\n    html: edit('^ *(?:comment *(?:\\\\n|\\\\s*$)'\n        + '|<(tag)[\\\\s\\\\S]+?</\\\\1> *(?:\\\\n{2,}|\\\\s*$)' // closed tag\n        + '|<tag(?:\"[^\"]*\"|\\'[^\\']*\\'|\\\\s[^\\'\"/>\\\\s]*)*?/?> *(?:\\\\n{2,}|\\\\s*$))')\n        .replace('comment', _comment)\n        .replace(/tag/g, '(?!(?:'\n        + 'a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub'\n        + '|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)'\n        + '\\\\b)\\\\w+(?!:|[^\\\\w\\\\s@]*@)\\\\b')\n        .getRegex(),\n    def: /^ *\\[([^\\]]+)\\]: *<?([^\\s>]+)>?(?: +([\"(][^\\n]+[\")]))? *(?:\\n+|$)/,\n    heading: /^(#{1,6})(.*)(?:\\n+|$)/,\n    fences: noopTest, // fences not supported\n    lheading: /^(.+?)\\n {0,3}(=+|-+) *(?:\\n+|$)/,\n    paragraph: edit(_paragraph)\n        .replace('hr', hr)\n        .replace('heading', ' *#{1,6} *[^\\n]')\n        .replace('lheading', lheading)\n        .replace('|table', '')\n        .replace('blockquote', ' {0,3}>')\n        .replace('|fences', '')\n        .replace('|list', '')\n        .replace('|html', '')\n        .replace('|tag', '')\n        .getRegex(),\n};\n/**\n * Inline-Level Grammar\n */\nconst escape$1 = /^\\\\([!\"#$%&'()*+,\\-./:;<=>?@\\[\\]\\\\^_`{|}~])/;\nconst inlineCode = /^(`+)([^`]|[^`][\\s\\S]*?[^`])\\1(?!`)/;\nconst br = /^( {2,}|\\\\)\\n(?!\\s*$)/;\nconst inlineText = /^(`+|[^`])(?:(?= {2,}\\n)|[\\s\\S]*?(?:(?=[\\\\<!\\[`*_]|\\b_|$)|[^ ](?= {2,}\\n)))/;\n// list of unicode punctuation marks, plus any missing characters from CommonMark spec\nconst _punctuation = /[\\p{P}\\p{S}]/u;\nconst _punctuationOrSpace = /[\\s\\p{P}\\p{S}]/u;\nconst _notPunctuationOrSpace = /[^\\s\\p{P}\\p{S}]/u;\nconst punctuation = edit(/^((?![*_])punctSpace)/, 'u')\n    .replace(/punctSpace/g, _punctuationOrSpace).getRegex();\n// GFM allows ~ inside strong and em for strikethrough\nconst _punctuationGfmStrongEm = /(?!~)[\\p{P}\\p{S}]/u;\nconst _punctuationOrSpaceGfmStrongEm = /(?!~)[\\s\\p{P}\\p{S}]/u;\nconst _notPunctuationOrSpaceGfmStrongEm = /(?:[^\\s\\p{P}\\p{S}]|~)/u;\n// sequences em should skip over [title](link), `code`, <html>\nconst blockSkip = /\\[[^[\\]]*?\\]\\((?:\\\\.|[^\\\\\\(\\)]|\\((?:\\\\.|[^\\\\\\(\\)])*\\))*\\)|`[^`]*?`|<[^<>]*?>/g;\nconst emStrongLDelimCore = /^(?:\\*+(?:((?!\\*)punct)|[^\\s*]))|^_+(?:((?!_)punct)|([^\\s_]))/;\nconst emStrongLDelim = edit(emStrongLDelimCore, 'u')\n    .replace(/punct/g, _punctuation)\n    .getRegex();\nconst emStrongLDelimGfm = edit(emStrongLDelimCore, 'u')\n    .replace(/punct/g, _punctuationGfmStrongEm)\n    .getRegex();\nconst emStrongRDelimAstCore = '^[^_*]*?__[^_*]*?\\\\*[^_*]*?(?=__)' // Skip orphan inside strong\n    + '|[^*]+(?=[^*])' // Consume to delim\n    + '|(?!\\\\*)punct(\\\\*+)(?=[\\\\s]|$)' // (1) #*** can only be a Right Delimiter\n    + '|notPunctSpace(\\\\*+)(?!\\\\*)(?=punctSpace|$)' // (2) a***#, a*** can only be a Right Delimiter\n    + '|(?!\\\\*)punctSpace(\\\\*+)(?=notPunctSpace)' // (3) #***a, ***a can only be Left Delimiter\n    + '|[\\\\s](\\\\*+)(?!\\\\*)(?=punct)' // (4) ***# can only be Left Delimiter\n    + '|(?!\\\\*)punct(\\\\*+)(?!\\\\*)(?=punct)' // (5) #***# can be either Left or Right Delimiter\n    + '|notPunctSpace(\\\\*+)(?=notPunctSpace)'; // (6) a***a can be either Left or Right Delimiter\nconst emStrongRDelimAst = edit(emStrongRDelimAstCore, 'gu')\n    .replace(/notPunctSpace/g, _notPunctuationOrSpace)\n    .replace(/punctSpace/g, _punctuationOrSpace)\n    .replace(/punct/g, _punctuation)\n    .getRegex();\nconst emStrongRDelimAstGfm = edit(emStrongRDelimAstCore, 'gu')\n    .replace(/notPunctSpace/g, _notPunctuationOrSpaceGfmStrongEm)\n    .replace(/punctSpace/g, _punctuationOrSpaceGfmStrongEm)\n    .replace(/punct/g, _punctuationGfmStrongEm)\n    .getRegex();\n// (6) Not allowed for _\nconst emStrongRDelimUnd = edit('^[^_*]*?\\\\*\\\\*[^_*]*?_[^_*]*?(?=\\\\*\\\\*)' // Skip orphan inside strong\n    + '|[^_]+(?=[^_])' // Consume to delim\n    + '|(?!_)punct(_+)(?=[\\\\s]|$)' // (1) #___ can only be a Right Delimiter\n    + '|notPunctSpace(_+)(?!_)(?=punctSpace|$)' // (2) a___#, a___ can only be a Right Delimiter\n    + '|(?!_)punctSpace(_+)(?=notPunctSpace)' // (3) #___a, ___a can only be Left Delimiter\n    + '|[\\\\s](_+)(?!_)(?=punct)' // (4) ___# can only be Left Delimiter\n    + '|(?!_)punct(_+)(?!_)(?=punct)', 'gu') // (5) #___# can be either Left or Right Delimiter\n    .replace(/notPunctSpace/g, _notPunctuationOrSpace)\n    .replace(/punctSpace/g, _punctuationOrSpace)\n    .replace(/punct/g, _punctuation)\n    .getRegex();\nconst anyPunctuation = edit(/\\\\(punct)/, 'gu')\n    .replace(/punct/g, _punctuation)\n    .getRegex();\nconst autolink = edit(/^<(scheme:[^\\s\\x00-\\x1f<>]*|email)>/)\n    .replace('scheme', /[a-zA-Z][a-zA-Z0-9+.-]{1,31}/)\n    .replace('email', /[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/)\n    .getRegex();\nconst _inlineComment = edit(_comment).replace('(?:-->|$)', '-->').getRegex();\nconst tag = edit('^comment'\n    + '|^</[a-zA-Z][\\\\w:-]*\\\\s*>' // self-closing tag\n    + '|^<[a-zA-Z][\\\\w-]*(?:attribute)*?\\\\s*/?>' // open tag\n    + '|^<\\\\?[\\\\s\\\\S]*?\\\\?>' // processing instruction, e.g. <?php ?>\n    + '|^<![a-zA-Z]+\\\\s[\\\\s\\\\S]*?>' // declaration, e.g. <!DOCTYPE html>\n    + '|^<!\\\\[CDATA\\\\[[\\\\s\\\\S]*?\\\\]\\\\]>') // CDATA section\n    .replace('comment', _inlineComment)\n    .replace('attribute', /\\s+[a-zA-Z:_][\\w.:-]*(?:\\s*=\\s*\"[^\"]*\"|\\s*=\\s*'[^']*'|\\s*=\\s*[^\\s\"'=<>`]+)?/)\n    .getRegex();\nconst _inlineLabel = /(?:\\[(?:\\\\.|[^\\[\\]\\\\])*\\]|\\\\.|`[^`]*`|[^\\[\\]\\\\`])*?/;\nconst link = edit(/^!?\\[(label)\\]\\(\\s*(href)(?:\\s+(title))?\\s*\\)/)\n    .replace('label', _inlineLabel)\n    .replace('href', /<(?:\\\\.|[^\\n<>\\\\])+>|[^\\s\\x00-\\x1f]*/)\n    .replace('title', /\"(?:\\\\\"?|[^\"\\\\])*\"|'(?:\\\\'?|[^'\\\\])*'|\\((?:\\\\\\)?|[^)\\\\])*\\)/)\n    .getRegex();\nconst reflink = edit(/^!?\\[(label)\\]\\[(ref)\\]/)\n    .replace('label', _inlineLabel)\n    .replace('ref', _blockLabel)\n    .getRegex();\nconst nolink = edit(/^!?\\[(ref)\\](?:\\[\\])?/)\n    .replace('ref', _blockLabel)\n    .getRegex();\nconst reflinkSearch = edit('reflink|nolink(?!\\\\()', 'g')\n    .replace('reflink', reflink)\n    .replace('nolink', nolink)\n    .getRegex();\n/**\n * Normal Inline Grammar\n */\nconst inlineNormal = {\n    _backpedal: noopTest, // only used for GFM url\n    anyPunctuation,\n    autolink,\n    blockSkip,\n    br,\n    code: inlineCode,\n    del: noopTest,\n    emStrongLDelim,\n    emStrongRDelimAst,\n    emStrongRDelimUnd,\n    escape: escape$1,\n    link,\n    nolink,\n    punctuation,\n    reflink,\n    reflinkSearch,\n    tag,\n    text: inlineText,\n    url: noopTest,\n};\n/**\n * Pedantic Inline Grammar\n */\nconst inlinePedantic = {\n    ...inlineNormal,\n    link: edit(/^!?\\[(label)\\]\\((.*?)\\)/)\n        .replace('label', _inlineLabel)\n        .getRegex(),\n    reflink: edit(/^!?\\[(label)\\]\\s*\\[([^\\]]*)\\]/)\n        .replace('label', _inlineLabel)\n        .getRegex(),\n};\n/**\n * GFM Inline Grammar\n */\nconst inlineGfm = {\n    ...inlineNormal,\n    emStrongRDelimAst: emStrongRDelimAstGfm,\n    emStrongLDelim: emStrongLDelimGfm,\n    url: edit(/^((?:ftp|https?):\\/\\/|www\\.)(?:[a-zA-Z0-9\\-]+\\.?)+[^\\s<]*|^email/, 'i')\n        .replace('email', /[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/)\n        .getRegex(),\n    _backpedal: /(?:[^?!.,:;*_'\"~()&]+|\\([^)]*\\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'\"~)]+(?!$))+/,\n    del: /^(~~?)(?=[^\\s~])((?:\\\\.|[^\\\\])*?(?:\\\\.|[^\\s~\\\\]))\\1(?=[^~]|$)/,\n    text: /^([`~]+|[^`~])(?:(?= {2,}\\n)|(?=[a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-]+@)|[\\s\\S]*?(?:(?=[\\\\<!\\[`*~_]|\\b_|https?:\\/\\/|ftp:\\/\\/|www\\.|$)|[^ ](?= {2,}\\n)|[^a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-](?=[a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-]+@)))/,\n};\n/**\n * GFM + Line Breaks Inline Grammar\n */\nconst inlineBreaks = {\n    ...inlineGfm,\n    br: edit(br).replace('{2,}', '*').getRegex(),\n    text: edit(inlineGfm.text)\n        .replace('\\\\b_', '\\\\b_| {2,}\\\\n')\n        .replace(/\\{2,\\}/g, '*')\n        .getRegex(),\n};\n/**\n * exports\n */\nconst block = {\n    normal: blockNormal,\n    gfm: blockGfm,\n    pedantic: blockPedantic,\n};\nconst inline = {\n    normal: inlineNormal,\n    gfm: inlineGfm,\n    breaks: inlineBreaks,\n    pedantic: inlinePedantic,\n};\n\n/**\n * Helpers\n */\nconst escapeReplacements = {\n    '&': '&amp;',\n    '<': '&lt;',\n    '>': '&gt;',\n    '\"': '&quot;',\n    \"'\": '&#39;',\n};\nconst getEscapeReplacement = (ch) => escapeReplacements[ch];\nfunction escape(html, encode) {\n    if (encode) {\n        if (other.escapeTest.test(html)) {\n            return html.replace(other.escapeReplace, getEscapeReplacement);\n        }\n    }\n    else {\n        if (other.escapeTestNoEncode.test(html)) {\n            return html.replace(other.escapeReplaceNoEncode, getEscapeReplacement);\n        }\n    }\n    return html;\n}\nfunction cleanUrl(href) {\n    try {\n        href = encodeURI(href).replace(other.percentDecode, '%');\n    }\n    catch {\n        return null;\n    }\n    return href;\n}\nfunction splitCells(tableRow, count) {\n    // ensure that every cell-delimiting pipe has a space\n    // before it to distinguish it from an escaped pipe\n    const row = tableRow.replace(other.findPipe, (match, offset, str) => {\n        let escaped = false;\n        let curr = offset;\n        while (--curr >= 0 && str[curr] === '\\\\')\n            escaped = !escaped;\n        if (escaped) {\n            // odd number of slashes means | is escaped\n            // so we leave it alone\n            return '|';\n        }\n        else {\n            // add space before unescaped |\n            return ' |';\n        }\n    }), cells = row.split(other.splitPipe);\n    let i = 0;\n    // First/last cell in a row cannot be empty if it has no leading/trailing pipe\n    if (!cells[0].trim()) {\n        cells.shift();\n    }\n    if (cells.length > 0 && !cells.at(-1)?.trim()) {\n        cells.pop();\n    }\n    if (count) {\n        if (cells.length > count) {\n            cells.splice(count);\n        }\n        else {\n            while (cells.length < count)\n                cells.push('');\n        }\n    }\n    for (; i < cells.length; i++) {\n        // leading or trailing whitespace is ignored per the gfm spec\n        cells[i] = cells[i].trim().replace(other.slashPipe, '|');\n    }\n    return cells;\n}\n/**\n * Remove trailing 'c's. Equivalent to str.replace(/c*$/, '').\n * /c*$/ is vulnerable to REDOS.\n *\n * @param str\n * @param c\n * @param invert Remove suffix of non-c chars instead. Default falsey.\n */\nfunction rtrim(str, c, invert) {\n    const l = str.length;\n    if (l === 0) {\n        return '';\n    }\n    // Length of suffix matching the invert condition.\n    let suffLen = 0;\n    // Step left until we fail to match the invert condition.\n    while (suffLen < l) {\n        const currChar = str.charAt(l - suffLen - 1);\n        if (currChar === c && true) {\n            suffLen++;\n        }\n        else {\n            break;\n        }\n    }\n    return str.slice(0, l - suffLen);\n}\nfunction findClosingBracket(str, b) {\n    if (str.indexOf(b[1]) === -1) {\n        return -1;\n    }\n    let level = 0;\n    for (let i = 0; i < str.length; i++) {\n        if (str[i] === '\\\\') {\n            i++;\n        }\n        else if (str[i] === b[0]) {\n            level++;\n        }\n        else if (str[i] === b[1]) {\n            level--;\n            if (level < 0) {\n                return i;\n            }\n        }\n    }\n    return -1;\n}\n\nfunction outputLink(cap, link, raw, lexer, rules) {\n    const href = link.href;\n    const title = link.title || null;\n    const text = cap[1].replace(rules.other.outputLinkReplace, '$1');\n    if (cap[0].charAt(0) !== '!') {\n        lexer.state.inLink = true;\n        const token = {\n            type: 'link',\n            raw,\n            href,\n            title,\n            text,\n            tokens: lexer.inlineTokens(text),\n        };\n        lexer.state.inLink = false;\n        return token;\n    }\n    return {\n        type: 'image',\n        raw,\n        href,\n        title,\n        text,\n    };\n}\nfunction indentCodeCompensation(raw, text, rules) {\n    const matchIndentToCode = raw.match(rules.other.indentCodeCompensation);\n    if (matchIndentToCode === null) {\n        return text;\n    }\n    const indentToCode = matchIndentToCode[1];\n    return text\n        .split('\\n')\n        .map(node => {\n        const matchIndentInNode = node.match(rules.other.beginningSpace);\n        if (matchIndentInNode === null) {\n            return node;\n        }\n        const [indentInNode] = matchIndentInNode;\n        if (indentInNode.length >= indentToCode.length) {\n            return node.slice(indentToCode.length);\n        }\n        return node;\n    })\n        .join('\\n');\n}\n/**\n * Tokenizer\n */\nclass _Tokenizer {\n    options;\n    rules; // set by the lexer\n    lexer; // set by the lexer\n    constructor(options) {\n        this.options = options || _defaults;\n    }\n    space(src) {\n        const cap = this.rules.block.newline.exec(src);\n        if (cap && cap[0].length > 0) {\n            return {\n                type: 'space',\n                raw: cap[0],\n            };\n        }\n    }\n    code(src) {\n        const cap = this.rules.block.code.exec(src);\n        if (cap) {\n            const text = cap[0].replace(this.rules.other.codeRemoveIndent, '');\n            return {\n                type: 'code',\n                raw: cap[0],\n                codeBlockStyle: 'indented',\n                text: !this.options.pedantic\n                    ? rtrim(text, '\\n')\n                    : text,\n            };\n        }\n    }\n    fences(src) {\n        const cap = this.rules.block.fences.exec(src);\n        if (cap) {\n            const raw = cap[0];\n            const text = indentCodeCompensation(raw, cap[3] || '', this.rules);\n            return {\n                type: 'code',\n                raw,\n                lang: cap[2] ? cap[2].trim().replace(this.rules.inline.anyPunctuation, '$1') : cap[2],\n                text,\n            };\n        }\n    }\n    heading(src) {\n        const cap = this.rules.block.heading.exec(src);\n        if (cap) {\n            let text = cap[2].trim();\n            // remove trailing #s\n            if (this.rules.other.endingHash.test(text)) {\n                const trimmed = rtrim(text, '#');\n                if (this.options.pedantic) {\n                    text = trimmed.trim();\n                }\n                else if (!trimmed || this.rules.other.endingSpaceChar.test(trimmed)) {\n                    // CommonMark requires space before trailing #s\n                    text = trimmed.trim();\n                }\n            }\n            return {\n                type: 'heading',\n                raw: cap[0],\n                depth: cap[1].length,\n                text,\n                tokens: this.lexer.inline(text),\n            };\n        }\n    }\n    hr(src) {\n        const cap = this.rules.block.hr.exec(src);\n        if (cap) {\n            return {\n                type: 'hr',\n                raw: rtrim(cap[0], '\\n'),\n            };\n        }\n    }\n    blockquote(src) {\n        const cap = this.rules.block.blockquote.exec(src);\n        if (cap) {\n            let lines = rtrim(cap[0], '\\n').split('\\n');\n            let raw = '';\n            let text = '';\n            const tokens = [];\n            while (lines.length > 0) {\n                let inBlockquote = false;\n                const currentLines = [];\n                let i;\n                for (i = 0; i < lines.length; i++) {\n                    // get lines up to a continuation\n                    if (this.rules.other.blockquoteStart.test(lines[i])) {\n                        currentLines.push(lines[i]);\n                        inBlockquote = true;\n                    }\n                    else if (!inBlockquote) {\n                        currentLines.push(lines[i]);\n                    }\n                    else {\n                        break;\n                    }\n                }\n                lines = lines.slice(i);\n                const currentRaw = currentLines.join('\\n');\n                const currentText = currentRaw\n                    // precede setext continuation with 4 spaces so it isn't a setext\n                    .replace(this.rules.other.blockquoteSetextReplace, '\\n    $1')\n                    .replace(this.rules.other.blockquoteSetextReplace2, '');\n                raw = raw ? `${raw}\\n${currentRaw}` : currentRaw;\n                text = text ? `${text}\\n${currentText}` : currentText;\n                // parse blockquote lines as top level tokens\n                // merge paragraphs if this is a continuation\n                const top = this.lexer.state.top;\n                this.lexer.state.top = true;\n                this.lexer.blockTokens(currentText, tokens, true);\n                this.lexer.state.top = top;\n                // if there is no continuation then we are done\n                if (lines.length === 0) {\n                    break;\n                }\n                const lastToken = tokens.at(-1);\n                if (lastToken?.type === 'code') {\n                    // blockquote continuation cannot be preceded by a code block\n                    break;\n                }\n                else if (lastToken?.type === 'blockquote') {\n                    // include continuation in nested blockquote\n                    const oldToken = lastToken;\n                    const newText = oldToken.raw + '\\n' + lines.join('\\n');\n                    const newToken = this.blockquote(newText);\n                    tokens[tokens.length - 1] = newToken;\n                    raw = raw.substring(0, raw.length - oldToken.raw.length) + newToken.raw;\n                    text = text.substring(0, text.length - oldToken.text.length) + newToken.text;\n                    break;\n                }\n                else if (lastToken?.type === 'list') {\n                    // include continuation in nested list\n                    const oldToken = lastToken;\n                    const newText = oldToken.raw + '\\n' + lines.join('\\n');\n                    const newToken = this.list(newText);\n                    tokens[tokens.length - 1] = newToken;\n                    raw = raw.substring(0, raw.length - lastToken.raw.length) + newToken.raw;\n                    text = text.substring(0, text.length - oldToken.raw.length) + newToken.raw;\n                    lines = newText.substring(tokens.at(-1).raw.length).split('\\n');\n                    continue;\n                }\n            }\n            return {\n                type: 'blockquote',\n                raw,\n                tokens,\n                text,\n            };\n        }\n    }\n    list(src) {\n        let cap = this.rules.block.list.exec(src);\n        if (cap) {\n            let bull = cap[1].trim();\n            const isordered = bull.length > 1;\n            const list = {\n                type: 'list',\n                raw: '',\n                ordered: isordered,\n                start: isordered ? +bull.slice(0, -1) : '',\n                loose: false,\n                items: [],\n            };\n            bull = isordered ? `\\\\d{1,9}\\\\${bull.slice(-1)}` : `\\\\${bull}`;\n            if (this.options.pedantic) {\n                bull = isordered ? bull : '[*+-]';\n            }\n            // Get next list item\n            const itemRegex = this.rules.other.listItemRegex(bull);\n            let endsWithBlankLine = false;\n            // Check if current bullet point can start a new List Item\n            while (src) {\n                let endEarly = false;\n                let raw = '';\n                let itemContents = '';\n                if (!(cap = itemRegex.exec(src))) {\n                    break;\n                }\n                if (this.rules.block.hr.test(src)) { // End list if bullet was actually HR (possibly move into itemRegex?)\n                    break;\n                }\n                raw = cap[0];\n                src = src.substring(raw.length);\n                let line = cap[2].split('\\n', 1)[0].replace(this.rules.other.listReplaceTabs, (t) => ' '.repeat(3 * t.length));\n                let nextLine = src.split('\\n', 1)[0];\n                let blankLine = !line.trim();\n                let indent = 0;\n                if (this.options.pedantic) {\n                    indent = 2;\n                    itemContents = line.trimStart();\n                }\n                else if (blankLine) {\n                    indent = cap[1].length + 1;\n                }\n                else {\n                    indent = cap[2].search(this.rules.other.nonSpaceChar); // Find first non-space char\n                    indent = indent > 4 ? 1 : indent; // Treat indented code blocks (> 4 spaces) as having only 1 indent\n                    itemContents = line.slice(indent);\n                    indent += cap[1].length;\n                }\n                if (blankLine && this.rules.other.blankLine.test(nextLine)) { // Items begin with at most one blank line\n                    raw += nextLine + '\\n';\n                    src = src.substring(nextLine.length + 1);\n                    endEarly = true;\n                }\n                if (!endEarly) {\n                    const nextBulletRegex = this.rules.other.nextBulletRegex(indent);\n                    const hrRegex = this.rules.other.hrRegex(indent);\n                    const fencesBeginRegex = this.rules.other.fencesBeginRegex(indent);\n                    const headingBeginRegex = this.rules.other.headingBeginRegex(indent);\n                    const htmlBeginRegex = this.rules.other.htmlBeginRegex(indent);\n                    // Check if following lines should be included in List Item\n                    while (src) {\n                        const rawLine = src.split('\\n', 1)[0];\n                        let nextLineWithoutTabs;\n                        nextLine = rawLine;\n                        // Re-align to follow commonmark nesting rules\n                        if (this.options.pedantic) {\n                            nextLine = nextLine.replace(this.rules.other.listReplaceNesting, '  ');\n                            nextLineWithoutTabs = nextLine;\n                        }\n                        else {\n                            nextLineWithoutTabs = nextLine.replace(this.rules.other.tabCharGlobal, '    ');\n                        }\n                        // End list item if found code fences\n                        if (fencesBeginRegex.test(nextLine)) {\n                            break;\n                        }\n                        // End list item if found start of new heading\n                        if (headingBeginRegex.test(nextLine)) {\n                            break;\n                        }\n                        // End list item if found start of html block\n                        if (htmlBeginRegex.test(nextLine)) {\n                            break;\n                        }\n                        // End list item if found start of new bullet\n                        if (nextBulletRegex.test(nextLine)) {\n                            break;\n                        }\n                        // Horizontal rule found\n                        if (hrRegex.test(nextLine)) {\n                            break;\n                        }\n                        if (nextLineWithoutTabs.search(this.rules.other.nonSpaceChar) >= indent || !nextLine.trim()) { // Dedent if possible\n                            itemContents += '\\n' + nextLineWithoutTabs.slice(indent);\n                        }\n                        else {\n                            // not enough indentation\n                            if (blankLine) {\n                                break;\n                            }\n                            // paragraph continuation unless last line was a different block level element\n                            if (line.replace(this.rules.other.tabCharGlobal, '    ').search(this.rules.other.nonSpaceChar) >= 4) { // indented code block\n                                break;\n                            }\n                            if (fencesBeginRegex.test(line)) {\n                                break;\n                            }\n                            if (headingBeginRegex.test(line)) {\n                                break;\n                            }\n                            if (hrRegex.test(line)) {\n                                break;\n                            }\n                            itemContents += '\\n' + nextLine;\n                        }\n                        if (!blankLine && !nextLine.trim()) { // Check if current line is blank\n                            blankLine = true;\n                        }\n                        raw += rawLine + '\\n';\n                        src = src.substring(rawLine.length + 1);\n                        line = nextLineWithoutTabs.slice(indent);\n                    }\n                }\n                if (!list.loose) {\n                    // If the previous item ended with a blank line, the list is loose\n                    if (endsWithBlankLine) {\n                        list.loose = true;\n                    }\n                    else if (this.rules.other.doubleBlankLine.test(raw)) {\n                        endsWithBlankLine = true;\n                    }\n                }\n                let istask = null;\n                let ischecked;\n                // Check for task list items\n                if (this.options.gfm) {\n                    istask = this.rules.other.listIsTask.exec(itemContents);\n                    if (istask) {\n                        ischecked = istask[0] !== '[ ] ';\n                        itemContents = itemContents.replace(this.rules.other.listReplaceTask, '');\n                    }\n                }\n                list.items.push({\n                    type: 'list_item',\n                    raw,\n                    task: !!istask,\n                    checked: ischecked,\n                    loose: false,\n                    text: itemContents,\n                    tokens: [],\n                });\n                list.raw += raw;\n            }\n            // Do not consume newlines at end of final item. Alternatively, make itemRegex *start* with any newlines to simplify/speed up endsWithBlankLine logic\n            const lastItem = list.items.at(-1);\n            if (lastItem) {\n                lastItem.raw = lastItem.raw.trimEnd();\n                lastItem.text = lastItem.text.trimEnd();\n            }\n            else {\n                // not a list since there were no items\n                return;\n            }\n            list.raw = list.raw.trimEnd();\n            // Item child tokens handled here at end because we needed to have the final item to trim it first\n            for (let i = 0; i < list.items.length; i++) {\n                this.lexer.state.top = false;\n                list.items[i].tokens = this.lexer.blockTokens(list.items[i].text, []);\n                if (!list.loose) {\n                    // Check if list should be loose\n                    const spacers = list.items[i].tokens.filter(t => t.type === 'space');\n                    const hasMultipleLineBreaks = spacers.length > 0 && spacers.some(t => this.rules.other.anyLine.test(t.raw));\n                    list.loose = hasMultipleLineBreaks;\n                }\n            }\n            // Set all items to loose if list is loose\n            if (list.loose) {\n                for (let i = 0; i < list.items.length; i++) {\n                    list.items[i].loose = true;\n                }\n            }\n            return list;\n        }\n    }\n    html(src) {\n        const cap = this.rules.block.html.exec(src);\n        if (cap) {\n            const token = {\n                type: 'html',\n                block: true,\n                raw: cap[0],\n                pre: cap[1] === 'pre' || cap[1] === 'script' || cap[1] === 'style',\n                text: cap[0],\n            };\n            return token;\n        }\n    }\n    def(src) {\n        const cap = this.rules.block.def.exec(src);\n        if (cap) {\n            const tag = cap[1].toLowerCase().replace(this.rules.other.multipleSpaceGlobal, ' ');\n            const href = cap[2] ? cap[2].replace(this.rules.other.hrefBrackets, '$1').replace(this.rules.inline.anyPunctuation, '$1') : '';\n            const title = cap[3] ? cap[3].substring(1, cap[3].length - 1).replace(this.rules.inline.anyPunctuation, '$1') : cap[3];\n            return {\n                type: 'def',\n                tag,\n                raw: cap[0],\n                href,\n                title,\n            };\n        }\n    }\n    table(src) {\n        const cap = this.rules.block.table.exec(src);\n        if (!cap) {\n            return;\n        }\n        if (!this.rules.other.tableDelimiter.test(cap[2])) {\n            // delimiter row must have a pipe (|) or colon (:) otherwise it is a setext heading\n            return;\n        }\n        const headers = splitCells(cap[1]);\n        const aligns = cap[2].replace(this.rules.other.tableAlignChars, '').split('|');\n        const rows = cap[3]?.trim() ? cap[3].replace(this.rules.other.tableRowBlankLine, '').split('\\n') : [];\n        const item = {\n            type: 'table',\n            raw: cap[0],\n            header: [],\n            align: [],\n            rows: [],\n        };\n        if (headers.length !== aligns.length) {\n            // header and align columns must be equal, rows can be different.\n            return;\n        }\n        for (const align of aligns) {\n            if (this.rules.other.tableAlignRight.test(align)) {\n                item.align.push('right');\n            }\n            else if (this.rules.other.tableAlignCenter.test(align)) {\n                item.align.push('center');\n            }\n            else if (this.rules.other.tableAlignLeft.test(align)) {\n                item.align.push('left');\n            }\n            else {\n                item.align.push(null);\n            }\n        }\n        for (let i = 0; i < headers.length; i++) {\n            item.header.push({\n                text: headers[i],\n                tokens: this.lexer.inline(headers[i]),\n                header: true,\n                align: item.align[i],\n            });\n        }\n        for (const row of rows) {\n            item.rows.push(splitCells(row, item.header.length).map((cell, i) => {\n                return {\n                    text: cell,\n                    tokens: this.lexer.inline(cell),\n                    header: false,\n                    align: item.align[i],\n                };\n            }));\n        }\n        return item;\n    }\n    lheading(src) {\n        const cap = this.rules.block.lheading.exec(src);\n        if (cap) {\n            return {\n                type: 'heading',\n                raw: cap[0],\n                depth: cap[2].charAt(0) === '=' ? 1 : 2,\n                text: cap[1],\n                tokens: this.lexer.inline(cap[1]),\n            };\n        }\n    }\n    paragraph(src) {\n        const cap = this.rules.block.paragraph.exec(src);\n        if (cap) {\n            const text = cap[1].charAt(cap[1].length - 1) === '\\n'\n                ? cap[1].slice(0, -1)\n                : cap[1];\n            return {\n                type: 'paragraph',\n                raw: cap[0],\n                text,\n                tokens: this.lexer.inline(text),\n            };\n        }\n    }\n    text(src) {\n        const cap = this.rules.block.text.exec(src);\n        if (cap) {\n            return {\n                type: 'text',\n                raw: cap[0],\n                text: cap[0],\n                tokens: this.lexer.inline(cap[0]),\n            };\n        }\n    }\n    escape(src) {\n        const cap = this.rules.inline.escape.exec(src);\n        if (cap) {\n            return {\n                type: 'escape',\n                raw: cap[0],\n                text: cap[1],\n            };\n        }\n    }\n    tag(src) {\n        const cap = this.rules.inline.tag.exec(src);\n        if (cap) {\n            if (!this.lexer.state.inLink && this.rules.other.startATag.test(cap[0])) {\n                this.lexer.state.inLink = true;\n            }\n            else if (this.lexer.state.inLink && this.rules.other.endATag.test(cap[0])) {\n                this.lexer.state.inLink = false;\n            }\n            if (!this.lexer.state.inRawBlock && this.rules.other.startPreScriptTag.test(cap[0])) {\n                this.lexer.state.inRawBlock = true;\n            }\n            else if (this.lexer.state.inRawBlock && this.rules.other.endPreScriptTag.test(cap[0])) {\n                this.lexer.state.inRawBlock = false;\n            }\n            return {\n                type: 'html',\n                raw: cap[0],\n                inLink: this.lexer.state.inLink,\n                inRawBlock: this.lexer.state.inRawBlock,\n                block: false,\n                text: cap[0],\n            };\n        }\n    }\n    link(src) {\n        const cap = this.rules.inline.link.exec(src);\n        if (cap) {\n            const trimmedUrl = cap[2].trim();\n            if (!this.options.pedantic && this.rules.other.startAngleBracket.test(trimmedUrl)) {\n                // commonmark requires matching angle brackets\n                if (!(this.rules.other.endAngleBracket.test(trimmedUrl))) {\n                    return;\n                }\n                // ending angle bracket cannot be escaped\n                const rtrimSlash = rtrim(trimmedUrl.slice(0, -1), '\\\\');\n                if ((trimmedUrl.length - rtrimSlash.length) % 2 === 0) {\n                    return;\n                }\n            }\n            else {\n                // find closing parenthesis\n                const lastParenIndex = findClosingBracket(cap[2], '()');\n                if (lastParenIndex > -1) {\n                    const start = cap[0].indexOf('!') === 0 ? 5 : 4;\n                    const linkLen = start + cap[1].length + lastParenIndex;\n                    cap[2] = cap[2].substring(0, lastParenIndex);\n                    cap[0] = cap[0].substring(0, linkLen).trim();\n                    cap[3] = '';\n                }\n            }\n            let href = cap[2];\n            let title = '';\n            if (this.options.pedantic) {\n                // split pedantic href and title\n                const link = this.rules.other.pedanticHrefTitle.exec(href);\n                if (link) {\n                    href = link[1];\n                    title = link[3];\n                }\n            }\n            else {\n                title = cap[3] ? cap[3].slice(1, -1) : '';\n            }\n            href = href.trim();\n            if (this.rules.other.startAngleBracket.test(href)) {\n                if (this.options.pedantic && !(this.rules.other.endAngleBracket.test(trimmedUrl))) {\n                    // pedantic allows starting angle bracket without ending angle bracket\n                    href = href.slice(1);\n                }\n                else {\n                    href = href.slice(1, -1);\n                }\n            }\n            return outputLink(cap, {\n                href: href ? href.replace(this.rules.inline.anyPunctuation, '$1') : href,\n                title: title ? title.replace(this.rules.inline.anyPunctuation, '$1') : title,\n            }, cap[0], this.lexer, this.rules);\n        }\n    }\n    reflink(src, links) {\n        let cap;\n        if ((cap = this.rules.inline.reflink.exec(src))\n            || (cap = this.rules.inline.nolink.exec(src))) {\n            const linkString = (cap[2] || cap[1]).replace(this.rules.other.multipleSpaceGlobal, ' ');\n            const link = links[linkString.toLowerCase()];\n            if (!link) {\n                const text = cap[0].charAt(0);\n                return {\n                    type: 'text',\n                    raw: text,\n                    text,\n                };\n            }\n            return outputLink(cap, link, cap[0], this.lexer, this.rules);\n        }\n    }\n    emStrong(src, maskedSrc, prevChar = '') {\n        let match = this.rules.inline.emStrongLDelim.exec(src);\n        if (!match)\n            return;\n        // _ can't be between two alphanumerics. \\p{L}\\p{N} includes non-english alphabet/numbers as well\n        if (match[3] && prevChar.match(this.rules.other.unicodeAlphaNumeric))\n            return;\n        const nextChar = match[1] || match[2] || '';\n        if (!nextChar || !prevChar || this.rules.inline.punctuation.exec(prevChar)) {\n            // unicode Regex counts emoji as 1 char; spread into array for proper count (used multiple times below)\n            const lLength = [...match[0]].length - 1;\n            let rDelim, rLength, delimTotal = lLength, midDelimTotal = 0;\n            const endReg = match[0][0] === '*' ? this.rules.inline.emStrongRDelimAst : this.rules.inline.emStrongRDelimUnd;\n            endReg.lastIndex = 0;\n            // Clip maskedSrc to same section of string as src (move to lexer?)\n            maskedSrc = maskedSrc.slice(-1 * src.length + lLength);\n            while ((match = endReg.exec(maskedSrc)) != null) {\n                rDelim = match[1] || match[2] || match[3] || match[4] || match[5] || match[6];\n                if (!rDelim)\n                    continue; // skip single * in __abc*abc__\n                rLength = [...rDelim].length;\n                if (match[3] || match[4]) { // found another Left Delim\n                    delimTotal += rLength;\n                    continue;\n                }\n                else if (match[5] || match[6]) { // either Left or Right Delim\n                    if (lLength % 3 && !((lLength + rLength) % 3)) {\n                        midDelimTotal += rLength;\n                        continue; // CommonMark Emphasis Rules 9-10\n                    }\n                }\n                delimTotal -= rLength;\n                if (delimTotal > 0)\n                    continue; // Haven't found enough closing delimiters\n                // Remove extra characters. *a*** -> *a*\n                rLength = Math.min(rLength, rLength + delimTotal + midDelimTotal);\n                // char length can be >1 for unicode characters;\n                const lastCharLength = [...match[0]][0].length;\n                const raw = src.slice(0, lLength + match.index + lastCharLength + rLength);\n                // Create `em` if smallest delimiter has odd char count. *a***\n                if (Math.min(lLength, rLength) % 2) {\n                    const text = raw.slice(1, -1);\n                    return {\n                        type: 'em',\n                        raw,\n                        text,\n                        tokens: this.lexer.inlineTokens(text),\n                    };\n                }\n                // Create 'strong' if smallest delimiter has even char count. **a***\n                const text = raw.slice(2, -2);\n                return {\n                    type: 'strong',\n                    raw,\n                    text,\n                    tokens: this.lexer.inlineTokens(text),\n                };\n            }\n        }\n    }\n    codespan(src) {\n        const cap = this.rules.inline.code.exec(src);\n        if (cap) {\n            let text = cap[2].replace(this.rules.other.newLineCharGlobal, ' ');\n            const hasNonSpaceChars = this.rules.other.nonSpaceChar.test(text);\n            const hasSpaceCharsOnBothEnds = this.rules.other.startingSpaceChar.test(text) && this.rules.other.endingSpaceChar.test(text);\n            if (hasNonSpaceChars && hasSpaceCharsOnBothEnds) {\n                text = text.substring(1, text.length - 1);\n            }\n            return {\n                type: 'codespan',\n                raw: cap[0],\n                text,\n            };\n        }\n    }\n    br(src) {\n        const cap = this.rules.inline.br.exec(src);\n        if (cap) {\n            return {\n                type: 'br',\n                raw: cap[0],\n            };\n        }\n    }\n    del(src) {\n        const cap = this.rules.inline.del.exec(src);\n        if (cap) {\n            return {\n                type: 'del',\n                raw: cap[0],\n                text: cap[2],\n                tokens: this.lexer.inlineTokens(cap[2]),\n            };\n        }\n    }\n    autolink(src) {\n        const cap = this.rules.inline.autolink.exec(src);\n        if (cap) {\n            let text, href;\n            if (cap[2] === '@') {\n                text = cap[1];\n                href = 'mailto:' + text;\n            }\n            else {\n                text = cap[1];\n                href = text;\n            }\n            return {\n                type: 'link',\n                raw: cap[0],\n                text,\n                href,\n                tokens: [\n                    {\n                        type: 'text',\n                        raw: text,\n                        text,\n                    },\n                ],\n            };\n        }\n    }\n    url(src) {\n        let cap;\n        if (cap = this.rules.inline.url.exec(src)) {\n            let text, href;\n            if (cap[2] === '@') {\n                text = cap[0];\n                href = 'mailto:' + text;\n            }\n            else {\n                // do extended autolink path validation\n                let prevCapZero;\n                do {\n                    prevCapZero = cap[0];\n                    cap[0] = this.rules.inline._backpedal.exec(cap[0])?.[0] ?? '';\n                } while (prevCapZero !== cap[0]);\n                text = cap[0];\n                if (cap[1] === 'www.') {\n                    href = 'http://' + cap[0];\n                }\n                else {\n                    href = cap[0];\n                }\n            }\n            return {\n                type: 'link',\n                raw: cap[0],\n                text,\n                href,\n                tokens: [\n                    {\n                        type: 'text',\n                        raw: text,\n                        text,\n                    },\n                ],\n            };\n        }\n    }\n    inlineText(src) {\n        const cap = this.rules.inline.text.exec(src);\n        if (cap) {\n            const escaped = this.lexer.state.inRawBlock;\n            return {\n                type: 'text',\n                raw: cap[0],\n                text: cap[0],\n                escaped,\n            };\n        }\n    }\n}\n\n/**\n * Block Lexer\n */\nclass _Lexer {\n    tokens;\n    options;\n    state;\n    tokenizer;\n    inlineQueue;\n    constructor(options) {\n        // TokenList cannot be created in one go\n        this.tokens = [];\n        this.tokens.links = Object.create(null);\n        this.options = options || _defaults;\n        this.options.tokenizer = this.options.tokenizer || new _Tokenizer();\n        this.tokenizer = this.options.tokenizer;\n        this.tokenizer.options = this.options;\n        this.tokenizer.lexer = this;\n        this.inlineQueue = [];\n        this.state = {\n            inLink: false,\n            inRawBlock: false,\n            top: true,\n        };\n        const rules = {\n            other,\n            block: block.normal,\n            inline: inline.normal,\n        };\n        if (this.options.pedantic) {\n            rules.block = block.pedantic;\n            rules.inline = inline.pedantic;\n        }\n        else if (this.options.gfm) {\n            rules.block = block.gfm;\n            if (this.options.breaks) {\n                rules.inline = inline.breaks;\n            }\n            else {\n                rules.inline = inline.gfm;\n            }\n        }\n        this.tokenizer.rules = rules;\n    }\n    /**\n     * Expose Rules\n     */\n    static get rules() {\n        return {\n            block,\n            inline,\n        };\n    }\n    /**\n     * Static Lex Method\n     */\n    static lex(src, options) {\n        const lexer = new _Lexer(options);\n        return lexer.lex(src);\n    }\n    /**\n     * Static Lex Inline Method\n     */\n    static lexInline(src, options) {\n        const lexer = new _Lexer(options);\n        return lexer.inlineTokens(src);\n    }\n    /**\n     * Preprocessing\n     */\n    lex(src) {\n        src = src.replace(other.carriageReturn, '\\n');\n        this.blockTokens(src, this.tokens);\n        for (let i = 0; i < this.inlineQueue.length; i++) {\n            const next = this.inlineQueue[i];\n            this.inlineTokens(next.src, next.tokens);\n        }\n        this.inlineQueue = [];\n        return this.tokens;\n    }\n    blockTokens(src, tokens = [], lastParagraphClipped = false) {\n        if (this.options.pedantic) {\n            src = src.replace(other.tabCharGlobal, '    ').replace(other.spaceLine, '');\n        }\n        while (src) {\n            let token;\n            if (this.options.extensions?.block?.some((extTokenizer) => {\n                if (token = extTokenizer.call({ lexer: this }, src, tokens)) {\n                    src = src.substring(token.raw.length);\n                    tokens.push(token);\n                    return true;\n                }\n                return false;\n            })) {\n                continue;\n            }\n            // newline\n            if (token = this.tokenizer.space(src)) {\n                src = src.substring(token.raw.length);\n                const lastToken = tokens.at(-1);\n                if (token.raw.length === 1 && lastToken !== undefined) {\n                    // if there's a single \\n as a spacer, it's terminating the last line,\n                    // so move it there so that we don't get unnecessary paragraph tags\n                    lastToken.raw += '\\n';\n                }\n                else {\n                    tokens.push(token);\n                }\n                continue;\n            }\n            // code\n            if (token = this.tokenizer.code(src)) {\n                src = src.substring(token.raw.length);\n                const lastToken = tokens.at(-1);\n                // An indented code block cannot interrupt a paragraph.\n                if (lastToken?.type === 'paragraph' || lastToken?.type === 'text') {\n                    lastToken.raw += '\\n' + token.raw;\n                    lastToken.text += '\\n' + token.text;\n                    this.inlineQueue.at(-1).src = lastToken.text;\n                }\n                else {\n                    tokens.push(token);\n                }\n                continue;\n            }\n            // fences\n            if (token = this.tokenizer.fences(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // heading\n            if (token = this.tokenizer.heading(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // hr\n            if (token = this.tokenizer.hr(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // blockquote\n            if (token = this.tokenizer.blockquote(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // list\n            if (token = this.tokenizer.list(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // html\n            if (token = this.tokenizer.html(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // def\n            if (token = this.tokenizer.def(src)) {\n                src = src.substring(token.raw.length);\n                const lastToken = tokens.at(-1);\n                if (lastToken?.type === 'paragraph' || lastToken?.type === 'text') {\n                    lastToken.raw += '\\n' + token.raw;\n                    lastToken.text += '\\n' + token.raw;\n                    this.inlineQueue.at(-1).src = lastToken.text;\n                }\n                else if (!this.tokens.links[token.tag]) {\n                    this.tokens.links[token.tag] = {\n                        href: token.href,\n                        title: token.title,\n                    };\n                }\n                continue;\n            }\n            // table (gfm)\n            if (token = this.tokenizer.table(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // lheading\n            if (token = this.tokenizer.lheading(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // top-level paragraph\n            // prevent paragraph consuming extensions by clipping 'src' to extension start\n            let cutSrc = src;\n            if (this.options.extensions?.startBlock) {\n                let startIndex = Infinity;\n                const tempSrc = src.slice(1);\n                let tempStart;\n                this.options.extensions.startBlock.forEach((getStartIndex) => {\n                    tempStart = getStartIndex.call({ lexer: this }, tempSrc);\n                    if (typeof tempStart === 'number' && tempStart >= 0) {\n                        startIndex = Math.min(startIndex, tempStart);\n                    }\n                });\n                if (startIndex < Infinity && startIndex >= 0) {\n                    cutSrc = src.substring(0, startIndex + 1);\n                }\n            }\n            if (this.state.top && (token = this.tokenizer.paragraph(cutSrc))) {\n                const lastToken = tokens.at(-1);\n                if (lastParagraphClipped && lastToken?.type === 'paragraph') {\n                    lastToken.raw += '\\n' + token.raw;\n                    lastToken.text += '\\n' + token.text;\n                    this.inlineQueue.pop();\n                    this.inlineQueue.at(-1).src = lastToken.text;\n                }\n                else {\n                    tokens.push(token);\n                }\n                lastParagraphClipped = cutSrc.length !== src.length;\n                src = src.substring(token.raw.length);\n                continue;\n            }\n            // text\n            if (token = this.tokenizer.text(src)) {\n                src = src.substring(token.raw.length);\n                const lastToken = tokens.at(-1);\n                if (lastToken?.type === 'text') {\n                    lastToken.raw += '\\n' + token.raw;\n                    lastToken.text += '\\n' + token.text;\n                    this.inlineQueue.pop();\n                    this.inlineQueue.at(-1).src = lastToken.text;\n                }\n                else {\n                    tokens.push(token);\n                }\n                continue;\n            }\n            if (src) {\n                const errMsg = 'Infinite loop on byte: ' + src.charCodeAt(0);\n                if (this.options.silent) {\n                    console.error(errMsg);\n                    break;\n                }\n                else {\n                    throw new Error(errMsg);\n                }\n            }\n        }\n        this.state.top = true;\n        return tokens;\n    }\n    inline(src, tokens = []) {\n        this.inlineQueue.push({ src, tokens });\n        return tokens;\n    }\n    /**\n     * Lexing/Compiling\n     */\n    inlineTokens(src, tokens = []) {\n        // String with links masked to avoid interference with em and strong\n        let maskedSrc = src;\n        let match = null;\n        // Mask out reflinks\n        if (this.tokens.links) {\n            const links = Object.keys(this.tokens.links);\n            if (links.length > 0) {\n                while ((match = this.tokenizer.rules.inline.reflinkSearch.exec(maskedSrc)) != null) {\n                    if (links.includes(match[0].slice(match[0].lastIndexOf('[') + 1, -1))) {\n                        maskedSrc = maskedSrc.slice(0, match.index)\n                            + '[' + 'a'.repeat(match[0].length - 2) + ']'\n                            + maskedSrc.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex);\n                    }\n                }\n            }\n        }\n        // Mask out escaped characters\n        while ((match = this.tokenizer.rules.inline.anyPunctuation.exec(maskedSrc)) != null) {\n            maskedSrc = maskedSrc.slice(0, match.index) + '++' + maskedSrc.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);\n        }\n        // Mask out other blocks\n        while ((match = this.tokenizer.rules.inline.blockSkip.exec(maskedSrc)) != null) {\n            maskedSrc = maskedSrc.slice(0, match.index) + '[' + 'a'.repeat(match[0].length - 2) + ']' + maskedSrc.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);\n        }\n        let keepPrevChar = false;\n        let prevChar = '';\n        while (src) {\n            if (!keepPrevChar) {\n                prevChar = '';\n            }\n            keepPrevChar = false;\n            let token;\n            // extensions\n            if (this.options.extensions?.inline?.some((extTokenizer) => {\n                if (token = extTokenizer.call({ lexer: this }, src, tokens)) {\n                    src = src.substring(token.raw.length);\n                    tokens.push(token);\n                    return true;\n                }\n                return false;\n            })) {\n                continue;\n            }\n            // escape\n            if (token = this.tokenizer.escape(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // tag\n            if (token = this.tokenizer.tag(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // link\n            if (token = this.tokenizer.link(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // reflink, nolink\n            if (token = this.tokenizer.reflink(src, this.tokens.links)) {\n                src = src.substring(token.raw.length);\n                const lastToken = tokens.at(-1);\n                if (token.type === 'text' && lastToken?.type === 'text') {\n                    lastToken.raw += token.raw;\n                    lastToken.text += token.text;\n                }\n                else {\n                    tokens.push(token);\n                }\n                continue;\n            }\n            // em & strong\n            if (token = this.tokenizer.emStrong(src, maskedSrc, prevChar)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // code\n            if (token = this.tokenizer.codespan(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // br\n            if (token = this.tokenizer.br(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // del (gfm)\n            if (token = this.tokenizer.del(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // autolink\n            if (token = this.tokenizer.autolink(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // url (gfm)\n            if (!this.state.inLink && (token = this.tokenizer.url(src))) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // text\n            // prevent inlineText consuming extensions by clipping 'src' to extension start\n            let cutSrc = src;\n            if (this.options.extensions?.startInline) {\n                let startIndex = Infinity;\n                const tempSrc = src.slice(1);\n                let tempStart;\n                this.options.extensions.startInline.forEach((getStartIndex) => {\n                    tempStart = getStartIndex.call({ lexer: this }, tempSrc);\n                    if (typeof tempStart === 'number' && tempStart >= 0) {\n                        startIndex = Math.min(startIndex, tempStart);\n                    }\n                });\n                if (startIndex < Infinity && startIndex >= 0) {\n                    cutSrc = src.substring(0, startIndex + 1);\n                }\n            }\n            if (token = this.tokenizer.inlineText(cutSrc)) {\n                src = src.substring(token.raw.length);\n                if (token.raw.slice(-1) !== '_') { // Track prevChar before string of ____ started\n                    prevChar = token.raw.slice(-1);\n                }\n                keepPrevChar = true;\n                const lastToken = tokens.at(-1);\n                if (lastToken?.type === 'text') {\n                    lastToken.raw += token.raw;\n                    lastToken.text += token.text;\n                }\n                else {\n                    tokens.push(token);\n                }\n                continue;\n            }\n            if (src) {\n                const errMsg = 'Infinite loop on byte: ' + src.charCodeAt(0);\n                if (this.options.silent) {\n                    console.error(errMsg);\n                    break;\n                }\n                else {\n                    throw new Error(errMsg);\n                }\n            }\n        }\n        return tokens;\n    }\n}\n\n/**\n * Renderer\n */\nclass _Renderer {\n    options;\n    parser; // set by the parser\n    constructor(options) {\n        this.options = options || _defaults;\n    }\n    space(token) {\n        return '';\n    }\n    code({ text, lang, escaped }) {\n        const langString = (lang || '').match(other.notSpaceStart)?.[0];\n        const code = text.replace(other.endingNewline, '') + '\\n';\n        if (!langString) {\n            return '<pre><code>'\n                + (escaped ? code : escape(code, true))\n                + '</code></pre>\\n';\n        }\n        return '<pre><code class=\"language-'\n            + escape(langString)\n            + '\">'\n            + (escaped ? code : escape(code, true))\n            + '</code></pre>\\n';\n    }\n    blockquote({ tokens }) {\n        const body = this.parser.parse(tokens);\n        return `<blockquote>\\n${body}</blockquote>\\n`;\n    }\n    html({ text }) {\n        return text;\n    }\n    heading({ tokens, depth }) {\n        return `<h${depth}>${this.parser.parseInline(tokens)}</h${depth}>\\n`;\n    }\n    hr(token) {\n        return '<hr>\\n';\n    }\n    list(token) {\n        const ordered = token.ordered;\n        const start = token.start;\n        let body = '';\n        for (let j = 0; j < token.items.length; j++) {\n            const item = token.items[j];\n            body += this.listitem(item);\n        }\n        const type = ordered ? 'ol' : 'ul';\n        const startAttr = (ordered && start !== 1) ? (' start=\"' + start + '\"') : '';\n        return '<' + type + startAttr + '>\\n' + body + '</' + type + '>\\n';\n    }\n    listitem(item) {\n        let itemBody = '';\n        if (item.task) {\n            const checkbox = this.checkbox({ checked: !!item.checked });\n            if (item.loose) {\n                if (item.tokens[0]?.type === 'paragraph') {\n                    item.tokens[0].text = checkbox + ' ' + item.tokens[0].text;\n                    if (item.tokens[0].tokens && item.tokens[0].tokens.length > 0 && item.tokens[0].tokens[0].type === 'text') {\n                        item.tokens[0].tokens[0].text = checkbox + ' ' + escape(item.tokens[0].tokens[0].text);\n                        item.tokens[0].tokens[0].escaped = true;\n                    }\n                }\n                else {\n                    item.tokens.unshift({\n                        type: 'text',\n                        raw: checkbox + ' ',\n                        text: checkbox + ' ',\n                        escaped: true,\n                    });\n                }\n            }\n            else {\n                itemBody += checkbox + ' ';\n            }\n        }\n        itemBody += this.parser.parse(item.tokens, !!item.loose);\n        return `<li>${itemBody}</li>\\n`;\n    }\n    checkbox({ checked }) {\n        return '<input '\n            + (checked ? 'checked=\"\" ' : '')\n            + 'disabled=\"\" type=\"checkbox\">';\n    }\n    paragraph({ tokens }) {\n        return `<p>${this.parser.parseInline(tokens)}</p>\\n`;\n    }\n    table(token) {\n        let header = '';\n        // header\n        let cell = '';\n        for (let j = 0; j < token.header.length; j++) {\n            cell += this.tablecell(token.header[j]);\n        }\n        header += this.tablerow({ text: cell });\n        let body = '';\n        for (let j = 0; j < token.rows.length; j++) {\n            const row = token.rows[j];\n            cell = '';\n            for (let k = 0; k < row.length; k++) {\n                cell += this.tablecell(row[k]);\n            }\n            body += this.tablerow({ text: cell });\n        }\n        if (body)\n            body = `<tbody>${body}</tbody>`;\n        return '<table>\\n'\n            + '<thead>\\n'\n            + header\n            + '</thead>\\n'\n            + body\n            + '</table>\\n';\n    }\n    tablerow({ text }) {\n        return `<tr>\\n${text}</tr>\\n`;\n    }\n    tablecell(token) {\n        const content = this.parser.parseInline(token.tokens);\n        const type = token.header ? 'th' : 'td';\n        const tag = token.align\n            ? `<${type} align=\"${token.align}\">`\n            : `<${type}>`;\n        return tag + content + `</${type}>\\n`;\n    }\n    /**\n     * span level renderer\n     */\n    strong({ tokens }) {\n        return `<strong>${this.parser.parseInline(tokens)}</strong>`;\n    }\n    em({ tokens }) {\n        return `<em>${this.parser.parseInline(tokens)}</em>`;\n    }\n    codespan({ text }) {\n        return `<code>${escape(text, true)}</code>`;\n    }\n    br(token) {\n        return '<br>';\n    }\n    del({ tokens }) {\n        return `<del>${this.parser.parseInline(tokens)}</del>`;\n    }\n    link({ href, title, tokens }) {\n        const text = this.parser.parseInline(tokens);\n        const cleanHref = cleanUrl(href);\n        if (cleanHref === null) {\n            return text;\n        }\n        href = cleanHref;\n        let out = '<a href=\"' + href + '\"';\n        if (title) {\n            out += ' title=\"' + (escape(title)) + '\"';\n        }\n        out += '>' + text + '</a>';\n        return out;\n    }\n    image({ href, title, text }) {\n        const cleanHref = cleanUrl(href);\n        if (cleanHref === null) {\n            return escape(text);\n        }\n        href = cleanHref;\n        let out = `<img src=\"${href}\" alt=\"${text}\"`;\n        if (title) {\n            out += ` title=\"${escape(title)}\"`;\n        }\n        out += '>';\n        return out;\n    }\n    text(token) {\n        return 'tokens' in token && token.tokens\n            ? this.parser.parseInline(token.tokens)\n            : ('escaped' in token && token.escaped ? token.text : escape(token.text));\n    }\n}\n\n/**\n * TextRenderer\n * returns only the textual part of the token\n */\nclass _TextRenderer {\n    // no need for block level renderers\n    strong({ text }) {\n        return text;\n    }\n    em({ text }) {\n        return text;\n    }\n    codespan({ text }) {\n        return text;\n    }\n    del({ text }) {\n        return text;\n    }\n    html({ text }) {\n        return text;\n    }\n    text({ text }) {\n        return text;\n    }\n    link({ text }) {\n        return '' + text;\n    }\n    image({ text }) {\n        return '' + text;\n    }\n    br() {\n        return '';\n    }\n}\n\n/**\n * Parsing & Compiling\n */\nclass _Parser {\n    options;\n    renderer;\n    textRenderer;\n    constructor(options) {\n        this.options = options || _defaults;\n        this.options.renderer = this.options.renderer || new _Renderer();\n        this.renderer = this.options.renderer;\n        this.renderer.options = this.options;\n        this.renderer.parser = this;\n        this.textRenderer = new _TextRenderer();\n    }\n    /**\n     * Static Parse Method\n     */\n    static parse(tokens, options) {\n        const parser = new _Parser(options);\n        return parser.parse(tokens);\n    }\n    /**\n     * Static Parse Inline Method\n     */\n    static parseInline(tokens, options) {\n        const parser = new _Parser(options);\n        return parser.parseInline(tokens);\n    }\n    /**\n     * Parse Loop\n     */\n    parse(tokens, top = true) {\n        let out = '';\n        for (let i = 0; i < tokens.length; i++) {\n            const anyToken = tokens[i];\n            // Run any renderer extensions\n            if (this.options.extensions?.renderers?.[anyToken.type]) {\n                const genericToken = anyToken;\n                const ret = this.options.extensions.renderers[genericToken.type].call({ parser: this }, genericToken);\n                if (ret !== false || !['space', 'hr', 'heading', 'code', 'table', 'blockquote', 'list', 'html', 'paragraph', 'text'].includes(genericToken.type)) {\n                    out += ret || '';\n                    continue;\n                }\n            }\n            const token = anyToken;\n            switch (token.type) {\n                case 'space': {\n                    out += this.renderer.space(token);\n                    continue;\n                }\n                case 'hr': {\n                    out += this.renderer.hr(token);\n                    continue;\n                }\n                case 'heading': {\n                    out += this.renderer.heading(token);\n                    continue;\n                }\n                case 'code': {\n                    out += this.renderer.code(token);\n                    continue;\n                }\n                case 'table': {\n                    out += this.renderer.table(token);\n                    continue;\n                }\n                case 'blockquote': {\n                    out += this.renderer.blockquote(token);\n                    continue;\n                }\n                case 'list': {\n                    out += this.renderer.list(token);\n                    continue;\n                }\n                case 'html': {\n                    out += this.renderer.html(token);\n                    continue;\n                }\n                case 'paragraph': {\n                    out += this.renderer.paragraph(token);\n                    continue;\n                }\n                case 'text': {\n                    let textToken = token;\n                    let body = this.renderer.text(textToken);\n                    while (i + 1 < tokens.length && tokens[i + 1].type === 'text') {\n                        textToken = tokens[++i];\n                        body += '\\n' + this.renderer.text(textToken);\n                    }\n                    if (top) {\n                        out += this.renderer.paragraph({\n                            type: 'paragraph',\n                            raw: body,\n                            text: body,\n                            tokens: [{ type: 'text', raw: body, text: body, escaped: true }],\n                        });\n                    }\n                    else {\n                        out += body;\n                    }\n                    continue;\n                }\n                default: {\n                    const errMsg = 'Token with \"' + token.type + '\" type was not found.';\n                    if (this.options.silent) {\n                        console.error(errMsg);\n                        return '';\n                    }\n                    else {\n                        throw new Error(errMsg);\n                    }\n                }\n            }\n        }\n        return out;\n    }\n    /**\n     * Parse Inline Tokens\n     */\n    parseInline(tokens, renderer = this.renderer) {\n        let out = '';\n        for (let i = 0; i < tokens.length; i++) {\n            const anyToken = tokens[i];\n            // Run any renderer extensions\n            if (this.options.extensions?.renderers?.[anyToken.type]) {\n                const ret = this.options.extensions.renderers[anyToken.type].call({ parser: this }, anyToken);\n                if (ret !== false || !['escape', 'html', 'link', 'image', 'strong', 'em', 'codespan', 'br', 'del', 'text'].includes(anyToken.type)) {\n                    out += ret || '';\n                    continue;\n                }\n            }\n            const token = anyToken;\n            switch (token.type) {\n                case 'escape': {\n                    out += renderer.text(token);\n                    break;\n                }\n                case 'html': {\n                    out += renderer.html(token);\n                    break;\n                }\n                case 'link': {\n                    out += renderer.link(token);\n                    break;\n                }\n                case 'image': {\n                    out += renderer.image(token);\n                    break;\n                }\n                case 'strong': {\n                    out += renderer.strong(token);\n                    break;\n                }\n                case 'em': {\n                    out += renderer.em(token);\n                    break;\n                }\n                case 'codespan': {\n                    out += renderer.codespan(token);\n                    break;\n                }\n                case 'br': {\n                    out += renderer.br(token);\n                    break;\n                }\n                case 'del': {\n                    out += renderer.del(token);\n                    break;\n                }\n                case 'text': {\n                    out += renderer.text(token);\n                    break;\n                }\n                default: {\n                    const errMsg = 'Token with \"' + token.type + '\" type was not found.';\n                    if (this.options.silent) {\n                        console.error(errMsg);\n                        return '';\n                    }\n                    else {\n                        throw new Error(errMsg);\n                    }\n                }\n            }\n        }\n        return out;\n    }\n}\n\nclass _Hooks {\n    options;\n    block;\n    constructor(options) {\n        this.options = options || _defaults;\n    }\n    static passThroughHooks = new Set([\n        'preprocess',\n        'postprocess',\n        'processAllTokens',\n    ]);\n    /**\n     * Process markdown before marked\n     */\n    preprocess(markdown) {\n        return markdown;\n    }\n    /**\n     * Process HTML after marked is finished\n     */\n    postprocess(html) {\n        return html;\n    }\n    /**\n     * Process all tokens before walk tokens\n     */\n    processAllTokens(tokens) {\n        return tokens;\n    }\n    /**\n     * Provide function to tokenize markdown\n     */\n    provideLexer() {\n        return this.block ? _Lexer.lex : _Lexer.lexInline;\n    }\n    /**\n     * Provide function to parse tokens\n     */\n    provideParser() {\n        return this.block ? _Parser.parse : _Parser.parseInline;\n    }\n}\n\nclass Marked {\n    defaults = _getDefaults();\n    options = this.setOptions;\n    parse = this.parseMarkdown(true);\n    parseInline = this.parseMarkdown(false);\n    Parser = _Parser;\n    Renderer = _Renderer;\n    TextRenderer = _TextRenderer;\n    Lexer = _Lexer;\n    Tokenizer = _Tokenizer;\n    Hooks = _Hooks;\n    constructor(...args) {\n        this.use(...args);\n    }\n    /**\n     * Run callback for every token\n     */\n    walkTokens(tokens, callback) {\n        let values = [];\n        for (const token of tokens) {\n            values = values.concat(callback.call(this, token));\n            switch (token.type) {\n                case 'table': {\n                    const tableToken = token;\n                    for (const cell of tableToken.header) {\n                        values = values.concat(this.walkTokens(cell.tokens, callback));\n                    }\n                    for (const row of tableToken.rows) {\n                        for (const cell of row) {\n                            values = values.concat(this.walkTokens(cell.tokens, callback));\n                        }\n                    }\n                    break;\n                }\n                case 'list': {\n                    const listToken = token;\n                    values = values.concat(this.walkTokens(listToken.items, callback));\n                    break;\n                }\n                default: {\n                    const genericToken = token;\n                    if (this.defaults.extensions?.childTokens?.[genericToken.type]) {\n                        this.defaults.extensions.childTokens[genericToken.type].forEach((childTokens) => {\n                            const tokens = genericToken[childTokens].flat(Infinity);\n                            values = values.concat(this.walkTokens(tokens, callback));\n                        });\n                    }\n                    else if (genericToken.tokens) {\n                        values = values.concat(this.walkTokens(genericToken.tokens, callback));\n                    }\n                }\n            }\n        }\n        return values;\n    }\n    use(...args) {\n        const extensions = this.defaults.extensions || { renderers: {}, childTokens: {} };\n        args.forEach((pack) => {\n            // copy options to new object\n            const opts = { ...pack };\n            // set async to true if it was set to true before\n            opts.async = this.defaults.async || opts.async || false;\n            // ==-- Parse \"addon\" extensions --== //\n            if (pack.extensions) {\n                pack.extensions.forEach((ext) => {\n                    if (!ext.name) {\n                        throw new Error('extension name required');\n                    }\n                    if ('renderer' in ext) { // Renderer extensions\n                        const prevRenderer = extensions.renderers[ext.name];\n                        if (prevRenderer) {\n                            // Replace extension with func to run new extension but fall back if false\n                            extensions.renderers[ext.name] = function (...args) {\n                                let ret = ext.renderer.apply(this, args);\n                                if (ret === false) {\n                                    ret = prevRenderer.apply(this, args);\n                                }\n                                return ret;\n                            };\n                        }\n                        else {\n                            extensions.renderers[ext.name] = ext.renderer;\n                        }\n                    }\n                    if ('tokenizer' in ext) { // Tokenizer Extensions\n                        if (!ext.level || (ext.level !== 'block' && ext.level !== 'inline')) {\n                            throw new Error(\"extension level must be 'block' or 'inline'\");\n                        }\n                        const extLevel = extensions[ext.level];\n                        if (extLevel) {\n                            extLevel.unshift(ext.tokenizer);\n                        }\n                        else {\n                            extensions[ext.level] = [ext.tokenizer];\n                        }\n                        if (ext.start) { // Function to check for start of token\n                            if (ext.level === 'block') {\n                                if (extensions.startBlock) {\n                                    extensions.startBlock.push(ext.start);\n                                }\n                                else {\n                                    extensions.startBlock = [ext.start];\n                                }\n                            }\n                            else if (ext.level === 'inline') {\n                                if (extensions.startInline) {\n                                    extensions.startInline.push(ext.start);\n                                }\n                                else {\n                                    extensions.startInline = [ext.start];\n                                }\n                            }\n                        }\n                    }\n                    if ('childTokens' in ext && ext.childTokens) { // Child tokens to be visited by walkTokens\n                        extensions.childTokens[ext.name] = ext.childTokens;\n                    }\n                });\n                opts.extensions = extensions;\n            }\n            // ==-- Parse \"overwrite\" extensions --== //\n            if (pack.renderer) {\n                const renderer = this.defaults.renderer || new _Renderer(this.defaults);\n                for (const prop in pack.renderer) {\n                    if (!(prop in renderer)) {\n                        throw new Error(`renderer '${prop}' does not exist`);\n                    }\n                    if (['options', 'parser'].includes(prop)) {\n                        // ignore options property\n                        continue;\n                    }\n                    const rendererProp = prop;\n                    const rendererFunc = pack.renderer[rendererProp];\n                    const prevRenderer = renderer[rendererProp];\n                    // Replace renderer with func to run extension, but fall back if false\n                    renderer[rendererProp] = (...args) => {\n                        let ret = rendererFunc.apply(renderer, args);\n                        if (ret === false) {\n                            ret = prevRenderer.apply(renderer, args);\n                        }\n                        return ret || '';\n                    };\n                }\n                opts.renderer = renderer;\n            }\n            if (pack.tokenizer) {\n                const tokenizer = this.defaults.tokenizer || new _Tokenizer(this.defaults);\n                for (const prop in pack.tokenizer) {\n                    if (!(prop in tokenizer)) {\n                        throw new Error(`tokenizer '${prop}' does not exist`);\n                    }\n                    if (['options', 'rules', 'lexer'].includes(prop)) {\n                        // ignore options, rules, and lexer properties\n                        continue;\n                    }\n                    const tokenizerProp = prop;\n                    const tokenizerFunc = pack.tokenizer[tokenizerProp];\n                    const prevTokenizer = tokenizer[tokenizerProp];\n                    // Replace tokenizer with func to run extension, but fall back if false\n                    // @ts-expect-error cannot type tokenizer function dynamically\n                    tokenizer[tokenizerProp] = (...args) => {\n                        let ret = tokenizerFunc.apply(tokenizer, args);\n                        if (ret === false) {\n                            ret = prevTokenizer.apply(tokenizer, args);\n                        }\n                        return ret;\n                    };\n                }\n                opts.tokenizer = tokenizer;\n            }\n            // ==-- Parse Hooks extensions --== //\n            if (pack.hooks) {\n                const hooks = this.defaults.hooks || new _Hooks();\n                for (const prop in pack.hooks) {\n                    if (!(prop in hooks)) {\n                        throw new Error(`hook '${prop}' does not exist`);\n                    }\n                    if (['options', 'block'].includes(prop)) {\n                        // ignore options and block properties\n                        continue;\n                    }\n                    const hooksProp = prop;\n                    const hooksFunc = pack.hooks[hooksProp];\n                    const prevHook = hooks[hooksProp];\n                    if (_Hooks.passThroughHooks.has(prop)) {\n                        // @ts-expect-error cannot type hook function dynamically\n                        hooks[hooksProp] = (arg) => {\n                            if (this.defaults.async) {\n                                return Promise.resolve(hooksFunc.call(hooks, arg)).then(ret => {\n                                    return prevHook.call(hooks, ret);\n                                });\n                            }\n                            const ret = hooksFunc.call(hooks, arg);\n                            return prevHook.call(hooks, ret);\n                        };\n                    }\n                    else {\n                        // @ts-expect-error cannot type hook function dynamically\n                        hooks[hooksProp] = (...args) => {\n                            let ret = hooksFunc.apply(hooks, args);\n                            if (ret === false) {\n                                ret = prevHook.apply(hooks, args);\n                            }\n                            return ret;\n                        };\n                    }\n                }\n                opts.hooks = hooks;\n            }\n            // ==-- Parse WalkTokens extensions --== //\n            if (pack.walkTokens) {\n                const walkTokens = this.defaults.walkTokens;\n                const packWalktokens = pack.walkTokens;\n                opts.walkTokens = function (token) {\n                    let values = [];\n                    values.push(packWalktokens.call(this, token));\n                    if (walkTokens) {\n                        values = values.concat(walkTokens.call(this, token));\n                    }\n                    return values;\n                };\n            }\n            this.defaults = { ...this.defaults, ...opts };\n        });\n        return this;\n    }\n    setOptions(opt) {\n        this.defaults = { ...this.defaults, ...opt };\n        return this;\n    }\n    lexer(src, options) {\n        return _Lexer.lex(src, options ?? this.defaults);\n    }\n    parser(tokens, options) {\n        return _Parser.parse(tokens, options ?? this.defaults);\n    }\n    parseMarkdown(blockType) {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        const parse = (src, options) => {\n            const origOpt = { ...options };\n            const opt = { ...this.defaults, ...origOpt };\n            const throwError = this.onError(!!opt.silent, !!opt.async);\n            // throw error if an extension set async to true but parse was called with async: false\n            if (this.defaults.async === true && origOpt.async === false) {\n                return throwError(new Error('marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise.'));\n            }\n            // throw error in case of non string input\n            if (typeof src === 'undefined' || src === null) {\n                return throwError(new Error('marked(): input parameter is undefined or null'));\n            }\n            if (typeof src !== 'string') {\n                return throwError(new Error('marked(): input parameter is of type '\n                    + Object.prototype.toString.call(src) + ', string expected'));\n            }\n            if (opt.hooks) {\n                opt.hooks.options = opt;\n                opt.hooks.block = blockType;\n            }\n            const lexer = opt.hooks ? opt.hooks.provideLexer() : (blockType ? _Lexer.lex : _Lexer.lexInline);\n            const parser = opt.hooks ? opt.hooks.provideParser() : (blockType ? _Parser.parse : _Parser.parseInline);\n            if (opt.async) {\n                return Promise.resolve(opt.hooks ? opt.hooks.preprocess(src) : src)\n                    .then(src => lexer(src, opt))\n                    .then(tokens => opt.hooks ? opt.hooks.processAllTokens(tokens) : tokens)\n                    .then(tokens => opt.walkTokens ? Promise.all(this.walkTokens(tokens, opt.walkTokens)).then(() => tokens) : tokens)\n                    .then(tokens => parser(tokens, opt))\n                    .then(html => opt.hooks ? opt.hooks.postprocess(html) : html)\n                    .catch(throwError);\n            }\n            try {\n                if (opt.hooks) {\n                    src = opt.hooks.preprocess(src);\n                }\n                let tokens = lexer(src, opt);\n                if (opt.hooks) {\n                    tokens = opt.hooks.processAllTokens(tokens);\n                }\n                if (opt.walkTokens) {\n                    this.walkTokens(tokens, opt.walkTokens);\n                }\n                let html = parser(tokens, opt);\n                if (opt.hooks) {\n                    html = opt.hooks.postprocess(html);\n                }\n                return html;\n            }\n            catch (e) {\n                return throwError(e);\n            }\n        };\n        return parse;\n    }\n    onError(silent, async) {\n        return (e) => {\n            e.message += '\\nPlease report this to https://github.com/markedjs/marked.';\n            if (silent) {\n                const msg = '<p>An error occurred:</p><pre>'\n                    + escape(e.message + '', true)\n                    + '</pre>';\n                if (async) {\n                    return Promise.resolve(msg);\n                }\n                return msg;\n            }\n            if (async) {\n                return Promise.reject(e);\n            }\n            throw e;\n        };\n    }\n}\n\nconst markedInstance = new Marked();\nfunction marked(src, opt) {\n    return markedInstance.parse(src, opt);\n}\n/**\n * Sets the default options.\n *\n * @param options Hash of options\n */\nmarked.options =\n    marked.setOptions = function (options) {\n        markedInstance.setOptions(options);\n        marked.defaults = markedInstance.defaults;\n        changeDefaults(marked.defaults);\n        return marked;\n    };\n/**\n * Gets the original marked default options.\n */\nmarked.getDefaults = _getDefaults;\nmarked.defaults = _defaults;\n/**\n * Use Extension\n */\nmarked.use = function (...args) {\n    markedInstance.use(...args);\n    marked.defaults = markedInstance.defaults;\n    changeDefaults(marked.defaults);\n    return marked;\n};\n/**\n * Run callback for every token\n */\nmarked.walkTokens = function (tokens, callback) {\n    return markedInstance.walkTokens(tokens, callback);\n};\n/**\n * Compiles markdown to HTML without enclosing `p` tag.\n *\n * @param src String of markdown source to be compiled\n * @param options Hash of options\n * @return String of compiled HTML\n */\nmarked.parseInline = markedInstance.parseInline;\n/**\n * Expose\n */\nmarked.Parser = _Parser;\nmarked.parser = _Parser.parse;\nmarked.Renderer = _Renderer;\nmarked.TextRenderer = _TextRenderer;\nmarked.Lexer = _Lexer;\nmarked.lexer = _Lexer.lex;\nmarked.Tokenizer = _Tokenizer;\nmarked.Hooks = _Hooks;\nmarked.parse = marked;\nconst options = marked.options;\nconst setOptions = marked.setOptions;\nconst use = marked.use;\nconst walkTokens = marked.walkTokens;\nconst parseInline = marked.parseInline;\nconst parse = marked;\nconst parser = _Parser.parse;\nconst lexer = _Lexer.lex;\n\n\n//# sourceMappingURL=marked.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/marked/lib/marked.esm.js\n");

/***/ })

};
;