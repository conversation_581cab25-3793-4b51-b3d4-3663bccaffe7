"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_reg_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/reg.mjs":
/*!**************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/reg.mjs ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Windows Registry Script\\\",\\\"fileTypes\\\":[\\\"reg\\\",\\\"REG\\\"],\\\"name\\\":\\\"reg\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"Windows Registry Editor Version 5\\\\\\\\.00|REGEDIT4\\\",\\\"name\\\":\\\"keyword.control.import.reg\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.reg\\\"}},\\\"match\\\":\\\"(;).*$\\\",\\\"name\\\":\\\"comment.line.semicolon.reg\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.section.reg\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.section.reg\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.section.reg\\\"}},\\\"match\\\":\\\"^\\\\\\\\s*(\\\\\\\\[(?!-))(.*?)(])\\\",\\\"name\\\":\\\"entity.name.function.section.add.reg\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.section.reg\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.section.reg\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.section.reg\\\"}},\\\"match\\\":\\\"^\\\\\\\\s*(\\\\\\\\[-)(.*?)(])\\\",\\\"name\\\":\\\"entity.name.function.section.delete.reg\\\"},{\\\"captures\\\":{\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.quote.reg\\\"},\\\"3\\\":{\\\"name\\\":\\\"support.function.regname.ini\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.quote.reg\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.definition.equals.reg\\\"},\\\"7\\\":{\\\"name\\\":\\\"keyword.operator.arithmetic.minus.reg\\\"},\\\"9\\\":{\\\"name\\\":\\\"punctuation.definition.quote.reg\\\"},\\\"10\\\":{\\\"name\\\":\\\"string.name.regdata.reg\\\"},\\\"11\\\":{\\\"name\\\":\\\"punctuation.definition.quote.reg\\\"},\\\"13\\\":{\\\"name\\\":\\\"support.type.dword.reg\\\"},\\\"14\\\":{\\\"name\\\":\\\"keyword.operator.arithmetic.colon.reg\\\"},\\\"15\\\":{\\\"name\\\":\\\"constant.numeric.dword.reg\\\"},\\\"17\\\":{\\\"name\\\":\\\"support.type.dword.reg\\\"},\\\"18\\\":{\\\"name\\\":\\\"keyword.operator.arithmetic.parenthesis.reg\\\"},\\\"19\\\":{\\\"name\\\":\\\"keyword.operator.arithmetic.parenthesis.reg\\\"},\\\"20\\\":{\\\"name\\\":\\\"constant.numeric.hex.size.reg\\\"},\\\"21\\\":{\\\"name\\\":\\\"keyword.operator.arithmetic.parenthesis.reg\\\"},\\\"22\\\":{\\\"name\\\":\\\"keyword.operator.arithmetic.colon.reg\\\"},\\\"23\\\":{\\\"name\\\":\\\"constant.numeric.hex.reg\\\"},\\\"24\\\":{\\\"name\\\":\\\"keyword.operator.arithmetic.linecontinuation.reg\\\"},\\\"25\\\":{\\\"name\\\":\\\"comment.declarationline.semicolon.reg\\\"}},\\\"match\\\":\\\"^(\\\\\\\\s*([\\\\\\\"']?)(.+?)([\\\\\\\"']?)\\\\\\\\s*(=))?\\\\\\\\s*((-)|(([\\\\\\\"'])(.*?)([\\\\\\\"']))|(((?i:dword))(:)\\\\\\\\s*([\\\\\\\\dabcdefABCDEF]{1,8}))|(((?i:hex))((\\\\\\\\()(\\\\\\\\d*)(\\\\\\\\)))?(:)(.*?)(\\\\\\\\\\\\\\\\?)))\\\\\\\\s*(;.*)?$\\\",\\\"name\\\":\\\"meta.declaration.reg\\\"},{\\\"match\\\":\\\"[0-9]+\\\",\\\"name\\\":\\\"constant.numeric.reg\\\"},{\\\"match\\\":\\\"[a-fA-F]+\\\",\\\"name\\\":\\\"constant.numeric.hex.reg\\\"},{\\\"match\\\":\\\",+\\\",\\\"name\\\":\\\"constant.numeric.hex.comma.reg\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.linecontinuation.reg\\\"}],\\\"scopeName\\\":\\\"source.reg\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/reg.mjs\n"));

/***/ })

}]);