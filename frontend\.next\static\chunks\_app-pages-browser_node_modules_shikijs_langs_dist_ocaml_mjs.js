"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_ocaml_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/ocaml.mjs":
/*!****************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/ocaml.mjs ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"OCaml\\\",\\\"fileTypes\\\":[\\\".ml\\\",\\\".mli\\\"],\\\"name\\\":\\\"ocaml\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#pragma\\\"},{\\\"include\\\":\\\"#decl\\\"}],\\\"repository\\\":{\\\"attribute\\\":{\\\"begin\\\":\\\"(\\\\\\\\[)\\\\\\\\s*((?<![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])@{1,3}(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.language constant.numeric entity.other.attribute-name.id.css strong\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp\\\"}},\\\"end\\\":\\\"]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"constant.language constant.numeric entity.other.attribute-name.id.css strong\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#attributePayload\\\"}]},\\\"attributeIdentifier\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp strong\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.tag\\\"}},\\\"match\\\":\\\"((?<![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])%(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]))((?!\\\\\\\\b(?:and|'|as|asr|assert|\\\\\\\\*|begin|class|[:,@]|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\\\\\.|fun|function|functor|[>-]|if|in|include|inherit|initializer|land|lazy|[{(\\\\\\\\[<]|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|[%+]|private|[?\\\\\\\"]|rec|[\\\\\\\\\\\\\\\\})\\\\\\\\];]|sig|/|struct|then|~|to|true|try|type|val|\\\\\\\\||virtual|when|while|with)\\\\\\\\b(?:[^']|$))\\\\\\\\b(?=[[:lower:]_])[[:alpha:]_][[:word:]']*)\\\"},\\\"attributePayload\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=(?:[^#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]%|^%))(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\",\\\"end\\\":\\\"((?<![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])[:?](?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]))|(?<=\\\\\\\\s)|(?=])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#pathModuleExtended\\\"},{\\\"include\\\":\\\"#pathRecord\\\"}]},{\\\"begin\\\":\\\"(?<=(?:[^#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]:|^:))(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\",\\\"end\\\":\\\"(?=])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#signature\\\"},{\\\"include\\\":\\\"#type\\\"}]},{\\\"begin\\\":\\\"(?<=(?:[^#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]\\\\\\\\?|^\\\\\\\\?))(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\",\\\"end\\\":\\\"(?=])\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=(?:[^#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]\\\\\\\\?|^\\\\\\\\?))(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\",\\\"end\\\":\\\"(?=])|\\\\\\\\bwhen\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"1\\\":{}},\\\"patterns\\\":[{\\\"include\\\":\\\"#pattern\\\"}]},{\\\"begin\\\":\\\"(?<=(?:\\\\\\\\P{word}when|^when))(?!\\\\\\\\p{word})\\\",\\\"end\\\":\\\"(?=])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#term\\\"}]}]},{\\\"include\\\":\\\"#term\\\"}]},\\\"bindClassTerm\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=(?:\\\\\\\\P{word}|^)(?:and|class|type))(?!\\\\\\\\p{word})\\\",\\\"end\\\":\\\"(?<![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])(:)|(=)(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])|(?=;;|[})\\\\\\\\]]|\\\\\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp strong\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.type strong\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=(?:\\\\\\\\P{word}|^)(?:and|class|type))(?!\\\\\\\\p{word})\\\",\\\"end\\\":\\\"(?=(?!\\\\\\\\b(?:and|'|as|asr|assert|\\\\\\\\*|begin|class|[:,@]|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\\\\\.|fun|function|functor|[>-]|if|in|include|inherit|initializer|land|lazy|[{(\\\\\\\\[<]|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|[%+]|private|[?\\\\\\\"]|rec|[\\\\\\\\\\\\\\\\})\\\\\\\\];]|sig|/|struct|then|~|to|true|try|type|val|\\\\\\\\||virtual|when|while|with)\\\\\\\\b(?:[^']|$))\\\\\\\\b(?=[[:lower:]_])[[:alpha:]_][[:word:]']*\\\\\\\\s*,|[^\\\\\\\\s[:lower:]%])|(?!\\\\\\\\b(?:and|'|as|asr|assert|\\\\\\\\*|begin|class|[:,@]|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\\\\\.|fun|function|functor|[>-]|if|in|include|inherit|initializer|land|lazy|[{(\\\\\\\\[<]|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|[%+]|private|[?\\\\\\\"]|rec|[\\\\\\\\\\\\\\\\})\\\\\\\\];]|sig|/|struct|then|~|to|true|try|type|val|\\\\\\\\||virtual|when|while|with)\\\\\\\\b(?:[^']|$))\\\\\\\\b(?=[[:lower:]_])[[:alpha:]_][[:word:]']*|(?=\\\\\\\\btype\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"entity.name.function strong emphasis\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#attributeIdentifier\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\[\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.tag\\\"}},\\\"end\\\":\\\"]\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type\\\"}]},{\\\"include\\\":\\\"#bindTermArgs\\\"}]},{\\\"begin\\\":\\\"(?<=(?:[^#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]:|^:))(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\",\\\"end\\\":\\\"(?<![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])=(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])|(?=[})\\\\\\\\]]|\\\\\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|val)\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.type strong\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#literalClassType\\\"}]},{\\\"begin\\\":\\\"(?<=(?:[^#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]=|^=))(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\",\\\"end\\\":\\\"\\\\\\\\band\\\\\\\\b|(?=;;|[})\\\\\\\\]]|\\\\\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp markup.underline\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#term\\\"}]}]},\\\"bindClassType\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=(?:\\\\\\\\P{word}|^)(?:and|class|type))(?!\\\\\\\\p{word})\\\",\\\"end\\\":\\\"(?<![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])(:)|(=)(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])|(?=;;|[})\\\\\\\\]]|\\\\\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp strong\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.type strong\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=(?:\\\\\\\\P{word}|^)(?:and|class|type))(?!\\\\\\\\p{word})\\\",\\\"end\\\":\\\"(?=(?!\\\\\\\\b(?:and|'|as|asr|assert|\\\\\\\\*|begin|class|[:,@]|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\\\\\.|fun|function|functor|[>-]|if|in|include|inherit|initializer|land|lazy|[{(\\\\\\\\[<]|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|[%+]|private|[?\\\\\\\"]|rec|[\\\\\\\\\\\\\\\\})\\\\\\\\];]|sig|/|struct|then|~|to|true|try|type|val|\\\\\\\\||virtual|when|while|with)\\\\\\\\b(?:[^']|$))\\\\\\\\b(?=[[:lower:]_])[[:alpha:]_][[:word:]']*\\\\\\\\s*,|[^\\\\\\\\s[:lower:]%])|(?!\\\\\\\\b(?:and|'|as|asr|assert|\\\\\\\\*|begin|class|[:,@]|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\\\\\.|fun|function|functor|[>-]|if|in|include|inherit|initializer|land|lazy|[{(\\\\\\\\[<]|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|[%+]|private|[?\\\\\\\"]|rec|[\\\\\\\\\\\\\\\\})\\\\\\\\];]|sig|/|struct|then|~|to|true|try|type|val|\\\\\\\\||virtual|when|while|with)\\\\\\\\b(?:[^']|$))\\\\\\\\b(?=[[:lower:]_])[[:alpha:]_][[:word:]']*|(?=\\\\\\\\btype\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"entity.name.function strong emphasis\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#attributeIdentifier\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\[\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.tag\\\"}},\\\"end\\\":\\\"]\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type\\\"}]},{\\\"include\\\":\\\"#bindTermArgs\\\"}]},{\\\"begin\\\":\\\"(?<=(?:[^#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]:|^:))(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\",\\\"end\\\":\\\"(?<![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])=(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])|(?=[})\\\\\\\\]]|\\\\\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|val)\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.type strong\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#literalClassType\\\"}]},{\\\"begin\\\":\\\"(?<=(?:[^#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]=|^=))(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\",\\\"end\\\":\\\"\\\\\\\\band\\\\\\\\b|(?=;;|[})\\\\\\\\]]|\\\\\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp markup.underline\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#literalClassType\\\"}]}]},\\\"bindConstructor\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=(?:\\\\\\\\P{word}exception|^exception))(?!\\\\\\\\p{word})|(?<=(?:[^#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]\\\\\\\\+=|^\\\\\\\\+=|[^#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]=|^=|[^#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]\\\\\\\\||^\\\\\\\\|))(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\",\\\"end\\\":\\\"(:)|(\\\\\\\\bof\\\\\\\\b)|((?<![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\\\\\\|(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]))|(?=;;|[})\\\\\\\\]]|\\\\\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp strong\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.tag\\\"},\\\"3\\\":{\\\"name\\\":\\\"support.type strong\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#attributeIdentifier\\\"},{\\\"match\\\":\\\"\\\\\\\\.\\\\\\\\.\\\",\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp\\\"},{\\\"match\\\":\\\"\\\\\\\\b\\\\\\\\b(?=\\\\\\\\p{upper})[[:alpha:]_][[:word:]']*\\\\\\\\b(?!\\\\\\\\s*(?:\\\\\\\\.|\\\\\\\\([^*]))\\\",\\\"name\\\":\\\"constant.language constant.numeric entity.other.attribute-name.id.css strong\\\"},{\\\"include\\\":\\\"#type\\\"}]},{\\\"begin\\\":\\\"(?<=(?:[^#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]:|^:))(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])|(?<=(?:\\\\\\\\P{word}of|^of))(?!\\\\\\\\p{word})\\\",\\\"end\\\":\\\"(?<![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\\\\\\|(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])|(?=;;|[})\\\\\\\\]]|\\\\\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.type strong\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#type\\\"}]}]},\\\"bindSignature\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"begin\\\":\\\"(?<=(?:\\\\\\\\P{word}type|^type))(?!\\\\\\\\p{word})\\\",\\\"end\\\":\\\"(?<![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])=(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.type strong\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#pathModuleExtended\\\"}]},{\\\"begin\\\":\\\"(?<=(?:[^#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]=|^=))(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\",\\\"end\\\":\\\"\\\\\\\\band\\\\\\\\b|(?=;;|[})\\\\\\\\]]|\\\\\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp markup.underline\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#signature\\\"}]}]},\\\"bindStructure\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"begin\\\":\\\"(?<=(?:\\\\\\\\P{word}and|^and))(?!\\\\\\\\p{word})|(?=\\\\\\\\p{upper})\\\",\\\"end\\\":\\\"(?<![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])(:(?!=))|(:?=)(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])|(?=[})\\\\\\\\]]|\\\\\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|open|type|val)\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp strong\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.type strong\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"match\\\":\\\"\\\\\\\\bmodule\\\\\\\\b\\\",\\\"name\\\":\\\"markup.inserted constant.language support.constant.property-value entity.name.filename\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?=\\\\\\\\p{upper})[[:alpha:]_][[:word:]']*\\\",\\\"name\\\":\\\"entity.name.function strong emphasis\\\"},{\\\"begin\\\":\\\"\\\\\\\\((?!\\\\\\\\))\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.tag\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"begin\\\":\\\"(?<![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]):(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp strong\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#signature\\\"}]},{\\\"include\\\":\\\"#variableModule\\\"}]},{\\\"include\\\":\\\"#literalUnit\\\"}]},{\\\"begin\\\":\\\"(?<=(?:[^#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]:|^:))(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\",\\\"end\\\":\\\"\\\\\\\\b(and)\\\\\\\\b|((?<![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])=(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]))|(?=;;|[})\\\\\\\\]]|\\\\\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp markup.underline\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.type strong\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#signature\\\"}]},{\\\"begin\\\":\\\"(?<=(?:[^#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]:=|^:=|[^#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]=|^=))(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\",\\\"end\\\":\\\"\\\\\\\\b(?:(and)|(with))\\\\\\\\b|(?=;;|[})\\\\\\\\]]|\\\\\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp markup.underline\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp markup.underline\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#structure\\\"}]}]},\\\"bindTerm\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=(?:[^#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]!|^!))(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])|(?<=(?:\\\\\\\\P{word}|^)(?:and|external|let|method|val))(?!\\\\\\\\p{word})\\\",\\\"end\\\":\\\"(\\\\\\\\bmodule\\\\\\\\b)|(\\\\\\\\bopen\\\\\\\\b)|(?<![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])(:)|((?<![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])=(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]))(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])|(?=;;|[})\\\\\\\\]]|\\\\\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"markup.inserted constant.language support.constant.property-value entity.name.filename\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp\\\"},\\\"3\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp strong\\\"},\\\"4\\\":{\\\"name\\\":\\\"support.type strong\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=(?:[^#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]!|^!))(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])|(?<=(?:\\\\\\\\P{word}|^)(?:and|external|let|method|val))(?!\\\\\\\\p{word})\\\",\\\"end\\\":\\\"(?=\\\\\\\\b(?:module|open)\\\\\\\\b)|(?=(?!\\\\\\\\b(?:and|'|as|asr|assert|\\\\\\\\*|begin|class|[:,@]|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\\\\\.|fun|function|functor|[>-]|if|in|include|inherit|initializer|land|lazy|[{(\\\\\\\\[<]|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|[%+]|private|[?\\\\\\\"]|rec|[\\\\\\\\\\\\\\\\})\\\\\\\\];]|sig|/|struct|then|~|to|true|try|type|val|\\\\\\\\||virtual|when|while|with)\\\\\\\\b(?:[^']|$))\\\\\\\\b(?=[[:lower:]_])[[:alpha:]_][[:word:]']*\\\\\\\\s*,|[^\\\\\\\\s[:lower:]%])|(\\\\\\\\brec\\\\\\\\b)|((?!\\\\\\\\b(?:and|'|as|asr|assert|\\\\\\\\*|begin|class|[:,@]|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\\\\\.|fun|function|functor|[>-]|if|in|include|inherit|initializer|land|lazy|[{(\\\\\\\\[<]|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|[%+]|private|[?\\\\\\\"]|rec|[\\\\\\\\\\\\\\\\})\\\\\\\\];]|sig|/|struct|then|~|to|true|try|type|val|\\\\\\\\||virtual|when|while|with)\\\\\\\\b(?:[^']|$))\\\\\\\\b(?=[[:lower:]_])[[:alpha:]_][[:word:]']*)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function strong emphasis\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#attributeIdentifier\\\"},{\\\"include\\\":\\\"#comment\\\"}]},{\\\"begin\\\":\\\"(?<=(?:\\\\\\\\P{word}rec|^rec))(?!\\\\\\\\p{word})\\\",\\\"end\\\":\\\"((?!\\\\\\\\b(?:and|'|as|asr|assert|\\\\\\\\*|begin|class|[:,@]|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\\\\\.|fun|function|functor|[>-]|if|in|include|inherit|initializer|land|lazy|[{(\\\\\\\\[<]|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|[%+]|private|[?\\\\\\\"]|rec|[\\\\\\\\\\\\\\\\})\\\\\\\\];]|sig|/|struct|then|~|to|true|try|type|val|\\\\\\\\||virtual|when|while|with)\\\\\\\\b(?:[^']|$))\\\\\\\\b(?=[[:lower:]_])[[:alpha:]_][[:word:]']*)|(?=[^\\\\\\\\s[:alpha:]])\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"entity.name.function strong emphasis\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#bindTermArgs\\\"}]},{\\\"include\\\":\\\"#bindTermArgs\\\"}]},{\\\"begin\\\":\\\"(?<=(?:\\\\\\\\P{word}module|^module))(?!\\\\\\\\p{word})\\\",\\\"end\\\":\\\"(?=;;|[})\\\\\\\\]]|\\\\\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\\\\\b)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#declModule\\\"}]},{\\\"begin\\\":\\\"(?<=(?:\\\\\\\\P{word}open|^open))(?!\\\\\\\\p{word})\\\",\\\"end\\\":\\\"(?=\\\\\\\\bin\\\\\\\\b)|(?=[})\\\\\\\\]]|\\\\\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\\\\\b)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#pathModuleSimple\\\"}]},{\\\"begin\\\":\\\"(?<=(?:[^#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]:|^:))(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\",\\\"end\\\":\\\"(?<![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])=(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])|(?=;;|[})\\\\\\\\]]|\\\\\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.type strong\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=(?:[^#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]:|^:))(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\",\\\"end\\\":\\\"\\\\\\\\btype\\\\\\\\b|(?=\\\\\\\\S)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.control\\\"}}},{\\\"begin\\\":\\\"(?<=(?:\\\\\\\\P{word}type|^type))(?!\\\\\\\\p{word})\\\",\\\"end\\\":\\\"(?<![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\\\\\\.(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#pattern\\\"}]},{\\\"include\\\":\\\"#type\\\"}]},{\\\"begin\\\":\\\"(?<=(?:[^#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]=|^=))(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\",\\\"end\\\":\\\"\\\\\\\\band\\\\\\\\b|(?=;;|[})\\\\\\\\]]|\\\\\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp markup.underline\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#term\\\"}]}]},\\\"bindTermArgs\\\":{\\\"patterns\\\":[{\\\"applyEndPatternLast\\\":true,\\\"begin\\\":\\\"[~?]\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp\\\"}},\\\"end\\\":\\\":|(?=\\\\\\\\S)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=(?:[^#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]~|^~|[^#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]\\\\\\\\?|^\\\\\\\\?))(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\",\\\"end\\\":\\\"(?!\\\\\\\\b(?:and|'|as|asr|assert|\\\\\\\\*|begin|class|[:,@]|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\\\\\.|fun|function|functor|[>-]|if|in|include|inherit|initializer|land|lazy|[{(\\\\\\\\[<]|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|[%+]|private|[?\\\\\\\"]|rec|[\\\\\\\\\\\\\\\\})\\\\\\\\];]|sig|/|struct|then|~|to|true|try|type|val|\\\\\\\\||virtual|when|while|with)\\\\\\\\b(?:[^']|$))\\\\\\\\b(?=[[:lower:]_])[[:alpha:]_][[:word:]']*|(?<=\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.inserted constant.language support.constant.property-value entity.name.filename\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"begin\\\":\\\"\\\\\\\\((?!\\\\\\\\*)\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.tag\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=\\\\\\\\()\\\",\\\"end\\\":\\\"[:=]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword\\\"}},\\\"patterns\\\":[{\\\"match\\\":\\\"(?!\\\\\\\\b(?:and|'|as|asr|assert|\\\\\\\\*|begin|class|[:,@]|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\\\\\.|fun|function|functor|[>-]|if|in|include|inherit|initializer|land|lazy|[{(\\\\\\\\[<]|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|[%+]|private|[?\\\\\\\"]|rec|[\\\\\\\\\\\\\\\\})\\\\\\\\];]|sig|/|struct|then|~|to|true|try|type|val|\\\\\\\\||virtual|when|while|with)\\\\\\\\b(?:[^']|$))\\\\\\\\b(?=[[:lower:]_])[[:alpha:]_][[:word:]']*\\\",\\\"name\\\":\\\"markup.inserted constant.language support.constant.property-value entity.name.filename\\\"}]},{\\\"begin\\\":\\\"(?<=:)\\\",\\\"end\\\":\\\"=|(?=\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#type\\\"}]},{\\\"begin\\\":\\\"(?<=(?:[^#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]=|^=))(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\",\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#term\\\"}]}]}]}]},{\\\"include\\\":\\\"#pattern\\\"}]},\\\"bindType\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=(?:\\\\\\\\P{word}|^)(?:and|type))(?!\\\\\\\\p{word})\\\",\\\"end\\\":\\\"(?<![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\\\\\\+=|=(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])|(?=;;|[})\\\\\\\\]]|\\\\\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.type strong\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#attributeIdentifier\\\"},{\\\"include\\\":\\\"#pathType\\\"},{\\\"match\\\":\\\"(?!\\\\\\\\b(?:and|'|as|asr|assert|\\\\\\\\*|begin|class|[:,@]|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\\\\\.|fun|function|functor|[>-]|if|in|include|inherit|initializer|land|lazy|[{(\\\\\\\\[<]|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|[%+]|private|[?\\\\\\\"]|rec|[\\\\\\\\\\\\\\\\})\\\\\\\\];]|sig|/|struct|then|~|to|true|try|type|val|\\\\\\\\||virtual|when|while|with)\\\\\\\\b(?:[^']|$))\\\\\\\\b(?=[[:lower:]_])[[:alpha:]_][[:word:]']*\\\",\\\"name\\\":\\\"entity.name.function strong\\\"},{\\\"include\\\":\\\"#type\\\"}]},{\\\"begin\\\":\\\"(?<=(?:[^#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]\\\\\\\\+=|^\\\\\\\\+=|[^#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]=|^=))(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\",\\\"end\\\":\\\"\\\\\\\\band\\\\\\\\b|(?=;;|[})\\\\\\\\]]|\\\\\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp markup.underline\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#bindConstructor\\\"}]}]},\\\"comment\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#attribute\\\"},{\\\"include\\\":\\\"#extension\\\"},{\\\"include\\\":\\\"#commentBlock\\\"},{\\\"include\\\":\\\"#commentDoc\\\"}]},\\\"commentBlock\\\":{\\\"begin\\\":\\\"\\\\\\\\(\\\\\\\\*(?!\\\\\\\\*[^)])\\\",\\\"contentName\\\":\\\"emphasis\\\",\\\"end\\\":\\\"\\\\\\\\*\\\\\\\\)\\\",\\\"name\\\":\\\"comment constant.regexp meta.separator.markdown\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#commentBlock\\\"},{\\\"include\\\":\\\"#commentDoc\\\"}]},\\\"commentDoc\\\":{\\\"begin\\\":\\\"\\\\\\\\(\\\\\\\\*\\\\\\\\*\\\",\\\"end\\\":\\\"\\\\\\\\*\\\\\\\\)\\\",\\\"name\\\":\\\"comment constant.regexp meta.separator.markdown\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\*\\\"},{\\\"include\\\":\\\"#comment\\\"}]},\\\"decl\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#declClass\\\"},{\\\"include\\\":\\\"#declException\\\"},{\\\"include\\\":\\\"#declInclude\\\"},{\\\"include\\\":\\\"#declModule\\\"},{\\\"include\\\":\\\"#declOpen\\\"},{\\\"include\\\":\\\"#declTerm\\\"},{\\\"include\\\":\\\"#declType\\\"}]},\\\"declClass\\\":{\\\"begin\\\":\\\"\\\\\\\\bclass\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"entity.name.class constant.numeric markup.underline\\\"}},\\\"end\\\":\\\";;|(?=[})\\\\\\\\]]|\\\\\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.tag\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#pragma\\\"},{\\\"begin\\\":\\\"(?<=(?:\\\\\\\\P{word}class|^class))(?!\\\\\\\\p{word})\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"entity.name.class constant.numeric markup.underline\\\"}},\\\"end\\\":\\\"\\\\\\\\btype\\\\\\\\b|(?=[})\\\\\\\\]]|\\\\\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|val)\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#bindClassTerm\\\"}]},{\\\"begin\\\":\\\"(?<=(?:\\\\\\\\P{word}type|^type))(?!\\\\\\\\p{word})\\\",\\\"end\\\":\\\"(?=;;|[})\\\\\\\\]]|\\\\\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\\\\\b)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#bindClassType\\\"}]}]},\\\"declException\\\":{\\\"begin\\\":\\\"\\\\\\\\bexception\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword markup.underline\\\"}},\\\"end\\\":\\\";;|(?=[})\\\\\\\\]]|\\\\\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.tag\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#attributeIdentifier\\\"},{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#pragma\\\"},{\\\"include\\\":\\\"#bindConstructor\\\"}]},\\\"declInclude\\\":{\\\"begin\\\":\\\"\\\\\\\\binclude\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp\\\"}},\\\"end\\\":\\\";;|(?=[})\\\\\\\\]]|\\\\\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.tag\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#attributeIdentifier\\\"},{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#pragma\\\"},{\\\"include\\\":\\\"#signature\\\"}]},\\\"declModule\\\":{\\\"begin\\\":\\\"(?<=(?:\\\\\\\\P{word}module|^module))(?!\\\\\\\\p{word})|\\\\\\\\bmodule\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.inserted constant.language support.constant.property-value entity.name.filename markup.underline\\\"}},\\\"end\\\":\\\";;|(?=[})\\\\\\\\]]|\\\\\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.tag\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#pragma\\\"},{\\\"begin\\\":\\\"(?<=(?:\\\\\\\\P{word}module|^module))(?!\\\\\\\\p{word})\\\",\\\"end\\\":\\\"(\\\\\\\\btype\\\\\\\\b)|(?=\\\\\\\\p{upper})\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#attributeIdentifier\\\"},{\\\"include\\\":\\\"#comment\\\"},{\\\"match\\\":\\\"\\\\\\\\brec\\\\\\\\b\\\",\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp\\\"}]},{\\\"begin\\\":\\\"(?<=(?:\\\\\\\\P{word}type|^type))(?!\\\\\\\\p{word})\\\",\\\"end\\\":\\\"(?=;;|[})\\\\\\\\]]|\\\\\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\\\\\b)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#bindSignature\\\"}]},{\\\"begin\\\":\\\"(?=\\\\\\\\p{upper})\\\",\\\"end\\\":\\\"(?=;;|[})\\\\\\\\]]|\\\\\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\\\\\b)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#bindStructure\\\"}]}]},\\\"declOpen\\\":{\\\"begin\\\":\\\"\\\\\\\\bopen\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp\\\"}},\\\"end\\\":\\\";;|(?=[})\\\\\\\\]]|\\\\\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.tag\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#attributeIdentifier\\\"},{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#pragma\\\"},{\\\"include\\\":\\\"#pathModuleExtended\\\"}]},\\\"declTerm\\\":{\\\"begin\\\":\\\"\\\\\\\\b(?:(external|val)|(method)|(let))\\\\\\\\b(!?)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.type markup.underline\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.type markup.underline\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.control markup.underline\\\"},\\\"4\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp\\\"}},\\\"end\\\":\\\";;|(?=[})\\\\\\\\]]|\\\\\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.tag\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#pragma\\\"},{\\\"include\\\":\\\"#bindTerm\\\"}]},\\\"declType\\\":{\\\"begin\\\":\\\"(?<=(?:\\\\\\\\P{word}type|^type))(?!\\\\\\\\p{word})|\\\\\\\\btype\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword markup.underline\\\"}},\\\"end\\\":\\\";;|(?=[})\\\\\\\\]]|\\\\\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.tag\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#pragma\\\"},{\\\"include\\\":\\\"#bindType\\\"}]},\\\"extension\\\":{\\\"begin\\\":\\\"(\\\\\\\\[)((?<![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])%{1,3}(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.language constant.numeric entity.other.attribute-name.id.css strong\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp\\\"}},\\\"end\\\":\\\"]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"constant.language constant.numeric entity.other.attribute-name.id.css strong\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#attributePayload\\\"}]},\\\"literal\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#termConstructor\\\"},{\\\"include\\\":\\\"#literalArray\\\"},{\\\"include\\\":\\\"#literalBoolean\\\"},{\\\"include\\\":\\\"#literalCharacter\\\"},{\\\"include\\\":\\\"#literalList\\\"},{\\\"include\\\":\\\"#literalNumber\\\"},{\\\"include\\\":\\\"#literalObjectTerm\\\"},{\\\"include\\\":\\\"#literalString\\\"},{\\\"include\\\":\\\"#literalRecord\\\"},{\\\"include\\\":\\\"#literalUnit\\\"}]},\\\"literalArray\\\":{\\\"begin\\\":\\\"\\\\\\\\[\\\\\\\\|\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"constant.language constant.numeric entity.other.attribute-name.id.css strong\\\"}},\\\"end\\\":\\\"\\\\\\\\|]\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#term\\\"}]},\\\"literalBoolean\\\":{\\\"match\\\":\\\"\\\\\\\\bfalse|true\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language constant.numeric entity.other.attribute-name.id.css strong\\\"},\\\"literalCharacter\\\":{\\\"begin\\\":\\\"(?<!\\\\\\\\p{word})'\\\",\\\"end\\\":\\\"'\\\",\\\"name\\\":\\\"markup.punctuation.quote.beginning\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#literalCharacterEscape\\\"}]},\\\"literalCharacterEscape\\\":{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\(?:[\\\\\\\\\\\\\\\\\\\\\\\"'ntbr]|\\\\\\\\d\\\\\\\\d\\\\\\\\d|x\\\\\\\\h\\\\\\\\h|o[0-3][0-7][0-7])\\\"},\\\"literalClassType\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"begin\\\":\\\"\\\\\\\\bobject\\\\\\\\b\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.tag emphasis\\\"}},\\\"end\\\":\\\"\\\\\\\\bend\\\\\\\\b\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\binherit\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp\\\"}},\\\"end\\\":\\\";;|(?=[})\\\\\\\\]]|\\\\\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.tag\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\bas\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp\\\"}},\\\"end\\\":\\\";;|(?=[})\\\\\\\\]]|\\\\\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\\\\\b)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#variablePattern\\\"}]},{\\\"include\\\":\\\"#type\\\"}]},{\\\"include\\\":\\\"#pattern\\\"},{\\\"include\\\":\\\"#declTerm\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\[\\\",\\\"end\\\":\\\"]\\\"}]},\\\"literalList\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\[\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"constant.language constant.numeric entity.other.attribute-name.id.css strong\\\"}},\\\"end\\\":\\\"]\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#term\\\"}]}]},\\\"literalNumber\\\":{\\\"match\\\":\\\"(?<!\\\\\\\\p{alpha})\\\\\\\\d\\\\\\\\d*(\\\\\\\\.\\\\\\\\d\\\\\\\\d*)?\\\",\\\"name\\\":\\\"constant.numeric\\\"},\\\"literalObjectTerm\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"begin\\\":\\\"\\\\\\\\bobject\\\\\\\\b\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.tag emphasis\\\"}},\\\"end\\\":\\\"\\\\\\\\bend\\\\\\\\b\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\binherit\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp\\\"}},\\\"end\\\":\\\";;|(?=[})\\\\\\\\]]|\\\\\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.tag\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\bas\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp\\\"}},\\\"end\\\":\\\";;|(?=[})\\\\\\\\]]|\\\\\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\\\\\b)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#variablePattern\\\"}]},{\\\"include\\\":\\\"#term\\\"}]},{\\\"include\\\":\\\"#pattern\\\"},{\\\"include\\\":\\\"#declTerm\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\[\\\",\\\"end\\\":\\\"]\\\"}]},\\\"literalRecord\\\":{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"constant.language constant.numeric entity.other.attribute-name.id.css strong strong\\\"}},\\\"end\\\":\\\"}\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=[{;])\\\",\\\"end\\\":\\\"(:)|(=)|(;)|(with)|(?=})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp strong\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.type strong\\\"},\\\"3\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp\\\"},\\\"4\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#pathModulePrefixSimple\\\"},{\\\"match\\\":\\\"(?!\\\\\\\\b(?:and|'|as|asr|assert|\\\\\\\\*|begin|class|[:,@]|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\\\\\.|fun|function|functor|[>-]|if|in|include|inherit|initializer|land|lazy|[{(\\\\\\\\[<]|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|[%+]|private|[?\\\\\\\"]|rec|[\\\\\\\\\\\\\\\\})\\\\\\\\];]|sig|/|struct|then|~|to|true|try|type|val|\\\\\\\\||virtual|when|while|with)\\\\\\\\b(?:[^']|$))\\\\\\\\b(?=[[:lower:]_])[[:alpha:]_][[:word:]']*\\\",\\\"name\\\":\\\"markup.inserted constant.language support.constant.property-value entity.name.filename emphasis\\\"}]},{\\\"begin\\\":\\\"(?<=(?:\\\\\\\\P{word}with|^with))(?!\\\\\\\\p{word})\\\",\\\"end\\\":\\\"(:)|(=)|(;)|(?=})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp strong\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.type strong\\\"},\\\"3\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp\\\"}},\\\"patterns\\\":[{\\\"match\\\":\\\"(?!\\\\\\\\b(?:and|'|as|asr|assert|\\\\\\\\*|begin|class|[:,@]|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\\\\\.|fun|function|functor|[>-]|if|in|include|inherit|initializer|land|lazy|[{(\\\\\\\\[<]|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|[%+]|private|[?\\\\\\\"]|rec|[\\\\\\\\\\\\\\\\})\\\\\\\\];]|sig|/|struct|then|~|to|true|try|type|val|\\\\\\\\||virtual|when|while|with)\\\\\\\\b(?:[^']|$))\\\\\\\\b(?=[[:lower:]_])[[:alpha:]_][[:word:]']*\\\",\\\"name\\\":\\\"markup.inserted constant.language support.constant.property-value entity.name.filename emphasis\\\"}]},{\\\"begin\\\":\\\"(?<=(?:[^#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]:|^:))(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\",\\\"end\\\":\\\"(;)|(=)|(?=})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.type strong\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#type\\\"}]},{\\\"begin\\\":\\\"(?<=(?:[^#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]=|^=))(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\",\\\"end\\\":\\\";|(?=})\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#term\\\"}]}]},\\\"literalString\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"end\\\":\\\"\\\\\\\"\\\",\\\"name\\\":\\\"string beginning.punctuation.definition.quote.markdown\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#literalStringEscape\\\"}]},{\\\"begin\\\":\\\"(\\\\\\\\{)([_[:lower:]]*?)(\\\\\\\\|)\\\",\\\"end\\\":\\\"(\\\\\\\\|)(\\\\\\\\2)(})\\\",\\\"name\\\":\\\"string beginning.punctuation.definition.quote.markdown\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#literalStringEscape\\\"}]}]},\\\"literalStringEscape\\\":{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\(?:[\\\\\\\\\\\\\\\\\\\\\\\"ntbr]|\\\\\\\\d\\\\\\\\d\\\\\\\\d|x\\\\\\\\h\\\\\\\\h|o[0-3][0-7][0-7])\\\"},\\\"literalUnit\\\":{\\\"match\\\":\\\"\\\\\\\\(\\\\\\\\)\\\",\\\"name\\\":\\\"constant.language constant.numeric entity.other.attribute-name.id.css strong\\\"},\\\"pathModuleExtended\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#pathModulePrefixExtended\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?=\\\\\\\\p{upper})[[:alpha:]_][[:word:]']*\\\",\\\"name\\\":\\\"entity.name.class constant.numeric\\\"}]},\\\"pathModulePrefixExtended\\\":{\\\"begin\\\":\\\"\\\\\\\\b(?=\\\\\\\\p{upper})[[:alpha:]_][[:word:]']*(?=\\\\\\\\s*\\\\\\\\.|$|\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"entity.name.class constant.numeric\\\"}},\\\"end\\\":\\\"(?![\\\\\\\\s.]|$|\\\\\\\\()\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.control\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(\\\\\\\\b(?=\\\\\\\\p{upper})[[:alpha:]_][[:word:]']*(?=\\\\\\\\s*\\\\\\\\)))\\\",\\\"name\\\":\\\"string.other.link variable.language variable.parameter emphasis\\\"},{\\\"include\\\":\\\"#structure\\\"}]},{\\\"begin\\\":\\\"(?<![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\\\\\\.(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword strong\\\"}},\\\"end\\\":\\\"(\\\\\\\\b(?=\\\\\\\\p{upper})[[:alpha:]_][[:word:]']*(?=\\\\\\\\s*\\\\\\\\.|$))|(\\\\\\\\b(?=\\\\\\\\p{upper})[[:alpha:]_][[:word:]']*(?=\\\\\\\\s*(?:$|\\\\\\\\()))|(\\\\\\\\b(?=\\\\\\\\p{upper})[[:alpha:]_][[:word:]']*(?=\\\\\\\\s*\\\\\\\\)))|(?![\\\\\\\\s.[:upper:]]|$|\\\\\\\\()\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.class constant.numeric\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function strong\\\"},\\\"3\\\":{\\\"name\\\":\\\"string.other.link variable.language variable.parameter emphasis\\\"}}}]},\\\"pathModulePrefixExtendedParens\\\":{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.control\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(\\\\\\\\b(?=\\\\\\\\p{upper})[[:alpha:]_][[:word:]']*(?=\\\\\\\\s*\\\\\\\\)))\\\",\\\"name\\\":\\\"string.other.link variable.language variable.parameter emphasis\\\"},{\\\"include\\\":\\\"#structure\\\"}]},\\\"pathModulePrefixSimple\\\":{\\\"begin\\\":\\\"\\\\\\\\b(?=\\\\\\\\p{upper})[[:alpha:]_][[:word:]']*(?=\\\\\\\\s*\\\\\\\\.)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"entity.name.class constant.numeric\\\"}},\\\"end\\\":\\\"(?![\\\\\\\\s.])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"begin\\\":\\\"(?<![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\\\\\\.(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword strong\\\"}},\\\"end\\\":\\\"(\\\\\\\\b(?=\\\\\\\\p{upper})[[:alpha:]_][[:word:]']*(?=\\\\\\\\s*\\\\\\\\.))|(\\\\\\\\b(?=\\\\\\\\p{upper})[[:alpha:]_][[:word:]']*(?=\\\\\\\\s*))|(?![\\\\\\\\s.[:upper:]])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.class constant.numeric\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.language constant.numeric entity.other.attribute-name.id.css strong\\\"}}}]},\\\"pathModuleSimple\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#pathModulePrefixSimple\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?=\\\\\\\\p{upper})[[:alpha:]_][[:word:]']*\\\",\\\"name\\\":\\\"entity.name.class constant.numeric\\\"}]},\\\"pathRecord\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?!\\\\\\\\b(?:and|'|as|asr|assert|\\\\\\\\*|begin|class|[:,@]|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\\\\\.|fun|function|functor|[>-]|if|in|include|inherit|initializer|land|lazy|[{(\\\\\\\\[<]|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|[%+]|private|[?\\\\\\\"]|rec|[\\\\\\\\\\\\\\\\})\\\\\\\\];]|sig|/|struct|then|~|to|true|try|type|val|\\\\\\\\||virtual|when|while|with)\\\\\\\\b(?:[^']|$))\\\\\\\\b(?=[[:lower:]_])[[:alpha:]_][[:word:]']*\\\",\\\"end\\\":\\\"(?=[^\\\\\\\\s.])(?!\\\\\\\\(\\\\\\\\*)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"begin\\\":\\\"(?<=(?:[^#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]\\\\\\\\.|^\\\\\\\\.))(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])|(?<![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\\\\\\.(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword strong\\\"}},\\\"end\\\":\\\"((?<![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\\\\\\.(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]))|((?!\\\\\\\\b(?:and|'|as|asr|assert|\\\\\\\\*|begin|class|[:,@]|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\\\\\.|fun|function|functor|[>-]|if|in|include|inherit|initializer|land|lazy|[{(\\\\\\\\[<]|let|lor|lsl|lsr|lxor|match|method|mod|mutable|nonrec|#|object|of|open|or|[%+]|private|[?\\\\\\\"]|rec|[\\\\\\\\\\\\\\\\})\\\\\\\\];]|sig|/|struct|then|~|to|true|try|type|val|\\\\\\\\||virtual|when|while|with)\\\\\\\\b(?:[^']|$))\\\\\\\\b(?=[[:lower:]_])[[:alpha:]_][[:word:]']*)|(?<=\\\\\\\\))|(?<=])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword strong\\\"},\\\"2\\\":{\\\"name\\\":\\\"markup.inserted constant.language support.constant.property-value entity.name.filename\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#pathModulePrefixSimple\\\"},{\\\"begin\\\":\\\"\\\\\\\\((?!\\\\\\\\*)\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#term\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\[\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp\\\"}},\\\"end\\\":\\\"]\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#pattern\\\"}]}]}]}]},\\\"pattern\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#patternArray\\\"},{\\\"include\\\":\\\"#patternLazy\\\"},{\\\"include\\\":\\\"#patternList\\\"},{\\\"include\\\":\\\"#patternMisc\\\"},{\\\"include\\\":\\\"#patternModule\\\"},{\\\"include\\\":\\\"#patternRecord\\\"},{\\\"include\\\":\\\"#literal\\\"},{\\\"include\\\":\\\"#patternParens\\\"},{\\\"include\\\":\\\"#patternType\\\"},{\\\"include\\\":\\\"#variablePattern\\\"},{\\\"include\\\":\\\"#termOperator\\\"}]},\\\"patternArray\\\":{\\\"begin\\\":\\\"\\\\\\\\[\\\\\\\\|\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"constant.language constant.numeric entity.other.attribute-name.id.css strong\\\"}},\\\"end\\\":\\\"\\\\\\\\|]\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#pattern\\\"}]},\\\"patternLazy\\\":{\\\"match\\\":\\\"lazy\\\",\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp\\\"},\\\"patternList\\\":{\\\"begin\\\":\\\"\\\\\\\\[\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"constant.language constant.numeric entity.other.attribute-name.id.css strong\\\"}},\\\"end\\\":\\\"]\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#pattern\\\"}]},\\\"patternMisc\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"string.regexp strong\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp\\\"},\\\"3\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp\\\"}},\\\"match\\\":\\\"((?<![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]),(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]))|([#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]+)|\\\\\\\\b(as)\\\\\\\\b\\\"},\\\"patternModule\\\":{\\\"begin\\\":\\\"\\\\\\\\bmodule\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.inserted constant.language support.constant.property-value entity.name.filename\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#declModule\\\"}]},\\\"patternParens\\\":{\\\"begin\\\":\\\"\\\\\\\\((?!\\\\\\\\))\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.tag\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"begin\\\":\\\"(?<![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]):(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp strong\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type\\\"}]},{\\\"include\\\":\\\"#pattern\\\"}]},\\\"patternRecord\\\":{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"constant.language constant.numeric entity.other.attribute-name.id.css strong strong\\\"}},\\\"end\\\":\\\"}\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=[{;])\\\",\\\"end\\\":\\\"(:)|(=)|(;)|(with)|(?=})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp strong\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.type strong\\\"},\\\"3\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp\\\"},\\\"4\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#pathModulePrefixSimple\\\"},{\\\"match\\\":\\\"(?!\\\\\\\\b(?:and|'|as|asr|assert|\\\\\\\\*|begin|class|[:,@]|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\\\\\.|fun|function|functor|[>-]|if|in|include|inherit|initializer|land|lazy|[{(\\\\\\\\[<]|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|[%+]|private|[?\\\\\\\"]|rec|[\\\\\\\\\\\\\\\\})\\\\\\\\];]|sig|/|struct|then|~|to|true|try|type|val|\\\\\\\\||virtual|when|while|with)\\\\\\\\b(?:[^']|$))\\\\\\\\b(?=[[:lower:]_])[[:alpha:]_][[:word:]']*\\\",\\\"name\\\":\\\"markup.inserted constant.language support.constant.property-value entity.name.filename emphasis\\\"}]},{\\\"begin\\\":\\\"(?<=(?:\\\\\\\\P{word}with|^with))(?!\\\\\\\\p{word})\\\",\\\"end\\\":\\\"(:)|(=)|(;)|(?=})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp strong\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.type strong\\\"},\\\"3\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp\\\"}},\\\"patterns\\\":[{\\\"match\\\":\\\"(?!\\\\\\\\b(?:and|'|as|asr|assert|\\\\\\\\*|begin|class|[:,@]|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\\\\\.|fun|function|functor|[>-]|if|in|include|inherit|initializer|land|lazy|[{(\\\\\\\\[<]|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|[%+]|private|[?\\\\\\\"]|rec|[\\\\\\\\\\\\\\\\})\\\\\\\\];]|sig|/|struct|then|~|to|true|try|type|val|\\\\\\\\||virtual|when|while|with)\\\\\\\\b(?:[^']|$))\\\\\\\\b(?=[[:lower:]_])[[:alpha:]_][[:word:]']*\\\",\\\"name\\\":\\\"markup.inserted constant.language support.constant.property-value entity.name.filename emphasis\\\"}]},{\\\"begin\\\":\\\"(?<=(?:[^#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]:|^:))(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\",\\\"end\\\":\\\"(;)|(=)|(?=})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.type strong\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#type\\\"}]},{\\\"begin\\\":\\\"(?<=(?:[^#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]=|^=))(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\",\\\"end\\\":\\\";|(?=})\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#pattern\\\"}]}]},\\\"patternType\\\":{\\\"begin\\\":\\\"\\\\\\\\btype\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#declType\\\"}]},\\\"pragma\\\":{\\\"begin\\\":\\\"(?<![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])#(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.tag\\\"}},\\\"end\\\":\\\"(?=;;|[})\\\\\\\\]]|\\\\\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\\\\\b)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#literalNumber\\\"},{\\\"include\\\":\\\"#literalString\\\"}]},\\\"signature\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#signatureLiteral\\\"},{\\\"include\\\":\\\"#signatureFunctor\\\"},{\\\"include\\\":\\\"#pathModuleExtended\\\"},{\\\"include\\\":\\\"#signatureParens\\\"},{\\\"include\\\":\\\"#signatureRecovered\\\"},{\\\"include\\\":\\\"#signatureConstraints\\\"}]},\\\"signatureConstraints\\\":{\\\"begin\\\":\\\"\\\\\\\\bwith\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp markup.underline\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))|(?=;;|[})\\\\\\\\]]|\\\\\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\\\\\b)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=(?:\\\\\\\\P{word}with|^with))(?!\\\\\\\\p{word})\\\",\\\"end\\\":\\\"\\\\\\\\b(?:(module)|(type))\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"markup.inserted constant.language support.constant.property-value entity.name.filename\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword\\\"}}},{\\\"include\\\":\\\"#declModule\\\"},{\\\"include\\\":\\\"#declType\\\"}]},\\\"signatureFunctor\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\bfunctor\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword\\\"}},\\\"end\\\":\\\"(?=;;|[})\\\\\\\\]]|\\\\\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\\\\\b)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=(?:\\\\\\\\P{word}functor|^functor))(?!\\\\\\\\p{word})\\\",\\\"end\\\":\\\"(\\\\\\\\(\\\\\\\\))|(\\\\\\\\((?!\\\\\\\\)))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.language constant.numeric entity.other.attribute-name.id.css strong\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.tag\\\"}}},{\\\"begin\\\":\\\"(?<=\\\\\\\\()\\\",\\\"end\\\":\\\"(:)|(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp strong\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.tag\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#variableModule\\\"}]},{\\\"begin\\\":\\\"(?<=(?:[^#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]:|^:))(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\",\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.tag\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#signature\\\"}]},{\\\"begin\\\":\\\"(?<=\\\\\\\\))\\\",\\\"end\\\":\\\"(\\\\\\\\()|((?<![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])->(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.tag\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.type strong\\\"}}},{\\\"begin\\\":\\\"(?<=(?:[^#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]->|^->))(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\",\\\"end\\\":\\\"(?=;;|[})\\\\\\\\]]|\\\\\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\\\\\b)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#signature\\\"}]}]},{\\\"match\\\":\\\"(?<![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])->(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\",\\\"name\\\":\\\"support.type strong\\\"}]},\\\"signatureLiteral\\\":{\\\"begin\\\":\\\"\\\\\\\\bsig\\\\\\\\b\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.tag emphasis\\\"}},\\\"end\\\":\\\"\\\\\\\\bend\\\\\\\\b\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#pragma\\\"},{\\\"include\\\":\\\"#decl\\\"}]},\\\"signatureParens\\\":{\\\"begin\\\":\\\"\\\\\\\\((?!\\\\\\\\))\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.tag\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"begin\\\":\\\"(?<![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]):(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp strong\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#signature\\\"}]},{\\\"include\\\":\\\"#signature\\\"}]},\\\"signatureRecovered\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(|(?<=(?:[^#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]:|^:|[^#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]->|^->))(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])|(?<=(?:\\\\\\\\P{word}|^)(?:include|open))(?!\\\\\\\\p{word})\\\",\\\"end\\\":\\\"\\\\\\\\bmodule\\\\\\\\b|(?!$|\\\\\\\\s|\\\\\\\\bmodule\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.inserted constant.language support.constant.property-value entity.name.filename\\\"}}},{\\\"begin\\\":\\\"(?<=(?:\\\\\\\\P{word}module|^module))(?!\\\\\\\\p{word})\\\",\\\"end\\\":\\\"(?=;;|[})\\\\\\\\]]|\\\\\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\\\\\b)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=(?:\\\\\\\\P{word}module|^module))(?!\\\\\\\\p{word})\\\",\\\"end\\\":\\\"\\\\\\\\btype\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword\\\"}}},{\\\"begin\\\":\\\"(?<=(?:\\\\\\\\P{word}type|^type))(?!\\\\\\\\p{word})\\\",\\\"end\\\":\\\"\\\\\\\\bof\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.tag\\\"}}},{\\\"begin\\\":\\\"(?<=(?:\\\\\\\\P{word}of|^of))(?!\\\\\\\\p{word})\\\",\\\"end\\\":\\\"(?=;;|[})\\\\\\\\]]|\\\\\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\\\\\b)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#signature\\\"}]}]}]},\\\"structure\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#structureLiteral\\\"},{\\\"include\\\":\\\"#structureFunctor\\\"},{\\\"include\\\":\\\"#pathModuleExtended\\\"},{\\\"include\\\":\\\"#structureParens\\\"}]},\\\"structureFunctor\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\bfunctor\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword\\\"}},\\\"end\\\":\\\"(?=;;|[})\\\\\\\\]]|\\\\\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\\\\\b)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=(?:\\\\\\\\P{word}functor|^functor))(?!\\\\\\\\p{word})\\\",\\\"end\\\":\\\"(\\\\\\\\(\\\\\\\\))|(\\\\\\\\((?!\\\\\\\\)))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.language constant.numeric entity.other.attribute-name.id.css strong\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.tag\\\"}}},{\\\"begin\\\":\\\"(?<=\\\\\\\\()\\\",\\\"end\\\":\\\"(:)|(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp strong\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.tag\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#variableModule\\\"}]},{\\\"begin\\\":\\\"(?<=(?:[^#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]:|^:))(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\",\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.tag\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#signature\\\"}]},{\\\"begin\\\":\\\"(?<=\\\\\\\\))\\\",\\\"end\\\":\\\"(\\\\\\\\()|((?<![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])->(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.tag\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.type strong\\\"}}},{\\\"begin\\\":\\\"(?<=(?:[^#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]->|^->))(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\",\\\"end\\\":\\\"(?=;;|[})\\\\\\\\]]|\\\\\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\\\\\b)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#structure\\\"}]}]},{\\\"match\\\":\\\"(?<![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])->(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\",\\\"name\\\":\\\"support.type strong\\\"}]},\\\"structureLiteral\\\":{\\\"begin\\\":\\\"\\\\\\\\bstruct\\\\\\\\b\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.tag emphasis\\\"}},\\\"end\\\":\\\"\\\\\\\\bend\\\\\\\\b\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#pragma\\\"},{\\\"include\\\":\\\"#decl\\\"}]},\\\"structureParens\\\":{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.tag\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#structureUnpack\\\"},{\\\"include\\\":\\\"#structure\\\"}]},\\\"structureUnpack\\\":{\\\"begin\\\":\\\"\\\\\\\\bval\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\"},\\\"term\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#termLet\\\"},{\\\"include\\\":\\\"#termAtomic\\\"}]},\\\"termAtomic\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#termConditional\\\"},{\\\"include\\\":\\\"#termConstructor\\\"},{\\\"include\\\":\\\"#termDelim\\\"},{\\\"include\\\":\\\"#termFor\\\"},{\\\"include\\\":\\\"#termFunction\\\"},{\\\"include\\\":\\\"#literal\\\"},{\\\"include\\\":\\\"#termMatch\\\"},{\\\"include\\\":\\\"#termMatchRule\\\"},{\\\"include\\\":\\\"#termPun\\\"},{\\\"include\\\":\\\"#termOperator\\\"},{\\\"include\\\":\\\"#termTry\\\"},{\\\"include\\\":\\\"#termWhile\\\"},{\\\"include\\\":\\\"#pathRecord\\\"}]},\\\"termConditional\\\":{\\\"match\\\":\\\"\\\\\\\\b(?:if|then|else)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control\\\"},\\\"termConstructor\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#pathModulePrefixSimple\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?=\\\\\\\\p{upper})[[:alpha:]_][[:word:]']*\\\",\\\"name\\\":\\\"constant.language constant.numeric entity.other.attribute-name.id.css strong\\\"}]},\\\"termDelim\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\((?!\\\\\\\\))\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.tag\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#term\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\bbegin\\\\\\\\b\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.tag\\\"}},\\\"end\\\":\\\"\\\\\\\\bend\\\\\\\\b\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#attributeIdentifier\\\"},{\\\"include\\\":\\\"#term\\\"}]}]},\\\"termFor\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\bfor\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.control\\\"}},\\\"end\\\":\\\"\\\\\\\\bdone\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.control\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=(?:\\\\\\\\P{word}for|^for))(?!\\\\\\\\p{word})\\\",\\\"end\\\":\\\"(?<![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])=(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.type strong\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#pattern\\\"}]},{\\\"begin\\\":\\\"(?<=(?:[^#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]=|^=))(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\",\\\"end\\\":\\\"\\\\\\\\b(?:downto|to)\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.control\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#term\\\"}]},{\\\"begin\\\":\\\"(?<=(?:\\\\\\\\P{word}to|^to))(?!\\\\\\\\p{word})\\\",\\\"end\\\":\\\"\\\\\\\\bdo\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.control\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#term\\\"}]},{\\\"begin\\\":\\\"(?<=(?:\\\\\\\\P{word}do|^do))(?!\\\\\\\\p{word})\\\",\\\"end\\\":\\\"(?=\\\\\\\\bdone\\\\\\\\b)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#term\\\"}]}]}]},\\\"termFunction\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.type\\\"}},\\\"match\\\":\\\"\\\\\\\\b(?:(fun)|(function))\\\\\\\\b\\\"},\\\"termLet\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?:(?<=(?:[^#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]=|^=|[^#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]->|^->))(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])|(?<=[;(]))(?=\\\\\\\\s|\\\\\\\\blet\\\\\\\\b)|(?<=(?:\\\\\\\\P{word}|^)(?:begin|do|else|in|struct|then|try))(?!\\\\\\\\p{word})|(?<=(?:[^#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]@@|^@@))(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\\\\\\s+\\\",\\\"end\\\":\\\"\\\\\\\\b(?:(and)|(let))\\\\\\\\b|(?=\\\\\\\\S)(?!\\\\\\\\(\\\\\\\\*)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp markup.underline\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.type markup.underline\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"}]},{\\\"begin\\\":\\\"(?<=(?:\\\\\\\\P{word}|^)(?:and|let))(?!\\\\\\\\p{word})|(let)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type markup.underline\\\"}},\\\"end\\\":\\\"\\\\\\\\b(?:(and)|(in))\\\\\\\\b|(?=[})\\\\\\\\]]|\\\\\\\\b(?:end|class|exception|external|include|inherit|initializer|let|method|module|open|type|val)\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp markup.underline\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.type markup.underline\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#bindTerm\\\"}]}]},\\\"termMatch\\\":{\\\"begin\\\":\\\"\\\\\\\\bmatch\\\\\\\\b\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.control\\\"}},\\\"end\\\":\\\"\\\\\\\\bwith\\\\\\\\b\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#term\\\"}]},\\\"termMatchRule\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=(?:\\\\\\\\P{word}|^)(?:fun|function|with))(?!\\\\\\\\p{word})\\\",\\\"end\\\":\\\"(?<![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])(\\\\\\\\|)|(->)(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.type strong\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.type strong\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#attributeIdentifier\\\"},{\\\"include\\\":\\\"#pattern\\\"}]},{\\\"begin\\\":\\\"(?<=(?:[^\\\\\\\\[#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]\\\\\\\\||^\\\\\\\\|))(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])|(?<![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\\\\\\|(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.type strong\\\"}},\\\"end\\\":\\\"(?<![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])(\\\\\\\\|)|(->)(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.type strong\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.type strong\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#pattern\\\"},{\\\"begin\\\":\\\"\\\\\\\\bwhen\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp\\\"}},\\\"end\\\":\\\"(?=(?<![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])->(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#term\\\"}]}]}]},\\\"termOperator\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])#(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword\\\"}},\\\"end\\\":\\\"(?!\\\\\\\\b(?:and|'|as|asr|assert|\\\\\\\\*|begin|class|[:,@]|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\\\\\.|fun|function|functor|[>-]|if|in|include|inherit|initializer|land|lazy|[{(\\\\\\\\[<]|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|[%+]|private|[?\\\\\\\"]|rec|[\\\\\\\\\\\\\\\\})\\\\\\\\];]|sig|/|struct|then|~|to|true|try|type|val|\\\\\\\\||virtual|when|while|with)\\\\\\\\b(?:[^']|$))\\\\\\\\b(?=[[:lower:]_])[[:alpha:]_][[:word:]']*\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"entity.name.function\\\"}}},{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.control strong\\\"}},\\\"match\\\":\\\"<-\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp\\\"}},\\\"match\\\":\\\"(,|[#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]+)|(;)\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?:and|assert|asr|land|lazy|lsr|lxor|mod|new|or)\\\\\\\\b\\\",\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp\\\"}]},\\\"termPun\\\":{\\\"applyEndPatternLast\\\":true,\\\"begin\\\":\\\"(?<![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\\\\\\?|~(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp\\\"}},\\\"end\\\":\\\":|(?=[^\\\\\\\\s:])\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=(?:[^#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]\\\\\\\\?|^\\\\\\\\?|[^#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]~|^~))(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\",\\\"end\\\":\\\"(?!\\\\\\\\b(?:and|'|as|asr|assert|\\\\\\\\*|begin|class|[:,@]|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\\\\\.|fun|function|functor|[>-]|if|in|include|inherit|initializer|land|lazy|[{(\\\\\\\\[<]|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|[%+]|private|[?\\\\\\\"]|rec|[\\\\\\\\\\\\\\\\})\\\\\\\\];]|sig|/|struct|then|~|to|true|try|type|val|\\\\\\\\||virtual|when|while|with)\\\\\\\\b(?:[^']|$))\\\\\\\\b(?=[[:lower:]_])[[:alpha:]_][[:word:]']*\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.inserted constant.language support.constant.property-value entity.name.filename\\\"}}}]},\\\"termTry\\\":{\\\"begin\\\":\\\"\\\\\\\\btry\\\\\\\\b\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.control\\\"}},\\\"end\\\":\\\"\\\\\\\\bwith\\\\\\\\b\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#term\\\"}]},\\\"termWhile\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\bwhile\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.control\\\"}},\\\"end\\\":\\\"\\\\\\\\bdone\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.control\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=(?:\\\\\\\\P{word}while|^while))(?!\\\\\\\\p{word})\\\",\\\"end\\\":\\\"\\\\\\\\bdo\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.control\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#term\\\"}]},{\\\"begin\\\":\\\"(?<=(?:\\\\\\\\P{word}do|^do))(?!\\\\\\\\p{word})\\\",\\\"end\\\":\\\"(?=\\\\\\\\bdone\\\\\\\\b)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#term\\\"}]}]}]},\\\"type\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"match\\\":\\\"\\\\\\\\bnonrec\\\\\\\\b\\\",\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp\\\"},{\\\"include\\\":\\\"#pathModulePrefixExtended\\\"},{\\\"include\\\":\\\"#typeLabel\\\"},{\\\"include\\\":\\\"#typeObject\\\"},{\\\"include\\\":\\\"#typeOperator\\\"},{\\\"include\\\":\\\"#typeParens\\\"},{\\\"include\\\":\\\"#typePolymorphicVariant\\\"},{\\\"include\\\":\\\"#typeRecord\\\"},{\\\"include\\\":\\\"#typeConstructor\\\"}]},\\\"typeConstructor\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(_)|((?!\\\\\\\\b(?:and|'|as|asr|assert|\\\\\\\\*|begin|class|[:,@]|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\\\\\.|fun|function|functor|[>-]|if|in|include|inherit|initializer|land|lazy|[{(\\\\\\\\[<]|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|[%+]|private|[?\\\\\\\"]|rec|[\\\\\\\\\\\\\\\\})\\\\\\\\];]|sig|/|struct|then|~|to|true|try|type|val|\\\\\\\\||virtual|when|while|with)\\\\\\\\b(?:[^']|$))\\\\\\\\b(?=[[:lower:]_])[[:alpha:]_][[:word:]']*)|(')((?!\\\\\\\\b(?:and|'|as|asr|assert|\\\\\\\\*|begin|class|[:,@]|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\\\\\.|fun|function|functor|[>-]|if|in|include|inherit|initializer|land|lazy|[{(\\\\\\\\[<]|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|[%+]|private|[?\\\\\\\"]|rec|[\\\\\\\\\\\\\\\\})\\\\\\\\];]|sig|/|struct|then|~|to|true|try|type|val|\\\\\\\\||virtual|when|while|with)\\\\\\\\b(?:[^']|$))\\\\\\\\b(?=[[:lower:]_])[[:alpha:]_][[:word:]']*)|(?<=[^*]\\\\\\\\)|])\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"comment constant.regexp meta.separator.markdown\\\"},\\\"3\\\":{\\\"name\\\":\\\"string.other.link variable.language variable.parameter emphasis strong emphasis\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.control emphasis\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\((?!\\\\\\\\*)|[*:,=.>\\\\\\\\-{\\\\\\\\[+})\\\\\\\\];|])|((?!\\\\\\\\b(?:and|'|as|asr|assert|\\\\\\\\*|begin|class|[:,@]|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\\\\\.|fun|function|functor|[>-]|if|in|include|inherit|initializer|land|lazy|[{(\\\\\\\\[<]|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|[%+]|private|[?\\\\\\\"]|rec|[\\\\\\\\\\\\\\\\})\\\\\\\\];]|sig|/|struct|then|~|to|true|try|type|val|\\\\\\\\||virtual|when|while|with)\\\\\\\\b(?:[^']|$))\\\\\\\\b(?=[[:lower:]_])[[:alpha:]_][[:word:]']*)[:space]*(?!\\\\\\\\(\\\\\\\\*|\\\\\\\\p{word})|(?=;;|[})\\\\\\\\]]|\\\\\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function strong\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#pathModulePrefixExtended\\\"}]}]},\\\"typeLabel\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(\\\\\\\\??)((?!\\\\\\\\b(?:and|'|as|asr|assert|\\\\\\\\*|begin|class|[:,@]|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\\\\\.|fun|function|functor|[>-]|if|in|include|inherit|initializer|land|lazy|[{(\\\\\\\\[<]|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|[%+]|private|[?\\\\\\\"]|rec|[\\\\\\\\\\\\\\\\})\\\\\\\\];]|sig|/|struct|then|~|to|true|try|type|val|\\\\\\\\||virtual|when|while|with)\\\\\\\\b(?:[^']|$))\\\\\\\\b(?=[[:lower:]_])[[:alpha:]_][[:word:]']*)\\\\\\\\s*((?<![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]):(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]))\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword strong emphasis\\\"},\\\"2\\\":{\\\"name\\\":\\\"markup.inserted constant.language support.constant.property-value entity.name.filename emphasis\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword\\\"}},\\\"end\\\":\\\"(?=(?<![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])->(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type\\\"}]}]},\\\"typeModule\\\":{\\\"begin\\\":\\\"\\\\\\\\bmodule\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.inserted constant.language support.constant.property-value entity.name.filename\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#pathModuleExtended\\\"},{\\\"include\\\":\\\"#signatureConstraints\\\"}]},\\\"typeObject\\\":{\\\"begin\\\":\\\"<\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"constant.language constant.numeric entity.other.attribute-name.id.css strong strong\\\"}},\\\"end\\\":\\\">\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=[<;])\\\",\\\"end\\\":\\\"(:)|(?=>)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp strong\\\"},\\\"3\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp\\\"},\\\"4\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#pathModulePrefixSimple\\\"},{\\\"match\\\":\\\"(?!\\\\\\\\b(?:and|'|as|asr|assert|\\\\\\\\*|begin|class|[:,@]|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\\\\\.|fun|function|functor|[>-]|if|in|include|inherit|initializer|land|lazy|[{(\\\\\\\\[<]|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|[%+]|private|[?\\\\\\\"]|rec|[\\\\\\\\\\\\\\\\})\\\\\\\\];]|sig|/|struct|then|~|to|true|try|type|val|\\\\\\\\||virtual|when|while|with)\\\\\\\\b(?:[^']|$))\\\\\\\\b(?=[[:lower:]_])[[:alpha:]_][[:word:]']*\\\",\\\"name\\\":\\\"markup.inserted constant.language support.constant.property-value entity.name.filename emphasis\\\"}]},{\\\"begin\\\":\\\"(?<=(?:[^#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]:|^:))(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\",\\\"end\\\":\\\"(;)|(?=>)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.type strong\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#type\\\"}]}]},\\\"typeOperator\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"[,;]|[#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]+\\\",\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp strong\\\"}]},\\\"typeParens\\\":{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.tag\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"patterns\\\":[{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp\\\"},{\\\"include\\\":\\\"#typeModule\\\"},{\\\"include\\\":\\\"#type\\\"}]},\\\"typePolymorphicVariant\\\":{\\\"begin\\\":\\\"\\\\\\\\[\\\",\\\"end\\\":\\\"]\\\",\\\"patterns\\\":[]},\\\"typeRecord\\\":{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"constant.language constant.numeric entity.other.attribute-name.id.css strong strong\\\"}},\\\"end\\\":\\\"}\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=[{;])\\\",\\\"end\\\":\\\"(:)|(=)|(;)|(with)|(?=})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp strong\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.type strong\\\"},\\\"3\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp\\\"},\\\"4\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#pathModulePrefixSimple\\\"},{\\\"match\\\":\\\"(?!\\\\\\\\b(?:and|'|as|asr|assert|\\\\\\\\*|begin|class|[:,@]|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\\\\\.|fun|function|functor|[>-]|if|in|include|inherit|initializer|land|lazy|[{(\\\\\\\\[<]|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|[%+]|private|[?\\\\\\\"]|rec|[\\\\\\\\\\\\\\\\})\\\\\\\\];]|sig|/|struct|then|~|to|true|try|type|val|\\\\\\\\||virtual|when|while|with)\\\\\\\\b(?:[^']|$))\\\\\\\\b(?=[[:lower:]_])[[:alpha:]_][[:word:]']*\\\",\\\"name\\\":\\\"markup.inserted constant.language support.constant.property-value entity.name.filename emphasis\\\"}]},{\\\"begin\\\":\\\"(?<=(?:\\\\\\\\P{word}with|^with))(?!\\\\\\\\p{word})\\\",\\\"end\\\":\\\"(:)|(=)|(;)|(?=})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp strong\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.type strong\\\"},\\\"3\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp\\\"}},\\\"patterns\\\":[{\\\"match\\\":\\\"(?!\\\\\\\\b(?:and|'|as|asr|assert|\\\\\\\\*|begin|class|[:,@]|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\\\\\.|fun|function|functor|[>-]|if|in|include|inherit|initializer|land|lazy|[{(\\\\\\\\[<]|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|[%+]|private|[?\\\\\\\"]|rec|[\\\\\\\\\\\\\\\\})\\\\\\\\];]|sig|/|struct|then|~|to|true|try|type|val|\\\\\\\\||virtual|when|while|with)\\\\\\\\b(?:[^']|$))\\\\\\\\b(?=[[:lower:]_])[[:alpha:]_][[:word:]']*\\\",\\\"name\\\":\\\"markup.inserted constant.language support.constant.property-value entity.name.filename emphasis\\\"}]},{\\\"begin\\\":\\\"(?<=(?:[^#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]:|^:))(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\",\\\"end\\\":\\\"(;)|(=)|(?=})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.type strong\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#type\\\"}]},{\\\"begin\\\":\\\"(?<=(?:[^#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$]=|^=))(?![#\\\\\\\\-:!?.@*/\\\\\\\\&%^+<=>|~$])\\\",\\\"end\\\":\\\";|(?=})\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"variable.other.class.js message.error variable.interpolation string.regexp\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#type\\\"}]}]},\\\"variableModule\\\":{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"string.other.link variable.language variable.parameter emphasis\\\"}},\\\"match\\\":\\\"\\\\\\\\b(?=\\\\\\\\p{upper})[[:alpha:]_][[:word:]']*\\\"},\\\"variablePattern\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"comment constant.regexp meta.separator.markdown\\\"},\\\"2\\\":{\\\"name\\\":\\\"string.other.link variable.language variable.parameter emphasis\\\"}},\\\"match\\\":\\\"(\\\\\\\\b_\\\\\\\\b)|((?!\\\\\\\\b(?:and|'|as|asr|assert|\\\\\\\\*|begin|class|[:,@]|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\\\\\.|fun|function|functor|[>-]|if|in|include|inherit|initializer|land|lazy|[{(\\\\\\\\[<]|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|[%+]|private|[?\\\\\\\"]|rec|[\\\\\\\\\\\\\\\\})\\\\\\\\];]|sig|/|struct|then|~|to|true|try|type|val|\\\\\\\\||virtual|when|while|with)\\\\\\\\b(?:[^']|$))\\\\\\\\b(?=[[:lower:]_])[[:alpha:]_][[:word:]']*)\\\"}},\\\"scopeName\\\":\\\"source.ocaml\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ac2hpa2lqcy9sYW5ncy9kaXN0L29jYW1sLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsd0NBQXdDLDZGQUE2Rix5QkFBeUIsRUFBRSx3QkFBd0IsRUFBRSxzQkFBc0Isa0JBQWtCLGVBQWUsOERBQThELElBQUksd0RBQXdELE9BQU8sMEZBQTBGLFFBQVEseUZBQXlGLGdDQUFnQyxPQUFPLDJGQUEyRixnQkFBZ0Isa0NBQWtDLEVBQUUsMEJBQTBCLGNBQWMsT0FBTywrRkFBK0YsUUFBUSx5Q0FBeUMsMlJBQTJSLGdJQUFnSSxPQUFPLDBJQUEwSSx1QkFBdUIsZUFBZSxtTkFBbU4sT0FBTyx5RkFBeUYsZ0JBQWdCLG9DQUFvQyxFQUFFLDRCQUE0QixFQUFFLEVBQUUsNEhBQTRILDJCQUEyQixFQUFFLHNCQUFzQixFQUFFLEVBQUUsb0lBQW9JLHFKQUFxSixTQUFTLGdCQUFnQix5QkFBeUIsRUFBRSxFQUFFLHlCQUF5QixLQUFLLHFCQUFxQixLQUFLLHFDQUFxQyxzQkFBc0IsRUFBRSxFQUFFLEVBQUUsc0JBQXNCLEVBQUUsb0JBQW9CLGVBQWUseUJBQXlCLEtBQUssK0JBQStCLEtBQUssOEZBQThGLEdBQUcsMElBQTBJLE9BQU8sK0ZBQStGLFFBQVEsa0NBQWtDLGdCQUFnQix5QkFBeUIsS0FBSywrQkFBK0IsS0FBSyx3TkFBd04sZ0lBQWdJLE9BQU8seVdBQXlXLGdJQUFnSSxPQUFPLDZLQUE2SyxPQUFPLG1EQUFtRCxnQkFBZ0IscUNBQXFDLEVBQUUsRUFBRSxrQ0FBa0MsT0FBTyx5Q0FBeUMsOEJBQThCLHNCQUFzQixFQUFFLEVBQUUsOEJBQThCLEVBQUUsRUFBRSwrS0FBK0sscUlBQXFJLE9BQU8sa0NBQWtDLGdCQUFnQixrQ0FBa0MsRUFBRSxFQUFFLHdIQUF3SCxHQUFHLDBJQUEwSSxPQUFPLDBHQUEwRyxnQkFBZ0Isc0JBQXNCLEVBQUUsRUFBRSxvQkFBb0IsZUFBZSx5QkFBeUIsS0FBSywrQkFBK0IsS0FBSyw4RkFBOEYsR0FBRywwSUFBMEksT0FBTywrRkFBK0YsUUFBUSxrQ0FBa0MsZ0JBQWdCLHlCQUF5QixLQUFLLCtCQUErQixLQUFLLHdOQUF3TixnSUFBZ0ksT0FBTyx5V0FBeVcsZ0lBQWdJLE9BQU8sNktBQTZLLE9BQU8sbURBQW1ELGdCQUFnQixxQ0FBcUMsRUFBRSxFQUFFLGtDQUFrQyxPQUFPLHlDQUF5Qyw4QkFBOEIsc0JBQXNCLEVBQUUsRUFBRSw4QkFBOEIsRUFBRSxFQUFFLCtLQUErSyxxSUFBcUksT0FBTyxrQ0FBa0MsZ0JBQWdCLGtDQUFrQyxFQUFFLEVBQUUsd0hBQXdILEdBQUcsMElBQTBJLE9BQU8sMEdBQTBHLGdCQUFnQixrQ0FBa0MsRUFBRSxFQUFFLHNCQUFzQixlQUFlLHlCQUF5QixLQUFLLCtCQUErQixLQUFLLHNSQUFzUixHQUFHLDBJQUEwSSxPQUFPLCtGQUErRixRQUFRLHdDQUF3QyxRQUFRLGtDQUFrQyxnQkFBZ0IscUNBQXFDLEVBQUUsaUhBQWlILEVBQUUsK0JBQStCLE1BQU0sd0pBQXdKLEVBQUUsc0JBQXNCLEVBQUUsRUFBRSxzR0FBc0csS0FBSyxpQkFBaUIsS0FBSyw0RkFBNEYsR0FBRywwSUFBMEksT0FBTyxrQ0FBa0MsZ0JBQWdCLHNCQUFzQixFQUFFLEVBQUUsb0JBQW9CLGVBQWUseUJBQXlCLEVBQUUseUJBQXlCLEtBQUsscUJBQXFCLEtBQUssc0dBQXNHLE9BQU8sa0NBQWtDLGdCQUFnQix5QkFBeUIsRUFBRSxvQ0FBb0MsRUFBRSxFQUFFLHdIQUF3SCxHQUFHLDBJQUEwSSxPQUFPLDBHQUEwRyxnQkFBZ0IsMkJBQTJCLEVBQUUsRUFBRSxvQkFBb0IsZUFBZSx5QkFBeUIsRUFBRSx5QkFBeUIsS0FBSyxtQkFBbUIsS0FBSyxXQUFXLE1BQU0scUdBQXFHLG1JQUFtSSxPQUFPLCtGQUErRixRQUFRLGtDQUFrQyxnQkFBZ0IseUJBQXlCLEVBQUUsbUlBQW1JLEVBQUUsMEJBQTBCLE1BQU0sOEVBQThFLEVBQUUsMkNBQTJDLE9BQU8seUNBQXlDLGtDQUFrQyx5QkFBeUIsRUFBRSxzR0FBc0csT0FBTyxnR0FBZ0csc0NBQXNDLDJCQUEyQixFQUFFLEVBQUUsZ0NBQWdDLEVBQUUsRUFBRSw2QkFBNkIsRUFBRSxFQUFFLGlNQUFpTSxHQUFHLDBJQUEwSSxPQUFPLHlHQUF5RyxRQUFRLGtDQUFrQyxnQkFBZ0IsMkJBQTJCLEVBQUUsRUFBRSwwS0FBMEssR0FBRywwSUFBMEksT0FBTyx5R0FBeUcsUUFBUSwwR0FBMEcsZ0JBQWdCLDJCQUEyQixFQUFFLEVBQUUsZUFBZSxlQUFlLHNHQUFzRyxLQUFLLDRDQUE0QyxLQUFLLHFNQUFxTSxHQUFHLDBJQUEwSSxPQUFPLG9HQUFvRyxRQUFRLHdGQUF3RixRQUFRLCtGQUErRixRQUFRLGtDQUFrQyxnQkFBZ0Isc0dBQXNHLEtBQUssNENBQTRDLEtBQUssc1BBQXNQLGdJQUFnSSxPQUFPLDBYQUEwWCxnSUFBZ0ksT0FBTywySkFBMkosT0FBTyx3RkFBd0YsUUFBUSxtREFBbUQsZ0JBQWdCLHFDQUFxQyxFQUFFLHlCQUF5QixFQUFFLEVBQUUseUJBQXlCLEtBQUssbUJBQW1CLEtBQUssc05BQXNOLGdJQUFnSSxPQUFPLGlMQUFpTCxPQUFPLG1EQUFtRCxnQkFBZ0IsOEJBQThCLEVBQUUsRUFBRSw4QkFBOEIsRUFBRSxFQUFFLHlCQUF5QixLQUFLLHlCQUF5QixLQUFLLG1CQUFtQixHQUFHLHdJQUF3SSw0QkFBNEIsRUFBRSxFQUFFLHlCQUF5QixLQUFLLHFCQUFxQixLQUFLLG9DQUFvQyx3SUFBd0ksa0NBQWtDLEVBQUUsRUFBRSwrS0FBK0ssR0FBRywwSUFBMEksT0FBTyxrQ0FBa0MsZ0JBQWdCLGlKQUFpSixPQUFPLCtCQUErQixFQUFFLHlCQUF5QixLQUFLLHFCQUFxQixLQUFLLDBHQUEwRyxPQUFPLHlGQUF5RixnQkFBZ0IseUJBQXlCLEVBQUUsRUFBRSxzQkFBc0IsRUFBRSxFQUFFLHdIQUF3SCxHQUFHLDBJQUEwSSxPQUFPLDBHQUEwRyxnQkFBZ0Isc0JBQXNCLEVBQUUsRUFBRSxtQkFBbUIsZUFBZSxtRUFBbUUsT0FBTyx5RkFBeUYsMENBQTBDLE9BQU8sc0JBQXNCLGdCQUFnQix1VkFBdVYsZ0lBQWdJLE9BQU8scUtBQXFLLE9BQU8scUdBQXFHLGdCQUFnQix5QkFBeUIsRUFBRSwyQ0FBMkMsT0FBTyx5Q0FBeUMsa0NBQWtDLDJEQUEyRCxPQUFPLHNCQUFzQixnQkFBZ0IsbU5BQW1OLGdJQUFnSSxPQUFPLDZPQUE2TyxFQUFFLEVBQUUsOERBQThELE9BQU8sc0JBQXNCLGdCQUFnQixzQkFBc0IsRUFBRSxFQUFFLGdJQUFnSSxzQkFBc0IsRUFBRSxFQUFFLEVBQUUsRUFBRSxFQUFFLHlCQUF5QixFQUFFLGVBQWUsZUFBZSx5QkFBeUIsS0FBSyx5QkFBeUIsS0FBSywrRkFBK0YsR0FBRywwSUFBMEksT0FBTyxrQ0FBa0MsZ0JBQWdCLHFDQUFxQyxFQUFFLDBCQUEwQixFQUFFLG1OQUFtTixnSUFBZ0ksT0FBTyxrTEFBa0wsRUFBRSxzQkFBc0IsRUFBRSxFQUFFLHFLQUFxSyxHQUFHLDBJQUEwSSxPQUFPLDBHQUEwRyxnQkFBZ0IsaUNBQWlDLEVBQUUsRUFBRSxjQUFjLGVBQWUsMkJBQTJCLEVBQUUsMkJBQTJCLEVBQUUsOEJBQThCLEVBQUUsNEJBQTRCLEVBQUUsbUJBQW1CLHNLQUFzSyw4QkFBOEIsRUFBRSw0QkFBNEIsRUFBRSxpQkFBaUIsaUlBQWlJLG9CQUFvQixFQUFFLHlCQUF5QixFQUFFLFdBQVcsZUFBZSwyQkFBMkIsRUFBRSwrQkFBK0IsRUFBRSw2QkFBNkIsRUFBRSw0QkFBNEIsRUFBRSwwQkFBMEIsRUFBRSwwQkFBMEIsRUFBRSwwQkFBMEIsRUFBRSxnQkFBZ0IsaURBQWlELE9BQU8sa0VBQWtFLGFBQWEsTUFBTSwwSUFBMEksT0FBTyx5Q0FBeUMsZ0JBQWdCLHlCQUF5QixFQUFFLHdCQUF3QixFQUFFLHlCQUF5QixLQUFLLHVCQUF1QixLQUFLLHVCQUF1QixPQUFPLGtFQUFrRSwrQkFBK0IscUlBQXFJLE9BQU8sc0JBQXNCLGdCQUFnQiwrQkFBK0IsRUFBRSxFQUFFLHlCQUF5QixLQUFLLHFCQUFxQixLQUFLLG1CQUFtQixHQUFHLHdJQUF3SSwrQkFBK0IsRUFBRSxFQUFFLG9CQUFvQixxREFBcUQsT0FBTyx1Q0FBdUMsYUFBYSxNQUFNLDBJQUEwSSxPQUFPLHlDQUF5QyxnQkFBZ0IscUNBQXFDLEVBQUUseUJBQXlCLEVBQUUsd0JBQXdCLEVBQUUsaUNBQWlDLEVBQUUsa0JBQWtCLG1EQUFtRCxPQUFPLHlGQUF5RixhQUFhLE1BQU0sMElBQTBJLE9BQU8seUNBQXlDLGdCQUFnQixxQ0FBcUMsRUFBRSx5QkFBeUIsRUFBRSx3QkFBd0IsRUFBRSwyQkFBMkIsRUFBRSxpQkFBaUIseUJBQXlCLEtBQUsseUJBQXlCLEtBQUssd0NBQXdDLE9BQU8sc0hBQXNILGFBQWEsTUFBTSwwSUFBMEksT0FBTyx5Q0FBeUMsZ0JBQWdCLHlCQUF5QixFQUFFLHdCQUF3QixFQUFFLHlCQUF5QixLQUFLLHlCQUF5QixLQUFLLHdDQUF3QyxNQUFNLHFCQUFxQixPQUFPLHNCQUFzQixnQkFBZ0IscUNBQXFDLEVBQUUseUJBQXlCLEVBQUUsb0hBQW9ILEVBQUUsRUFBRSx5QkFBeUIsS0FBSyxxQkFBcUIsS0FBSyxtQkFBbUIsR0FBRyx3SUFBd0ksK0JBQStCLEVBQUUsRUFBRSxxQkFBcUIsTUFBTSxtQkFBbUIsR0FBRyx3SUFBd0ksK0JBQStCLEVBQUUsRUFBRSxlQUFlLGdEQUFnRCxPQUFPLHlGQUF5RixhQUFhLE1BQU0sMElBQTBJLE9BQU8seUNBQXlDLGdCQUFnQixxQ0FBcUMsRUFBRSx5QkFBeUIsRUFBRSx3QkFBd0IsRUFBRSxvQ0FBb0MsRUFBRSxlQUFlLGlGQUFpRixPQUFPLDJDQUEyQyxRQUFRLDJDQUEyQyxRQUFRLDhDQUE4QyxRQUFRLHlGQUF5RixhQUFhLE1BQU0sMElBQTBJLE9BQU8seUNBQXlDLGdCQUFnQix5QkFBeUIsRUFBRSx3QkFBd0IsRUFBRSwwQkFBMEIsRUFBRSxlQUFlLHlCQUF5QixLQUFLLHFCQUFxQixLQUFLLHNDQUFzQyxPQUFPLHVDQUF1QyxhQUFhLE1BQU0sMElBQTBJLE9BQU8seUNBQXlDLGdCQUFnQix5QkFBeUIsRUFBRSx3QkFBd0IsRUFBRSwwQkFBMEIsRUFBRSxnQkFBZ0Isd0RBQXdELElBQUksd0RBQXdELE9BQU8sMEZBQTBGLFFBQVEseUZBQXlGLGdDQUFnQyxPQUFPLDJGQUEyRixnQkFBZ0Isa0NBQWtDLEVBQUUsY0FBYyxlQUFlLGlDQUFpQyxFQUFFLDhCQUE4QixFQUFFLGdDQUFnQyxFQUFFLGtDQUFrQyxFQUFFLDZCQUE2QixFQUFFLCtCQUErQixFQUFFLG1DQUFtQyxFQUFFLCtCQUErQixFQUFFLCtCQUErQixFQUFFLDZCQUE2QixFQUFFLG1CQUFtQix1Q0FBdUMsT0FBTywyRkFBMkYsbUNBQW1DLHNCQUFzQixFQUFFLHFCQUFxQiw2SEFBNkgsdUJBQXVCLHNCQUFzQixLQUFLLGtGQUFrRix3Q0FBd0MsRUFBRSw2QkFBNkIsMkZBQTJGLHVCQUF1QixlQUFlLHlCQUF5QixFQUFFLDZDQUE2QyxPQUFPLGtEQUFrRCwwQ0FBMEMsbURBQW1ELE9BQU8seUZBQXlGLGFBQWEsTUFBTSwwSUFBMEksT0FBTyx5Q0FBeUMsZ0JBQWdCLDhDQUE4QyxPQUFPLHlGQUF5RixhQUFhLE1BQU0sd0lBQXdJLGlDQUFpQyxFQUFFLEVBQUUsc0JBQXNCLEVBQUUsRUFBRSx5QkFBeUIsRUFBRSwwQkFBMEIsRUFBRSxFQUFFLGtDQUFrQyxFQUFFLGtCQUFrQixlQUFlLGtDQUFrQyxPQUFPLDJGQUEyRiw4QkFBOEIsc0JBQXNCLEVBQUUsRUFBRSxvQkFBb0Isc0JBQXNCLE1BQU0sZ0VBQWdFLHdCQUF3QixlQUFlLHlCQUF5QixFQUFFLDZDQUE2QyxPQUFPLGtEQUFrRCwwQ0FBMEMsbURBQW1ELE9BQU8seUZBQXlGLGFBQWEsTUFBTSwwSUFBMEksT0FBTyx5Q0FBeUMsZ0JBQWdCLDhDQUE4QyxPQUFPLHlGQUF5RixhQUFhLE1BQU0sd0lBQXdJLGlDQUFpQyxFQUFFLEVBQUUsc0JBQXNCLEVBQUUsRUFBRSx5QkFBeUIsRUFBRSwwQkFBMEIsRUFBRSxFQUFFLGtDQUFrQyxFQUFFLG9CQUFvQixpQkFBaUIsaUJBQWlCLE9BQU8sa0dBQWtHLFlBQVksa0JBQWtCLG1CQUFtQix5QkFBeUIsYUFBYSxxQkFBcUIsT0FBTywrRkFBK0YsUUFBUSxpQ0FBaUMsUUFBUSx3RkFBd0YsUUFBUSx5RkFBeUYsZ0JBQWdCLHlCQUF5QixFQUFFLHdDQUF3QyxFQUFFLG1OQUFtTixnSUFBZ0ksT0FBTyxzUEFBc1AsRUFBRSxFQUFFLHlCQUF5QixLQUFLLHFCQUFxQixLQUFLLHdCQUF3QixNQUFNLHFCQUFxQixPQUFPLCtGQUErRixRQUFRLGlDQUFpQyxRQUFRLHlGQUF5RixnQkFBZ0IsbU5BQW1OLGdJQUFnSSxPQUFPLHNQQUFzUCxFQUFFLEVBQUUsdUdBQXVHLFVBQVUscUJBQXFCLE9BQU8sd0ZBQXdGLFFBQVEsa0NBQWtDLGdCQUFnQixzQkFBc0IsRUFBRSxFQUFFLHNHQUFzRyxLQUFLLHFCQUFxQixPQUFPLHlGQUF5RixnQkFBZ0Isc0JBQXNCLEVBQUUsRUFBRSxvQkFBb0IsZUFBZSx1SEFBdUgscUNBQXFDLEVBQUUsRUFBRSxrQkFBa0IscURBQXFELHVGQUF1RixxQ0FBcUMsRUFBRSxFQUFFLDBCQUEwQiwwRkFBMEYsa0JBQWtCLG1IQUFtSCx5QkFBeUIsZUFBZSwwQ0FBMEMsRUFBRSwwQkFBMEIsTUFBTSw0RUFBNEUsRUFBRSwrQkFBK0IsMEJBQTBCLE1BQU0sc0VBQXNFLE9BQU8saURBQWlELGlEQUFpRCx5QkFBeUIsRUFBRSxrQ0FBa0MsT0FBTyw4QkFBOEIsa0NBQWtDLDJCQUEyQixNQUFNLHlIQUF5SCxFQUFFLDJCQUEyQixFQUFFLEVBQUUsMEdBQTBHLE9BQU8sNkJBQTZCLDBCQUEwQixNQUFNLDJEQUEyRCxNQUFNLCtEQUErRCxNQUFNLDJGQUEyRixPQUFPLGdEQUFnRCxRQUFRLHlDQUF5QyxRQUFRLCtFQUErRSxFQUFFLHFDQUFxQyxrQ0FBa0MsT0FBTyw4QkFBOEIsa0NBQWtDLDJCQUEyQixNQUFNLHlIQUF5SCxFQUFFLDJCQUEyQixFQUFFLDZCQUE2QiwwQkFBMEIsTUFBTSw4REFBOEQsT0FBTyxpREFBaUQseUNBQXlDLHlCQUF5QixFQUFFLDBHQUEwRyxPQUFPLDZCQUE2QiwwQkFBMEIsTUFBTSx5REFBeUQsTUFBTSw4RUFBOEUsT0FBTyxnREFBZ0QsUUFBUSw0RkFBNEYsRUFBRSx1QkFBdUIsZUFBZSx3Q0FBd0MsRUFBRSwwQkFBMEIsTUFBTSw0RUFBNEUsRUFBRSxpQkFBaUIsZUFBZSxtTkFBbU4sZ0lBQWdJLE9BQU8sZ01BQWdNLHlCQUF5QixFQUFFLCtMQUErTCxPQUFPLDZCQUE2Qiw4UkFBOFIscUhBQXFILE9BQU8sNktBQTZLLE9BQU8sNEJBQTRCLFFBQVEscUdBQXFHLGdCQUFnQix5QkFBeUIsRUFBRSx3Q0FBd0MsRUFBRSwyQ0FBMkMsT0FBTyx5RkFBeUYsa0NBQWtDLHNCQUFzQixFQUFFLEVBQUUsa0NBQWtDLE9BQU8seUZBQXlGLDhCQUE4Qix5QkFBeUIsRUFBRSxFQUFFLEVBQUUsRUFBRSxjQUFjLGVBQWUseUJBQXlCLEVBQUUsOEJBQThCLEVBQUUsNkJBQTZCLEVBQUUsNkJBQTZCLEVBQUUsNkJBQTZCLEVBQUUsK0JBQStCLEVBQUUsK0JBQStCLEVBQUUseUJBQXlCLEVBQUUsK0JBQStCLEVBQUUsNkJBQTZCLEVBQUUsaUNBQWlDLEVBQUUsOEJBQThCLEVBQUUsbUJBQW1CLHVDQUF1QyxPQUFPLDJGQUEyRixtQ0FBbUMseUJBQXlCLEVBQUUsa0JBQWtCLDJHQUEyRyxrQkFBa0Isa0NBQWtDLE9BQU8sMkZBQTJGLDhCQUE4Qix5QkFBeUIsRUFBRSxrQkFBa0IsY0FBYyxPQUFPLGtDQUFrQyxRQUFRLHdGQUF3RixRQUFRLHlGQUF5RixzSUFBc0ksb0JBQW9CLGtEQUFrRCxPQUFPLHFHQUFxRyxzQ0FBc0MsNEJBQTRCLEVBQUUsb0JBQW9CLDJDQUEyQyxPQUFPLHlDQUF5QyxrQ0FBa0MseUJBQXlCLEVBQUUsc0dBQXNHLE9BQU8sZ0dBQWdHLHNDQUFzQyxzQkFBc0IsRUFBRSxFQUFFLHlCQUF5QixFQUFFLG9CQUFvQixpQkFBaUIsaUJBQWlCLE9BQU8sa0dBQWtHLFlBQVksa0JBQWtCLG1CQUFtQix5QkFBeUIsYUFBYSxxQkFBcUIsT0FBTywrRkFBK0YsUUFBUSxpQ0FBaUMsUUFBUSx3RkFBd0YsUUFBUSx5RkFBeUYsZ0JBQWdCLHlCQUF5QixFQUFFLHdDQUF3QyxFQUFFLG1OQUFtTixnSUFBZ0ksT0FBTyxzUEFBc1AsRUFBRSxFQUFFLHlCQUF5QixLQUFLLHFCQUFxQixLQUFLLHdCQUF3QixNQUFNLHFCQUFxQixPQUFPLCtGQUErRixRQUFRLGlDQUFpQyxRQUFRLHlGQUF5RixnQkFBZ0IsbU5BQW1OLGdJQUFnSSxPQUFPLHNQQUFzUCxFQUFFLEVBQUUsdUdBQXVHLFVBQVUscUJBQXFCLE9BQU8sd0ZBQXdGLFFBQVEsa0NBQWtDLGdCQUFnQixzQkFBc0IsRUFBRSxFQUFFLHNHQUFzRyxLQUFLLHFCQUFxQixPQUFPLHlGQUF5RixnQkFBZ0IseUJBQXlCLEVBQUUsRUFBRSxrQkFBa0IsZ0RBQWdELE9BQU8sc0JBQXNCLHNDQUFzQywwQkFBMEIsRUFBRSxhQUFhLHNHQUFzRyxPQUFPLHlDQUF5QyxnQkFBZ0IsR0FBRyx3SUFBd0kseUJBQXlCLEVBQUUsK0JBQStCLEVBQUUsK0JBQStCLEVBQUUsZ0JBQWdCLGVBQWUseUJBQXlCLEVBQUUsa0NBQWtDLEVBQUUsa0NBQWtDLEVBQUUsb0NBQW9DLEVBQUUsaUNBQWlDLEVBQUUsb0NBQW9DLEVBQUUsc0NBQXNDLEVBQUUsMkJBQTJCLGdEQUFnRCxPQUFPLDBHQUEwRywwQkFBMEIsR0FBRyx3SUFBd0kseUJBQXlCLEtBQUsscUJBQXFCLEtBQUssK0RBQStELE9BQU8sb0dBQW9HLFFBQVEsdUJBQXVCLEVBQUUsNEJBQTRCLEVBQUUsMEJBQTBCLEVBQUUsdUJBQXVCLGVBQWUsbURBQW1ELE9BQU8sc0JBQXNCLGdCQUFnQixHQUFHLHdJQUF3SSx5QkFBeUIsS0FBSywyQkFBMkIsS0FBSywrREFBK0QsT0FBTywwRkFBMEYsUUFBUSwwQ0FBMEMsRUFBRSxrRUFBa0UsT0FBTywrRkFBK0YsUUFBUSx5Q0FBeUMsZ0JBQWdCLGdDQUFnQyxFQUFFLEVBQUUsOEhBQThILE9BQU8seUNBQXlDLGdCQUFnQiwyQkFBMkIsRUFBRSxFQUFFLHNJQUFzSSxPQUFPLHdDQUF3QyxRQUFRLG1DQUFtQyxFQUFFLDRHQUE0RyxHQUFHLHdJQUF3SSwyQkFBMkIsRUFBRSxFQUFFLEVBQUUscUhBQXFILEVBQUUsdUJBQXVCLDBDQUEwQyxPQUFPLGtEQUFrRCwwQ0FBMEMseUJBQXlCLEVBQUUsd0JBQXdCLEVBQUUsc0JBQXNCLEVBQUUsc0JBQXNCLDJDQUEyQyxPQUFPLHlDQUF5QyxrQ0FBa0MseUJBQXlCLEVBQUUsc0dBQXNHLE9BQU8sZ0dBQWdHLHNDQUFzQywyQkFBMkIsRUFBRSxFQUFFLDJCQUEyQixFQUFFLHlCQUF5QixlQUFlLGlKQUFpSixLQUFLLDZCQUE2QixLQUFLLCtFQUErRSxPQUFPLHNHQUFzRyxFQUFFLHlCQUF5QixLQUFLLHlCQUF5QixLQUFLLG1CQUFtQixHQUFHLHdJQUF3SSx5QkFBeUIsS0FBSyx5QkFBeUIsS0FBSyxnREFBZ0QsT0FBTyx1QkFBdUIsRUFBRSx5QkFBeUIsS0FBSyxxQkFBcUIsS0FBSyw4Q0FBOEMsT0FBTywwQ0FBMEMsRUFBRSx5QkFBeUIsS0FBSyxpQkFBaUIsS0FBSyxtQkFBbUIsR0FBRyx3SUFBd0ksMkJBQTJCLEVBQUUsRUFBRSxFQUFFLGdCQUFnQixlQUFlLHlCQUF5QixFQUFFLGtDQUFrQyxFQUFFLGtDQUFrQyxFQUFFLG9DQUFvQyxFQUFFLGlDQUFpQyxFQUFFLHVCQUF1QixlQUFlLG1EQUFtRCxPQUFPLHNCQUFzQixnQkFBZ0IsR0FBRyx3SUFBd0kseUJBQXlCLEtBQUssMkJBQTJCLEtBQUssK0RBQStELE9BQU8sMEZBQTBGLFFBQVEsMENBQTBDLEVBQUUsa0VBQWtFLE9BQU8sK0ZBQStGLFFBQVEseUNBQXlDLGdCQUFnQixnQ0FBZ0MsRUFBRSxFQUFFLDhIQUE4SCxPQUFPLHlDQUF5QyxnQkFBZ0IsMkJBQTJCLEVBQUUsRUFBRSxzSUFBc0ksT0FBTyx3Q0FBd0MsUUFBUSxtQ0FBbUMsRUFBRSw0R0FBNEcsR0FBRyx3SUFBd0ksMkJBQTJCLEVBQUUsRUFBRSxFQUFFLHFIQUFxSCxFQUFFLHVCQUF1Qiw2Q0FBNkMsT0FBTyxrREFBa0QsMENBQTBDLHlCQUF5QixFQUFFLHdCQUF3QixFQUFFLHNCQUFzQixFQUFFLHNCQUFzQixrQ0FBa0MsT0FBTyx5Q0FBeUMsa0NBQWtDLGlDQUFpQyxFQUFFLDJCQUEyQixFQUFFLHNCQUFzQiwrQ0FBK0MsT0FBTyx5RkFBeUYsdUJBQXVCLFdBQVcsZUFBZSx5QkFBeUIsRUFBRSw0QkFBNEIsRUFBRSxpQkFBaUIsZUFBZSx5QkFBeUIsRUFBRSxpQ0FBaUMsRUFBRSxpQ0FBaUMsRUFBRSwyQkFBMkIsRUFBRSx5QkFBeUIsRUFBRSw4QkFBOEIsRUFBRSx5QkFBeUIsRUFBRSwyQkFBMkIsRUFBRSwrQkFBK0IsRUFBRSx5QkFBeUIsRUFBRSw4QkFBOEIsRUFBRSx5QkFBeUIsRUFBRSwyQkFBMkIsRUFBRSw0QkFBNEIsRUFBRSxzQkFBc0Isc0VBQXNFLHNCQUFzQixlQUFlLHdDQUF3QyxFQUFFLDBCQUEwQixNQUFNLHNIQUFzSCxFQUFFLGdCQUFnQixlQUFlLDJDQUEyQyxPQUFPLHlDQUF5QyxrQ0FBa0Msc0JBQXNCLEVBQUUsRUFBRSw0Q0FBNEMsT0FBTyx5Q0FBeUMsMENBQTBDLHFDQUFxQyxFQUFFLHNCQUFzQixFQUFFLEVBQUUsY0FBYyxlQUFlLCtDQUErQyxPQUFPLDhCQUE4Qiw2Q0FBNkMsT0FBTyw4QkFBOEIsZ0JBQWdCLHlCQUF5QixLQUFLLG1CQUFtQixLQUFLLHNHQUFzRyxPQUFPLGtDQUFrQyxnQkFBZ0IseUJBQXlCLEVBQUUsRUFBRSxnSkFBZ0osT0FBTyw4QkFBOEIsZ0JBQWdCLHNCQUFzQixFQUFFLEVBQUUseUJBQXlCLEtBQUssaUJBQWlCLEtBQUssOENBQThDLE9BQU8sOEJBQThCLGdCQUFnQixzQkFBc0IsRUFBRSxFQUFFLHlCQUF5QixLQUFLLGlCQUFpQixLQUFLLGtEQUFrRCxzQkFBc0IsRUFBRSxFQUFFLEVBQUUsbUJBQW1CLGNBQWMsT0FBTywwQkFBMEIsUUFBUSwyQkFBMkIsOENBQThDLGNBQWMsZUFBZSx1SUFBdUkseUNBQXlDLEtBQUssaURBQWlELEtBQUssd0tBQXdLLE9BQU8seUdBQXlHLFFBQVEsNENBQTRDLGdCQUFnQix5QkFBeUIsRUFBRSxFQUFFLHlCQUF5QixLQUFLLHdCQUF3QixLQUFLLDZCQUE2QixPQUFPLDRDQUE0Qyx5Q0FBeUMsbUlBQW1JLE9BQU8seUdBQXlHLFFBQVEsNENBQTRDLGdCQUFnQiwwQkFBMEIsRUFBRSxFQUFFLGdCQUFnQiw0Q0FBNEMsT0FBTyw4QkFBOEIsMkNBQTJDLHNCQUFzQixFQUFFLG9CQUFvQixlQUFlLHlCQUF5QixLQUFLLGtDQUFrQyxLQUFLLGlIQUFpSCxPQUFPLGlDQUFpQyxRQUFRLGtDQUFrQyxnQkFBZ0IseUJBQXlCLEVBQUUscUNBQXFDLEVBQUUseUJBQXlCLEVBQUUsRUFBRSxvTUFBb00sT0FBTyxrQ0FBa0MsOEdBQThHLE9BQU8saUNBQWlDLFFBQVEsa0NBQWtDLGdCQUFnQix5QkFBeUIsRUFBRSxnREFBZ0QsT0FBTyx5RkFBeUYsc0dBQXNHLHNCQUFzQixFQUFFLEVBQUUsRUFBRSxtQkFBbUIsZUFBZSxzR0FBc0csT0FBTyxzQkFBc0Isa05BQWtOLGdJQUFnSSxPQUFPLDBKQUEwSixPQUFPLG9DQUFvQyxFQUFFLGNBQWMsT0FBTyxxQ0FBcUMsa0JBQWtCLEVBQUUsY0FBYyxPQUFPLHdGQUF3RixRQUFRLHlGQUF5RixrREFBa0QsSUFBSSxFQUFFLGlLQUFpSyxFQUFFLGNBQWMseUlBQXlJLE9BQU8seUZBQXlGLDhDQUE4QyxPQUFPLHNCQUFzQixnQkFBZ0IsdVZBQXVWLGdJQUFnSSxPQUFPLDBKQUEwSixPQUFPLHNHQUFzRyxFQUFFLGNBQWMsMENBQTBDLE9BQU8sOEJBQThCLDJDQUEyQyxzQkFBc0IsRUFBRSxnQkFBZ0IsZUFBZSxpREFBaUQsT0FBTyw4QkFBOEIsNkNBQTZDLE9BQU8sOEJBQThCLGdCQUFnQix5QkFBeUIsS0FBSyx1QkFBdUIsS0FBSyw4Q0FBOEMsT0FBTyw4QkFBOEIsZ0JBQWdCLHNCQUFzQixFQUFFLEVBQUUseUJBQXlCLEtBQUssaUJBQWlCLEtBQUssa0RBQWtELHNCQUFzQixFQUFFLEVBQUUsRUFBRSxXQUFXLGVBQWUseUJBQXlCLEVBQUUsdUhBQXVILEVBQUUsMENBQTBDLEVBQUUsMkJBQTJCLEVBQUUsNEJBQTRCLEVBQUUsOEJBQThCLEVBQUUsNEJBQTRCLEVBQUUsd0NBQXdDLEVBQUUsNEJBQTRCLEVBQUUsaUNBQWlDLEVBQUUsc0JBQXNCLGVBQWUsd05BQXdOLGdJQUFnSSxPQUFPLG1WQUFtVixnSUFBZ0ksT0FBTyw4S0FBOEssT0FBTyw2REFBNkQsUUFBUSw2RkFBNkYsUUFBUSx1Q0FBdUMsMENBQTBDLE9BQU8sT0FBTyw0TUFBNE0sZ0lBQWdJLE9BQU8sb0tBQW9LLEtBQUssT0FBTyxHQUFHLDBJQUEwSSxPQUFPLDBDQUEwQyxnQkFBZ0IseUJBQXlCLEVBQUUsMENBQTBDLEVBQUUsRUFBRSxnQkFBZ0IsZUFBZSw0TkFBNE4sZ0lBQWdJLE9BQU8sb09BQW9PLE9BQU8scUNBQXFDLFFBQVEsNkdBQTZHLFFBQVEsc0JBQXNCLHNHQUFzRyxzQkFBc0IsRUFBRSxFQUFFLGlCQUFpQixrREFBa0QsT0FBTyxxR0FBcUcsc0NBQXNDLG9DQUFvQyxFQUFFLHNDQUFzQyxFQUFFLGlCQUFpQiw4QkFBOEIsT0FBTyxrR0FBa0csOEJBQThCLG1CQUFtQiw0Q0FBNEMsT0FBTywrRkFBK0YsUUFBUSx3RkFBd0YsUUFBUSx5RkFBeUYsZ0JBQWdCLHlCQUF5QixFQUFFLHdDQUF3QyxFQUFFLG1OQUFtTixnSUFBZ0ksT0FBTyxzUEFBc1AsRUFBRSxFQUFFLHVHQUF1RywyQkFBMkIsT0FBTyx3RkFBd0YsUUFBUSxrQ0FBa0MsZ0JBQWdCLHNCQUFzQixFQUFFLEVBQUUsbUJBQW1CLGVBQWUsZUFBZSxrSUFBa0ksRUFBRSxpQkFBaUIsa0NBQWtDLE9BQU8seUNBQXlDLGtDQUFrQyx3R0FBd0csRUFBRSw0QkFBNEIsRUFBRSxzQkFBc0IsRUFBRSw2QkFBNkIsa0RBQWtELGlCQUFpQixpQkFBaUIsaUJBQWlCLE9BQU8sa0dBQWtHLFlBQVksa0JBQWtCLG1CQUFtQix5QkFBeUIsYUFBYSxxQkFBcUIsT0FBTywrRkFBK0YsUUFBUSxpQ0FBaUMsUUFBUSx3RkFBd0YsUUFBUSx5RkFBeUYsZ0JBQWdCLHlCQUF5QixFQUFFLHdDQUF3QyxFQUFFLG1OQUFtTixnSUFBZ0ksT0FBTyxzUEFBc1AsRUFBRSxFQUFFLHlCQUF5QixLQUFLLHFCQUFxQixLQUFLLHdCQUF3QixNQUFNLHFCQUFxQixPQUFPLCtGQUErRixRQUFRLGlDQUFpQyxRQUFRLHlGQUF5RixnQkFBZ0IsbU5BQW1OLGdJQUFnSSxPQUFPLHNQQUFzUCxFQUFFLEVBQUUsdUdBQXVHLFVBQVUscUJBQXFCLE9BQU8sd0ZBQXdGLFFBQVEsa0NBQWtDLGdCQUFnQixzQkFBc0IsRUFBRSxFQUFFLHNHQUFzRyxLQUFLLHFCQUFxQixPQUFPLHlGQUF5RixnQkFBZ0Isc0JBQXNCLEVBQUUsRUFBRSxxQkFBcUIsY0FBYyxPQUFPLDhFQUE4RSwyQkFBMkIsTUFBTSw0QkFBNEIsc0JBQXNCLGNBQWMsT0FBTyw2REFBNkQsUUFBUSw4RUFBOEUsbU9BQW1PLGdJQUFnSSxPQUFPLDJJQUEySSxnQ0FBZ0M7O0FBRWowbUUsaUVBQWU7QUFDZjtBQUNBLENBQUMiLCJzb3VyY2VzIjpbIkQ6XFxzb2Z0XFxTZWFQcm9qZWN0XFxnaXRodWJfb3Blbl9wcm9qZWN0X3N1Y2Nlc3NcXHN1bmFcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXEBzaGlraWpzXFxsYW5nc1xcZGlzdFxcb2NhbWwubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGxhbmcgPSBPYmplY3QuZnJlZXplKEpTT04ucGFyc2UoXCJ7XFxcImRpc3BsYXlOYW1lXFxcIjpcXFwiT0NhbWxcXFwiLFxcXCJmaWxlVHlwZXNcXFwiOltcXFwiLm1sXFxcIixcXFwiLm1saVxcXCJdLFxcXCJuYW1lXFxcIjpcXFwib2NhbWxcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNjb21tZW50XFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3ByYWdtYVxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNkZWNsXFxcIn1dLFxcXCJyZXBvc2l0b3J5XFxcIjp7XFxcImF0dHJpYnV0ZVxcXCI6e1xcXCJiZWdpblxcXCI6XFxcIihcXFxcXFxcXFspXFxcXFxcXFxzKigoPzwhWyNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXSlAezEsM30oPyFbI1xcXFxcXFxcLTohPy5AKi9cXFxcXFxcXCYlXis8PT58fiRdKSlcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcImNvbnN0YW50Lmxhbmd1YWdlIGNvbnN0YW50Lm51bWVyaWMgZW50aXR5Lm90aGVyLmF0dHJpYnV0ZS1uYW1lLmlkLmNzcyBzdHJvbmdcXFwifSxcXFwiMlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwidmFyaWFibGUub3RoZXIuY2xhc3MuanMgbWVzc2FnZS5lcnJvciB2YXJpYWJsZS5pbnRlcnBvbGF0aW9uIHN0cmluZy5yZWdleHBcXFwifX0sXFxcImVuZFxcXCI6XFxcIl1cXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJjb25zdGFudC5sYW5ndWFnZSBjb25zdGFudC5udW1lcmljIGVudGl0eS5vdGhlci5hdHRyaWJ1dGUtbmFtZS5pZC5jc3Mgc3Ryb25nXFxcIn19LFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNhdHRyaWJ1dGVQYXlsb2FkXFxcIn1dfSxcXFwiYXR0cmlidXRlSWRlbnRpZmllclxcXCI6e1xcXCJjYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJ2YXJpYWJsZS5vdGhlci5jbGFzcy5qcyBtZXNzYWdlLmVycm9yIHZhcmlhYmxlLmludGVycG9sYXRpb24gc3RyaW5nLnJlZ2V4cCBzdHJvbmdcXFwifSxcXFwiMlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uZGVmaW5pdGlvbi50YWdcXFwifX0sXFxcIm1hdGNoXFxcIjpcXFwiKCg/PCFbI1xcXFxcXFxcLTohPy5AKi9cXFxcXFxcXCYlXis8PT58fiRdKSUoPyFbI1xcXFxcXFxcLTohPy5AKi9cXFxcXFxcXCYlXis8PT58fiRdKSkoKD8hXFxcXFxcXFxiKD86YW5kfCd8YXN8YXNyfGFzc2VydHxcXFxcXFxcXCp8YmVnaW58Y2xhc3N8WzosQF18Y29uc3RyYWludHxkb3xkb25lfGRvd250b3xlbHNlfGVuZHw9fGV4Y2VwdGlvbnxleHRlcm5hbHxmYWxzZXxmb3J8XFxcXFxcXFwufGZ1bnxmdW5jdGlvbnxmdW5jdG9yfFs+LV18aWZ8aW58aW5jbHVkZXxpbmhlcml0fGluaXRpYWxpemVyfGxhbmR8bGF6eXxbeyhcXFxcXFxcXFs8XXxsZXR8bG9yfGxzbHxsc3J8bHhvcnxtYXRjaHxtZXRob2R8bW9kfG1vZHVsZXxtdXRhYmxlfG5ld3xub25yZWN8I3xvYmplY3R8b2Z8b3BlbnxvcnxbJStdfHByaXZhdGV8Wz9cXFxcXFxcIl18cmVjfFtcXFxcXFxcXFxcXFxcXFxcfSlcXFxcXFxcXF07XXxzaWd8L3xzdHJ1Y3R8dGhlbnx+fHRvfHRydWV8dHJ5fHR5cGV8dmFsfFxcXFxcXFxcfHx2aXJ0dWFsfHdoZW58d2hpbGV8d2l0aClcXFxcXFxcXGIoPzpbXiddfCQpKVxcXFxcXFxcYig/PVtbOmxvd2VyOl1fXSlbWzphbHBoYTpdX11bWzp3b3JkOl0nXSopXFxcIn0sXFxcImF0dHJpYnV0ZVBheWxvYWRcXFwiOntcXFwicGF0dGVybnNcXFwiOlt7XFxcImJlZ2luXFxcIjpcXFwiKD88PSg/OlteI1xcXFxcXFxcLTohPy5AKi9cXFxcXFxcXCYlXis8PT58fiRdJXxeJSkpKD8hWyNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXSlcXFwiLFxcXCJlbmRcXFwiOlxcXCIoKD88IVsjXFxcXFxcXFwtOiE/LkAqL1xcXFxcXFxcJiVeKzw9Pnx+JF0pWzo/XSg/IVsjXFxcXFxcXFwtOiE/LkAqL1xcXFxcXFxcJiVeKzw9Pnx+JF0pKXwoPzw9XFxcXFxcXFxzKXwoPz1dKVxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcInZhcmlhYmxlLm90aGVyLmNsYXNzLmpzIG1lc3NhZ2UuZXJyb3IgdmFyaWFibGUuaW50ZXJwb2xhdGlvbiBzdHJpbmcucmVnZXhwXFxcIn19LFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNwYXRoTW9kdWxlRXh0ZW5kZWRcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjcGF0aFJlY29yZFxcXCJ9XX0se1xcXCJiZWdpblxcXCI6XFxcIig/PD0oPzpbXiNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXTp8XjopKSg/IVsjXFxcXFxcXFwtOiE/LkAqL1xcXFxcXFxcJiVeKzw9Pnx+JF0pXFxcIixcXFwiZW5kXFxcIjpcXFwiKD89XSlcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNzaWduYXR1cmVcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjdHlwZVxcXCJ9XX0se1xcXCJiZWdpblxcXCI6XFxcIig/PD0oPzpbXiNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXVxcXFxcXFxcP3xeXFxcXFxcXFw/KSkoPyFbI1xcXFxcXFxcLTohPy5AKi9cXFxcXFxcXCYlXis8PT58fiRdKVxcXCIsXFxcImVuZFxcXCI6XFxcIig/PV0pXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImJlZ2luXFxcIjpcXFwiKD88PSg/OlteI1xcXFxcXFxcLTohPy5AKi9cXFxcXFxcXCYlXis8PT58fiRdXFxcXFxcXFw/fF5cXFxcXFxcXD8pKSg/IVsjXFxcXFxcXFwtOiE/LkAqL1xcXFxcXFxcJiVeKzw9Pnx+JF0pXFxcIixcXFwiZW5kXFxcIjpcXFwiKD89XSl8XFxcXFxcXFxid2hlblxcXFxcXFxcYlxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOnt9fSxcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjcGF0dGVyblxcXCJ9XX0se1xcXCJiZWdpblxcXCI6XFxcIig/PD0oPzpcXFxcXFxcXFB7d29yZH13aGVufF53aGVuKSkoPyFcXFxcXFxcXHB7d29yZH0pXFxcIixcXFwiZW5kXFxcIjpcXFwiKD89XSlcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiN0ZXJtXFxcIn1dfV19LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiN0ZXJtXFxcIn1dfSxcXFwiYmluZENsYXNzVGVybVxcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiYmVnaW5cXFwiOlxcXCIoPzw9KD86XFxcXFxcXFxQe3dvcmR9fF4pKD86YW5kfGNsYXNzfHR5cGUpKSg/IVxcXFxcXFxccHt3b3JkfSlcXFwiLFxcXCJlbmRcXFwiOlxcXCIoPzwhWyNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXSkoOil8KD0pKD8hWyNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXSl8KD89Ozt8W30pXFxcXFxcXFxdXXxcXFxcXFxcXGIoPzplbmR8YW5kfGNsYXNzfGV4Y2VwdGlvbnxleHRlcm5hbHxpbnxpbmNsdWRlfGluaGVyaXR8aW5pdGlhbGl6ZXJ8bGV0fG1ldGhvZHxtb2R1bGV8b3Blbnx0eXBlfHZhbClcXFxcXFxcXGIpXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwidmFyaWFibGUub3RoZXIuY2xhc3MuanMgbWVzc2FnZS5lcnJvciB2YXJpYWJsZS5pbnRlcnBvbGF0aW9uIHN0cmluZy5yZWdleHAgc3Ryb25nXFxcIn0sXFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcInN1cHBvcnQudHlwZSBzdHJvbmdcXFwifX0sXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJiZWdpblxcXCI6XFxcIig/PD0oPzpcXFxcXFxcXFB7d29yZH18XikoPzphbmR8Y2xhc3N8dHlwZSkpKD8hXFxcXFxcXFxwe3dvcmR9KVxcXCIsXFxcImVuZFxcXCI6XFxcIig/PSg/IVxcXFxcXFxcYig/OmFuZHwnfGFzfGFzcnxhc3NlcnR8XFxcXFxcXFwqfGJlZ2lufGNsYXNzfFs6LEBdfGNvbnN0cmFpbnR8ZG98ZG9uZXxkb3dudG98ZWxzZXxlbmR8PXxleGNlcHRpb258ZXh0ZXJuYWx8ZmFsc2V8Zm9yfFxcXFxcXFxcLnxmdW58ZnVuY3Rpb258ZnVuY3RvcnxbPi1dfGlmfGlufGluY2x1ZGV8aW5oZXJpdHxpbml0aWFsaXplcnxsYW5kfGxhenl8W3soXFxcXFxcXFxbPF18bGV0fGxvcnxsc2x8bHNyfGx4b3J8bWF0Y2h8bWV0aG9kfG1vZHxtb2R1bGV8bXV0YWJsZXxuZXd8bm9ucmVjfCN8b2JqZWN0fG9mfG9wZW58b3J8WyUrXXxwcml2YXRlfFs/XFxcXFxcXCJdfHJlY3xbXFxcXFxcXFxcXFxcXFxcXH0pXFxcXFxcXFxdO118c2lnfC98c3RydWN0fHRoZW58fnx0b3x0cnVlfHRyeXx0eXBlfHZhbHxcXFxcXFxcXHx8dmlydHVhbHx3aGVufHdoaWxlfHdpdGgpXFxcXFxcXFxiKD86W14nXXwkKSlcXFxcXFxcXGIoPz1bWzpsb3dlcjpdX10pW1s6YWxwaGE6XV9dW1s6d29yZDpdJ10qXFxcXFxcXFxzKix8W15cXFxcXFxcXHNbOmxvd2VyOl0lXSl8KD8hXFxcXFxcXFxiKD86YW5kfCd8YXN8YXNyfGFzc2VydHxcXFxcXFxcXCp8YmVnaW58Y2xhc3N8WzosQF18Y29uc3RyYWludHxkb3xkb25lfGRvd250b3xlbHNlfGVuZHw9fGV4Y2VwdGlvbnxleHRlcm5hbHxmYWxzZXxmb3J8XFxcXFxcXFwufGZ1bnxmdW5jdGlvbnxmdW5jdG9yfFs+LV18aWZ8aW58aW5jbHVkZXxpbmhlcml0fGluaXRpYWxpemVyfGxhbmR8bGF6eXxbeyhcXFxcXFxcXFs8XXxsZXR8bG9yfGxzbHxsc3J8bHhvcnxtYXRjaHxtZXRob2R8bW9kfG1vZHVsZXxtdXRhYmxlfG5ld3xub25yZWN8I3xvYmplY3R8b2Z8b3BlbnxvcnxbJStdfHByaXZhdGV8Wz9cXFxcXFxcIl18cmVjfFtcXFxcXFxcXFxcXFxcXFxcfSlcXFxcXFxcXF07XXxzaWd8L3xzdHJ1Y3R8dGhlbnx+fHRvfHRydWV8dHJ5fHR5cGV8dmFsfFxcXFxcXFxcfHx2aXJ0dWFsfHdoZW58d2hpbGV8d2l0aClcXFxcXFxcXGIoPzpbXiddfCQpKVxcXFxcXFxcYig/PVtbOmxvd2VyOl1fXSlbWzphbHBoYTpdX11bWzp3b3JkOl0nXSp8KD89XFxcXFxcXFxidHlwZVxcXFxcXFxcYilcXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJlbnRpdHkubmFtZS5mdW5jdGlvbiBzdHJvbmcgZW1waGFzaXNcXFwifX0sXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2F0dHJpYnV0ZUlkZW50aWZpZXJcXFwifV19LHtcXFwiYmVnaW5cXFwiOlxcXCJcXFxcXFxcXFtcXFwiLFxcXCJjYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLnRhZ1xcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiXVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3R5cGVcXFwifV19LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNiaW5kVGVybUFyZ3NcXFwifV19LHtcXFwiYmVnaW5cXFwiOlxcXCIoPzw9KD86W14jXFxcXFxcXFwtOiE/LkAqL1xcXFxcXFxcJiVeKzw9Pnx+JF06fF46KSkoPyFbI1xcXFxcXFxcLTohPy5AKi9cXFxcXFxcXCYlXis8PT58fiRdKVxcXCIsXFxcImVuZFxcXCI6XFxcIig/PCFbI1xcXFxcXFxcLTohPy5AKi9cXFxcXFxcXCYlXis8PT58fiRdKT0oPyFbI1xcXFxcXFxcLTohPy5AKi9cXFxcXFxcXCYlXis8PT58fiRdKXwoPz1bfSlcXFxcXFxcXF1dfFxcXFxcXFxcYig/OmVuZHxhbmR8Y2xhc3N8ZXhjZXB0aW9ufGV4dGVybmFsfGlufGluY2x1ZGV8aW5oZXJpdHxpbml0aWFsaXplcnxsZXR8bWV0aG9kfG1vZHVsZXxvcGVufHZhbClcXFxcXFxcXGIpXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwic3VwcG9ydC50eXBlIHN0cm9uZ1xcXCJ9fSxcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjbGl0ZXJhbENsYXNzVHlwZVxcXCJ9XX0se1xcXCJiZWdpblxcXCI6XFxcIig/PD0oPzpbXiNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXT18Xj0pKSg/IVsjXFxcXFxcXFwtOiE/LkAqL1xcXFxcXFxcJiVeKzw9Pnx+JF0pXFxcIixcXFwiZW5kXFxcIjpcXFwiXFxcXFxcXFxiYW5kXFxcXFxcXFxifCg/PTs7fFt9KVxcXFxcXFxcXV18XFxcXFxcXFxiKD86ZW5kfGFuZHxjbGFzc3xleGNlcHRpb258ZXh0ZXJuYWx8aW58aW5jbHVkZXxpbmhlcml0fGluaXRpYWxpemVyfGxldHxtZXRob2R8bW9kdWxlfG9wZW58dHlwZXx2YWwpXFxcXFxcXFxiKVxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInZhcmlhYmxlLm90aGVyLmNsYXNzLmpzIG1lc3NhZ2UuZXJyb3IgdmFyaWFibGUuaW50ZXJwb2xhdGlvbiBzdHJpbmcucmVnZXhwIG1hcmt1cC51bmRlcmxpbmVcXFwifX0sXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3Rlcm1cXFwifV19XX0sXFxcImJpbmRDbGFzc1R5cGVcXFwiOntcXFwicGF0dGVybnNcXFwiOlt7XFxcImJlZ2luXFxcIjpcXFwiKD88PSg/OlxcXFxcXFxcUHt3b3JkfXxeKSg/OmFuZHxjbGFzc3x0eXBlKSkoPyFcXFxcXFxcXHB7d29yZH0pXFxcIixcXFwiZW5kXFxcIjpcXFwiKD88IVsjXFxcXFxcXFwtOiE/LkAqL1xcXFxcXFxcJiVeKzw9Pnx+JF0pKDopfCg9KSg/IVsjXFxcXFxcXFwtOiE/LkAqL1xcXFxcXFxcJiVeKzw9Pnx+JF0pfCg/PTs7fFt9KVxcXFxcXFxcXV18XFxcXFxcXFxiKD86ZW5kfGFuZHxjbGFzc3xleGNlcHRpb258ZXh0ZXJuYWx8aW58aW5jbHVkZXxpbmhlcml0fGluaXRpYWxpemVyfGxldHxtZXRob2R8bW9kdWxlfG9wZW58dHlwZXx2YWwpXFxcXFxcXFxiKVxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcInZhcmlhYmxlLm90aGVyLmNsYXNzLmpzIG1lc3NhZ2UuZXJyb3IgdmFyaWFibGUuaW50ZXJwb2xhdGlvbiBzdHJpbmcucmVnZXhwIHN0cm9uZ1xcXCJ9LFxcXCIyXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJzdXBwb3J0LnR5cGUgc3Ryb25nXFxcIn19LFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiYmVnaW5cXFwiOlxcXCIoPzw9KD86XFxcXFxcXFxQe3dvcmR9fF4pKD86YW5kfGNsYXNzfHR5cGUpKSg/IVxcXFxcXFxccHt3b3JkfSlcXFwiLFxcXCJlbmRcXFwiOlxcXCIoPz0oPyFcXFxcXFxcXGIoPzphbmR8J3xhc3xhc3J8YXNzZXJ0fFxcXFxcXFxcKnxiZWdpbnxjbGFzc3xbOixAXXxjb25zdHJhaW50fGRvfGRvbmV8ZG93bnRvfGVsc2V8ZW5kfD18ZXhjZXB0aW9ufGV4dGVybmFsfGZhbHNlfGZvcnxcXFxcXFxcXC58ZnVufGZ1bmN0aW9ufGZ1bmN0b3J8Wz4tXXxpZnxpbnxpbmNsdWRlfGluaGVyaXR8aW5pdGlhbGl6ZXJ8bGFuZHxsYXp5fFt7KFxcXFxcXFxcWzxdfGxldHxsb3J8bHNsfGxzcnxseG9yfG1hdGNofG1ldGhvZHxtb2R8bW9kdWxlfG11dGFibGV8bmV3fG5vbnJlY3wjfG9iamVjdHxvZnxvcGVufG9yfFslK118cHJpdmF0ZXxbP1xcXFxcXFwiXXxyZWN8W1xcXFxcXFxcXFxcXFxcXFx9KVxcXFxcXFxcXTtdfHNpZ3wvfHN0cnVjdHx0aGVufH58dG98dHJ1ZXx0cnl8dHlwZXx2YWx8XFxcXFxcXFx8fHZpcnR1YWx8d2hlbnx3aGlsZXx3aXRoKVxcXFxcXFxcYig/OlteJ118JCkpXFxcXFxcXFxiKD89W1s6bG93ZXI6XV9dKVtbOmFscGhhOl1fXVtbOndvcmQ6XSddKlxcXFxcXFxccyosfFteXFxcXFxcXFxzWzpsb3dlcjpdJV0pfCg/IVxcXFxcXFxcYig/OmFuZHwnfGFzfGFzcnxhc3NlcnR8XFxcXFxcXFwqfGJlZ2lufGNsYXNzfFs6LEBdfGNvbnN0cmFpbnR8ZG98ZG9uZXxkb3dudG98ZWxzZXxlbmR8PXxleGNlcHRpb258ZXh0ZXJuYWx8ZmFsc2V8Zm9yfFxcXFxcXFxcLnxmdW58ZnVuY3Rpb258ZnVuY3RvcnxbPi1dfGlmfGlufGluY2x1ZGV8aW5oZXJpdHxpbml0aWFsaXplcnxsYW5kfGxhenl8W3soXFxcXFxcXFxbPF18bGV0fGxvcnxsc2x8bHNyfGx4b3J8bWF0Y2h8bWV0aG9kfG1vZHxtb2R1bGV8bXV0YWJsZXxuZXd8bm9ucmVjfCN8b2JqZWN0fG9mfG9wZW58b3J8WyUrXXxwcml2YXRlfFs/XFxcXFxcXCJdfHJlY3xbXFxcXFxcXFxcXFxcXFxcXH0pXFxcXFxcXFxdO118c2lnfC98c3RydWN0fHRoZW58fnx0b3x0cnVlfHRyeXx0eXBlfHZhbHxcXFxcXFxcXHx8dmlydHVhbHx3aGVufHdoaWxlfHdpdGgpXFxcXFxcXFxiKD86W14nXXwkKSlcXFxcXFxcXGIoPz1bWzpsb3dlcjpdX10pW1s6YWxwaGE6XV9dW1s6d29yZDpdJ10qfCg/PVxcXFxcXFxcYnR5cGVcXFxcXFxcXGIpXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwiZW50aXR5Lm5hbWUuZnVuY3Rpb24gc3Ryb25nIGVtcGhhc2lzXFxcIn19LFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNhdHRyaWJ1dGVJZGVudGlmaWVyXFxcIn1dfSx7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXFxbXFxcIixcXFwiY2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uZGVmaW5pdGlvbi50YWdcXFwifX0sXFxcImVuZFxcXCI6XFxcIl1cXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiN0eXBlXFxcIn1dfSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjYmluZFRlcm1BcmdzXFxcIn1dfSx7XFxcImJlZ2luXFxcIjpcXFwiKD88PSg/OlteI1xcXFxcXFxcLTohPy5AKi9cXFxcXFxcXCYlXis8PT58fiRdOnxeOikpKD8hWyNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXSlcXFwiLFxcXCJlbmRcXFwiOlxcXCIoPzwhWyNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXSk9KD8hWyNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXSl8KD89W30pXFxcXFxcXFxdXXxcXFxcXFxcXGIoPzplbmR8YW5kfGNsYXNzfGV4Y2VwdGlvbnxleHRlcm5hbHxpbnxpbmNsdWRlfGluaGVyaXR8aW5pdGlhbGl6ZXJ8bGV0fG1ldGhvZHxtb2R1bGV8b3Blbnx2YWwpXFxcXFxcXFxiKVxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInN1cHBvcnQudHlwZSBzdHJvbmdcXFwifX0sXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2xpdGVyYWxDbGFzc1R5cGVcXFwifV19LHtcXFwiYmVnaW5cXFwiOlxcXCIoPzw9KD86W14jXFxcXFxcXFwtOiE/LkAqL1xcXFxcXFxcJiVeKzw9Pnx+JF09fF49KSkoPyFbI1xcXFxcXFxcLTohPy5AKi9cXFxcXFxcXCYlXis8PT58fiRdKVxcXCIsXFxcImVuZFxcXCI6XFxcIlxcXFxcXFxcYmFuZFxcXFxcXFxcYnwoPz07O3xbfSlcXFxcXFxcXF1dfFxcXFxcXFxcYig/OmVuZHxhbmR8Y2xhc3N8ZXhjZXB0aW9ufGV4dGVybmFsfGlufGluY2x1ZGV8aW5oZXJpdHxpbml0aWFsaXplcnxsZXR8bWV0aG9kfG1vZHVsZXxvcGVufHR5cGV8dmFsKVxcXFxcXFxcYilcXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJ2YXJpYWJsZS5vdGhlci5jbGFzcy5qcyBtZXNzYWdlLmVycm9yIHZhcmlhYmxlLmludGVycG9sYXRpb24gc3RyaW5nLnJlZ2V4cCBtYXJrdXAudW5kZXJsaW5lXFxcIn19LFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNsaXRlcmFsQ2xhc3NUeXBlXFxcIn1dfV19LFxcXCJiaW5kQ29uc3RydWN0b3JcXFwiOntcXFwicGF0dGVybnNcXFwiOlt7XFxcImJlZ2luXFxcIjpcXFwiKD88PSg/OlxcXFxcXFxcUHt3b3JkfWV4Y2VwdGlvbnxeZXhjZXB0aW9uKSkoPyFcXFxcXFxcXHB7d29yZH0pfCg/PD0oPzpbXiNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXVxcXFxcXFxcKz18XlxcXFxcXFxcKz18W14jXFxcXFxcXFwtOiE/LkAqL1xcXFxcXFxcJiVeKzw9Pnx+JF09fF49fFteI1xcXFxcXFxcLTohPy5AKi9cXFxcXFxcXCYlXis8PT58fiRdXFxcXFxcXFx8fF5cXFxcXFxcXHwpKSg/IVsjXFxcXFxcXFwtOiE/LkAqL1xcXFxcXFxcJiVeKzw9Pnx+JF0pXFxcIixcXFwiZW5kXFxcIjpcXFwiKDopfChcXFxcXFxcXGJvZlxcXFxcXFxcYil8KCg/PCFbI1xcXFxcXFxcLTohPy5AKi9cXFxcXFxcXCYlXis8PT58fiRdKVxcXFxcXFxcfCg/IVsjXFxcXFxcXFwtOiE/LkAqL1xcXFxcXFxcJiVeKzw9Pnx+JF0pKXwoPz07O3xbfSlcXFxcXFxcXF1dfFxcXFxcXFxcYig/OmVuZHxhbmR8Y2xhc3N8ZXhjZXB0aW9ufGV4dGVybmFsfGlufGluY2x1ZGV8aW5oZXJpdHxpbml0aWFsaXplcnxsZXR8bWV0aG9kfG1vZHVsZXxvcGVufHR5cGV8dmFsKVxcXFxcXFxcYilcXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJ2YXJpYWJsZS5vdGhlci5jbGFzcy5qcyBtZXNzYWdlLmVycm9yIHZhcmlhYmxlLmludGVycG9sYXRpb24gc3RyaW5nLnJlZ2V4cCBzdHJvbmdcXFwifSxcXFwiMlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uZGVmaW5pdGlvbi50YWdcXFwifSxcXFwiM1xcXCI6e1xcXCJuYW1lXFxcIjpcXFwic3VwcG9ydC50eXBlIHN0cm9uZ1xcXCJ9fSxcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjYXR0cmlidXRlSWRlbnRpZmllclxcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCJcXFxcXFxcXC5cXFxcXFxcXC5cXFwiLFxcXCJuYW1lXFxcIjpcXFwidmFyaWFibGUub3RoZXIuY2xhc3MuanMgbWVzc2FnZS5lcnJvciB2YXJpYWJsZS5pbnRlcnBvbGF0aW9uIHN0cmluZy5yZWdleHBcXFwifSx7XFxcIm1hdGNoXFxcIjpcXFwiXFxcXFxcXFxiXFxcXFxcXFxiKD89XFxcXFxcXFxwe3VwcGVyfSlbWzphbHBoYTpdX11bWzp3b3JkOl0nXSpcXFxcXFxcXGIoPyFcXFxcXFxcXHMqKD86XFxcXFxcXFwufFxcXFxcXFxcKFteKl0pKVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJjb25zdGFudC5sYW5ndWFnZSBjb25zdGFudC5udW1lcmljIGVudGl0eS5vdGhlci5hdHRyaWJ1dGUtbmFtZS5pZC5jc3Mgc3Ryb25nXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3R5cGVcXFwifV19LHtcXFwiYmVnaW5cXFwiOlxcXCIoPzw9KD86W14jXFxcXFxcXFwtOiE/LkAqL1xcXFxcXFxcJiVeKzw9Pnx+JF06fF46KSkoPyFbI1xcXFxcXFxcLTohPy5AKi9cXFxcXFxcXCYlXis8PT58fiRdKXwoPzw9KD86XFxcXFxcXFxQe3dvcmR9b2Z8Xm9mKSkoPyFcXFxcXFxcXHB7d29yZH0pXFxcIixcXFwiZW5kXFxcIjpcXFwiKD88IVsjXFxcXFxcXFwtOiE/LkAqL1xcXFxcXFxcJiVeKzw9Pnx+JF0pXFxcXFxcXFx8KD8hWyNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXSl8KD89Ozt8W30pXFxcXFxcXFxdXXxcXFxcXFxcXGIoPzplbmR8YW5kfGNsYXNzfGV4Y2VwdGlvbnxleHRlcm5hbHxpbnxpbmNsdWRlfGluaGVyaXR8aW5pdGlhbGl6ZXJ8bGV0fG1ldGhvZHxtb2R1bGV8b3Blbnx0eXBlfHZhbClcXFxcXFxcXGIpXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwic3VwcG9ydC50eXBlIHN0cm9uZ1xcXCJ9fSxcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjdHlwZVxcXCJ9XX1dfSxcXFwiYmluZFNpZ25hdHVyZVxcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNjb21tZW50XFxcIn0se1xcXCJiZWdpblxcXCI6XFxcIig/PD0oPzpcXFxcXFxcXFB7d29yZH10eXBlfF50eXBlKSkoPyFcXFxcXFxcXHB7d29yZH0pXFxcIixcXFwiZW5kXFxcIjpcXFwiKD88IVsjXFxcXFxcXFwtOiE/LkAqL1xcXFxcXFxcJiVeKzw9Pnx+JF0pPSg/IVsjXFxcXFxcXFwtOiE/LkAqL1xcXFxcXFxcJiVeKzw9Pnx+JF0pXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwic3VwcG9ydC50eXBlIHN0cm9uZ1xcXCJ9fSxcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjY29tbWVudFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNwYXRoTW9kdWxlRXh0ZW5kZWRcXFwifV19LHtcXFwiYmVnaW5cXFwiOlxcXCIoPzw9KD86W14jXFxcXFxcXFwtOiE/LkAqL1xcXFxcXFxcJiVeKzw9Pnx+JF09fF49KSkoPyFbI1xcXFxcXFxcLTohPy5AKi9cXFxcXFxcXCYlXis8PT58fiRdKVxcXCIsXFxcImVuZFxcXCI6XFxcIlxcXFxcXFxcYmFuZFxcXFxcXFxcYnwoPz07O3xbfSlcXFxcXFxcXF1dfFxcXFxcXFxcYig/OmVuZHxhbmR8Y2xhc3N8ZXhjZXB0aW9ufGV4dGVybmFsfGlufGluY2x1ZGV8aW5oZXJpdHxpbml0aWFsaXplcnxsZXR8bWV0aG9kfG1vZHVsZXxvcGVufHR5cGV8dmFsKVxcXFxcXFxcYilcXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJ2YXJpYWJsZS5vdGhlci5jbGFzcy5qcyBtZXNzYWdlLmVycm9yIHZhcmlhYmxlLmludGVycG9sYXRpb24gc3RyaW5nLnJlZ2V4cCBtYXJrdXAudW5kZXJsaW5lXFxcIn19LFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNzaWduYXR1cmVcXFwifV19XX0sXFxcImJpbmRTdHJ1Y3R1cmVcXFwiOntcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjY29tbWVudFxcXCJ9LHtcXFwiYmVnaW5cXFwiOlxcXCIoPzw9KD86XFxcXFxcXFxQe3dvcmR9YW5kfF5hbmQpKSg/IVxcXFxcXFxccHt3b3JkfSl8KD89XFxcXFxcXFxwe3VwcGVyfSlcXFwiLFxcXCJlbmRcXFwiOlxcXCIoPzwhWyNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXSkoOig/IT0pKXwoOj89KSg/IVsjXFxcXFxcXFwtOiE/LkAqL1xcXFxcXFxcJiVeKzw9Pnx+JF0pfCg/PVt9KVxcXFxcXFxcXV18XFxcXFxcXFxiKD86ZW5kfGFuZHxjbGFzc3xleGNlcHRpb258ZXh0ZXJuYWx8aW58aW5jbHVkZXxpbmhlcml0fGluaXRpYWxpemVyfGxldHxtZXRob2R8b3Blbnx0eXBlfHZhbClcXFxcXFxcXGIpXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwidmFyaWFibGUub3RoZXIuY2xhc3MuanMgbWVzc2FnZS5lcnJvciB2YXJpYWJsZS5pbnRlcnBvbGF0aW9uIHN0cmluZy5yZWdleHAgc3Ryb25nXFxcIn0sXFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcInN1cHBvcnQudHlwZSBzdHJvbmdcXFwifX0sXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2NvbW1lbnRcXFwifSx7XFxcIm1hdGNoXFxcIjpcXFwiXFxcXFxcXFxibW9kdWxlXFxcXFxcXFxiXFxcIixcXFwibmFtZVxcXCI6XFxcIm1hcmt1cC5pbnNlcnRlZCBjb25zdGFudC5sYW5ndWFnZSBzdXBwb3J0LmNvbnN0YW50LnByb3BlcnR5LXZhbHVlIGVudGl0eS5uYW1lLmZpbGVuYW1lXFxcIn0se1xcXCJtYXRjaFxcXCI6XFxcIlxcXFxcXFxcYig/PVxcXFxcXFxccHt1cHBlcn0pW1s6YWxwaGE6XV9dW1s6d29yZDpdJ10qXFxcIixcXFwibmFtZVxcXCI6XFxcImVudGl0eS5uYW1lLmZ1bmN0aW9uIHN0cm9uZyBlbXBoYXNpc1xcXCJ9LHtcXFwiYmVnaW5cXFwiOlxcXCJcXFxcXFxcXCgoPyFcXFxcXFxcXCkpXFxcIixcXFwiY2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uZGVmaW5pdGlvbi50YWdcXFwifX0sXFxcImVuZFxcXCI6XFxcIlxcXFxcXFxcKVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2NvbW1lbnRcXFwifSx7XFxcImJlZ2luXFxcIjpcXFwiKD88IVsjXFxcXFxcXFwtOiE/LkAqL1xcXFxcXFxcJiVeKzw9Pnx+JF0pOig/IVsjXFxcXFxcXFwtOiE/LkAqL1xcXFxcXFxcJiVeKzw9Pnx+JF0pXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJ2YXJpYWJsZS5vdGhlci5jbGFzcy5qcyBtZXNzYWdlLmVycm9yIHZhcmlhYmxlLmludGVycG9sYXRpb24gc3RyaW5nLnJlZ2V4cCBzdHJvbmdcXFwifX0sXFxcImVuZFxcXCI6XFxcIig/PVxcXFxcXFxcKSlcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNzaWduYXR1cmVcXFwifV19LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiN2YXJpYWJsZU1vZHVsZVxcXCJ9XX0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2xpdGVyYWxVbml0XFxcIn1dfSx7XFxcImJlZ2luXFxcIjpcXFwiKD88PSg/OlteI1xcXFxcXFxcLTohPy5AKi9cXFxcXFxcXCYlXis8PT58fiRdOnxeOikpKD8hWyNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXSlcXFwiLFxcXCJlbmRcXFwiOlxcXCJcXFxcXFxcXGIoYW5kKVxcXFxcXFxcYnwoKD88IVsjXFxcXFxcXFwtOiE/LkAqL1xcXFxcXFxcJiVeKzw9Pnx+JF0pPSg/IVsjXFxcXFxcXFwtOiE/LkAqL1xcXFxcXFxcJiVeKzw9Pnx+JF0pKXwoPz07O3xbfSlcXFxcXFxcXF1dfFxcXFxcXFxcYig/OmVuZHxhbmR8Y2xhc3N8ZXhjZXB0aW9ufGV4dGVybmFsfGlufGluY2x1ZGV8aW5oZXJpdHxpbml0aWFsaXplcnxsZXR8bWV0aG9kfG1vZHVsZXxvcGVufHR5cGV8dmFsKVxcXFxcXFxcYilcXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJ2YXJpYWJsZS5vdGhlci5jbGFzcy5qcyBtZXNzYWdlLmVycm9yIHZhcmlhYmxlLmludGVycG9sYXRpb24gc3RyaW5nLnJlZ2V4cCBtYXJrdXAudW5kZXJsaW5lXFxcIn0sXFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcInN1cHBvcnQudHlwZSBzdHJvbmdcXFwifX0sXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3NpZ25hdHVyZVxcXCJ9XX0se1xcXCJiZWdpblxcXCI6XFxcIig/PD0oPzpbXiNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXTo9fF46PXxbXiNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXT18Xj0pKSg/IVsjXFxcXFxcXFwtOiE/LkAqL1xcXFxcXFxcJiVeKzw9Pnx+JF0pXFxcIixcXFwiZW5kXFxcIjpcXFwiXFxcXFxcXFxiKD86KGFuZCl8KHdpdGgpKVxcXFxcXFxcYnwoPz07O3xbfSlcXFxcXFxcXF1dfFxcXFxcXFxcYig/OmVuZHxhbmR8Y2xhc3N8ZXhjZXB0aW9ufGV4dGVybmFsfGlufGluY2x1ZGV8aW5oZXJpdHxpbml0aWFsaXplcnxsZXR8bWV0aG9kfG1vZHVsZXxvcGVufHR5cGV8dmFsKVxcXFxcXFxcYilcXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJ2YXJpYWJsZS5vdGhlci5jbGFzcy5qcyBtZXNzYWdlLmVycm9yIHZhcmlhYmxlLmludGVycG9sYXRpb24gc3RyaW5nLnJlZ2V4cCBtYXJrdXAudW5kZXJsaW5lXFxcIn0sXFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcInZhcmlhYmxlLm90aGVyLmNsYXNzLmpzIG1lc3NhZ2UuZXJyb3IgdmFyaWFibGUuaW50ZXJwb2xhdGlvbiBzdHJpbmcucmVnZXhwIG1hcmt1cC51bmRlcmxpbmVcXFwifX0sXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3N0cnVjdHVyZVxcXCJ9XX1dfSxcXFwiYmluZFRlcm1cXFwiOntcXFwicGF0dGVybnNcXFwiOlt7XFxcImJlZ2luXFxcIjpcXFwiKD88PSg/OlteI1xcXFxcXFxcLTohPy5AKi9cXFxcXFxcXCYlXis8PT58fiRdIXxeISkpKD8hWyNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXSl8KD88PSg/OlxcXFxcXFxcUHt3b3JkfXxeKSg/OmFuZHxleHRlcm5hbHxsZXR8bWV0aG9kfHZhbCkpKD8hXFxcXFxcXFxwe3dvcmR9KVxcXCIsXFxcImVuZFxcXCI6XFxcIihcXFxcXFxcXGJtb2R1bGVcXFxcXFxcXGIpfChcXFxcXFxcXGJvcGVuXFxcXFxcXFxiKXwoPzwhWyNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXSkoOil8KCg/PCFbI1xcXFxcXFxcLTohPy5AKi9cXFxcXFxcXCYlXis8PT58fiRdKT0oPyFbI1xcXFxcXFxcLTohPy5AKi9cXFxcXFxcXCYlXis8PT58fiRdKSkoPyFbI1xcXFxcXFxcLTohPy5AKi9cXFxcXFxcXCYlXis8PT58fiRdKXwoPz07O3xbfSlcXFxcXFxcXF1dfFxcXFxcXFxcYig/OmVuZHxhbmR8Y2xhc3N8ZXhjZXB0aW9ufGV4dGVybmFsfGlufGluY2x1ZGV8aW5oZXJpdHxpbml0aWFsaXplcnxsZXR8bWV0aG9kfG1vZHVsZXxvcGVufHR5cGV8dmFsKVxcXFxcXFxcYilcXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJtYXJrdXAuaW5zZXJ0ZWQgY29uc3RhbnQubGFuZ3VhZ2Ugc3VwcG9ydC5jb25zdGFudC5wcm9wZXJ0eS12YWx1ZSBlbnRpdHkubmFtZS5maWxlbmFtZVxcXCJ9LFxcXCIyXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJ2YXJpYWJsZS5vdGhlci5jbGFzcy5qcyBtZXNzYWdlLmVycm9yIHZhcmlhYmxlLmludGVycG9sYXRpb24gc3RyaW5nLnJlZ2V4cFxcXCJ9LFxcXCIzXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJ2YXJpYWJsZS5vdGhlci5jbGFzcy5qcyBtZXNzYWdlLmVycm9yIHZhcmlhYmxlLmludGVycG9sYXRpb24gc3RyaW5nLnJlZ2V4cCBzdHJvbmdcXFwifSxcXFwiNFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwic3VwcG9ydC50eXBlIHN0cm9uZ1xcXCJ9fSxcXFwicGF0dGVybnNcXFwiOlt7XFxcImJlZ2luXFxcIjpcXFwiKD88PSg/OlteI1xcXFxcXFxcLTohPy5AKi9cXFxcXFxcXCYlXis8PT58fiRdIXxeISkpKD8hWyNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXSl8KD88PSg/OlxcXFxcXFxcUHt3b3JkfXxeKSg/OmFuZHxleHRlcm5hbHxsZXR8bWV0aG9kfHZhbCkpKD8hXFxcXFxcXFxwe3dvcmR9KVxcXCIsXFxcImVuZFxcXCI6XFxcIig/PVxcXFxcXFxcYig/Om1vZHVsZXxvcGVuKVxcXFxcXFxcYil8KD89KD8hXFxcXFxcXFxiKD86YW5kfCd8YXN8YXNyfGFzc2VydHxcXFxcXFxcXCp8YmVnaW58Y2xhc3N8WzosQF18Y29uc3RyYWludHxkb3xkb25lfGRvd250b3xlbHNlfGVuZHw9fGV4Y2VwdGlvbnxleHRlcm5hbHxmYWxzZXxmb3J8XFxcXFxcXFwufGZ1bnxmdW5jdGlvbnxmdW5jdG9yfFs+LV18aWZ8aW58aW5jbHVkZXxpbmhlcml0fGluaXRpYWxpemVyfGxhbmR8bGF6eXxbeyhcXFxcXFxcXFs8XXxsZXR8bG9yfGxzbHxsc3J8bHhvcnxtYXRjaHxtZXRob2R8bW9kfG1vZHVsZXxtdXRhYmxlfG5ld3xub25yZWN8I3xvYmplY3R8b2Z8b3BlbnxvcnxbJStdfHByaXZhdGV8Wz9cXFxcXFxcIl18cmVjfFtcXFxcXFxcXFxcXFxcXFxcfSlcXFxcXFxcXF07XXxzaWd8L3xzdHJ1Y3R8dGhlbnx+fHRvfHRydWV8dHJ5fHR5cGV8dmFsfFxcXFxcXFxcfHx2aXJ0dWFsfHdoZW58d2hpbGV8d2l0aClcXFxcXFxcXGIoPzpbXiddfCQpKVxcXFxcXFxcYig/PVtbOmxvd2VyOl1fXSlbWzphbHBoYTpdX11bWzp3b3JkOl0nXSpcXFxcXFxcXHMqLHxbXlxcXFxcXFxcc1s6bG93ZXI6XSVdKXwoXFxcXFxcXFxicmVjXFxcXFxcXFxiKXwoKD8hXFxcXFxcXFxiKD86YW5kfCd8YXN8YXNyfGFzc2VydHxcXFxcXFxcXCp8YmVnaW58Y2xhc3N8WzosQF18Y29uc3RyYWludHxkb3xkb25lfGRvd250b3xlbHNlfGVuZHw9fGV4Y2VwdGlvbnxleHRlcm5hbHxmYWxzZXxmb3J8XFxcXFxcXFwufGZ1bnxmdW5jdGlvbnxmdW5jdG9yfFs+LV18aWZ8aW58aW5jbHVkZXxpbmhlcml0fGluaXRpYWxpemVyfGxhbmR8bGF6eXxbeyhcXFxcXFxcXFs8XXxsZXR8bG9yfGxzbHxsc3J8bHhvcnxtYXRjaHxtZXRob2R8bW9kfG1vZHVsZXxtdXRhYmxlfG5ld3xub25yZWN8I3xvYmplY3R8b2Z8b3BlbnxvcnxbJStdfHByaXZhdGV8Wz9cXFxcXFxcIl18cmVjfFtcXFxcXFxcXFxcXFxcXFxcfSlcXFxcXFxcXF07XXxzaWd8L3xzdHJ1Y3R8dGhlbnx+fHRvfHRydWV8dHJ5fHR5cGV8dmFsfFxcXFxcXFxcfHx2aXJ0dWFsfHdoZW58d2hpbGV8d2l0aClcXFxcXFxcXGIoPzpbXiddfCQpKVxcXFxcXFxcYig/PVtbOmxvd2VyOl1fXSlbWzphbHBoYTpdX11bWzp3b3JkOl0nXSopXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwidmFyaWFibGUub3RoZXIuY2xhc3MuanMgbWVzc2FnZS5lcnJvciB2YXJpYWJsZS5pbnRlcnBvbGF0aW9uIHN0cmluZy5yZWdleHBcXFwifSxcXFwiMlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwiZW50aXR5Lm5hbWUuZnVuY3Rpb24gc3Ryb25nIGVtcGhhc2lzXFxcIn19LFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNhdHRyaWJ1dGVJZGVudGlmaWVyXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2NvbW1lbnRcXFwifV19LHtcXFwiYmVnaW5cXFwiOlxcXCIoPzw9KD86XFxcXFxcXFxQe3dvcmR9cmVjfF5yZWMpKSg/IVxcXFxcXFxccHt3b3JkfSlcXFwiLFxcXCJlbmRcXFwiOlxcXCIoKD8hXFxcXFxcXFxiKD86YW5kfCd8YXN8YXNyfGFzc2VydHxcXFxcXFxcXCp8YmVnaW58Y2xhc3N8WzosQF18Y29uc3RyYWludHxkb3xkb25lfGRvd250b3xlbHNlfGVuZHw9fGV4Y2VwdGlvbnxleHRlcm5hbHxmYWxzZXxmb3J8XFxcXFxcXFwufGZ1bnxmdW5jdGlvbnxmdW5jdG9yfFs+LV18aWZ8aW58aW5jbHVkZXxpbmhlcml0fGluaXRpYWxpemVyfGxhbmR8bGF6eXxbeyhcXFxcXFxcXFs8XXxsZXR8bG9yfGxzbHxsc3J8bHhvcnxtYXRjaHxtZXRob2R8bW9kfG1vZHVsZXxtdXRhYmxlfG5ld3xub25yZWN8I3xvYmplY3R8b2Z8b3BlbnxvcnxbJStdfHByaXZhdGV8Wz9cXFxcXFxcIl18cmVjfFtcXFxcXFxcXFxcXFxcXFxcfSlcXFxcXFxcXF07XXxzaWd8L3xzdHJ1Y3R8dGhlbnx+fHRvfHRydWV8dHJ5fHR5cGV8dmFsfFxcXFxcXFxcfHx2aXJ0dWFsfHdoZW58d2hpbGV8d2l0aClcXFxcXFxcXGIoPzpbXiddfCQpKVxcXFxcXFxcYig/PVtbOmxvd2VyOl1fXSlbWzphbHBoYTpdX11bWzp3b3JkOl0nXSopfCg/PVteXFxcXFxcXFxzWzphbHBoYTpdXSlcXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJlbnRpdHkubmFtZS5mdW5jdGlvbiBzdHJvbmcgZW1waGFzaXNcXFwifX0sXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2JpbmRUZXJtQXJnc1xcXCJ9XX0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2JpbmRUZXJtQXJnc1xcXCJ9XX0se1xcXCJiZWdpblxcXCI6XFxcIig/PD0oPzpcXFxcXFxcXFB7d29yZH1tb2R1bGV8Xm1vZHVsZSkpKD8hXFxcXFxcXFxwe3dvcmR9KVxcXCIsXFxcImVuZFxcXCI6XFxcIig/PTs7fFt9KVxcXFxcXFxcXV18XFxcXFxcXFxiKD86ZW5kfGFuZHxjbGFzc3xleGNlcHRpb258ZXh0ZXJuYWx8aW58aW5jbHVkZXxpbmhlcml0fGluaXRpYWxpemVyfGxldHxtZXRob2R8bW9kdWxlfG9wZW58dHlwZXx2YWwpXFxcXFxcXFxiKVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2RlY2xNb2R1bGVcXFwifV19LHtcXFwiYmVnaW5cXFwiOlxcXCIoPzw9KD86XFxcXFxcXFxQe3dvcmR9b3Blbnxeb3BlbikpKD8hXFxcXFxcXFxwe3dvcmR9KVxcXCIsXFxcImVuZFxcXCI6XFxcIig/PVxcXFxcXFxcYmluXFxcXFxcXFxiKXwoPz1bfSlcXFxcXFxcXF1dfFxcXFxcXFxcYig/OmVuZHxhbmR8Y2xhc3N8ZXhjZXB0aW9ufGV4dGVybmFsfGlufGluY2x1ZGV8aW5oZXJpdHxpbml0aWFsaXplcnxsZXR8bWV0aG9kfG1vZHVsZXxvcGVufHR5cGV8dmFsKVxcXFxcXFxcYilcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNwYXRoTW9kdWxlU2ltcGxlXFxcIn1dfSx7XFxcImJlZ2luXFxcIjpcXFwiKD88PSg/OlteI1xcXFxcXFxcLTohPy5AKi9cXFxcXFxcXCYlXis8PT58fiRdOnxeOikpKD8hWyNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXSlcXFwiLFxcXCJlbmRcXFwiOlxcXCIoPzwhWyNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXSk9KD8hWyNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXSl8KD89Ozt8W30pXFxcXFxcXFxdXXxcXFxcXFxcXGIoPzplbmR8YW5kfGNsYXNzfGV4Y2VwdGlvbnxleHRlcm5hbHxpbnxpbmNsdWRlfGluaGVyaXR8aW5pdGlhbGl6ZXJ8bGV0fG1ldGhvZHxtb2R1bGV8b3Blbnx0eXBlfHZhbClcXFxcXFxcXGIpXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwic3VwcG9ydC50eXBlIHN0cm9uZ1xcXCJ9fSxcXFwicGF0dGVybnNcXFwiOlt7XFxcImJlZ2luXFxcIjpcXFwiKD88PSg/OlteI1xcXFxcXFxcLTohPy5AKi9cXFxcXFxcXCYlXis8PT58fiRdOnxeOikpKD8hWyNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXSlcXFwiLFxcXCJlbmRcXFwiOlxcXCJcXFxcXFxcXGJ0eXBlXFxcXFxcXFxifCg/PVxcXFxcXFxcUylcXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLmNvbnRyb2xcXFwifX19LHtcXFwiYmVnaW5cXFwiOlxcXCIoPzw9KD86XFxcXFxcXFxQe3dvcmR9dHlwZXxedHlwZSkpKD8hXFxcXFxcXFxwe3dvcmR9KVxcXCIsXFxcImVuZFxcXCI6XFxcIig/PCFbI1xcXFxcXFxcLTohPy5AKi9cXFxcXFxcXCYlXis8PT58fiRdKVxcXFxcXFxcLig/IVsjXFxcXFxcXFwtOiE/LkAqL1xcXFxcXFxcJiVeKzw9Pnx+JF0pXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwidmFyaWFibGUub3RoZXIuY2xhc3MuanMgbWVzc2FnZS5lcnJvciB2YXJpYWJsZS5pbnRlcnBvbGF0aW9uIHN0cmluZy5yZWdleHBcXFwifX0sXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3BhdHRlcm5cXFwifV19LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiN0eXBlXFxcIn1dfSx7XFxcImJlZ2luXFxcIjpcXFwiKD88PSg/OlteI1xcXFxcXFxcLTohPy5AKi9cXFxcXFxcXCYlXis8PT58fiRdPXxePSkpKD8hWyNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXSlcXFwiLFxcXCJlbmRcXFwiOlxcXCJcXFxcXFxcXGJhbmRcXFxcXFxcXGJ8KD89Ozt8W30pXFxcXFxcXFxdXXxcXFxcXFxcXGIoPzplbmR8YW5kfGNsYXNzfGV4Y2VwdGlvbnxleHRlcm5hbHxpbnxpbmNsdWRlfGluaGVyaXR8aW5pdGlhbGl6ZXJ8bGV0fG1ldGhvZHxtb2R1bGV8b3Blbnx0eXBlfHZhbClcXFxcXFxcXGIpXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwidmFyaWFibGUub3RoZXIuY2xhc3MuanMgbWVzc2FnZS5lcnJvciB2YXJpYWJsZS5pbnRlcnBvbGF0aW9uIHN0cmluZy5yZWdleHAgbWFya3VwLnVuZGVybGluZVxcXCJ9fSxcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjdGVybVxcXCJ9XX1dfSxcXFwiYmluZFRlcm1BcmdzXFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJhcHBseUVuZFBhdHRlcm5MYXN0XFxcIjp0cnVlLFxcXCJiZWdpblxcXCI6XFxcIlt+P11cXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInZhcmlhYmxlLm90aGVyLmNsYXNzLmpzIG1lc3NhZ2UuZXJyb3IgdmFyaWFibGUuaW50ZXJwb2xhdGlvbiBzdHJpbmcucmVnZXhwXFxcIn19LFxcXCJlbmRcXFwiOlxcXCI6fCg/PVxcXFxcXFxcUylcXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkXFxcIn19LFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiYmVnaW5cXFwiOlxcXCIoPzw9KD86W14jXFxcXFxcXFwtOiE/LkAqL1xcXFxcXFxcJiVeKzw9Pnx+JF1+fF5+fFteI1xcXFxcXFxcLTohPy5AKi9cXFxcXFxcXCYlXis8PT58fiRdXFxcXFxcXFw/fF5cXFxcXFxcXD8pKSg/IVsjXFxcXFxcXFwtOiE/LkAqL1xcXFxcXFxcJiVeKzw9Pnx+JF0pXFxcIixcXFwiZW5kXFxcIjpcXFwiKD8hXFxcXFxcXFxiKD86YW5kfCd8YXN8YXNyfGFzc2VydHxcXFxcXFxcXCp8YmVnaW58Y2xhc3N8WzosQF18Y29uc3RyYWludHxkb3xkb25lfGRvd250b3xlbHNlfGVuZHw9fGV4Y2VwdGlvbnxleHRlcm5hbHxmYWxzZXxmb3J8XFxcXFxcXFwufGZ1bnxmdW5jdGlvbnxmdW5jdG9yfFs+LV18aWZ8aW58aW5jbHVkZXxpbmhlcml0fGluaXRpYWxpemVyfGxhbmR8bGF6eXxbeyhcXFxcXFxcXFs8XXxsZXR8bG9yfGxzbHxsc3J8bHhvcnxtYXRjaHxtZXRob2R8bW9kfG1vZHVsZXxtdXRhYmxlfG5ld3xub25yZWN8I3xvYmplY3R8b2Z8b3BlbnxvcnxbJStdfHByaXZhdGV8Wz9cXFxcXFxcIl18cmVjfFtcXFxcXFxcXFxcXFxcXFxcfSlcXFxcXFxcXF07XXxzaWd8L3xzdHJ1Y3R8dGhlbnx+fHRvfHRydWV8dHJ5fHR5cGV8dmFsfFxcXFxcXFxcfHx2aXJ0dWFsfHdoZW58d2hpbGV8d2l0aClcXFxcXFxcXGIoPzpbXiddfCQpKVxcXFxcXFxcYig/PVtbOmxvd2VyOl1fXSlbWzphbHBoYTpdX11bWzp3b3JkOl0nXSp8KD88PVxcXFxcXFxcKSlcXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJtYXJrdXAuaW5zZXJ0ZWQgY29uc3RhbnQubGFuZ3VhZ2Ugc3VwcG9ydC5jb25zdGFudC5wcm9wZXJ0eS12YWx1ZSBlbnRpdHkubmFtZS5maWxlbmFtZVxcXCJ9fSxcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjY29tbWVudFxcXCJ9LHtcXFwiYmVnaW5cXFwiOlxcXCJcXFxcXFxcXCgoPyFcXFxcXFxcXCopXFxcIixcXFwiY2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uZGVmaW5pdGlvbi50YWdcXFwifX0sXFxcImVuZFxcXCI6XFxcIlxcXFxcXFxcKVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJiZWdpblxcXCI6XFxcIig/PD1cXFxcXFxcXCgpXFxcIixcXFwiZW5kXFxcIjpcXFwiWzo9XVxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmRcXFwifX0sXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJtYXRjaFxcXCI6XFxcIig/IVxcXFxcXFxcYig/OmFuZHwnfGFzfGFzcnxhc3NlcnR8XFxcXFxcXFwqfGJlZ2lufGNsYXNzfFs6LEBdfGNvbnN0cmFpbnR8ZG98ZG9uZXxkb3dudG98ZWxzZXxlbmR8PXxleGNlcHRpb258ZXh0ZXJuYWx8ZmFsc2V8Zm9yfFxcXFxcXFxcLnxmdW58ZnVuY3Rpb258ZnVuY3RvcnxbPi1dfGlmfGlufGluY2x1ZGV8aW5oZXJpdHxpbml0aWFsaXplcnxsYW5kfGxhenl8W3soXFxcXFxcXFxbPF18bGV0fGxvcnxsc2x8bHNyfGx4b3J8bWF0Y2h8bWV0aG9kfG1vZHxtb2R1bGV8bXV0YWJsZXxuZXd8bm9ucmVjfCN8b2JqZWN0fG9mfG9wZW58b3J8WyUrXXxwcml2YXRlfFs/XFxcXFxcXCJdfHJlY3xbXFxcXFxcXFxcXFxcXFxcXH0pXFxcXFxcXFxdO118c2lnfC98c3RydWN0fHRoZW58fnx0b3x0cnVlfHRyeXx0eXBlfHZhbHxcXFxcXFxcXHx8dmlydHVhbHx3aGVufHdoaWxlfHdpdGgpXFxcXFxcXFxiKD86W14nXXwkKSlcXFxcXFxcXGIoPz1bWzpsb3dlcjpdX10pW1s6YWxwaGE6XV9dW1s6d29yZDpdJ10qXFxcIixcXFwibmFtZVxcXCI6XFxcIm1hcmt1cC5pbnNlcnRlZCBjb25zdGFudC5sYW5ndWFnZSBzdXBwb3J0LmNvbnN0YW50LnByb3BlcnR5LXZhbHVlIGVudGl0eS5uYW1lLmZpbGVuYW1lXFxcIn1dfSx7XFxcImJlZ2luXFxcIjpcXFwiKD88PTopXFxcIixcXFwiZW5kXFxcIjpcXFwiPXwoPz1cXFxcXFxcXCkpXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZFxcXCJ9fSxcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjdHlwZVxcXCJ9XX0se1xcXCJiZWdpblxcXCI6XFxcIig/PD0oPzpbXiNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXT18Xj0pKSg/IVsjXFxcXFxcXFwtOiE/LkAqL1xcXFxcXFxcJiVeKzw9Pnx+JF0pXFxcIixcXFwiZW5kXFxcIjpcXFwiKD89XFxcXFxcXFwpKVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3Rlcm1cXFwifV19XX1dfV19LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNwYXR0ZXJuXFxcIn1dfSxcXFwiYmluZFR5cGVcXFwiOntcXFwicGF0dGVybnNcXFwiOlt7XFxcImJlZ2luXFxcIjpcXFwiKD88PSg/OlxcXFxcXFxcUHt3b3JkfXxeKSg/OmFuZHx0eXBlKSkoPyFcXFxcXFxcXHB7d29yZH0pXFxcIixcXFwiZW5kXFxcIjpcXFwiKD88IVsjXFxcXFxcXFwtOiE/LkAqL1xcXFxcXFxcJiVeKzw9Pnx+JF0pXFxcXFxcXFwrPXw9KD8hWyNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXSl8KD89Ozt8W30pXFxcXFxcXFxdXXxcXFxcXFxcXGIoPzplbmR8YW5kfGNsYXNzfGV4Y2VwdGlvbnxleHRlcm5hbHxpbnxpbmNsdWRlfGluaGVyaXR8aW5pdGlhbGl6ZXJ8bGV0fG1ldGhvZHxtb2R1bGV8b3Blbnx0eXBlfHZhbClcXFxcXFxcXGIpXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwic3VwcG9ydC50eXBlIHN0cm9uZ1xcXCJ9fSxcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjYXR0cmlidXRlSWRlbnRpZmllclxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNwYXRoVHlwZVxcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCIoPyFcXFxcXFxcXGIoPzphbmR8J3xhc3xhc3J8YXNzZXJ0fFxcXFxcXFxcKnxiZWdpbnxjbGFzc3xbOixAXXxjb25zdHJhaW50fGRvfGRvbmV8ZG93bnRvfGVsc2V8ZW5kfD18ZXhjZXB0aW9ufGV4dGVybmFsfGZhbHNlfGZvcnxcXFxcXFxcXC58ZnVufGZ1bmN0aW9ufGZ1bmN0b3J8Wz4tXXxpZnxpbnxpbmNsdWRlfGluaGVyaXR8aW5pdGlhbGl6ZXJ8bGFuZHxsYXp5fFt7KFxcXFxcXFxcWzxdfGxldHxsb3J8bHNsfGxzcnxseG9yfG1hdGNofG1ldGhvZHxtb2R8bW9kdWxlfG11dGFibGV8bmV3fG5vbnJlY3wjfG9iamVjdHxvZnxvcGVufG9yfFslK118cHJpdmF0ZXxbP1xcXFxcXFwiXXxyZWN8W1xcXFxcXFxcXFxcXFxcXFx9KVxcXFxcXFxcXTtdfHNpZ3wvfHN0cnVjdHx0aGVufH58dG98dHJ1ZXx0cnl8dHlwZXx2YWx8XFxcXFxcXFx8fHZpcnR1YWx8d2hlbnx3aGlsZXx3aXRoKVxcXFxcXFxcYig/OlteJ118JCkpXFxcXFxcXFxiKD89W1s6bG93ZXI6XV9dKVtbOmFscGhhOl1fXVtbOndvcmQ6XSddKlxcXCIsXFxcIm5hbWVcXFwiOlxcXCJlbnRpdHkubmFtZS5mdW5jdGlvbiBzdHJvbmdcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjdHlwZVxcXCJ9XX0se1xcXCJiZWdpblxcXCI6XFxcIig/PD0oPzpbXiNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXVxcXFxcXFxcKz18XlxcXFxcXFxcKz18W14jXFxcXFxcXFwtOiE/LkAqL1xcXFxcXFxcJiVeKzw9Pnx+JF09fF49KSkoPyFbI1xcXFxcXFxcLTohPy5AKi9cXFxcXFxcXCYlXis8PT58fiRdKVxcXCIsXFxcImVuZFxcXCI6XFxcIlxcXFxcXFxcYmFuZFxcXFxcXFxcYnwoPz07O3xbfSlcXFxcXFxcXF1dfFxcXFxcXFxcYig/OmVuZHxhbmR8Y2xhc3N8ZXhjZXB0aW9ufGV4dGVybmFsfGlufGluY2x1ZGV8aW5oZXJpdHxpbml0aWFsaXplcnxsZXR8bWV0aG9kfG1vZHVsZXxvcGVufHR5cGV8dmFsKVxcXFxcXFxcYilcXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJ2YXJpYWJsZS5vdGhlci5jbGFzcy5qcyBtZXNzYWdlLmVycm9yIHZhcmlhYmxlLmludGVycG9sYXRpb24gc3RyaW5nLnJlZ2V4cCBtYXJrdXAudW5kZXJsaW5lXFxcIn19LFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNiaW5kQ29uc3RydWN0b3JcXFwifV19XX0sXFxcImNvbW1lbnRcXFwiOntcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjYXR0cmlidXRlXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2V4dGVuc2lvblxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNjb21tZW50QmxvY2tcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjY29tbWVudERvY1xcXCJ9XX0sXFxcImNvbW1lbnRCbG9ja1xcXCI6e1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxcKFxcXFxcXFxcKig/IVxcXFxcXFxcKlteKV0pXFxcIixcXFwiY29udGVudE5hbWVcXFwiOlxcXCJlbXBoYXNpc1xcXCIsXFxcImVuZFxcXCI6XFxcIlxcXFxcXFxcKlxcXFxcXFxcKVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJjb21tZW50IGNvbnN0YW50LnJlZ2V4cCBtZXRhLnNlcGFyYXRvci5tYXJrZG93blxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2NvbW1lbnRCbG9ja1xcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNjb21tZW50RG9jXFxcIn1dfSxcXFwiY29tbWVudERvY1xcXCI6e1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxcKFxcXFxcXFxcKlxcXFxcXFxcKlxcXCIsXFxcImVuZFxcXCI6XFxcIlxcXFxcXFxcKlxcXFxcXFxcKVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJjb21tZW50IGNvbnN0YW50LnJlZ2V4cCBtZXRhLnNlcGFyYXRvci5tYXJrZG93blxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJtYXRjaFxcXCI6XFxcIlxcXFxcXFxcKlxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNjb21tZW50XFxcIn1dfSxcXFwiZGVjbFxcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNkZWNsQ2xhc3NcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjZGVjbEV4Y2VwdGlvblxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNkZWNsSW5jbHVkZVxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNkZWNsTW9kdWxlXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2RlY2xPcGVuXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2RlY2xUZXJtXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2RlY2xUeXBlXFxcIn1dfSxcXFwiZGVjbENsYXNzXFxcIjp7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXFxiY2xhc3NcXFxcXFxcXGJcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcImVudGl0eS5uYW1lLmNsYXNzIGNvbnN0YW50Lm51bWVyaWMgbWFya3VwLnVuZGVybGluZVxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiOzt8KD89W30pXFxcXFxcXFxdXXxcXFxcXFxcXGIoPzplbmR8YW5kfGNsYXNzfGV4Y2VwdGlvbnxleHRlcm5hbHxpbnxpbmNsdWRlfGluaGVyaXR8aW5pdGlhbGl6ZXJ8bGV0fG1ldGhvZHxtb2R1bGV8b3Blbnx0eXBlfHZhbClcXFxcXFxcXGIpXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uZGVmaW5pdGlvbi50YWdcXFwifX0sXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2NvbW1lbnRcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjcHJhZ21hXFxcIn0se1xcXCJiZWdpblxcXCI6XFxcIig/PD0oPzpcXFxcXFxcXFB7d29yZH1jbGFzc3xeY2xhc3MpKSg/IVxcXFxcXFxccHt3b3JkfSlcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcImVudGl0eS5uYW1lLmNsYXNzIGNvbnN0YW50Lm51bWVyaWMgbWFya3VwLnVuZGVybGluZVxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiXFxcXFxcXFxidHlwZVxcXFxcXFxcYnwoPz1bfSlcXFxcXFxcXF1dfFxcXFxcXFxcYig/OmVuZHxhbmR8Y2xhc3N8ZXhjZXB0aW9ufGV4dGVybmFsfGlufGluY2x1ZGV8aW5oZXJpdHxpbml0aWFsaXplcnxsZXR8bWV0aG9kfG1vZHVsZXxvcGVufHZhbClcXFxcXFxcXGIpXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZFxcXCJ9fSxcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjYmluZENsYXNzVGVybVxcXCJ9XX0se1xcXCJiZWdpblxcXCI6XFxcIig/PD0oPzpcXFxcXFxcXFB7d29yZH10eXBlfF50eXBlKSkoPyFcXFxcXFxcXHB7d29yZH0pXFxcIixcXFwiZW5kXFxcIjpcXFwiKD89Ozt8W30pXFxcXFxcXFxdXXxcXFxcXFxcXGIoPzplbmR8YW5kfGNsYXNzfGV4Y2VwdGlvbnxleHRlcm5hbHxpbnxpbmNsdWRlfGluaGVyaXR8aW5pdGlhbGl6ZXJ8bGV0fG1ldGhvZHxtb2R1bGV8b3Blbnx0eXBlfHZhbClcXFxcXFxcXGIpXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjYmluZENsYXNzVHlwZVxcXCJ9XX1dfSxcXFwiZGVjbEV4Y2VwdGlvblxcXCI6e1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxcYmV4Y2VwdGlvblxcXFxcXFxcYlxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZCBtYXJrdXAudW5kZXJsaW5lXFxcIn19LFxcXCJlbmRcXFwiOlxcXCI7O3woPz1bfSlcXFxcXFxcXF1dfFxcXFxcXFxcYig/OmVuZHxhbmR8Y2xhc3N8ZXhjZXB0aW9ufGV4dGVybmFsfGlufGluY2x1ZGV8aW5oZXJpdHxpbml0aWFsaXplcnxsZXR8bWV0aG9kfG1vZHVsZXxvcGVufHR5cGV8dmFsKVxcXFxcXFxcYilcXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLnRhZ1xcXCJ9fSxcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjYXR0cmlidXRlSWRlbnRpZmllclxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNjb21tZW50XFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3ByYWdtYVxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNiaW5kQ29uc3RydWN0b3JcXFwifV19LFxcXCJkZWNsSW5jbHVkZVxcXCI6e1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxcYmluY2x1ZGVcXFxcXFxcXGJcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInZhcmlhYmxlLm90aGVyLmNsYXNzLmpzIG1lc3NhZ2UuZXJyb3IgdmFyaWFibGUuaW50ZXJwb2xhdGlvbiBzdHJpbmcucmVnZXhwXFxcIn19LFxcXCJlbmRcXFwiOlxcXCI7O3woPz1bfSlcXFxcXFxcXF1dfFxcXFxcXFxcYig/OmVuZHxhbmR8Y2xhc3N8ZXhjZXB0aW9ufGV4dGVybmFsfGlufGluY2x1ZGV8aW5oZXJpdHxpbml0aWFsaXplcnxsZXR8bWV0aG9kfG1vZHVsZXxvcGVufHR5cGV8dmFsKVxcXFxcXFxcYilcXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLnRhZ1xcXCJ9fSxcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjYXR0cmlidXRlSWRlbnRpZmllclxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNjb21tZW50XFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3ByYWdtYVxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNzaWduYXR1cmVcXFwifV19LFxcXCJkZWNsTW9kdWxlXFxcIjp7XFxcImJlZ2luXFxcIjpcXFwiKD88PSg/OlxcXFxcXFxcUHt3b3JkfW1vZHVsZXxebW9kdWxlKSkoPyFcXFxcXFxcXHB7d29yZH0pfFxcXFxcXFxcYm1vZHVsZVxcXFxcXFxcYlxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwibWFya3VwLmluc2VydGVkIGNvbnN0YW50Lmxhbmd1YWdlIHN1cHBvcnQuY29uc3RhbnQucHJvcGVydHktdmFsdWUgZW50aXR5Lm5hbWUuZmlsZW5hbWUgbWFya3VwLnVuZGVybGluZVxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiOzt8KD89W30pXFxcXFxcXFxdXXxcXFxcXFxcXGIoPzplbmR8YW5kfGNsYXNzfGV4Y2VwdGlvbnxleHRlcm5hbHxpbnxpbmNsdWRlfGluaGVyaXR8aW5pdGlhbGl6ZXJ8bGV0fG1ldGhvZHxtb2R1bGV8b3Blbnx0eXBlfHZhbClcXFxcXFxcXGIpXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uZGVmaW5pdGlvbi50YWdcXFwifX0sXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2NvbW1lbnRcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjcHJhZ21hXFxcIn0se1xcXCJiZWdpblxcXCI6XFxcIig/PD0oPzpcXFxcXFxcXFB7d29yZH1tb2R1bGV8Xm1vZHVsZSkpKD8hXFxcXFxcXFxwe3dvcmR9KVxcXCIsXFxcImVuZFxcXCI6XFxcIihcXFxcXFxcXGJ0eXBlXFxcXFxcXFxiKXwoPz1cXFxcXFxcXHB7dXBwZXJ9KVxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmRcXFwifX0sXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2F0dHJpYnV0ZUlkZW50aWZpZXJcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjY29tbWVudFxcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCJcXFxcXFxcXGJyZWNcXFxcXFxcXGJcXFwiLFxcXCJuYW1lXFxcIjpcXFwidmFyaWFibGUub3RoZXIuY2xhc3MuanMgbWVzc2FnZS5lcnJvciB2YXJpYWJsZS5pbnRlcnBvbGF0aW9uIHN0cmluZy5yZWdleHBcXFwifV19LHtcXFwiYmVnaW5cXFwiOlxcXCIoPzw9KD86XFxcXFxcXFxQe3dvcmR9dHlwZXxedHlwZSkpKD8hXFxcXFxcXFxwe3dvcmR9KVxcXCIsXFxcImVuZFxcXCI6XFxcIig/PTs7fFt9KVxcXFxcXFxcXV18XFxcXFxcXFxiKD86ZW5kfGFuZHxjbGFzc3xleGNlcHRpb258ZXh0ZXJuYWx8aW58aW5jbHVkZXxpbmhlcml0fGluaXRpYWxpemVyfGxldHxtZXRob2R8bW9kdWxlfG9wZW58dHlwZXx2YWwpXFxcXFxcXFxiKVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2JpbmRTaWduYXR1cmVcXFwifV19LHtcXFwiYmVnaW5cXFwiOlxcXCIoPz1cXFxcXFxcXHB7dXBwZXJ9KVxcXCIsXFxcImVuZFxcXCI6XFxcIig/PTs7fFt9KVxcXFxcXFxcXV18XFxcXFxcXFxiKD86ZW5kfGFuZHxjbGFzc3xleGNlcHRpb258ZXh0ZXJuYWx8aW58aW5jbHVkZXxpbmhlcml0fGluaXRpYWxpemVyfGxldHxtZXRob2R8bW9kdWxlfG9wZW58dHlwZXx2YWwpXFxcXFxcXFxiKVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2JpbmRTdHJ1Y3R1cmVcXFwifV19XX0sXFxcImRlY2xPcGVuXFxcIjp7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXFxib3BlblxcXFxcXFxcYlxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwidmFyaWFibGUub3RoZXIuY2xhc3MuanMgbWVzc2FnZS5lcnJvciB2YXJpYWJsZS5pbnRlcnBvbGF0aW9uIHN0cmluZy5yZWdleHBcXFwifX0sXFxcImVuZFxcXCI6XFxcIjs7fCg/PVt9KVxcXFxcXFxcXV18XFxcXFxcXFxiKD86ZW5kfGFuZHxjbGFzc3xleGNlcHRpb258ZXh0ZXJuYWx8aW58aW5jbHVkZXxpbmhlcml0fGluaXRpYWxpemVyfGxldHxtZXRob2R8bW9kdWxlfG9wZW58dHlwZXx2YWwpXFxcXFxcXFxiKVxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24udGFnXFxcIn19LFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNhdHRyaWJ1dGVJZGVudGlmaWVyXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2NvbW1lbnRcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjcHJhZ21hXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3BhdGhNb2R1bGVFeHRlbmRlZFxcXCJ9XX0sXFxcImRlY2xUZXJtXFxcIjp7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXFxiKD86KGV4dGVybmFsfHZhbCl8KG1ldGhvZCl8KGxldCkpXFxcXFxcXFxiKCE/KVxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwic3VwcG9ydC50eXBlIG1hcmt1cC51bmRlcmxpbmVcXFwifSxcXFwiMlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwic3RvcmFnZS50eXBlIG1hcmt1cC51bmRlcmxpbmVcXFwifSxcXFwiM1xcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5jb250cm9sIG1hcmt1cC51bmRlcmxpbmVcXFwifSxcXFwiNFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwidmFyaWFibGUub3RoZXIuY2xhc3MuanMgbWVzc2FnZS5lcnJvciB2YXJpYWJsZS5pbnRlcnBvbGF0aW9uIHN0cmluZy5yZWdleHBcXFwifX0sXFxcImVuZFxcXCI6XFxcIjs7fCg/PVt9KVxcXFxcXFxcXV18XFxcXFxcXFxiKD86ZW5kfGFuZHxjbGFzc3xleGNlcHRpb258ZXh0ZXJuYWx8aW58aW5jbHVkZXxpbmhlcml0fGluaXRpYWxpemVyfGxldHxtZXRob2R8bW9kdWxlfG9wZW58dHlwZXx2YWwpXFxcXFxcXFxiKVxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24udGFnXFxcIn19LFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNjb21tZW50XFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3ByYWdtYVxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNiaW5kVGVybVxcXCJ9XX0sXFxcImRlY2xUeXBlXFxcIjp7XFxcImJlZ2luXFxcIjpcXFwiKD88PSg/OlxcXFxcXFxcUHt3b3JkfXR5cGV8XnR5cGUpKSg/IVxcXFxcXFxccHt3b3JkfSl8XFxcXFxcXFxidHlwZVxcXFxcXFxcYlxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZCBtYXJrdXAudW5kZXJsaW5lXFxcIn19LFxcXCJlbmRcXFwiOlxcXCI7O3woPz1bfSlcXFxcXFxcXF1dfFxcXFxcXFxcYig/OmVuZHxhbmR8Y2xhc3N8ZXhjZXB0aW9ufGV4dGVybmFsfGlufGluY2x1ZGV8aW5oZXJpdHxpbml0aWFsaXplcnxsZXR8bWV0aG9kfG1vZHVsZXxvcGVufHR5cGV8dmFsKVxcXFxcXFxcYilcXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLnRhZ1xcXCJ9fSxcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjY29tbWVudFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNwcmFnbWFcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjYmluZFR5cGVcXFwifV19LFxcXCJleHRlbnNpb25cXFwiOntcXFwiYmVnaW5cXFwiOlxcXCIoXFxcXFxcXFxbKSgoPzwhWyNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXSklezEsM30oPyFbI1xcXFxcXFxcLTohPy5AKi9cXFxcXFxcXCYlXis8PT58fiRdKSlcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcImNvbnN0YW50Lmxhbmd1YWdlIGNvbnN0YW50Lm51bWVyaWMgZW50aXR5Lm90aGVyLmF0dHJpYnV0ZS1uYW1lLmlkLmNzcyBzdHJvbmdcXFwifSxcXFwiMlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwidmFyaWFibGUub3RoZXIuY2xhc3MuanMgbWVzc2FnZS5lcnJvciB2YXJpYWJsZS5pbnRlcnBvbGF0aW9uIHN0cmluZy5yZWdleHBcXFwifX0sXFxcImVuZFxcXCI6XFxcIl1cXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJjb25zdGFudC5sYW5ndWFnZSBjb25zdGFudC5udW1lcmljIGVudGl0eS5vdGhlci5hdHRyaWJ1dGUtbmFtZS5pZC5jc3Mgc3Ryb25nXFxcIn19LFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNhdHRyaWJ1dGVQYXlsb2FkXFxcIn1dfSxcXFwibGl0ZXJhbFxcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiN0ZXJtQ29uc3RydWN0b3JcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjbGl0ZXJhbEFycmF5XFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2xpdGVyYWxCb29sZWFuXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2xpdGVyYWxDaGFyYWN0ZXJcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjbGl0ZXJhbExpc3RcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjbGl0ZXJhbE51bWJlclxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNsaXRlcmFsT2JqZWN0VGVybVxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNsaXRlcmFsU3RyaW5nXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2xpdGVyYWxSZWNvcmRcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjbGl0ZXJhbFVuaXRcXFwifV19LFxcXCJsaXRlcmFsQXJyYXlcXFwiOntcXFwiYmVnaW5cXFwiOlxcXCJcXFxcXFxcXFtcXFxcXFxcXHxcXFwiLFxcXCJjYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJjb25zdGFudC5sYW5ndWFnZSBjb25zdGFudC5udW1lcmljIGVudGl0eS5vdGhlci5hdHRyaWJ1dGUtbmFtZS5pZC5jc3Mgc3Ryb25nXFxcIn19LFxcXCJlbmRcXFwiOlxcXCJcXFxcXFxcXHxdXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjdGVybVxcXCJ9XX0sXFxcImxpdGVyYWxCb29sZWFuXFxcIjp7XFxcIm1hdGNoXFxcIjpcXFwiXFxcXFxcXFxiZmFsc2V8dHJ1ZVxcXFxcXFxcYlxcXCIsXFxcIm5hbWVcXFwiOlxcXCJjb25zdGFudC5sYW5ndWFnZSBjb25zdGFudC5udW1lcmljIGVudGl0eS5vdGhlci5hdHRyaWJ1dGUtbmFtZS5pZC5jc3Mgc3Ryb25nXFxcIn0sXFxcImxpdGVyYWxDaGFyYWN0ZXJcXFwiOntcXFwiYmVnaW5cXFwiOlxcXCIoPzwhXFxcXFxcXFxwe3dvcmR9KSdcXFwiLFxcXCJlbmRcXFwiOlxcXCInXFxcIixcXFwibmFtZVxcXCI6XFxcIm1hcmt1cC5wdW5jdHVhdGlvbi5xdW90ZS5iZWdpbm5pbmdcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNsaXRlcmFsQ2hhcmFjdGVyRXNjYXBlXFxcIn1dfSxcXFwibGl0ZXJhbENoYXJhY3RlckVzY2FwZVxcXCI6e1xcXCJtYXRjaFxcXCI6XFxcIlxcXFxcXFxcXFxcXFxcXFwoPzpbXFxcXFxcXFxcXFxcXFxcXFxcXFxcXFwiJ250YnJdfFxcXFxcXFxcZFxcXFxcXFxcZFxcXFxcXFxcZHx4XFxcXFxcXFxoXFxcXFxcXFxofG9bMC0zXVswLTddWzAtN10pXFxcIn0sXFxcImxpdGVyYWxDbGFzc1R5cGVcXFwiOntcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjY29tbWVudFxcXCJ9LHtcXFwiYmVnaW5cXFwiOlxcXCJcXFxcXFxcXGJvYmplY3RcXFxcXFxcXGJcXFwiLFxcXCJjYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLnRhZyBlbXBoYXNpc1xcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiXFxcXFxcXFxiZW5kXFxcXFxcXFxiXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXFxiaW5oZXJpdFxcXFxcXFxcYlxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwidmFyaWFibGUub3RoZXIuY2xhc3MuanMgbWVzc2FnZS5lcnJvciB2YXJpYWJsZS5pbnRlcnBvbGF0aW9uIHN0cmluZy5yZWdleHBcXFwifX0sXFxcImVuZFxcXCI6XFxcIjs7fCg/PVt9KVxcXFxcXFxcXV18XFxcXFxcXFxiKD86ZW5kfGFuZHxjbGFzc3xleGNlcHRpb258ZXh0ZXJuYWx8aW58aW5jbHVkZXxpbmhlcml0fGluaXRpYWxpemVyfGxldHxtZXRob2R8bW9kdWxlfG9wZW58dHlwZXx2YWwpXFxcXFxcXFxiKVxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24udGFnXFxcIn19LFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiYmVnaW5cXFwiOlxcXCJcXFxcXFxcXGJhc1xcXFxcXFxcYlxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwidmFyaWFibGUub3RoZXIuY2xhc3MuanMgbWVzc2FnZS5lcnJvciB2YXJpYWJsZS5pbnRlcnBvbGF0aW9uIHN0cmluZy5yZWdleHBcXFwifX0sXFxcImVuZFxcXCI6XFxcIjs7fCg/PVt9KVxcXFxcXFxcXV18XFxcXFxcXFxiKD86ZW5kfGFuZHxjbGFzc3xleGNlcHRpb258ZXh0ZXJuYWx8aW58aW5jbHVkZXxpbmhlcml0fGluaXRpYWxpemVyfGxldHxtZXRob2R8bW9kdWxlfG9wZW58dHlwZXx2YWwpXFxcXFxcXFxiKVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3ZhcmlhYmxlUGF0dGVyblxcXCJ9XX0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3R5cGVcXFwifV19LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNwYXR0ZXJuXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2RlY2xUZXJtXFxcIn1dfSx7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXFxbXFxcIixcXFwiZW5kXFxcIjpcXFwiXVxcXCJ9XX0sXFxcImxpdGVyYWxMaXN0XFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxcW1xcXCIsXFxcImNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcImNvbnN0YW50Lmxhbmd1YWdlIGNvbnN0YW50Lm51bWVyaWMgZW50aXR5Lm90aGVyLmF0dHJpYnV0ZS1uYW1lLmlkLmNzcyBzdHJvbmdcXFwifX0sXFxcImVuZFxcXCI6XFxcIl1cXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiN0ZXJtXFxcIn1dfV19LFxcXCJsaXRlcmFsTnVtYmVyXFxcIjp7XFxcIm1hdGNoXFxcIjpcXFwiKD88IVxcXFxcXFxccHthbHBoYX0pXFxcXFxcXFxkXFxcXFxcXFxkKihcXFxcXFxcXC5cXFxcXFxcXGRcXFxcXFxcXGQqKT9cXFwiLFxcXCJuYW1lXFxcIjpcXFwiY29uc3RhbnQubnVtZXJpY1xcXCJ9LFxcXCJsaXRlcmFsT2JqZWN0VGVybVxcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNjb21tZW50XFxcIn0se1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxcYm9iamVjdFxcXFxcXFxcYlxcXCIsXFxcImNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24udGFnIGVtcGhhc2lzXFxcIn19LFxcXCJlbmRcXFwiOlxcXCJcXFxcXFxcXGJlbmRcXFxcXFxcXGJcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiYmVnaW5cXFwiOlxcXCJcXFxcXFxcXGJpbmhlcml0XFxcXFxcXFxiXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJ2YXJpYWJsZS5vdGhlci5jbGFzcy5qcyBtZXNzYWdlLmVycm9yIHZhcmlhYmxlLmludGVycG9sYXRpb24gc3RyaW5nLnJlZ2V4cFxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiOzt8KD89W30pXFxcXFxcXFxdXXxcXFxcXFxcXGIoPzplbmR8YW5kfGNsYXNzfGV4Y2VwdGlvbnxleHRlcm5hbHxpbnxpbmNsdWRlfGluaGVyaXR8aW5pdGlhbGl6ZXJ8bGV0fG1ldGhvZHxtb2R1bGV8b3Blbnx0eXBlfHZhbClcXFxcXFxcXGIpXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uZGVmaW5pdGlvbi50YWdcXFwifX0sXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxcYmFzXFxcXFxcXFxiXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJ2YXJpYWJsZS5vdGhlci5jbGFzcy5qcyBtZXNzYWdlLmVycm9yIHZhcmlhYmxlLmludGVycG9sYXRpb24gc3RyaW5nLnJlZ2V4cFxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiOzt8KD89W30pXFxcXFxcXFxdXXxcXFxcXFxcXGIoPzplbmR8YW5kfGNsYXNzfGV4Y2VwdGlvbnxleHRlcm5hbHxpbnxpbmNsdWRlfGluaGVyaXR8aW5pdGlhbGl6ZXJ8bGV0fG1ldGhvZHxtb2R1bGV8b3Blbnx0eXBlfHZhbClcXFxcXFxcXGIpXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjdmFyaWFibGVQYXR0ZXJuXFxcIn1dfSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjdGVybVxcXCJ9XX0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3BhdHRlcm5cXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjZGVjbFRlcm1cXFwifV19LHtcXFwiYmVnaW5cXFwiOlxcXCJcXFxcXFxcXFtcXFwiLFxcXCJlbmRcXFwiOlxcXCJdXFxcIn1dfSxcXFwibGl0ZXJhbFJlY29yZFxcXCI6e1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxce1xcXCIsXFxcImNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcImNvbnN0YW50Lmxhbmd1YWdlIGNvbnN0YW50Lm51bWVyaWMgZW50aXR5Lm90aGVyLmF0dHJpYnV0ZS1uYW1lLmlkLmNzcyBzdHJvbmcgc3Ryb25nXFxcIn19LFxcXCJlbmRcXFwiOlxcXCJ9XFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImJlZ2luXFxcIjpcXFwiKD88PVt7O10pXFxcIixcXFwiZW5kXFxcIjpcXFwiKDopfCg9KXwoOyl8KHdpdGgpfCg/PX0pXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwidmFyaWFibGUub3RoZXIuY2xhc3MuanMgbWVzc2FnZS5lcnJvciB2YXJpYWJsZS5pbnRlcnBvbGF0aW9uIHN0cmluZy5yZWdleHAgc3Ryb25nXFxcIn0sXFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcInN1cHBvcnQudHlwZSBzdHJvbmdcXFwifSxcXFwiM1xcXCI6e1xcXCJuYW1lXFxcIjpcXFwidmFyaWFibGUub3RoZXIuY2xhc3MuanMgbWVzc2FnZS5lcnJvciB2YXJpYWJsZS5pbnRlcnBvbGF0aW9uIHN0cmluZy5yZWdleHBcXFwifSxcXFwiNFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwidmFyaWFibGUub3RoZXIuY2xhc3MuanMgbWVzc2FnZS5lcnJvciB2YXJpYWJsZS5pbnRlcnBvbGF0aW9uIHN0cmluZy5yZWdleHBcXFwifX0sXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2NvbW1lbnRcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjcGF0aE1vZHVsZVByZWZpeFNpbXBsZVxcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCIoPyFcXFxcXFxcXGIoPzphbmR8J3xhc3xhc3J8YXNzZXJ0fFxcXFxcXFxcKnxiZWdpbnxjbGFzc3xbOixAXXxjb25zdHJhaW50fGRvfGRvbmV8ZG93bnRvfGVsc2V8ZW5kfD18ZXhjZXB0aW9ufGV4dGVybmFsfGZhbHNlfGZvcnxcXFxcXFxcXC58ZnVufGZ1bmN0aW9ufGZ1bmN0b3J8Wz4tXXxpZnxpbnxpbmNsdWRlfGluaGVyaXR8aW5pdGlhbGl6ZXJ8bGFuZHxsYXp5fFt7KFxcXFxcXFxcWzxdfGxldHxsb3J8bHNsfGxzcnxseG9yfG1hdGNofG1ldGhvZHxtb2R8bW9kdWxlfG11dGFibGV8bmV3fG5vbnJlY3wjfG9iamVjdHxvZnxvcGVufG9yfFslK118cHJpdmF0ZXxbP1xcXFxcXFwiXXxyZWN8W1xcXFxcXFxcXFxcXFxcXFx9KVxcXFxcXFxcXTtdfHNpZ3wvfHN0cnVjdHx0aGVufH58dG98dHJ1ZXx0cnl8dHlwZXx2YWx8XFxcXFxcXFx8fHZpcnR1YWx8d2hlbnx3aGlsZXx3aXRoKVxcXFxcXFxcYig/OlteJ118JCkpXFxcXFxcXFxiKD89W1s6bG93ZXI6XV9dKVtbOmFscGhhOl1fXVtbOndvcmQ6XSddKlxcXCIsXFxcIm5hbWVcXFwiOlxcXCJtYXJrdXAuaW5zZXJ0ZWQgY29uc3RhbnQubGFuZ3VhZ2Ugc3VwcG9ydC5jb25zdGFudC5wcm9wZXJ0eS12YWx1ZSBlbnRpdHkubmFtZS5maWxlbmFtZSBlbXBoYXNpc1xcXCJ9XX0se1xcXCJiZWdpblxcXCI6XFxcIig/PD0oPzpcXFxcXFxcXFB7d29yZH13aXRofF53aXRoKSkoPyFcXFxcXFxcXHB7d29yZH0pXFxcIixcXFwiZW5kXFxcIjpcXFwiKDopfCg9KXwoOyl8KD89fSlcXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJ2YXJpYWJsZS5vdGhlci5jbGFzcy5qcyBtZXNzYWdlLmVycm9yIHZhcmlhYmxlLmludGVycG9sYXRpb24gc3RyaW5nLnJlZ2V4cCBzdHJvbmdcXFwifSxcXFwiMlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwic3VwcG9ydC50eXBlIHN0cm9uZ1xcXCJ9LFxcXCIzXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJ2YXJpYWJsZS5vdGhlci5jbGFzcy5qcyBtZXNzYWdlLmVycm9yIHZhcmlhYmxlLmludGVycG9sYXRpb24gc3RyaW5nLnJlZ2V4cFxcXCJ9fSxcXFwicGF0dGVybnNcXFwiOlt7XFxcIm1hdGNoXFxcIjpcXFwiKD8hXFxcXFxcXFxiKD86YW5kfCd8YXN8YXNyfGFzc2VydHxcXFxcXFxcXCp8YmVnaW58Y2xhc3N8WzosQF18Y29uc3RyYWludHxkb3xkb25lfGRvd250b3xlbHNlfGVuZHw9fGV4Y2VwdGlvbnxleHRlcm5hbHxmYWxzZXxmb3J8XFxcXFxcXFwufGZ1bnxmdW5jdGlvbnxmdW5jdG9yfFs+LV18aWZ8aW58aW5jbHVkZXxpbmhlcml0fGluaXRpYWxpemVyfGxhbmR8bGF6eXxbeyhcXFxcXFxcXFs8XXxsZXR8bG9yfGxzbHxsc3J8bHhvcnxtYXRjaHxtZXRob2R8bW9kfG1vZHVsZXxtdXRhYmxlfG5ld3xub25yZWN8I3xvYmplY3R8b2Z8b3BlbnxvcnxbJStdfHByaXZhdGV8Wz9cXFxcXFxcIl18cmVjfFtcXFxcXFxcXFxcXFxcXFxcfSlcXFxcXFxcXF07XXxzaWd8L3xzdHJ1Y3R8dGhlbnx+fHRvfHRydWV8dHJ5fHR5cGV8dmFsfFxcXFxcXFxcfHx2aXJ0dWFsfHdoZW58d2hpbGV8d2l0aClcXFxcXFxcXGIoPzpbXiddfCQpKVxcXFxcXFxcYig/PVtbOmxvd2VyOl1fXSlbWzphbHBoYTpdX11bWzp3b3JkOl0nXSpcXFwiLFxcXCJuYW1lXFxcIjpcXFwibWFya3VwLmluc2VydGVkIGNvbnN0YW50Lmxhbmd1YWdlIHN1cHBvcnQuY29uc3RhbnQucHJvcGVydHktdmFsdWUgZW50aXR5Lm5hbWUuZmlsZW5hbWUgZW1waGFzaXNcXFwifV19LHtcXFwiYmVnaW5cXFwiOlxcXCIoPzw9KD86W14jXFxcXFxcXFwtOiE/LkAqL1xcXFxcXFxcJiVeKzw9Pnx+JF06fF46KSkoPyFbI1xcXFxcXFxcLTohPy5AKi9cXFxcXFxcXCYlXis8PT58fiRdKVxcXCIsXFxcImVuZFxcXCI6XFxcIig7KXwoPSl8KD89fSlcXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJ2YXJpYWJsZS5vdGhlci5jbGFzcy5qcyBtZXNzYWdlLmVycm9yIHZhcmlhYmxlLmludGVycG9sYXRpb24gc3RyaW5nLnJlZ2V4cFxcXCJ9LFxcXCIyXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJzdXBwb3J0LnR5cGUgc3Ryb25nXFxcIn19LFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiN0eXBlXFxcIn1dfSx7XFxcImJlZ2luXFxcIjpcXFwiKD88PSg/OlteI1xcXFxcXFxcLTohPy5AKi9cXFxcXFxcXCYlXis8PT58fiRdPXxePSkpKD8hWyNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXSlcXFwiLFxcXCJlbmRcXFwiOlxcXCI7fCg/PX0pXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwidmFyaWFibGUub3RoZXIuY2xhc3MuanMgbWVzc2FnZS5lcnJvciB2YXJpYWJsZS5pbnRlcnBvbGF0aW9uIHN0cmluZy5yZWdleHBcXFwifX0sXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3Rlcm1cXFwifV19XX0sXFxcImxpdGVyYWxTdHJpbmdcXFwiOntcXFwicGF0dGVybnNcXFwiOlt7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXCJcXFwiLFxcXCJlbmRcXFwiOlxcXCJcXFxcXFxcIlxcXCIsXFxcIm5hbWVcXFwiOlxcXCJzdHJpbmcgYmVnaW5uaW5nLnB1bmN0dWF0aW9uLmRlZmluaXRpb24ucXVvdGUubWFya2Rvd25cXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNsaXRlcmFsU3RyaW5nRXNjYXBlXFxcIn1dfSx7XFxcImJlZ2luXFxcIjpcXFwiKFxcXFxcXFxceykoW19bOmxvd2VyOl1dKj8pKFxcXFxcXFxcfClcXFwiLFxcXCJlbmRcXFwiOlxcXCIoXFxcXFxcXFx8KShcXFxcXFxcXDIpKH0pXFxcIixcXFwibmFtZVxcXCI6XFxcInN0cmluZyBiZWdpbm5pbmcucHVuY3R1YXRpb24uZGVmaW5pdGlvbi5xdW90ZS5tYXJrZG93blxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2xpdGVyYWxTdHJpbmdFc2NhcGVcXFwifV19XX0sXFxcImxpdGVyYWxTdHJpbmdFc2NhcGVcXFwiOntcXFwibWF0Y2hcXFwiOlxcXCJcXFxcXFxcXFxcXFxcXFxcKD86W1xcXFxcXFxcXFxcXFxcXFxcXFxcXFxcIm50YnJdfFxcXFxcXFxcZFxcXFxcXFxcZFxcXFxcXFxcZHx4XFxcXFxcXFxoXFxcXFxcXFxofG9bMC0zXVswLTddWzAtN10pXFxcIn0sXFxcImxpdGVyYWxVbml0XFxcIjp7XFxcIm1hdGNoXFxcIjpcXFwiXFxcXFxcXFwoXFxcXFxcXFwpXFxcIixcXFwibmFtZVxcXCI6XFxcImNvbnN0YW50Lmxhbmd1YWdlIGNvbnN0YW50Lm51bWVyaWMgZW50aXR5Lm90aGVyLmF0dHJpYnV0ZS1uYW1lLmlkLmNzcyBzdHJvbmdcXFwifSxcXFwicGF0aE1vZHVsZUV4dGVuZGVkXFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3BhdGhNb2R1bGVQcmVmaXhFeHRlbmRlZFxcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCJcXFxcXFxcXGIoPz1cXFxcXFxcXHB7dXBwZXJ9KVtbOmFscGhhOl1fXVtbOndvcmQ6XSddKlxcXCIsXFxcIm5hbWVcXFwiOlxcXCJlbnRpdHkubmFtZS5jbGFzcyBjb25zdGFudC5udW1lcmljXFxcIn1dfSxcXFwicGF0aE1vZHVsZVByZWZpeEV4dGVuZGVkXFxcIjp7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXFxiKD89XFxcXFxcXFxwe3VwcGVyfSlbWzphbHBoYTpdX11bWzp3b3JkOl0nXSooPz1cXFxcXFxcXHMqXFxcXFxcXFwufCR8XFxcXFxcXFwoKVxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwiZW50aXR5Lm5hbWUuY2xhc3MgY29uc3RhbnQubnVtZXJpY1xcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiKD8hW1xcXFxcXFxccy5dfCR8XFxcXFxcXFwoKVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2NvbW1lbnRcXFwifSx7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXFwoXFxcIixcXFwiY2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5jb250cm9sXFxcIn19LFxcXCJlbmRcXFwiOlxcXCJcXFxcXFxcXClcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwibWF0Y2hcXFwiOlxcXCIoXFxcXFxcXFxiKD89XFxcXFxcXFxwe3VwcGVyfSlbWzphbHBoYTpdX11bWzp3b3JkOl0nXSooPz1cXFxcXFxcXHMqXFxcXFxcXFwpKSlcXFwiLFxcXCJuYW1lXFxcIjpcXFwic3RyaW5nLm90aGVyLmxpbmsgdmFyaWFibGUubGFuZ3VhZ2UgdmFyaWFibGUucGFyYW1ldGVyIGVtcGhhc2lzXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3N0cnVjdHVyZVxcXCJ9XX0se1xcXCJiZWdpblxcXCI6XFxcIig/PCFbI1xcXFxcXFxcLTohPy5AKi9cXFxcXFxcXCYlXis8PT58fiRdKVxcXFxcXFxcLig/IVsjXFxcXFxcXFwtOiE/LkAqL1xcXFxcXFxcJiVeKzw9Pnx+JF0pXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkIHN0cm9uZ1xcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiKFxcXFxcXFxcYig/PVxcXFxcXFxccHt1cHBlcn0pW1s6YWxwaGE6XV9dW1s6d29yZDpdJ10qKD89XFxcXFxcXFxzKlxcXFxcXFxcLnwkKSl8KFxcXFxcXFxcYig/PVxcXFxcXFxccHt1cHBlcn0pW1s6YWxwaGE6XV9dW1s6d29yZDpdJ10qKD89XFxcXFxcXFxzKig/OiR8XFxcXFxcXFwoKSkpfChcXFxcXFxcXGIoPz1cXFxcXFxcXHB7dXBwZXJ9KVtbOmFscGhhOl1fXVtbOndvcmQ6XSddKig/PVxcXFxcXFxccypcXFxcXFxcXCkpKXwoPyFbXFxcXFxcXFxzLls6dXBwZXI6XV18JHxcXFxcXFxcXCgpXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwiZW50aXR5Lm5hbWUuY2xhc3MgY29uc3RhbnQubnVtZXJpY1xcXCJ9LFxcXCIyXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJlbnRpdHkubmFtZS5mdW5jdGlvbiBzdHJvbmdcXFwifSxcXFwiM1xcXCI6e1xcXCJuYW1lXFxcIjpcXFwic3RyaW5nLm90aGVyLmxpbmsgdmFyaWFibGUubGFuZ3VhZ2UgdmFyaWFibGUucGFyYW1ldGVyIGVtcGhhc2lzXFxcIn19fV19LFxcXCJwYXRoTW9kdWxlUHJlZml4RXh0ZW5kZWRQYXJlbnNcXFwiOntcXFwiYmVnaW5cXFwiOlxcXCJcXFxcXFxcXChcXFwiLFxcXCJjYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLmNvbnRyb2xcXFwifX0sXFxcImVuZFxcXCI6XFxcIlxcXFxcXFxcKVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJtYXRjaFxcXCI6XFxcIihcXFxcXFxcXGIoPz1cXFxcXFxcXHB7dXBwZXJ9KVtbOmFscGhhOl1fXVtbOndvcmQ6XSddKig/PVxcXFxcXFxccypcXFxcXFxcXCkpKVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJzdHJpbmcub3RoZXIubGluayB2YXJpYWJsZS5sYW5ndWFnZSB2YXJpYWJsZS5wYXJhbWV0ZXIgZW1waGFzaXNcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjc3RydWN0dXJlXFxcIn1dfSxcXFwicGF0aE1vZHVsZVByZWZpeFNpbXBsZVxcXCI6e1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxcYig/PVxcXFxcXFxccHt1cHBlcn0pW1s6YWxwaGE6XV9dW1s6d29yZDpdJ10qKD89XFxcXFxcXFxzKlxcXFxcXFxcLilcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcImVudGl0eS5uYW1lLmNsYXNzIGNvbnN0YW50Lm51bWVyaWNcXFwifX0sXFxcImVuZFxcXCI6XFxcIig/IVtcXFxcXFxcXHMuXSlcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNjb21tZW50XFxcIn0se1xcXCJiZWdpblxcXCI6XFxcIig/PCFbI1xcXFxcXFxcLTohPy5AKi9cXFxcXFxcXCYlXis8PT58fiRdKVxcXFxcXFxcLig/IVsjXFxcXFxcXFwtOiE/LkAqL1xcXFxcXFxcJiVeKzw9Pnx+JF0pXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkIHN0cm9uZ1xcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiKFxcXFxcXFxcYig/PVxcXFxcXFxccHt1cHBlcn0pW1s6YWxwaGE6XV9dW1s6d29yZDpdJ10qKD89XFxcXFxcXFxzKlxcXFxcXFxcLikpfChcXFxcXFxcXGIoPz1cXFxcXFxcXHB7dXBwZXJ9KVtbOmFscGhhOl1fXVtbOndvcmQ6XSddKig/PVxcXFxcXFxccyopKXwoPyFbXFxcXFxcXFxzLls6dXBwZXI6XV0pXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwiZW50aXR5Lm5hbWUuY2xhc3MgY29uc3RhbnQubnVtZXJpY1xcXCJ9LFxcXCIyXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJjb25zdGFudC5sYW5ndWFnZSBjb25zdGFudC5udW1lcmljIGVudGl0eS5vdGhlci5hdHRyaWJ1dGUtbmFtZS5pZC5jc3Mgc3Ryb25nXFxcIn19fV19LFxcXCJwYXRoTW9kdWxlU2ltcGxlXFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3BhdGhNb2R1bGVQcmVmaXhTaW1wbGVcXFwifSx7XFxcIm1hdGNoXFxcIjpcXFwiXFxcXFxcXFxiKD89XFxcXFxcXFxwe3VwcGVyfSlbWzphbHBoYTpdX11bWzp3b3JkOl0nXSpcXFwiLFxcXCJuYW1lXFxcIjpcXFwiZW50aXR5Lm5hbWUuY2xhc3MgY29uc3RhbnQubnVtZXJpY1xcXCJ9XX0sXFxcInBhdGhSZWNvcmRcXFwiOntcXFwicGF0dGVybnNcXFwiOlt7XFxcImJlZ2luXFxcIjpcXFwiKD8hXFxcXFxcXFxiKD86YW5kfCd8YXN8YXNyfGFzc2VydHxcXFxcXFxcXCp8YmVnaW58Y2xhc3N8WzosQF18Y29uc3RyYWludHxkb3xkb25lfGRvd250b3xlbHNlfGVuZHw9fGV4Y2VwdGlvbnxleHRlcm5hbHxmYWxzZXxmb3J8XFxcXFxcXFwufGZ1bnxmdW5jdGlvbnxmdW5jdG9yfFs+LV18aWZ8aW58aW5jbHVkZXxpbmhlcml0fGluaXRpYWxpemVyfGxhbmR8bGF6eXxbeyhcXFxcXFxcXFs8XXxsZXR8bG9yfGxzbHxsc3J8bHhvcnxtYXRjaHxtZXRob2R8bW9kfG1vZHVsZXxtdXRhYmxlfG5ld3xub25yZWN8I3xvYmplY3R8b2Z8b3BlbnxvcnxbJStdfHByaXZhdGV8Wz9cXFxcXFxcIl18cmVjfFtcXFxcXFxcXFxcXFxcXFxcfSlcXFxcXFxcXF07XXxzaWd8L3xzdHJ1Y3R8dGhlbnx+fHRvfHRydWV8dHJ5fHR5cGV8dmFsfFxcXFxcXFxcfHx2aXJ0dWFsfHdoZW58d2hpbGV8d2l0aClcXFxcXFxcXGIoPzpbXiddfCQpKVxcXFxcXFxcYig/PVtbOmxvd2VyOl1fXSlbWzphbHBoYTpdX11bWzp3b3JkOl0nXSpcXFwiLFxcXCJlbmRcXFwiOlxcXCIoPz1bXlxcXFxcXFxccy5dKSg/IVxcXFxcXFxcKFxcXFxcXFxcKilcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNjb21tZW50XFxcIn0se1xcXCJiZWdpblxcXCI6XFxcIig/PD0oPzpbXiNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXVxcXFxcXFxcLnxeXFxcXFxcXFwuKSkoPyFbI1xcXFxcXFxcLTohPy5AKi9cXFxcXFxcXCYlXis8PT58fiRdKXwoPzwhWyNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXSlcXFxcXFxcXC4oPyFbI1xcXFxcXFxcLTohPy5AKi9cXFxcXFxcXCYlXis8PT58fiRdKVxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZCBzdHJvbmdcXFwifX0sXFxcImVuZFxcXCI6XFxcIigoPzwhWyNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXSlcXFxcXFxcXC4oPyFbI1xcXFxcXFxcLTohPy5AKi9cXFxcXFxcXCYlXis8PT58fiRdKSl8KCg/IVxcXFxcXFxcYig/OmFuZHwnfGFzfGFzcnxhc3NlcnR8XFxcXFxcXFwqfGJlZ2lufGNsYXNzfFs6LEBdfGNvbnN0cmFpbnR8ZG98ZG9uZXxkb3dudG98ZWxzZXxlbmR8PXxleGNlcHRpb258ZXh0ZXJuYWx8ZmFsc2V8Zm9yfFxcXFxcXFxcLnxmdW58ZnVuY3Rpb258ZnVuY3RvcnxbPi1dfGlmfGlufGluY2x1ZGV8aW5oZXJpdHxpbml0aWFsaXplcnxsYW5kfGxhenl8W3soXFxcXFxcXFxbPF18bGV0fGxvcnxsc2x8bHNyfGx4b3J8bWF0Y2h8bWV0aG9kfG1vZHxtdXRhYmxlfG5vbnJlY3wjfG9iamVjdHxvZnxvcGVufG9yfFslK118cHJpdmF0ZXxbP1xcXFxcXFwiXXxyZWN8W1xcXFxcXFxcXFxcXFxcXFx9KVxcXFxcXFxcXTtdfHNpZ3wvfHN0cnVjdHx0aGVufH58dG98dHJ1ZXx0cnl8dHlwZXx2YWx8XFxcXFxcXFx8fHZpcnR1YWx8d2hlbnx3aGlsZXx3aXRoKVxcXFxcXFxcYig/OlteJ118JCkpXFxcXFxcXFxiKD89W1s6bG93ZXI6XV9dKVtbOmFscGhhOl1fXVtbOndvcmQ6XSddKil8KD88PVxcXFxcXFxcKSl8KD88PV0pXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZCBzdHJvbmdcXFwifSxcXFwiMlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwibWFya3VwLmluc2VydGVkIGNvbnN0YW50Lmxhbmd1YWdlIHN1cHBvcnQuY29uc3RhbnQucHJvcGVydHktdmFsdWUgZW50aXR5Lm5hbWUuZmlsZW5hbWVcXFwifX0sXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2NvbW1lbnRcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjcGF0aE1vZHVsZVByZWZpeFNpbXBsZVxcXCJ9LHtcXFwiYmVnaW5cXFwiOlxcXCJcXFxcXFxcXCgoPyFcXFxcXFxcXCopXFxcIixcXFwiY2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwidmFyaWFibGUub3RoZXIuY2xhc3MuanMgbWVzc2FnZS5lcnJvciB2YXJpYWJsZS5pbnRlcnBvbGF0aW9uIHN0cmluZy5yZWdleHBcXFwifX0sXFxcImVuZFxcXCI6XFxcIlxcXFxcXFxcKVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3Rlcm1cXFwifV19LHtcXFwiYmVnaW5cXFwiOlxcXCJcXFxcXFxcXFtcXFwiLFxcXCJjYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJ2YXJpYWJsZS5vdGhlci5jbGFzcy5qcyBtZXNzYWdlLmVycm9yIHZhcmlhYmxlLmludGVycG9sYXRpb24gc3RyaW5nLnJlZ2V4cFxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiXVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3BhdHRlcm5cXFwifV19XX1dfV19LFxcXCJwYXR0ZXJuXFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2NvbW1lbnRcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjcGF0dGVybkFycmF5XFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3BhdHRlcm5MYXp5XFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3BhdHRlcm5MaXN0XFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3BhdHRlcm5NaXNjXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3BhdHRlcm5Nb2R1bGVcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjcGF0dGVyblJlY29yZFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNsaXRlcmFsXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3BhdHRlcm5QYXJlbnNcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjcGF0dGVyblR5cGVcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjdmFyaWFibGVQYXR0ZXJuXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3Rlcm1PcGVyYXRvclxcXCJ9XX0sXFxcInBhdHRlcm5BcnJheVxcXCI6e1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxcW1xcXFxcXFxcfFxcXCIsXFxcImNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcImNvbnN0YW50Lmxhbmd1YWdlIGNvbnN0YW50Lm51bWVyaWMgZW50aXR5Lm90aGVyLmF0dHJpYnV0ZS1uYW1lLmlkLmNzcyBzdHJvbmdcXFwifX0sXFxcImVuZFxcXCI6XFxcIlxcXFxcXFxcfF1cXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNwYXR0ZXJuXFxcIn1dfSxcXFwicGF0dGVybkxhenlcXFwiOntcXFwibWF0Y2hcXFwiOlxcXCJsYXp5XFxcIixcXFwibmFtZVxcXCI6XFxcInZhcmlhYmxlLm90aGVyLmNsYXNzLmpzIG1lc3NhZ2UuZXJyb3IgdmFyaWFibGUuaW50ZXJwb2xhdGlvbiBzdHJpbmcucmVnZXhwXFxcIn0sXFxcInBhdHRlcm5MaXN0XFxcIjp7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXFxbXFxcIixcXFwiY2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwiY29uc3RhbnQubGFuZ3VhZ2UgY29uc3RhbnQubnVtZXJpYyBlbnRpdHkub3RoZXIuYXR0cmlidXRlLW5hbWUuaWQuY3NzIHN0cm9uZ1xcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiXVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3BhdHRlcm5cXFwifV19LFxcXCJwYXR0ZXJuTWlzY1xcXCI6e1xcXCJjYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJzdHJpbmcucmVnZXhwIHN0cm9uZ1xcXCJ9LFxcXCIyXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJ2YXJpYWJsZS5vdGhlci5jbGFzcy5qcyBtZXNzYWdlLmVycm9yIHZhcmlhYmxlLmludGVycG9sYXRpb24gc3RyaW5nLnJlZ2V4cFxcXCJ9LFxcXCIzXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJ2YXJpYWJsZS5vdGhlci5jbGFzcy5qcyBtZXNzYWdlLmVycm9yIHZhcmlhYmxlLmludGVycG9sYXRpb24gc3RyaW5nLnJlZ2V4cFxcXCJ9fSxcXFwibWF0Y2hcXFwiOlxcXCIoKD88IVsjXFxcXFxcXFwtOiE/LkAqL1xcXFxcXFxcJiVeKzw9Pnx+JF0pLCg/IVsjXFxcXFxcXFwtOiE/LkAqL1xcXFxcXFxcJiVeKzw9Pnx+JF0pKXwoWyNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXSspfFxcXFxcXFxcYihhcylcXFxcXFxcXGJcXFwifSxcXFwicGF0dGVybk1vZHVsZVxcXCI6e1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxcYm1vZHVsZVxcXFxcXFxcYlxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwibWFya3VwLmluc2VydGVkIGNvbnN0YW50Lmxhbmd1YWdlIHN1cHBvcnQuY29uc3RhbnQucHJvcGVydHktdmFsdWUgZW50aXR5Lm5hbWUuZmlsZW5hbWVcXFwifX0sXFxcImVuZFxcXCI6XFxcIig/PVxcXFxcXFxcKSlcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNkZWNsTW9kdWxlXFxcIn1dfSxcXFwicGF0dGVyblBhcmVuc1xcXCI6e1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxcKCg/IVxcXFxcXFxcKSlcXFwiLFxcXCJjYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLnRhZ1xcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiXFxcXFxcXFwpXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjY29tbWVudFxcXCJ9LHtcXFwiYmVnaW5cXFwiOlxcXCIoPzwhWyNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXSk6KD8hWyNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXSlcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInZhcmlhYmxlLm90aGVyLmNsYXNzLmpzIG1lc3NhZ2UuZXJyb3IgdmFyaWFibGUuaW50ZXJwb2xhdGlvbiBzdHJpbmcucmVnZXhwIHN0cm9uZ1xcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiKD89XFxcXFxcXFwpKVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3R5cGVcXFwifV19LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNwYXR0ZXJuXFxcIn1dfSxcXFwicGF0dGVyblJlY29yZFxcXCI6e1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxce1xcXCIsXFxcImNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcImNvbnN0YW50Lmxhbmd1YWdlIGNvbnN0YW50Lm51bWVyaWMgZW50aXR5Lm90aGVyLmF0dHJpYnV0ZS1uYW1lLmlkLmNzcyBzdHJvbmcgc3Ryb25nXFxcIn19LFxcXCJlbmRcXFwiOlxcXCJ9XFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImJlZ2luXFxcIjpcXFwiKD88PVt7O10pXFxcIixcXFwiZW5kXFxcIjpcXFwiKDopfCg9KXwoOyl8KHdpdGgpfCg/PX0pXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwidmFyaWFibGUub3RoZXIuY2xhc3MuanMgbWVzc2FnZS5lcnJvciB2YXJpYWJsZS5pbnRlcnBvbGF0aW9uIHN0cmluZy5yZWdleHAgc3Ryb25nXFxcIn0sXFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcInN1cHBvcnQudHlwZSBzdHJvbmdcXFwifSxcXFwiM1xcXCI6e1xcXCJuYW1lXFxcIjpcXFwidmFyaWFibGUub3RoZXIuY2xhc3MuanMgbWVzc2FnZS5lcnJvciB2YXJpYWJsZS5pbnRlcnBvbGF0aW9uIHN0cmluZy5yZWdleHBcXFwifSxcXFwiNFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwidmFyaWFibGUub3RoZXIuY2xhc3MuanMgbWVzc2FnZS5lcnJvciB2YXJpYWJsZS5pbnRlcnBvbGF0aW9uIHN0cmluZy5yZWdleHBcXFwifX0sXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2NvbW1lbnRcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjcGF0aE1vZHVsZVByZWZpeFNpbXBsZVxcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCIoPyFcXFxcXFxcXGIoPzphbmR8J3xhc3xhc3J8YXNzZXJ0fFxcXFxcXFxcKnxiZWdpbnxjbGFzc3xbOixAXXxjb25zdHJhaW50fGRvfGRvbmV8ZG93bnRvfGVsc2V8ZW5kfD18ZXhjZXB0aW9ufGV4dGVybmFsfGZhbHNlfGZvcnxcXFxcXFxcXC58ZnVufGZ1bmN0aW9ufGZ1bmN0b3J8Wz4tXXxpZnxpbnxpbmNsdWRlfGluaGVyaXR8aW5pdGlhbGl6ZXJ8bGFuZHxsYXp5fFt7KFxcXFxcXFxcWzxdfGxldHxsb3J8bHNsfGxzcnxseG9yfG1hdGNofG1ldGhvZHxtb2R8bW9kdWxlfG11dGFibGV8bmV3fG5vbnJlY3wjfG9iamVjdHxvZnxvcGVufG9yfFslK118cHJpdmF0ZXxbP1xcXFxcXFwiXXxyZWN8W1xcXFxcXFxcXFxcXFxcXFx9KVxcXFxcXFxcXTtdfHNpZ3wvfHN0cnVjdHx0aGVufH58dG98dHJ1ZXx0cnl8dHlwZXx2YWx8XFxcXFxcXFx8fHZpcnR1YWx8d2hlbnx3aGlsZXx3aXRoKVxcXFxcXFxcYig/OlteJ118JCkpXFxcXFxcXFxiKD89W1s6bG93ZXI6XV9dKVtbOmFscGhhOl1fXVtbOndvcmQ6XSddKlxcXCIsXFxcIm5hbWVcXFwiOlxcXCJtYXJrdXAuaW5zZXJ0ZWQgY29uc3RhbnQubGFuZ3VhZ2Ugc3VwcG9ydC5jb25zdGFudC5wcm9wZXJ0eS12YWx1ZSBlbnRpdHkubmFtZS5maWxlbmFtZSBlbXBoYXNpc1xcXCJ9XX0se1xcXCJiZWdpblxcXCI6XFxcIig/PD0oPzpcXFxcXFxcXFB7d29yZH13aXRofF53aXRoKSkoPyFcXFxcXFxcXHB7d29yZH0pXFxcIixcXFwiZW5kXFxcIjpcXFwiKDopfCg9KXwoOyl8KD89fSlcXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJ2YXJpYWJsZS5vdGhlci5jbGFzcy5qcyBtZXNzYWdlLmVycm9yIHZhcmlhYmxlLmludGVycG9sYXRpb24gc3RyaW5nLnJlZ2V4cCBzdHJvbmdcXFwifSxcXFwiMlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwic3VwcG9ydC50eXBlIHN0cm9uZ1xcXCJ9LFxcXCIzXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJ2YXJpYWJsZS5vdGhlci5jbGFzcy5qcyBtZXNzYWdlLmVycm9yIHZhcmlhYmxlLmludGVycG9sYXRpb24gc3RyaW5nLnJlZ2V4cFxcXCJ9fSxcXFwicGF0dGVybnNcXFwiOlt7XFxcIm1hdGNoXFxcIjpcXFwiKD8hXFxcXFxcXFxiKD86YW5kfCd8YXN8YXNyfGFzc2VydHxcXFxcXFxcXCp8YmVnaW58Y2xhc3N8WzosQF18Y29uc3RyYWludHxkb3xkb25lfGRvd250b3xlbHNlfGVuZHw9fGV4Y2VwdGlvbnxleHRlcm5hbHxmYWxzZXxmb3J8XFxcXFxcXFwufGZ1bnxmdW5jdGlvbnxmdW5jdG9yfFs+LV18aWZ8aW58aW5jbHVkZXxpbmhlcml0fGluaXRpYWxpemVyfGxhbmR8bGF6eXxbeyhcXFxcXFxcXFs8XXxsZXR8bG9yfGxzbHxsc3J8bHhvcnxtYXRjaHxtZXRob2R8bW9kfG1vZHVsZXxtdXRhYmxlfG5ld3xub25yZWN8I3xvYmplY3R8b2Z8b3BlbnxvcnxbJStdfHByaXZhdGV8Wz9cXFxcXFxcIl18cmVjfFtcXFxcXFxcXFxcXFxcXFxcfSlcXFxcXFxcXF07XXxzaWd8L3xzdHJ1Y3R8dGhlbnx+fHRvfHRydWV8dHJ5fHR5cGV8dmFsfFxcXFxcXFxcfHx2aXJ0dWFsfHdoZW58d2hpbGV8d2l0aClcXFxcXFxcXGIoPzpbXiddfCQpKVxcXFxcXFxcYig/PVtbOmxvd2VyOl1fXSlbWzphbHBoYTpdX11bWzp3b3JkOl0nXSpcXFwiLFxcXCJuYW1lXFxcIjpcXFwibWFya3VwLmluc2VydGVkIGNvbnN0YW50Lmxhbmd1YWdlIHN1cHBvcnQuY29uc3RhbnQucHJvcGVydHktdmFsdWUgZW50aXR5Lm5hbWUuZmlsZW5hbWUgZW1waGFzaXNcXFwifV19LHtcXFwiYmVnaW5cXFwiOlxcXCIoPzw9KD86W14jXFxcXFxcXFwtOiE/LkAqL1xcXFxcXFxcJiVeKzw9Pnx+JF06fF46KSkoPyFbI1xcXFxcXFxcLTohPy5AKi9cXFxcXFxcXCYlXis8PT58fiRdKVxcXCIsXFxcImVuZFxcXCI6XFxcIig7KXwoPSl8KD89fSlcXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJ2YXJpYWJsZS5vdGhlci5jbGFzcy5qcyBtZXNzYWdlLmVycm9yIHZhcmlhYmxlLmludGVycG9sYXRpb24gc3RyaW5nLnJlZ2V4cFxcXCJ9LFxcXCIyXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJzdXBwb3J0LnR5cGUgc3Ryb25nXFxcIn19LFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiN0eXBlXFxcIn1dfSx7XFxcImJlZ2luXFxcIjpcXFwiKD88PSg/OlteI1xcXFxcXFxcLTohPy5AKi9cXFxcXFxcXCYlXis8PT58fiRdPXxePSkpKD8hWyNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXSlcXFwiLFxcXCJlbmRcXFwiOlxcXCI7fCg/PX0pXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwidmFyaWFibGUub3RoZXIuY2xhc3MuanMgbWVzc2FnZS5lcnJvciB2YXJpYWJsZS5pbnRlcnBvbGF0aW9uIHN0cmluZy5yZWdleHBcXFwifX0sXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3BhdHRlcm5cXFwifV19XX0sXFxcInBhdHRlcm5UeXBlXFxcIjp7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXFxidHlwZVxcXFxcXFxcYlxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZFxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiKD89XFxcXFxcXFwpKVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2RlY2xUeXBlXFxcIn1dfSxcXFwicHJhZ21hXFxcIjp7XFxcImJlZ2luXFxcIjpcXFwiKD88IVsjXFxcXFxcXFwtOiE/LkAqL1xcXFxcXFxcJiVeKzw9Pnx+JF0pIyg/IVsjXFxcXFxcXFwtOiE/LkAqL1xcXFxcXFxcJiVeKzw9Pnx+JF0pXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLnRhZ1xcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiKD89Ozt8W30pXFxcXFxcXFxdXXxcXFxcXFxcXGIoPzplbmR8YW5kfGNsYXNzfGV4Y2VwdGlvbnxleHRlcm5hbHxpbnxpbmNsdWRlfGluaGVyaXR8aW5pdGlhbGl6ZXJ8bGV0fG1ldGhvZHxtb2R1bGV8b3Blbnx0eXBlfHZhbClcXFxcXFxcXGIpXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjY29tbWVudFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNsaXRlcmFsTnVtYmVyXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2xpdGVyYWxTdHJpbmdcXFwifV19LFxcXCJzaWduYXR1cmVcXFwiOntcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjY29tbWVudFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNzaWduYXR1cmVMaXRlcmFsXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3NpZ25hdHVyZUZ1bmN0b3JcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjcGF0aE1vZHVsZUV4dGVuZGVkXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3NpZ25hdHVyZVBhcmVuc1xcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNzaWduYXR1cmVSZWNvdmVyZWRcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjc2lnbmF0dXJlQ29uc3RyYWludHNcXFwifV19LFxcXCJzaWduYXR1cmVDb25zdHJhaW50c1xcXCI6e1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxcYndpdGhcXFxcXFxcXGJcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInZhcmlhYmxlLm90aGVyLmNsYXNzLmpzIG1lc3NhZ2UuZXJyb3IgdmFyaWFibGUuaW50ZXJwb2xhdGlvbiBzdHJpbmcucmVnZXhwIG1hcmt1cC51bmRlcmxpbmVcXFwifX0sXFxcImVuZFxcXCI6XFxcIig/PVxcXFxcXFxcKSl8KD89Ozt8W30pXFxcXFxcXFxdXXxcXFxcXFxcXGIoPzplbmR8YW5kfGNsYXNzfGV4Y2VwdGlvbnxleHRlcm5hbHxpbnxpbmNsdWRlfGluaGVyaXR8aW5pdGlhbGl6ZXJ8bGV0fG1ldGhvZHxtb2R1bGV8b3Blbnx0eXBlfHZhbClcXFxcXFxcXGIpXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImJlZ2luXFxcIjpcXFwiKD88PSg/OlxcXFxcXFxcUHt3b3JkfXdpdGh8XndpdGgpKSg/IVxcXFxcXFxccHt3b3JkfSlcXFwiLFxcXCJlbmRcXFwiOlxcXCJcXFxcXFxcXGIoPzoobW9kdWxlKXwodHlwZSkpXFxcXFxcXFxiXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwibWFya3VwLmluc2VydGVkIGNvbnN0YW50Lmxhbmd1YWdlIHN1cHBvcnQuY29uc3RhbnQucHJvcGVydHktdmFsdWUgZW50aXR5Lm5hbWUuZmlsZW5hbWVcXFwifSxcXFwiMlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZFxcXCJ9fX0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2RlY2xNb2R1bGVcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjZGVjbFR5cGVcXFwifV19LFxcXCJzaWduYXR1cmVGdW5jdG9yXFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxcYmZ1bmN0b3JcXFxcXFxcXGJcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmRcXFwifX0sXFxcImVuZFxcXCI6XFxcIig/PTs7fFt9KVxcXFxcXFxcXV18XFxcXFxcXFxiKD86ZW5kfGFuZHxjbGFzc3xleGNlcHRpb258ZXh0ZXJuYWx8aW58aW5jbHVkZXxpbmhlcml0fGluaXRpYWxpemVyfGxldHxtZXRob2R8bW9kdWxlfG9wZW58dHlwZXx2YWwpXFxcXFxcXFxiKVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJiZWdpblxcXCI6XFxcIig/PD0oPzpcXFxcXFxcXFB7d29yZH1mdW5jdG9yfF5mdW5jdG9yKSkoPyFcXFxcXFxcXHB7d29yZH0pXFxcIixcXFwiZW5kXFxcIjpcXFwiKFxcXFxcXFxcKFxcXFxcXFxcKSl8KFxcXFxcXFxcKCg/IVxcXFxcXFxcKSkpXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwiY29uc3RhbnQubGFuZ3VhZ2UgY29uc3RhbnQubnVtZXJpYyBlbnRpdHkub3RoZXIuYXR0cmlidXRlLW5hbWUuaWQuY3NzIHN0cm9uZ1xcXCJ9LFxcXCIyXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLnRhZ1xcXCJ9fX0se1xcXCJiZWdpblxcXCI6XFxcIig/PD1cXFxcXFxcXCgpXFxcIixcXFwiZW5kXFxcIjpcXFwiKDopfChcXFxcXFxcXCkpXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwidmFyaWFibGUub3RoZXIuY2xhc3MuanMgbWVzc2FnZS5lcnJvciB2YXJpYWJsZS5pbnRlcnBvbGF0aW9uIHN0cmluZy5yZWdleHAgc3Ryb25nXFxcIn0sXFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24udGFnXFxcIn19LFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiN2YXJpYWJsZU1vZHVsZVxcXCJ9XX0se1xcXCJiZWdpblxcXCI6XFxcIig/PD0oPzpbXiNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXTp8XjopKSg/IVsjXFxcXFxcXFwtOiE/LkAqL1xcXFxcXFxcJiVeKzw9Pnx+JF0pXFxcIixcXFwiZW5kXFxcIjpcXFwiXFxcXFxcXFwpXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uZGVmaW5pdGlvbi50YWdcXFwifX0sXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3NpZ25hdHVyZVxcXCJ9XX0se1xcXCJiZWdpblxcXCI6XFxcIig/PD1cXFxcXFxcXCkpXFxcIixcXFwiZW5kXFxcIjpcXFwiKFxcXFxcXFxcKCl8KCg/PCFbI1xcXFxcXFxcLTohPy5AKi9cXFxcXFxcXCYlXis8PT58fiRdKS0+KD8hWyNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXSkpXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uZGVmaW5pdGlvbi50YWdcXFwifSxcXFwiMlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwic3VwcG9ydC50eXBlIHN0cm9uZ1xcXCJ9fX0se1xcXCJiZWdpblxcXCI6XFxcIig/PD0oPzpbXiNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXS0+fF4tPikpKD8hWyNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXSlcXFwiLFxcXCJlbmRcXFwiOlxcXCIoPz07O3xbfSlcXFxcXFxcXF1dfFxcXFxcXFxcYig/OmVuZHxhbmR8Y2xhc3N8ZXhjZXB0aW9ufGV4dGVybmFsfGlufGluY2x1ZGV8aW5oZXJpdHxpbml0aWFsaXplcnxsZXR8bWV0aG9kfG1vZHVsZXxvcGVufHR5cGV8dmFsKVxcXFxcXFxcYilcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNzaWduYXR1cmVcXFwifV19XX0se1xcXCJtYXRjaFxcXCI6XFxcIig/PCFbI1xcXFxcXFxcLTohPy5AKi9cXFxcXFxcXCYlXis8PT58fiRdKS0+KD8hWyNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXSlcXFwiLFxcXCJuYW1lXFxcIjpcXFwic3VwcG9ydC50eXBlIHN0cm9uZ1xcXCJ9XX0sXFxcInNpZ25hdHVyZUxpdGVyYWxcXFwiOntcXFwiYmVnaW5cXFwiOlxcXCJcXFxcXFxcXGJzaWdcXFxcXFxcXGJcXFwiLFxcXCJjYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLnRhZyBlbXBoYXNpc1xcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiXFxcXFxcXFxiZW5kXFxcXFxcXFxiXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjY29tbWVudFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNwcmFnbWFcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjZGVjbFxcXCJ9XX0sXFxcInNpZ25hdHVyZVBhcmVuc1xcXCI6e1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxcKCg/IVxcXFxcXFxcKSlcXFwiLFxcXCJjYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLnRhZ1xcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiXFxcXFxcXFwpXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjY29tbWVudFxcXCJ9LHtcXFwiYmVnaW5cXFwiOlxcXCIoPzwhWyNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXSk6KD8hWyNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXSlcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInZhcmlhYmxlLm90aGVyLmNsYXNzLmpzIG1lc3NhZ2UuZXJyb3IgdmFyaWFibGUuaW50ZXJwb2xhdGlvbiBzdHJpbmcucmVnZXhwIHN0cm9uZ1xcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiKD89XFxcXFxcXFwpKVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3NpZ25hdHVyZVxcXCJ9XX0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3NpZ25hdHVyZVxcXCJ9XX0sXFxcInNpZ25hdHVyZVJlY292ZXJlZFxcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiYmVnaW5cXFwiOlxcXCJcXFxcXFxcXCh8KD88PSg/OlteI1xcXFxcXFxcLTohPy5AKi9cXFxcXFxcXCYlXis8PT58fiRdOnxeOnxbXiNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXS0+fF4tPikpKD8hWyNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXSl8KD88PSg/OlxcXFxcXFxcUHt3b3JkfXxeKSg/OmluY2x1ZGV8b3BlbikpKD8hXFxcXFxcXFxwe3dvcmR9KVxcXCIsXFxcImVuZFxcXCI6XFxcIlxcXFxcXFxcYm1vZHVsZVxcXFxcXFxcYnwoPyEkfFxcXFxcXFxcc3xcXFxcXFxcXGJtb2R1bGVcXFxcXFxcXGIpXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwibWFya3VwLmluc2VydGVkIGNvbnN0YW50Lmxhbmd1YWdlIHN1cHBvcnQuY29uc3RhbnQucHJvcGVydHktdmFsdWUgZW50aXR5Lm5hbWUuZmlsZW5hbWVcXFwifX19LHtcXFwiYmVnaW5cXFwiOlxcXCIoPzw9KD86XFxcXFxcXFxQe3dvcmR9bW9kdWxlfF5tb2R1bGUpKSg/IVxcXFxcXFxccHt3b3JkfSlcXFwiLFxcXCJlbmRcXFwiOlxcXCIoPz07O3xbfSlcXFxcXFxcXF1dfFxcXFxcXFxcYig/OmVuZHxhbmR8Y2xhc3N8ZXhjZXB0aW9ufGV4dGVybmFsfGlufGluY2x1ZGV8aW5oZXJpdHxpbml0aWFsaXplcnxsZXR8bWV0aG9kfG1vZHVsZXxvcGVufHR5cGV8dmFsKVxcXFxcXFxcYilcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiYmVnaW5cXFwiOlxcXCIoPzw9KD86XFxcXFxcXFxQe3dvcmR9bW9kdWxlfF5tb2R1bGUpKSg/IVxcXFxcXFxccHt3b3JkfSlcXFwiLFxcXCJlbmRcXFwiOlxcXCJcXFxcXFxcXGJ0eXBlXFxcXFxcXFxiXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZFxcXCJ9fX0se1xcXCJiZWdpblxcXCI6XFxcIig/PD0oPzpcXFxcXFxcXFB7d29yZH10eXBlfF50eXBlKSkoPyFcXFxcXFxcXHB7d29yZH0pXFxcIixcXFwiZW5kXFxcIjpcXFwiXFxcXFxcXFxib2ZcXFxcXFxcXGJcXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLnRhZ1xcXCJ9fX0se1xcXCJiZWdpblxcXCI6XFxcIig/PD0oPzpcXFxcXFxcXFB7d29yZH1vZnxeb2YpKSg/IVxcXFxcXFxccHt3b3JkfSlcXFwiLFxcXCJlbmRcXFwiOlxcXCIoPz07O3xbfSlcXFxcXFxcXF1dfFxcXFxcXFxcYig/OmVuZHxhbmR8Y2xhc3N8ZXhjZXB0aW9ufGV4dGVybmFsfGlufGluY2x1ZGV8aW5oZXJpdHxpbml0aWFsaXplcnxsZXR8bWV0aG9kfG1vZHVsZXxvcGVufHR5cGV8dmFsKVxcXFxcXFxcYilcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNzaWduYXR1cmVcXFwifV19XX1dfSxcXFwic3RydWN0dXJlXFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2NvbW1lbnRcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjc3RydWN0dXJlTGl0ZXJhbFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNzdHJ1Y3R1cmVGdW5jdG9yXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3BhdGhNb2R1bGVFeHRlbmRlZFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNzdHJ1Y3R1cmVQYXJlbnNcXFwifV19LFxcXCJzdHJ1Y3R1cmVGdW5jdG9yXFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxcYmZ1bmN0b3JcXFxcXFxcXGJcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmRcXFwifX0sXFxcImVuZFxcXCI6XFxcIig/PTs7fFt9KVxcXFxcXFxcXV18XFxcXFxcXFxiKD86ZW5kfGFuZHxjbGFzc3xleGNlcHRpb258ZXh0ZXJuYWx8aW58aW5jbHVkZXxpbmhlcml0fGluaXRpYWxpemVyfGxldHxtZXRob2R8bW9kdWxlfG9wZW58dHlwZXx2YWwpXFxcXFxcXFxiKVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJiZWdpblxcXCI6XFxcIig/PD0oPzpcXFxcXFxcXFB7d29yZH1mdW5jdG9yfF5mdW5jdG9yKSkoPyFcXFxcXFxcXHB7d29yZH0pXFxcIixcXFwiZW5kXFxcIjpcXFwiKFxcXFxcXFxcKFxcXFxcXFxcKSl8KFxcXFxcXFxcKCg/IVxcXFxcXFxcKSkpXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwiY29uc3RhbnQubGFuZ3VhZ2UgY29uc3RhbnQubnVtZXJpYyBlbnRpdHkub3RoZXIuYXR0cmlidXRlLW5hbWUuaWQuY3NzIHN0cm9uZ1xcXCJ9LFxcXCIyXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLnRhZ1xcXCJ9fX0se1xcXCJiZWdpblxcXCI6XFxcIig/PD1cXFxcXFxcXCgpXFxcIixcXFwiZW5kXFxcIjpcXFwiKDopfChcXFxcXFxcXCkpXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwidmFyaWFibGUub3RoZXIuY2xhc3MuanMgbWVzc2FnZS5lcnJvciB2YXJpYWJsZS5pbnRlcnBvbGF0aW9uIHN0cmluZy5yZWdleHAgc3Ryb25nXFxcIn0sXFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24udGFnXFxcIn19LFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiN2YXJpYWJsZU1vZHVsZVxcXCJ9XX0se1xcXCJiZWdpblxcXCI6XFxcIig/PD0oPzpbXiNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXTp8XjopKSg/IVsjXFxcXFxcXFwtOiE/LkAqL1xcXFxcXFxcJiVeKzw9Pnx+JF0pXFxcIixcXFwiZW5kXFxcIjpcXFwiXFxcXFxcXFwpXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uZGVmaW5pdGlvbi50YWdcXFwifX0sXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3NpZ25hdHVyZVxcXCJ9XX0se1xcXCJiZWdpblxcXCI6XFxcIig/PD1cXFxcXFxcXCkpXFxcIixcXFwiZW5kXFxcIjpcXFwiKFxcXFxcXFxcKCl8KCg/PCFbI1xcXFxcXFxcLTohPy5AKi9cXFxcXFxcXCYlXis8PT58fiRdKS0+KD8hWyNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXSkpXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uZGVmaW5pdGlvbi50YWdcXFwifSxcXFwiMlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwic3VwcG9ydC50eXBlIHN0cm9uZ1xcXCJ9fX0se1xcXCJiZWdpblxcXCI6XFxcIig/PD0oPzpbXiNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXS0+fF4tPikpKD8hWyNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXSlcXFwiLFxcXCJlbmRcXFwiOlxcXCIoPz07O3xbfSlcXFxcXFxcXF1dfFxcXFxcXFxcYig/OmVuZHxhbmR8Y2xhc3N8ZXhjZXB0aW9ufGV4dGVybmFsfGlufGluY2x1ZGV8aW5oZXJpdHxpbml0aWFsaXplcnxsZXR8bWV0aG9kfG1vZHVsZXxvcGVufHR5cGV8dmFsKVxcXFxcXFxcYilcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNzdHJ1Y3R1cmVcXFwifV19XX0se1xcXCJtYXRjaFxcXCI6XFxcIig/PCFbI1xcXFxcXFxcLTohPy5AKi9cXFxcXFxcXCYlXis8PT58fiRdKS0+KD8hWyNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXSlcXFwiLFxcXCJuYW1lXFxcIjpcXFwic3VwcG9ydC50eXBlIHN0cm9uZ1xcXCJ9XX0sXFxcInN0cnVjdHVyZUxpdGVyYWxcXFwiOntcXFwiYmVnaW5cXFwiOlxcXCJcXFxcXFxcXGJzdHJ1Y3RcXFxcXFxcXGJcXFwiLFxcXCJjYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLnRhZyBlbXBoYXNpc1xcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiXFxcXFxcXFxiZW5kXFxcXFxcXFxiXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjY29tbWVudFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNwcmFnbWFcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjZGVjbFxcXCJ9XX0sXFxcInN0cnVjdHVyZVBhcmVuc1xcXCI6e1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxcKFxcXCIsXFxcImNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24udGFnXFxcIn19LFxcXCJlbmRcXFwiOlxcXCJcXFxcXFxcXClcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNzdHJ1Y3R1cmVVbnBhY2tcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjc3RydWN0dXJlXFxcIn1dfSxcXFwic3RydWN0dXJlVW5wYWNrXFxcIjp7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXFxidmFsXFxcXFxcXFxiXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJ2YXJpYWJsZS5vdGhlci5jbGFzcy5qcyBtZXNzYWdlLmVycm9yIHZhcmlhYmxlLmludGVycG9sYXRpb24gc3RyaW5nLnJlZ2V4cFxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiKD89XFxcXFxcXFwpKVxcXCJ9LFxcXCJ0ZXJtXFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3Rlcm1MZXRcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjdGVybUF0b21pY1xcXCJ9XX0sXFxcInRlcm1BdG9taWNcXFwiOntcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjY29tbWVudFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiN0ZXJtQ29uZGl0aW9uYWxcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjdGVybUNvbnN0cnVjdG9yXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3Rlcm1EZWxpbVxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiN0ZXJtRm9yXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3Rlcm1GdW5jdGlvblxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNsaXRlcmFsXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3Rlcm1NYXRjaFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiN0ZXJtTWF0Y2hSdWxlXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3Rlcm1QdW5cXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjdGVybU9wZXJhdG9yXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3Rlcm1UcnlcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjdGVybVdoaWxlXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3BhdGhSZWNvcmRcXFwifV19LFxcXCJ0ZXJtQ29uZGl0aW9uYWxcXFwiOntcXFwibWF0Y2hcXFwiOlxcXCJcXFxcXFxcXGIoPzppZnx0aGVufGVsc2UpXFxcXFxcXFxiXFxcIixcXFwibmFtZVxcXCI6XFxcImtleXdvcmQuY29udHJvbFxcXCJ9LFxcXCJ0ZXJtQ29uc3RydWN0b3JcXFwiOntcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjcGF0aE1vZHVsZVByZWZpeFNpbXBsZVxcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCJcXFxcXFxcXGIoPz1cXFxcXFxcXHB7dXBwZXJ9KVtbOmFscGhhOl1fXVtbOndvcmQ6XSddKlxcXCIsXFxcIm5hbWVcXFwiOlxcXCJjb25zdGFudC5sYW5ndWFnZSBjb25zdGFudC5udW1lcmljIGVudGl0eS5vdGhlci5hdHRyaWJ1dGUtbmFtZS5pZC5jc3Mgc3Ryb25nXFxcIn1dfSxcXFwidGVybURlbGltXFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxcKCg/IVxcXFxcXFxcKSlcXFwiLFxcXCJjYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLnRhZ1xcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiXFxcXFxcXFwpXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjdGVybVxcXCJ9XX0se1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxcYmJlZ2luXFxcXFxcXFxiXFxcIixcXFwiY2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uZGVmaW5pdGlvbi50YWdcXFwifX0sXFxcImVuZFxcXCI6XFxcIlxcXFxcXFxcYmVuZFxcXFxcXFxcYlxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2F0dHJpYnV0ZUlkZW50aWZpZXJcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjdGVybVxcXCJ9XX1dfSxcXFwidGVybUZvclxcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiYmVnaW5cXFwiOlxcXCJcXFxcXFxcXGJmb3JcXFxcXFxcXGJcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQuY29udHJvbFxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiXFxcXFxcXFxiZG9uZVxcXFxcXFxcYlxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQuY29udHJvbFxcXCJ9fSxcXFwicGF0dGVybnNcXFwiOlt7XFxcImJlZ2luXFxcIjpcXFwiKD88PSg/OlxcXFxcXFxcUHt3b3JkfWZvcnxeZm9yKSkoPyFcXFxcXFxcXHB7d29yZH0pXFxcIixcXFwiZW5kXFxcIjpcXFwiKD88IVsjXFxcXFxcXFwtOiE/LkAqL1xcXFxcXFxcJiVeKzw9Pnx+JF0pPSg/IVsjXFxcXFxcXFwtOiE/LkAqL1xcXFxcXFxcJiVeKzw9Pnx+JF0pXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwic3VwcG9ydC50eXBlIHN0cm9uZ1xcXCJ9fSxcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjcGF0dGVyblxcXCJ9XX0se1xcXCJiZWdpblxcXCI6XFxcIig/PD0oPzpbXiNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXT18Xj0pKSg/IVsjXFxcXFxcXFwtOiE/LkAqL1xcXFxcXFxcJiVeKzw9Pnx+JF0pXFxcIixcXFwiZW5kXFxcIjpcXFwiXFxcXFxcXFxiKD86ZG93bnRvfHRvKVxcXFxcXFxcYlxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQuY29udHJvbFxcXCJ9fSxcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjdGVybVxcXCJ9XX0se1xcXCJiZWdpblxcXCI6XFxcIig/PD0oPzpcXFxcXFxcXFB7d29yZH10b3xedG8pKSg/IVxcXFxcXFxccHt3b3JkfSlcXFwiLFxcXCJlbmRcXFwiOlxcXCJcXFxcXFxcXGJkb1xcXFxcXFxcYlxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQuY29udHJvbFxcXCJ9fSxcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjdGVybVxcXCJ9XX0se1xcXCJiZWdpblxcXCI6XFxcIig/PD0oPzpcXFxcXFxcXFB7d29yZH1kb3xeZG8pKSg/IVxcXFxcXFxccHt3b3JkfSlcXFwiLFxcXCJlbmRcXFwiOlxcXCIoPz1cXFxcXFxcXGJkb25lXFxcXFxcXFxiKVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3Rlcm1cXFwifV19XX1dfSxcXFwidGVybUZ1bmN0aW9uXFxcIjp7XFxcImNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcInN0b3JhZ2UudHlwZVxcXCJ9LFxcXCIyXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJzdG9yYWdlLnR5cGVcXFwifX0sXFxcIm1hdGNoXFxcIjpcXFwiXFxcXFxcXFxiKD86KGZ1bil8KGZ1bmN0aW9uKSlcXFxcXFxcXGJcXFwifSxcXFwidGVybUxldFxcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiYmVnaW5cXFwiOlxcXCIoPzooPzw9KD86W14jXFxcXFxcXFwtOiE/LkAqL1xcXFxcXFxcJiVeKzw9Pnx+JF09fF49fFteI1xcXFxcXFxcLTohPy5AKi9cXFxcXFxcXCYlXis8PT58fiRdLT58Xi0+KSkoPyFbI1xcXFxcXFxcLTohPy5AKi9cXFxcXFxcXCYlXis8PT58fiRdKXwoPzw9WzsoXSkpKD89XFxcXFxcXFxzfFxcXFxcXFxcYmxldFxcXFxcXFxcYil8KD88PSg/OlxcXFxcXFxcUHt3b3JkfXxeKSg/OmJlZ2lufGRvfGVsc2V8aW58c3RydWN0fHRoZW58dHJ5KSkoPyFcXFxcXFxcXHB7d29yZH0pfCg/PD0oPzpbXiNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXUBAfF5AQCkpKD8hWyNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXSlcXFxcXFxcXHMrXFxcIixcXFwiZW5kXFxcIjpcXFwiXFxcXFxcXFxiKD86KGFuZCl8KGxldCkpXFxcXFxcXFxifCg/PVxcXFxcXFxcUykoPyFcXFxcXFxcXChcXFxcXFxcXCopXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwidmFyaWFibGUub3RoZXIuY2xhc3MuanMgbWVzc2FnZS5lcnJvciB2YXJpYWJsZS5pbnRlcnBvbGF0aW9uIHN0cmluZy5yZWdleHAgbWFya3VwLnVuZGVybGluZVxcXCJ9LFxcXCIyXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJzdG9yYWdlLnR5cGUgbWFya3VwLnVuZGVybGluZVxcXCJ9fSxcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjY29tbWVudFxcXCJ9XX0se1xcXCJiZWdpblxcXCI6XFxcIig/PD0oPzpcXFxcXFxcXFB7d29yZH18XikoPzphbmR8bGV0KSkoPyFcXFxcXFxcXHB7d29yZH0pfChsZXQpXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJzdG9yYWdlLnR5cGUgbWFya3VwLnVuZGVybGluZVxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiXFxcXFxcXFxiKD86KGFuZCl8KGluKSlcXFxcXFxcXGJ8KD89W30pXFxcXFxcXFxdXXxcXFxcXFxcXGIoPzplbmR8Y2xhc3N8ZXhjZXB0aW9ufGV4dGVybmFsfGluY2x1ZGV8aW5oZXJpdHxpbml0aWFsaXplcnxsZXR8bWV0aG9kfG1vZHVsZXxvcGVufHR5cGV8dmFsKVxcXFxcXFxcYilcXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJ2YXJpYWJsZS5vdGhlci5jbGFzcy5qcyBtZXNzYWdlLmVycm9yIHZhcmlhYmxlLmludGVycG9sYXRpb24gc3RyaW5nLnJlZ2V4cCBtYXJrdXAudW5kZXJsaW5lXFxcIn0sXFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcInN0b3JhZ2UudHlwZSBtYXJrdXAudW5kZXJsaW5lXFxcIn19LFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNiaW5kVGVybVxcXCJ9XX1dfSxcXFwidGVybU1hdGNoXFxcIjp7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXFxibWF0Y2hcXFxcXFxcXGJcXFwiLFxcXCJjYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLmNvbnRyb2xcXFwifX0sXFxcImVuZFxcXCI6XFxcIlxcXFxcXFxcYndpdGhcXFxcXFxcXGJcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiN0ZXJtXFxcIn1dfSxcXFwidGVybU1hdGNoUnVsZVxcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiYmVnaW5cXFwiOlxcXCIoPzw9KD86XFxcXFxcXFxQe3dvcmR9fF4pKD86ZnVufGZ1bmN0aW9ufHdpdGgpKSg/IVxcXFxcXFxccHt3b3JkfSlcXFwiLFxcXCJlbmRcXFwiOlxcXCIoPzwhWyNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXSkoXFxcXFxcXFx8KXwoLT4pKD8hWyNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXSlcXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJzdXBwb3J0LnR5cGUgc3Ryb25nXFxcIn0sXFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcInN1cHBvcnQudHlwZSBzdHJvbmdcXFwifX0sXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2NvbW1lbnRcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjYXR0cmlidXRlSWRlbnRpZmllclxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNwYXR0ZXJuXFxcIn1dfSx7XFxcImJlZ2luXFxcIjpcXFwiKD88PSg/OlteXFxcXFxcXFxbI1xcXFxcXFxcLTohPy5AKi9cXFxcXFxcXCYlXis8PT58fiRdXFxcXFxcXFx8fF5cXFxcXFxcXHwpKSg/IVsjXFxcXFxcXFwtOiE/LkAqL1xcXFxcXFxcJiVeKzw9Pnx+JF0pfCg/PCFbI1xcXFxcXFxcLTohPy5AKi9cXFxcXFxcXCYlXis8PT58fiRdKVxcXFxcXFxcfCg/IVsjXFxcXFxcXFwtOiE/LkAqL1xcXFxcXFxcJiVeKzw9Pnx+JF0pXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJzdXBwb3J0LnR5cGUgc3Ryb25nXFxcIn19LFxcXCJlbmRcXFwiOlxcXCIoPzwhWyNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXSkoXFxcXFxcXFx8KXwoLT4pKD8hWyNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXSlcXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJzdXBwb3J0LnR5cGUgc3Ryb25nXFxcIn0sXFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcInN1cHBvcnQudHlwZSBzdHJvbmdcXFwifX0sXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3BhdHRlcm5cXFwifSx7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXFxid2hlblxcXFxcXFxcYlxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwidmFyaWFibGUub3RoZXIuY2xhc3MuanMgbWVzc2FnZS5lcnJvciB2YXJpYWJsZS5pbnRlcnBvbGF0aW9uIHN0cmluZy5yZWdleHBcXFwifX0sXFxcImVuZFxcXCI6XFxcIig/PSg/PCFbI1xcXFxcXFxcLTohPy5AKi9cXFxcXFxcXCYlXis8PT58fiRdKS0+KD8hWyNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXSkpXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjdGVybVxcXCJ9XX1dfV19LFxcXCJ0ZXJtT3BlcmF0b3JcXFwiOntcXFwicGF0dGVybnNcXFwiOlt7XFxcImJlZ2luXFxcIjpcXFwiKD88IVsjXFxcXFxcXFwtOiE/LkAqL1xcXFxcXFxcJiVeKzw9Pnx+JF0pIyg/IVsjXFxcXFxcXFwtOiE/LkAqL1xcXFxcXFxcJiVeKzw9Pnx+JF0pXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkXFxcIn19LFxcXCJlbmRcXFwiOlxcXCIoPyFcXFxcXFxcXGIoPzphbmR8J3xhc3xhc3J8YXNzZXJ0fFxcXFxcXFxcKnxiZWdpbnxjbGFzc3xbOixAXXxjb25zdHJhaW50fGRvfGRvbmV8ZG93bnRvfGVsc2V8ZW5kfD18ZXhjZXB0aW9ufGV4dGVybmFsfGZhbHNlfGZvcnxcXFxcXFxcXC58ZnVufGZ1bmN0aW9ufGZ1bmN0b3J8Wz4tXXxpZnxpbnxpbmNsdWRlfGluaGVyaXR8aW5pdGlhbGl6ZXJ8bGFuZHxsYXp5fFt7KFxcXFxcXFxcWzxdfGxldHxsb3J8bHNsfGxzcnxseG9yfG1hdGNofG1ldGhvZHxtb2R8bW9kdWxlfG11dGFibGV8bmV3fG5vbnJlY3wjfG9iamVjdHxvZnxvcGVufG9yfFslK118cHJpdmF0ZXxbP1xcXFxcXFwiXXxyZWN8W1xcXFxcXFxcXFxcXFxcXFx9KVxcXFxcXFxcXTtdfHNpZ3wvfHN0cnVjdHx0aGVufH58dG98dHJ1ZXx0cnl8dHlwZXx2YWx8XFxcXFxcXFx8fHZpcnR1YWx8d2hlbnx3aGlsZXx3aXRoKVxcXFxcXFxcYig/OlteJ118JCkpXFxcXFxcXFxiKD89W1s6bG93ZXI6XV9dKVtbOmFscGhhOl1fXVtbOndvcmQ6XSddKlxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcImVudGl0eS5uYW1lLmZ1bmN0aW9uXFxcIn19fSx7XFxcImNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQuY29udHJvbCBzdHJvbmdcXFwifX0sXFxcIm1hdGNoXFxcIjpcXFwiPC1cXFwifSx7XFxcImNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcInZhcmlhYmxlLm90aGVyLmNsYXNzLmpzIG1lc3NhZ2UuZXJyb3IgdmFyaWFibGUuaW50ZXJwb2xhdGlvbiBzdHJpbmcucmVnZXhwXFxcIn0sXFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcInZhcmlhYmxlLm90aGVyLmNsYXNzLmpzIG1lc3NhZ2UuZXJyb3IgdmFyaWFibGUuaW50ZXJwb2xhdGlvbiBzdHJpbmcucmVnZXhwXFxcIn19LFxcXCJtYXRjaFxcXCI6XFxcIigsfFsjXFxcXFxcXFwtOiE/LkAqL1xcXFxcXFxcJiVeKzw9Pnx+JF0rKXwoOylcXFwifSx7XFxcIm1hdGNoXFxcIjpcXFwiXFxcXFxcXFxiKD86YW5kfGFzc2VydHxhc3J8bGFuZHxsYXp5fGxzcnxseG9yfG1vZHxuZXd8b3IpXFxcXFxcXFxiXFxcIixcXFwibmFtZVxcXCI6XFxcInZhcmlhYmxlLm90aGVyLmNsYXNzLmpzIG1lc3NhZ2UuZXJyb3IgdmFyaWFibGUuaW50ZXJwb2xhdGlvbiBzdHJpbmcucmVnZXhwXFxcIn1dfSxcXFwidGVybVB1blxcXCI6e1xcXCJhcHBseUVuZFBhdHRlcm5MYXN0XFxcIjp0cnVlLFxcXCJiZWdpblxcXCI6XFxcIig/PCFbI1xcXFxcXFxcLTohPy5AKi9cXFxcXFxcXCYlXis8PT58fiRdKVxcXFxcXFxcP3x+KD8hWyNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXSlcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInZhcmlhYmxlLm90aGVyLmNsYXNzLmpzIG1lc3NhZ2UuZXJyb3IgdmFyaWFibGUuaW50ZXJwb2xhdGlvbiBzdHJpbmcucmVnZXhwXFxcIn19LFxcXCJlbmRcXFwiOlxcXCI6fCg/PVteXFxcXFxcXFxzOl0pXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZFxcXCJ9fSxcXFwicGF0dGVybnNcXFwiOlt7XFxcImJlZ2luXFxcIjpcXFwiKD88PSg/OlteI1xcXFxcXFxcLTohPy5AKi9cXFxcXFxcXCYlXis8PT58fiRdXFxcXFxcXFw/fF5cXFxcXFxcXD98W14jXFxcXFxcXFwtOiE/LkAqL1xcXFxcXFxcJiVeKzw9Pnx+JF1+fF5+KSkoPyFbI1xcXFxcXFxcLTohPy5AKi9cXFxcXFxcXCYlXis8PT58fiRdKVxcXCIsXFxcImVuZFxcXCI6XFxcIig/IVxcXFxcXFxcYig/OmFuZHwnfGFzfGFzcnxhc3NlcnR8XFxcXFxcXFwqfGJlZ2lufGNsYXNzfFs6LEBdfGNvbnN0cmFpbnR8ZG98ZG9uZXxkb3dudG98ZWxzZXxlbmR8PXxleGNlcHRpb258ZXh0ZXJuYWx8ZmFsc2V8Zm9yfFxcXFxcXFxcLnxmdW58ZnVuY3Rpb258ZnVuY3RvcnxbPi1dfGlmfGlufGluY2x1ZGV8aW5oZXJpdHxpbml0aWFsaXplcnxsYW5kfGxhenl8W3soXFxcXFxcXFxbPF18bGV0fGxvcnxsc2x8bHNyfGx4b3J8bWF0Y2h8bWV0aG9kfG1vZHxtb2R1bGV8bXV0YWJsZXxuZXd8bm9ucmVjfCN8b2JqZWN0fG9mfG9wZW58b3J8WyUrXXxwcml2YXRlfFs/XFxcXFxcXCJdfHJlY3xbXFxcXFxcXFxcXFxcXFxcXH0pXFxcXFxcXFxdO118c2lnfC98c3RydWN0fHRoZW58fnx0b3x0cnVlfHRyeXx0eXBlfHZhbHxcXFxcXFxcXHx8dmlydHVhbHx3aGVufHdoaWxlfHdpdGgpXFxcXFxcXFxiKD86W14nXXwkKSlcXFxcXFxcXGIoPz1bWzpsb3dlcjpdX10pW1s6YWxwaGE6XV9dW1s6d29yZDpdJ10qXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwibWFya3VwLmluc2VydGVkIGNvbnN0YW50Lmxhbmd1YWdlIHN1cHBvcnQuY29uc3RhbnQucHJvcGVydHktdmFsdWUgZW50aXR5Lm5hbWUuZmlsZW5hbWVcXFwifX19XX0sXFxcInRlcm1UcnlcXFwiOntcXFwiYmVnaW5cXFwiOlxcXCJcXFxcXFxcXGJ0cnlcXFxcXFxcXGJcXFwiLFxcXCJjYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLmNvbnRyb2xcXFwifX0sXFxcImVuZFxcXCI6XFxcIlxcXFxcXFxcYndpdGhcXFxcXFxcXGJcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiN0ZXJtXFxcIn1dfSxcXFwidGVybVdoaWxlXFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxcYndoaWxlXFxcXFxcXFxiXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLmNvbnRyb2xcXFwifX0sXFxcImVuZFxcXCI6XFxcIlxcXFxcXFxcYmRvbmVcXFxcXFxcXGJcXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLmNvbnRyb2xcXFwifX0sXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJiZWdpblxcXCI6XFxcIig/PD0oPzpcXFxcXFxcXFB7d29yZH13aGlsZXxed2hpbGUpKSg/IVxcXFxcXFxccHt3b3JkfSlcXFwiLFxcXCJlbmRcXFwiOlxcXCJcXFxcXFxcXGJkb1xcXFxcXFxcYlxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQuY29udHJvbFxcXCJ9fSxcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjdGVybVxcXCJ9XX0se1xcXCJiZWdpblxcXCI6XFxcIig/PD0oPzpcXFxcXFxcXFB7d29yZH1kb3xeZG8pKSg/IVxcXFxcXFxccHt3b3JkfSlcXFwiLFxcXCJlbmRcXFwiOlxcXCIoPz1cXFxcXFxcXGJkb25lXFxcXFxcXFxiKVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3Rlcm1cXFwifV19XX1dfSxcXFwidHlwZVxcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNjb21tZW50XFxcIn0se1xcXCJtYXRjaFxcXCI6XFxcIlxcXFxcXFxcYm5vbnJlY1xcXFxcXFxcYlxcXCIsXFxcIm5hbWVcXFwiOlxcXCJ2YXJpYWJsZS5vdGhlci5jbGFzcy5qcyBtZXNzYWdlLmVycm9yIHZhcmlhYmxlLmludGVycG9sYXRpb24gc3RyaW5nLnJlZ2V4cFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNwYXRoTW9kdWxlUHJlZml4RXh0ZW5kZWRcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjdHlwZUxhYmVsXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3R5cGVPYmplY3RcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjdHlwZU9wZXJhdG9yXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3R5cGVQYXJlbnNcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjdHlwZVBvbHltb3JwaGljVmFyaWFudFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiN0eXBlUmVjb3JkXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3R5cGVDb25zdHJ1Y3RvclxcXCJ9XX0sXFxcInR5cGVDb25zdHJ1Y3RvclxcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiYmVnaW5cXFwiOlxcXCIoXyl8KCg/IVxcXFxcXFxcYig/OmFuZHwnfGFzfGFzcnxhc3NlcnR8XFxcXFxcXFwqfGJlZ2lufGNsYXNzfFs6LEBdfGNvbnN0cmFpbnR8ZG98ZG9uZXxkb3dudG98ZWxzZXxlbmR8PXxleGNlcHRpb258ZXh0ZXJuYWx8ZmFsc2V8Zm9yfFxcXFxcXFxcLnxmdW58ZnVuY3Rpb258ZnVuY3RvcnxbPi1dfGlmfGlufGluY2x1ZGV8aW5oZXJpdHxpbml0aWFsaXplcnxsYW5kfGxhenl8W3soXFxcXFxcXFxbPF18bGV0fGxvcnxsc2x8bHNyfGx4b3J8bWF0Y2h8bWV0aG9kfG1vZHxtb2R1bGV8bXV0YWJsZXxuZXd8bm9ucmVjfCN8b2JqZWN0fG9mfG9wZW58b3J8WyUrXXxwcml2YXRlfFs/XFxcXFxcXCJdfHJlY3xbXFxcXFxcXFxcXFxcXFxcXH0pXFxcXFxcXFxdO118c2lnfC98c3RydWN0fHRoZW58fnx0b3x0cnVlfHRyeXx0eXBlfHZhbHxcXFxcXFxcXHx8dmlydHVhbHx3aGVufHdoaWxlfHdpdGgpXFxcXFxcXFxiKD86W14nXXwkKSlcXFxcXFxcXGIoPz1bWzpsb3dlcjpdX10pW1s6YWxwaGE6XV9dW1s6d29yZDpdJ10qKXwoJykoKD8hXFxcXFxcXFxiKD86YW5kfCd8YXN8YXNyfGFzc2VydHxcXFxcXFxcXCp8YmVnaW58Y2xhc3N8WzosQF18Y29uc3RyYWludHxkb3xkb25lfGRvd250b3xlbHNlfGVuZHw9fGV4Y2VwdGlvbnxleHRlcm5hbHxmYWxzZXxmb3J8XFxcXFxcXFwufGZ1bnxmdW5jdGlvbnxmdW5jdG9yfFs+LV18aWZ8aW58aW5jbHVkZXxpbmhlcml0fGluaXRpYWxpemVyfGxhbmR8bGF6eXxbeyhcXFxcXFxcXFs8XXxsZXR8bG9yfGxzbHxsc3J8bHhvcnxtYXRjaHxtZXRob2R8bW9kfG1vZHVsZXxtdXRhYmxlfG5ld3xub25yZWN8I3xvYmplY3R8b2Z8b3BlbnxvcnxbJStdfHByaXZhdGV8Wz9cXFxcXFxcIl18cmVjfFtcXFxcXFxcXFxcXFxcXFxcfSlcXFxcXFxcXF07XXxzaWd8L3xzdHJ1Y3R8dGhlbnx+fHRvfHRydWV8dHJ5fHR5cGV8dmFsfFxcXFxcXFxcfHx2aXJ0dWFsfHdoZW58d2hpbGV8d2l0aClcXFxcXFxcXGIoPzpbXiddfCQpKVxcXFxcXFxcYig/PVtbOmxvd2VyOl1fXSlbWzphbHBoYTpdX11bWzp3b3JkOl0nXSopfCg/PD1bXipdXFxcXFxcXFwpfF0pXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJjb21tZW50IGNvbnN0YW50LnJlZ2V4cCBtZXRhLnNlcGFyYXRvci5tYXJrZG93blxcXCJ9LFxcXCIzXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJzdHJpbmcub3RoZXIubGluayB2YXJpYWJsZS5sYW5ndWFnZSB2YXJpYWJsZS5wYXJhbWV0ZXIgZW1waGFzaXMgc3Ryb25nIGVtcGhhc2lzXFxcIn0sXFxcIjRcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQuY29udHJvbCBlbXBoYXNpc1xcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiKD89XFxcXFxcXFwoKD8hXFxcXFxcXFwqKXxbKjosPS4+XFxcXFxcXFwte1xcXFxcXFxcWyt9KVxcXFxcXFxcXTt8XSl8KCg/IVxcXFxcXFxcYig/OmFuZHwnfGFzfGFzcnxhc3NlcnR8XFxcXFxcXFwqfGJlZ2lufGNsYXNzfFs6LEBdfGNvbnN0cmFpbnR8ZG98ZG9uZXxkb3dudG98ZWxzZXxlbmR8PXxleGNlcHRpb258ZXh0ZXJuYWx8ZmFsc2V8Zm9yfFxcXFxcXFxcLnxmdW58ZnVuY3Rpb258ZnVuY3RvcnxbPi1dfGlmfGlufGluY2x1ZGV8aW5oZXJpdHxpbml0aWFsaXplcnxsYW5kfGxhenl8W3soXFxcXFxcXFxbPF18bGV0fGxvcnxsc2x8bHNyfGx4b3J8bWF0Y2h8bWV0aG9kfG1vZHxtb2R1bGV8bXV0YWJsZXxuZXd8bm9ucmVjfCN8b2JqZWN0fG9mfG9wZW58b3J8WyUrXXxwcml2YXRlfFs/XFxcXFxcXCJdfHJlY3xbXFxcXFxcXFxcXFxcXFxcXH0pXFxcXFxcXFxdO118c2lnfC98c3RydWN0fHRoZW58fnx0b3x0cnVlfHRyeXx0eXBlfHZhbHxcXFxcXFxcXHx8dmlydHVhbHx3aGVufHdoaWxlfHdpdGgpXFxcXFxcXFxiKD86W14nXXwkKSlcXFxcXFxcXGIoPz1bWzpsb3dlcjpdX10pW1s6YWxwaGE6XV9dW1s6d29yZDpdJ10qKVs6c3BhY2VdKig/IVxcXFxcXFxcKFxcXFxcXFxcKnxcXFxcXFxcXHB7d29yZH0pfCg/PTs7fFt9KVxcXFxcXFxcXV18XFxcXFxcXFxiKD86ZW5kfGFuZHxjbGFzc3xleGNlcHRpb258ZXh0ZXJuYWx8aW58aW5jbHVkZXxpbmhlcml0fGluaXRpYWxpemVyfGxldHxtZXRob2R8bW9kdWxlfG9wZW58dHlwZXx2YWwpXFxcXFxcXFxiKVxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcImVudGl0eS5uYW1lLmZ1bmN0aW9uIHN0cm9uZ1xcXCJ9fSxcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjY29tbWVudFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNwYXRoTW9kdWxlUHJlZml4RXh0ZW5kZWRcXFwifV19XX0sXFxcInR5cGVMYWJlbFxcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiYmVnaW5cXFwiOlxcXCIoXFxcXFxcXFw/PykoKD8hXFxcXFxcXFxiKD86YW5kfCd8YXN8YXNyfGFzc2VydHxcXFxcXFxcXCp8YmVnaW58Y2xhc3N8WzosQF18Y29uc3RyYWludHxkb3xkb25lfGRvd250b3xlbHNlfGVuZHw9fGV4Y2VwdGlvbnxleHRlcm5hbHxmYWxzZXxmb3J8XFxcXFxcXFwufGZ1bnxmdW5jdGlvbnxmdW5jdG9yfFs+LV18aWZ8aW58aW5jbHVkZXxpbmhlcml0fGluaXRpYWxpemVyfGxhbmR8bGF6eXxbeyhcXFxcXFxcXFs8XXxsZXR8bG9yfGxzbHxsc3J8bHhvcnxtYXRjaHxtZXRob2R8bW9kfG1vZHVsZXxtdXRhYmxlfG5ld3xub25yZWN8I3xvYmplY3R8b2Z8b3BlbnxvcnxbJStdfHByaXZhdGV8Wz9cXFxcXFxcIl18cmVjfFtcXFxcXFxcXFxcXFxcXFxcfSlcXFxcXFxcXF07XXxzaWd8L3xzdHJ1Y3R8dGhlbnx+fHRvfHRydWV8dHJ5fHR5cGV8dmFsfFxcXFxcXFxcfHx2aXJ0dWFsfHdoZW58d2hpbGV8d2l0aClcXFxcXFxcXGIoPzpbXiddfCQpKVxcXFxcXFxcYig/PVtbOmxvd2VyOl1fXSlbWzphbHBoYTpdX11bWzp3b3JkOl0nXSopXFxcXFxcXFxzKigoPzwhWyNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXSk6KD8hWyNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXSkpXFxcIixcXFwiY2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZCBzdHJvbmcgZW1waGFzaXNcXFwifSxcXFwiMlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwibWFya3VwLmluc2VydGVkIGNvbnN0YW50Lmxhbmd1YWdlIHN1cHBvcnQuY29uc3RhbnQucHJvcGVydHktdmFsdWUgZW50aXR5Lm5hbWUuZmlsZW5hbWUgZW1waGFzaXNcXFwifSxcXFwiM1xcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZFxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiKD89KD88IVsjXFxcXFxcXFwtOiE/LkAqL1xcXFxcXFxcJiVeKzw9Pnx+JF0pLT4oPyFbI1xcXFxcXFxcLTohPy5AKi9cXFxcXFxcXCYlXis8PT58fiRdKSlcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiN0eXBlXFxcIn1dfV19LFxcXCJ0eXBlTW9kdWxlXFxcIjp7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXFxibW9kdWxlXFxcXFxcXFxiXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJtYXJrdXAuaW5zZXJ0ZWQgY29uc3RhbnQubGFuZ3VhZ2Ugc3VwcG9ydC5jb25zdGFudC5wcm9wZXJ0eS12YWx1ZSBlbnRpdHkubmFtZS5maWxlbmFtZVxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiKD89XFxcXFxcXFwpKVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3BhdGhNb2R1bGVFeHRlbmRlZFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNzaWduYXR1cmVDb25zdHJhaW50c1xcXCJ9XX0sXFxcInR5cGVPYmplY3RcXFwiOntcXFwiYmVnaW5cXFwiOlxcXCI8XFxcIixcXFwiY2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwiY29uc3RhbnQubGFuZ3VhZ2UgY29uc3RhbnQubnVtZXJpYyBlbnRpdHkub3RoZXIuYXR0cmlidXRlLW5hbWUuaWQuY3NzIHN0cm9uZyBzdHJvbmdcXFwifX0sXFxcImVuZFxcXCI6XFxcIj5cXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiYmVnaW5cXFwiOlxcXCIoPzw9Wzw7XSlcXFwiLFxcXCJlbmRcXFwiOlxcXCIoOil8KD89PilcXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJ2YXJpYWJsZS5vdGhlci5jbGFzcy5qcyBtZXNzYWdlLmVycm9yIHZhcmlhYmxlLmludGVycG9sYXRpb24gc3RyaW5nLnJlZ2V4cCBzdHJvbmdcXFwifSxcXFwiM1xcXCI6e1xcXCJuYW1lXFxcIjpcXFwidmFyaWFibGUub3RoZXIuY2xhc3MuanMgbWVzc2FnZS5lcnJvciB2YXJpYWJsZS5pbnRlcnBvbGF0aW9uIHN0cmluZy5yZWdleHBcXFwifSxcXFwiNFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwidmFyaWFibGUub3RoZXIuY2xhc3MuanMgbWVzc2FnZS5lcnJvciB2YXJpYWJsZS5pbnRlcnBvbGF0aW9uIHN0cmluZy5yZWdleHBcXFwifX0sXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2NvbW1lbnRcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjcGF0aE1vZHVsZVByZWZpeFNpbXBsZVxcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCIoPyFcXFxcXFxcXGIoPzphbmR8J3xhc3xhc3J8YXNzZXJ0fFxcXFxcXFxcKnxiZWdpbnxjbGFzc3xbOixAXXxjb25zdHJhaW50fGRvfGRvbmV8ZG93bnRvfGVsc2V8ZW5kfD18ZXhjZXB0aW9ufGV4dGVybmFsfGZhbHNlfGZvcnxcXFxcXFxcXC58ZnVufGZ1bmN0aW9ufGZ1bmN0b3J8Wz4tXXxpZnxpbnxpbmNsdWRlfGluaGVyaXR8aW5pdGlhbGl6ZXJ8bGFuZHxsYXp5fFt7KFxcXFxcXFxcWzxdfGxldHxsb3J8bHNsfGxzcnxseG9yfG1hdGNofG1ldGhvZHxtb2R8bW9kdWxlfG11dGFibGV8bmV3fG5vbnJlY3wjfG9iamVjdHxvZnxvcGVufG9yfFslK118cHJpdmF0ZXxbP1xcXFxcXFwiXXxyZWN8W1xcXFxcXFxcXFxcXFxcXFx9KVxcXFxcXFxcXTtdfHNpZ3wvfHN0cnVjdHx0aGVufH58dG98dHJ1ZXx0cnl8dHlwZXx2YWx8XFxcXFxcXFx8fHZpcnR1YWx8d2hlbnx3aGlsZXx3aXRoKVxcXFxcXFxcYig/OlteJ118JCkpXFxcXFxcXFxiKD89W1s6bG93ZXI6XV9dKVtbOmFscGhhOl1fXVtbOndvcmQ6XSddKlxcXCIsXFxcIm5hbWVcXFwiOlxcXCJtYXJrdXAuaW5zZXJ0ZWQgY29uc3RhbnQubGFuZ3VhZ2Ugc3VwcG9ydC5jb25zdGFudC5wcm9wZXJ0eS12YWx1ZSBlbnRpdHkubmFtZS5maWxlbmFtZSBlbXBoYXNpc1xcXCJ9XX0se1xcXCJiZWdpblxcXCI6XFxcIig/PD0oPzpbXiNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXTp8XjopKSg/IVsjXFxcXFxcXFwtOiE/LkAqL1xcXFxcXFxcJiVeKzw9Pnx+JF0pXFxcIixcXFwiZW5kXFxcIjpcXFwiKDspfCg/PT4pXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwidmFyaWFibGUub3RoZXIuY2xhc3MuanMgbWVzc2FnZS5lcnJvciB2YXJpYWJsZS5pbnRlcnBvbGF0aW9uIHN0cmluZy5yZWdleHBcXFwifSxcXFwiMlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwic3VwcG9ydC50eXBlIHN0cm9uZ1xcXCJ9fSxcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjdHlwZVxcXCJ9XX1dfSxcXFwidHlwZU9wZXJhdG9yXFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJtYXRjaFxcXCI6XFxcIlssO118WyNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXStcXFwiLFxcXCJuYW1lXFxcIjpcXFwidmFyaWFibGUub3RoZXIuY2xhc3MuanMgbWVzc2FnZS5lcnJvciB2YXJpYWJsZS5pbnRlcnBvbGF0aW9uIHN0cmluZy5yZWdleHAgc3Ryb25nXFxcIn1dfSxcXFwidHlwZVBhcmVuc1xcXCI6e1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxcKFxcXCIsXFxcImNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24udGFnXFxcIn19LFxcXCJlbmRcXFwiOlxcXCJcXFxcXFxcXClcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwibWF0Y2hcXFwiOlxcXCIsXFxcIixcXFwibmFtZVxcXCI6XFxcInZhcmlhYmxlLm90aGVyLmNsYXNzLmpzIG1lc3NhZ2UuZXJyb3IgdmFyaWFibGUuaW50ZXJwb2xhdGlvbiBzdHJpbmcucmVnZXhwXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3R5cGVNb2R1bGVcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjdHlwZVxcXCJ9XX0sXFxcInR5cGVQb2x5bW9ycGhpY1ZhcmlhbnRcXFwiOntcXFwiYmVnaW5cXFwiOlxcXCJcXFxcXFxcXFtcXFwiLFxcXCJlbmRcXFwiOlxcXCJdXFxcIixcXFwicGF0dGVybnNcXFwiOltdfSxcXFwidHlwZVJlY29yZFxcXCI6e1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxce1xcXCIsXFxcImNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcImNvbnN0YW50Lmxhbmd1YWdlIGNvbnN0YW50Lm51bWVyaWMgZW50aXR5Lm90aGVyLmF0dHJpYnV0ZS1uYW1lLmlkLmNzcyBzdHJvbmcgc3Ryb25nXFxcIn19LFxcXCJlbmRcXFwiOlxcXCJ9XFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImJlZ2luXFxcIjpcXFwiKD88PVt7O10pXFxcIixcXFwiZW5kXFxcIjpcXFwiKDopfCg9KXwoOyl8KHdpdGgpfCg/PX0pXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwidmFyaWFibGUub3RoZXIuY2xhc3MuanMgbWVzc2FnZS5lcnJvciB2YXJpYWJsZS5pbnRlcnBvbGF0aW9uIHN0cmluZy5yZWdleHAgc3Ryb25nXFxcIn0sXFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcInN1cHBvcnQudHlwZSBzdHJvbmdcXFwifSxcXFwiM1xcXCI6e1xcXCJuYW1lXFxcIjpcXFwidmFyaWFibGUub3RoZXIuY2xhc3MuanMgbWVzc2FnZS5lcnJvciB2YXJpYWJsZS5pbnRlcnBvbGF0aW9uIHN0cmluZy5yZWdleHBcXFwifSxcXFwiNFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwidmFyaWFibGUub3RoZXIuY2xhc3MuanMgbWVzc2FnZS5lcnJvciB2YXJpYWJsZS5pbnRlcnBvbGF0aW9uIHN0cmluZy5yZWdleHBcXFwifX0sXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2NvbW1lbnRcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjcGF0aE1vZHVsZVByZWZpeFNpbXBsZVxcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCIoPyFcXFxcXFxcXGIoPzphbmR8J3xhc3xhc3J8YXNzZXJ0fFxcXFxcXFxcKnxiZWdpbnxjbGFzc3xbOixAXXxjb25zdHJhaW50fGRvfGRvbmV8ZG93bnRvfGVsc2V8ZW5kfD18ZXhjZXB0aW9ufGV4dGVybmFsfGZhbHNlfGZvcnxcXFxcXFxcXC58ZnVufGZ1bmN0aW9ufGZ1bmN0b3J8Wz4tXXxpZnxpbnxpbmNsdWRlfGluaGVyaXR8aW5pdGlhbGl6ZXJ8bGFuZHxsYXp5fFt7KFxcXFxcXFxcWzxdfGxldHxsb3J8bHNsfGxzcnxseG9yfG1hdGNofG1ldGhvZHxtb2R8bW9kdWxlfG11dGFibGV8bmV3fG5vbnJlY3wjfG9iamVjdHxvZnxvcGVufG9yfFslK118cHJpdmF0ZXxbP1xcXFxcXFwiXXxyZWN8W1xcXFxcXFxcXFxcXFxcXFx9KVxcXFxcXFxcXTtdfHNpZ3wvfHN0cnVjdHx0aGVufH58dG98dHJ1ZXx0cnl8dHlwZXx2YWx8XFxcXFxcXFx8fHZpcnR1YWx8d2hlbnx3aGlsZXx3aXRoKVxcXFxcXFxcYig/OlteJ118JCkpXFxcXFxcXFxiKD89W1s6bG93ZXI6XV9dKVtbOmFscGhhOl1fXVtbOndvcmQ6XSddKlxcXCIsXFxcIm5hbWVcXFwiOlxcXCJtYXJrdXAuaW5zZXJ0ZWQgY29uc3RhbnQubGFuZ3VhZ2Ugc3VwcG9ydC5jb25zdGFudC5wcm9wZXJ0eS12YWx1ZSBlbnRpdHkubmFtZS5maWxlbmFtZSBlbXBoYXNpc1xcXCJ9XX0se1xcXCJiZWdpblxcXCI6XFxcIig/PD0oPzpcXFxcXFxcXFB7d29yZH13aXRofF53aXRoKSkoPyFcXFxcXFxcXHB7d29yZH0pXFxcIixcXFwiZW5kXFxcIjpcXFwiKDopfCg9KXwoOyl8KD89fSlcXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJ2YXJpYWJsZS5vdGhlci5jbGFzcy5qcyBtZXNzYWdlLmVycm9yIHZhcmlhYmxlLmludGVycG9sYXRpb24gc3RyaW5nLnJlZ2V4cCBzdHJvbmdcXFwifSxcXFwiMlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwic3VwcG9ydC50eXBlIHN0cm9uZ1xcXCJ9LFxcXCIzXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJ2YXJpYWJsZS5vdGhlci5jbGFzcy5qcyBtZXNzYWdlLmVycm9yIHZhcmlhYmxlLmludGVycG9sYXRpb24gc3RyaW5nLnJlZ2V4cFxcXCJ9fSxcXFwicGF0dGVybnNcXFwiOlt7XFxcIm1hdGNoXFxcIjpcXFwiKD8hXFxcXFxcXFxiKD86YW5kfCd8YXN8YXNyfGFzc2VydHxcXFxcXFxcXCp8YmVnaW58Y2xhc3N8WzosQF18Y29uc3RyYWludHxkb3xkb25lfGRvd250b3xlbHNlfGVuZHw9fGV4Y2VwdGlvbnxleHRlcm5hbHxmYWxzZXxmb3J8XFxcXFxcXFwufGZ1bnxmdW5jdGlvbnxmdW5jdG9yfFs+LV18aWZ8aW58aW5jbHVkZXxpbmhlcml0fGluaXRpYWxpemVyfGxhbmR8bGF6eXxbeyhcXFxcXFxcXFs8XXxsZXR8bG9yfGxzbHxsc3J8bHhvcnxtYXRjaHxtZXRob2R8bW9kfG1vZHVsZXxtdXRhYmxlfG5ld3xub25yZWN8I3xvYmplY3R8b2Z8b3BlbnxvcnxbJStdfHByaXZhdGV8Wz9cXFxcXFxcIl18cmVjfFtcXFxcXFxcXFxcXFxcXFxcfSlcXFxcXFxcXF07XXxzaWd8L3xzdHJ1Y3R8dGhlbnx+fHRvfHRydWV8dHJ5fHR5cGV8dmFsfFxcXFxcXFxcfHx2aXJ0dWFsfHdoZW58d2hpbGV8d2l0aClcXFxcXFxcXGIoPzpbXiddfCQpKVxcXFxcXFxcYig/PVtbOmxvd2VyOl1fXSlbWzphbHBoYTpdX11bWzp3b3JkOl0nXSpcXFwiLFxcXCJuYW1lXFxcIjpcXFwibWFya3VwLmluc2VydGVkIGNvbnN0YW50Lmxhbmd1YWdlIHN1cHBvcnQuY29uc3RhbnQucHJvcGVydHktdmFsdWUgZW50aXR5Lm5hbWUuZmlsZW5hbWUgZW1waGFzaXNcXFwifV19LHtcXFwiYmVnaW5cXFwiOlxcXCIoPzw9KD86W14jXFxcXFxcXFwtOiE/LkAqL1xcXFxcXFxcJiVeKzw9Pnx+JF06fF46KSkoPyFbI1xcXFxcXFxcLTohPy5AKi9cXFxcXFxcXCYlXis8PT58fiRdKVxcXCIsXFxcImVuZFxcXCI6XFxcIig7KXwoPSl8KD89fSlcXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJ2YXJpYWJsZS5vdGhlci5jbGFzcy5qcyBtZXNzYWdlLmVycm9yIHZhcmlhYmxlLmludGVycG9sYXRpb24gc3RyaW5nLnJlZ2V4cFxcXCJ9LFxcXCIyXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJzdXBwb3J0LnR5cGUgc3Ryb25nXFxcIn19LFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiN0eXBlXFxcIn1dfSx7XFxcImJlZ2luXFxcIjpcXFwiKD88PSg/OlteI1xcXFxcXFxcLTohPy5AKi9cXFxcXFxcXCYlXis8PT58fiRdPXxePSkpKD8hWyNcXFxcXFxcXC06IT8uQCovXFxcXFxcXFwmJV4rPD0+fH4kXSlcXFwiLFxcXCJlbmRcXFwiOlxcXCI7fCg/PX0pXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwidmFyaWFibGUub3RoZXIuY2xhc3MuanMgbWVzc2FnZS5lcnJvciB2YXJpYWJsZS5pbnRlcnBvbGF0aW9uIHN0cmluZy5yZWdleHBcXFwifX0sXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3R5cGVcXFwifV19XX0sXFxcInZhcmlhYmxlTW9kdWxlXFxcIjp7XFxcImNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInN0cmluZy5vdGhlci5saW5rIHZhcmlhYmxlLmxhbmd1YWdlIHZhcmlhYmxlLnBhcmFtZXRlciBlbXBoYXNpc1xcXCJ9fSxcXFwibWF0Y2hcXFwiOlxcXCJcXFxcXFxcXGIoPz1cXFxcXFxcXHB7dXBwZXJ9KVtbOmFscGhhOl1fXVtbOndvcmQ6XSddKlxcXCJ9LFxcXCJ2YXJpYWJsZVBhdHRlcm5cXFwiOntcXFwiY2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwiY29tbWVudCBjb25zdGFudC5yZWdleHAgbWV0YS5zZXBhcmF0b3IubWFya2Rvd25cXFwifSxcXFwiMlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwic3RyaW5nLm90aGVyLmxpbmsgdmFyaWFibGUubGFuZ3VhZ2UgdmFyaWFibGUucGFyYW1ldGVyIGVtcGhhc2lzXFxcIn19LFxcXCJtYXRjaFxcXCI6XFxcIihcXFxcXFxcXGJfXFxcXFxcXFxiKXwoKD8hXFxcXFxcXFxiKD86YW5kfCd8YXN8YXNyfGFzc2VydHxcXFxcXFxcXCp8YmVnaW58Y2xhc3N8WzosQF18Y29uc3RyYWludHxkb3xkb25lfGRvd250b3xlbHNlfGVuZHw9fGV4Y2VwdGlvbnxleHRlcm5hbHxmYWxzZXxmb3J8XFxcXFxcXFwufGZ1bnxmdW5jdGlvbnxmdW5jdG9yfFs+LV18aWZ8aW58aW5jbHVkZXxpbmhlcml0fGluaXRpYWxpemVyfGxhbmR8bGF6eXxbeyhcXFxcXFxcXFs8XXxsZXR8bG9yfGxzbHxsc3J8bHhvcnxtYXRjaHxtZXRob2R8bW9kfG1vZHVsZXxtdXRhYmxlfG5ld3xub25yZWN8I3xvYmplY3R8b2Z8b3BlbnxvcnxbJStdfHByaXZhdGV8Wz9cXFxcXFxcIl18cmVjfFtcXFxcXFxcXFxcXFxcXFxcfSlcXFxcXFxcXF07XXxzaWd8L3xzdHJ1Y3R8dGhlbnx+fHRvfHRydWV8dHJ5fHR5cGV8dmFsfFxcXFxcXFxcfHx2aXJ0dWFsfHdoZW58d2hpbGV8d2l0aClcXFxcXFxcXGIoPzpbXiddfCQpKVxcXFxcXFxcYig/PVtbOmxvd2VyOl1fXSlbWzphbHBoYTpdX11bWzp3b3JkOl0nXSopXFxcIn19LFxcXCJzY29wZU5hbWVcXFwiOlxcXCJzb3VyY2Uub2NhbWxcXFwifVwiKSlcblxuZXhwb3J0IGRlZmF1bHQgW1xubGFuZ1xuXVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/ocaml.mjs\n"));

/***/ })

}]);