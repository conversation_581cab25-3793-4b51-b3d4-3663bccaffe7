"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_desktop_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/desktop.mjs":
/*!******************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/desktop.mjs ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Desktop\\\",\\\"name\\\":\\\"desktop\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#layout\\\"},{\\\"include\\\":\\\"#keywords\\\"},{\\\"include\\\":\\\"#values\\\"},{\\\"include\\\":\\\"#inCommands\\\"},{\\\"include\\\":\\\"#inCategories\\\"}],\\\"repository\\\":{\\\"inCategories\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=^Categories.*)AudioVideo|(?<=^Categories.*)Audio|(?<=^Categories.*)Video|(?<=^Categories.*)Development|(?<=^Categories.*)Education|(?<=^Categories.*)Game|(?<=^Categories.*)Graphics|(?<=^Categories.*)Network|(?<=^Categories.*)Office|(?<=^Categories.*)Science|(?<=^Categories.*)Settings|(?<=^Categories.*)System|(?<=^Categories.*)Utility\\\",\\\"name\\\":\\\"markup.bold\\\"}]},\\\"inCommands\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=^Exec.*\\\\\\\\s)-+\\\\\\\\S+\\\",\\\"name\\\":\\\"variable.parameter\\\"},{\\\"match\\\":\\\"(?<=^Exec.*)\\\\\\\\s%[fFuUick]\\\\\\\\s\\\",\\\"name\\\":\\\"variable.language\\\"},{\\\"match\\\":\\\"\\\\\\\".*\\\\\\\"\\\",\\\"name\\\":\\\"string\\\"}]},\\\"keywords\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"^(?:Type\\\\\\\\b|Version\\\\\\\\b|Name\\\\\\\\b|GenericName\\\\\\\\b|NoDisplay\\\\\\\\b|Comment\\\\\\\\b|Icon\\\\\\\\b|Hidden\\\\\\\\b|OnlyShowIn\\\\\\\\b|NotShowIn\\\\\\\\b|DBusActivatable\\\\\\\\b|TryExec\\\\\\\\b|Exec\\\\\\\\b|Path\\\\\\\\b|Terminal\\\\\\\\b|Actions\\\\\\\\b|MimeType\\\\\\\\b|Categories\\\\\\\\b|Implements\\\\\\\\b|Keywords\\\\\\\\b|StartupNotify\\\\\\\\b|StartupWMClass\\\\\\\\b|URL\\\\\\\\b|PrefersNonDefaultGPU\\\\\\\\b|Encoding\\\\\\\\b)\\\",\\\"name\\\":\\\"keyword\\\"},{\\\"match\\\":\\\"^X-[A-z 0-9-]*\\\",\\\"name\\\":\\\"keyword.other\\\"},{\\\"match\\\":\\\"(?<!^)\\\\\\\\[.+]\\\",\\\"name\\\":\\\"constant.language\\\"},{\\\"match\\\":\\\"^(?:GtkTheme\\\\\\\\b|MetacityTheme\\\\\\\\b|IconTheme\\\\\\\\b|CursorTheme\\\\\\\\b|ButtonLayout\\\\\\\\b|ApplicationFont\\\\\\\\b)\\\",\\\"name\\\":\\\"keyword\\\"}]},\\\"layout\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\[Desktop\\\",\\\"end\\\":\\\"]\\\",\\\"name\\\":\\\"markup.heading\\\"},{\\\"begin\\\":\\\"^\\\\\\\\[X-\\\\\\\\w*\\\",\\\"end\\\":\\\"]\\\",\\\"name\\\":\\\"markup.heading\\\"},{\\\"match\\\":\\\"^\\\\\\\\s*#.*\\\",\\\"name\\\":\\\"comment\\\"},{\\\"match\\\":\\\";\\\",\\\"name\\\":\\\"strong\\\"}]},\\\"values\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=^\\\\\\\\S+)=\\\",\\\"name\\\":\\\"keyword.operator\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?:true\\\\\\\\b|false\\\\\\\\b)\\\",\\\"name\\\":\\\"variable.other\\\"},{\\\"match\\\":\\\"(?<=^Version.*)\\\\\\\\d+(\\\\\\\\.?\\\\\\\\d*)\\\",\\\"name\\\":\\\"variable.other\\\"}]}},\\\"scopeName\\\":\\\"source.desktop\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ac2hpa2lqcy9sYW5ncy9kaXN0L2Rlc2t0b3AubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSx3Q0FBd0MsZ0VBQWdFLHdCQUF3QixFQUFFLDBCQUEwQixFQUFFLHdCQUF3QixFQUFFLDRCQUE0QixFQUFFLDhCQUE4QixrQkFBa0Isa0JBQWtCLGVBQWUsMlhBQTJYLEVBQUUsaUJBQWlCLGVBQWUsd0VBQXdFLEVBQUUsOEVBQThFLEVBQUUsNkNBQTZDLEVBQUUsZUFBZSxlQUFlLGdaQUFnWixFQUFFLHdEQUF3RCxFQUFFLDREQUE0RCxFQUFFLGdKQUFnSixFQUFFLGFBQWEsZUFBZSxzRUFBc0UsRUFBRSx1RUFBdUUsRUFBRSw4Q0FBOEMsRUFBRSxhQUFhLHVCQUF1QixFQUFFLGFBQWEsZUFBZSwwREFBMEQsRUFBRSx3RUFBd0UsRUFBRSw4RUFBOEUsR0FBRyxrQ0FBa0M7O0FBRS9sRSxpRUFBZTtBQUNmO0FBQ0EsQ0FBQyIsInNvdXJjZXMiOlsiRDpcXHNvZnRcXFNlYVByb2plY3RcXGdpdGh1Yl9vcGVuX3Byb2plY3Rfc3VjY2Vzc1xcc3VuYVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcQHNoaWtpanNcXGxhbmdzXFxkaXN0XFxkZXNrdG9wLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBsYW5nID0gT2JqZWN0LmZyZWV6ZShKU09OLnBhcnNlKFwie1xcXCJkaXNwbGF5TmFtZVxcXCI6XFxcIkRlc2t0b3BcXFwiLFxcXCJuYW1lXFxcIjpcXFwiZGVza3RvcFxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2xheW91dFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNrZXl3b3Jkc1xcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiN2YWx1ZXNcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjaW5Db21tYW5kc1xcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNpbkNhdGVnb3JpZXNcXFwifV0sXFxcInJlcG9zaXRvcnlcXFwiOntcXFwiaW5DYXRlZ29yaWVzXFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJtYXRjaFxcXCI6XFxcIig/PD1eQ2F0ZWdvcmllcy4qKUF1ZGlvVmlkZW98KD88PV5DYXRlZ29yaWVzLiopQXVkaW98KD88PV5DYXRlZ29yaWVzLiopVmlkZW98KD88PV5DYXRlZ29yaWVzLiopRGV2ZWxvcG1lbnR8KD88PV5DYXRlZ29yaWVzLiopRWR1Y2F0aW9ufCg/PD1eQ2F0ZWdvcmllcy4qKUdhbWV8KD88PV5DYXRlZ29yaWVzLiopR3JhcGhpY3N8KD88PV5DYXRlZ29yaWVzLiopTmV0d29ya3woPzw9XkNhdGVnb3JpZXMuKilPZmZpY2V8KD88PV5DYXRlZ29yaWVzLiopU2NpZW5jZXwoPzw9XkNhdGVnb3JpZXMuKilTZXR0aW5nc3woPzw9XkNhdGVnb3JpZXMuKilTeXN0ZW18KD88PV5DYXRlZ29yaWVzLiopVXRpbGl0eVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJtYXJrdXAuYm9sZFxcXCJ9XX0sXFxcImluQ29tbWFuZHNcXFwiOntcXFwicGF0dGVybnNcXFwiOlt7XFxcIm1hdGNoXFxcIjpcXFwiKD88PV5FeGVjLipcXFxcXFxcXHMpLStcXFxcXFxcXFMrXFxcIixcXFwibmFtZVxcXCI6XFxcInZhcmlhYmxlLnBhcmFtZXRlclxcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCIoPzw9XkV4ZWMuKilcXFxcXFxcXHMlW2ZGdVVpY2tdXFxcXFxcXFxzXFxcIixcXFwibmFtZVxcXCI6XFxcInZhcmlhYmxlLmxhbmd1YWdlXFxcIn0se1xcXCJtYXRjaFxcXCI6XFxcIlxcXFxcXFwiLipcXFxcXFxcIlxcXCIsXFxcIm5hbWVcXFwiOlxcXCJzdHJpbmdcXFwifV19LFxcXCJrZXl3b3Jkc1xcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwibWF0Y2hcXFwiOlxcXCJeKD86VHlwZVxcXFxcXFxcYnxWZXJzaW9uXFxcXFxcXFxifE5hbWVcXFxcXFxcXGJ8R2VuZXJpY05hbWVcXFxcXFxcXGJ8Tm9EaXNwbGF5XFxcXFxcXFxifENvbW1lbnRcXFxcXFxcXGJ8SWNvblxcXFxcXFxcYnxIaWRkZW5cXFxcXFxcXGJ8T25seVNob3dJblxcXFxcXFxcYnxOb3RTaG93SW5cXFxcXFxcXGJ8REJ1c0FjdGl2YXRhYmxlXFxcXFxcXFxifFRyeUV4ZWNcXFxcXFxcXGJ8RXhlY1xcXFxcXFxcYnxQYXRoXFxcXFxcXFxifFRlcm1pbmFsXFxcXFxcXFxifEFjdGlvbnNcXFxcXFxcXGJ8TWltZVR5cGVcXFxcXFxcXGJ8Q2F0ZWdvcmllc1xcXFxcXFxcYnxJbXBsZW1lbnRzXFxcXFxcXFxifEtleXdvcmRzXFxcXFxcXFxifFN0YXJ0dXBOb3RpZnlcXFxcXFxcXGJ8U3RhcnR1cFdNQ2xhc3NcXFxcXFxcXGJ8VVJMXFxcXFxcXFxifFByZWZlcnNOb25EZWZhdWx0R1BVXFxcXFxcXFxifEVuY29kaW5nXFxcXFxcXFxiKVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkXFxcIn0se1xcXCJtYXRjaFxcXCI6XFxcIl5YLVtBLXogMC05LV0qXFxcIixcXFwibmFtZVxcXCI6XFxcImtleXdvcmQub3RoZXJcXFwifSx7XFxcIm1hdGNoXFxcIjpcXFwiKD88IV4pXFxcXFxcXFxbLitdXFxcIixcXFwibmFtZVxcXCI6XFxcImNvbnN0YW50Lmxhbmd1YWdlXFxcIn0se1xcXCJtYXRjaFxcXCI6XFxcIl4oPzpHdGtUaGVtZVxcXFxcXFxcYnxNZXRhY2l0eVRoZW1lXFxcXFxcXFxifEljb25UaGVtZVxcXFxcXFxcYnxDdXJzb3JUaGVtZVxcXFxcXFxcYnxCdXR0b25MYXlvdXRcXFxcXFxcXGJ8QXBwbGljYXRpb25Gb250XFxcXFxcXFxiKVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkXFxcIn1dfSxcXFwibGF5b3V0XFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJiZWdpblxcXCI6XFxcIl5cXFxcXFxcXFtEZXNrdG9wXFxcIixcXFwiZW5kXFxcIjpcXFwiXVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJtYXJrdXAuaGVhZGluZ1xcXCJ9LHtcXFwiYmVnaW5cXFwiOlxcXCJeXFxcXFxcXFxbWC1cXFxcXFxcXHcqXFxcIixcXFwiZW5kXFxcIjpcXFwiXVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJtYXJrdXAuaGVhZGluZ1xcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCJeXFxcXFxcXFxzKiMuKlxcXCIsXFxcIm5hbWVcXFwiOlxcXCJjb21tZW50XFxcIn0se1xcXCJtYXRjaFxcXCI6XFxcIjtcXFwiLFxcXCJuYW1lXFxcIjpcXFwic3Ryb25nXFxcIn1dfSxcXFwidmFsdWVzXFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJtYXRjaFxcXCI6XFxcIig/PD1eXFxcXFxcXFxTKyk9XFxcIixcXFwibmFtZVxcXCI6XFxcImtleXdvcmQub3BlcmF0b3JcXFwifSx7XFxcIm1hdGNoXFxcIjpcXFwiXFxcXFxcXFxiKD86dHJ1ZVxcXFxcXFxcYnxmYWxzZVxcXFxcXFxcYilcXFwiLFxcXCJuYW1lXFxcIjpcXFwidmFyaWFibGUub3RoZXJcXFwifSx7XFxcIm1hdGNoXFxcIjpcXFwiKD88PV5WZXJzaW9uLiopXFxcXFxcXFxkKyhcXFxcXFxcXC4/XFxcXFxcXFxkKilcXFwiLFxcXCJuYW1lXFxcIjpcXFwidmFyaWFibGUub3RoZXJcXFwifV19fSxcXFwic2NvcGVOYW1lXFxcIjpcXFwic291cmNlLmRlc2t0b3BcXFwifVwiKSlcblxuZXhwb3J0IGRlZmF1bHQgW1xubGFuZ1xuXVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/desktop.mjs\n"));

/***/ })

}]);