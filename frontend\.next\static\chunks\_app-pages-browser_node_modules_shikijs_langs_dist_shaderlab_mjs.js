"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_shaderlab_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/hlsl.mjs":
/*!***************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/hlsl.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"HLSL\\\",\\\"name\\\":\\\"hlsl\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"/\\\\\\\\*\\\",\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"name\\\":\\\"comment.line.block.hlsl\\\"},{\\\"begin\\\":\\\"//\\\",\\\"end\\\":\\\"$\\\",\\\"name\\\":\\\"comment.line.double-slash.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b[0-9]+\\\\\\\\.[0-9]*([Ff])?\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.decimal.hlsl\\\"},{\\\"match\\\":\\\"(\\\\\\\\.([0-9]+)([Ff])?)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.decimal.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b([0-9]+([Ff])?)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.decimal.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(0([xX])\\\\\\\\h+)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.hex.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(false|true)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.hlsl\\\"},{\\\"match\\\":\\\"^\\\\\\\\s*#\\\\\\\\s*(define|elif|else|endif|ifdef|ifndef|if|undef|include|line|error|pragma)\\\",\\\"name\\\":\\\"keyword.preprocessor.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(break|case|continue|default|discard|do|else|for|if|return|switch|while)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(compile)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.fx.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(typedef)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.typealias.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(bool([1-4](x[1-4])?)?|double([1-4](x[1-4])?)?|dword|float([1-4](x[1-4])?)?|half([1-4](x[1-4])?)?|int([1-4](x[1-4])?)?|matrix|min10float([1-4](x[1-4])?)?|min12int([1-4](x[1-4])?)?|min16float([1-4](x[1-4])?)?|min16int([1-4](x[1-4])?)?|min16uint([1-4](x[1-4])?)?|unsigned|uint([1-4](x[1-4])?)?|vector|void)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.basic.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b([a-zA-Z_][a-zA-Z0-9_]*)(?=\\\\\\\\s*\\\\\\\\()\\\",\\\"name\\\":\\\"support.function.hlsl\\\"},{\\\"match\\\":\\\"(?<=:(?:\\\\\\\\s|))(?i:BINORMAL[0-9]*|BLENDINDICES[0-9]*|BLENDWEIGHT[0-9]*|COLOR[0-9]*|NORMAL[0-9]*|POSITIONT|POSITION|PSIZE[0-9]*|TANGENT[0-9]*|TEXCOORD[0-9]*|FOG|TESSFACTOR[0-9]*|VFACE|VPOS|DEPTH[0-9]*)\\\\\\\\b\\\",\\\"name\\\":\\\"support.variable.semantic.hlsl\\\"},{\\\"match\\\":\\\"(?<=:(?:\\\\\\\\s|))(?i:SV_(?:ClipDistance[0-9]*|CullDistance[0-9]*|Coverage|Depth|DepthGreaterEqual[0-9]*|DepthLessEqual[0-9]*|InstanceID|IsFrontFace|Position|RenderTargetArrayIndex|SampleIndex|StencilRef|Target[0-7]?|VertexID|ViewportArrayIndex))\\\\\\\\b\\\",\\\"name\\\":\\\"support.variable.semantic.sm4.hlsl\\\"},{\\\"match\\\":\\\"(?<=:(?:\\\\\\\\s|))(?i:SV_(?:DispatchThreadID|DomainLocation|GroupID|GroupIndex|GroupThreadID|GSInstanceID|InsideTessFactor|OutputControlPointID|TessFactor))\\\\\\\\b\\\",\\\"name\\\":\\\"support.variable.semantic.sm5.hlsl\\\"},{\\\"match\\\":\\\"(?<=:(?:\\\\\\\\s|))(?i:SV_(?:InnerCoverage|StencilRef))\\\\\\\\b\\\",\\\"name\\\":\\\"support.variable.semantic.sm5_1.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(column_major|const|export|extern|globallycoherent|groupshared|inline|inout|in|out|precise|row_major|shared|static|uniform|volatile)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.modifier.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(snorm|unorm)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.modifier.float.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(packoffset|register)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.modifier.postfix.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(centroid|linear|nointerpolation|noperspective|sample)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.modifier.interpolation.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(lineadj|line|point|triangle|triangleadj)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.modifier.geometryshader.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(string)\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.other.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(AppendStructuredBuffer|Buffer|ByteAddressBuffer|ConstantBuffer|ConsumeStructuredBuffer|InputPatch|OutputPatch)\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.object.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(RasterizerOrdered(?:Buffer|ByteAddressBuffer|StructuredBuffer|Texture1D|Texture1DArray|Texture2D|Texture2DArray|Texture3D))\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.object.rasterizerordered.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(RW(?:Buffer|ByteAddressBuffer|StructuredBuffer|Texture1D|Texture1DArray|Texture2D|Texture2DArray|Texture3D))\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.object.rw.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(LineStream|PointStream|TriangleStream)\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.object.geometryshader.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(sampler(?:|1D|2D|3D|CUBE|_state))\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.sampler.legacy.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(Sampler(?:State|ComparisonState))\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.sampler.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(texture(?:2D|CUBE))\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.texture.legacy.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(Texture(?:1D|1DArray|2D|2DArray|2DMS|2DMSArray|3D|Cube|CubeArray))\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.texture.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(cbuffer|class|interface|namespace|struct|tbuffer)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.structured.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(FALSE|TRUE|NULL)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.fx.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(BlendState|DepthStencilState|RasterizerState)\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.fx.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(technique|Technique|technique10|technique11|pass)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.fx.technique.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(AlphaToCoverageEnable|BlendEnable|SrcBlend|DestBlend|BlendOp|SrcBlendAlpha|DestBlendAlpha|BlendOpAlpha|RenderTargetWriteMask)\\\\\\\\b\\\",\\\"name\\\":\\\"meta.object-literal.key.fx.blendstate.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(DepthEnable|DepthWriteMask|DepthFunc|StencilEnable|StencilReadMask|StencilWriteMask|FrontFaceStencilFail|FrontFaceStencilZFail|FrontFaceStencilPass|FrontFaceStencilFunc|BackFaceStencilFail|BackFaceStencilZFail|BackFaceStencilPass|BackFaceStencilFunc)\\\\\\\\b\\\",\\\"name\\\":\\\"meta.object-literal.key.fx.depthstencilstate.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(FillMode|CullMode|FrontCounterClockwise|DepthBias|DepthBiasClamp|SlopeScaleDepthBias|ZClipEnable|ScissorEnable|MultiSampleEnable|AntiAliasedLineEnable)\\\\\\\\b\\\",\\\"name\\\":\\\"meta.object-literal.key.fx.rasterizerstate.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(Filter|AddressU|AddressV|AddressW|MipLODBias|MaxAnisotropy|ComparisonFunc|BorderColor|MinLOD|MaxLOD)\\\\\\\\b\\\",\\\"name\\\":\\\"meta.object-literal.key.fx.samplerstate.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i:ZERO|ONE|SRC_COLOR|INV_SRC_COLOR|SRC_ALPHA|INV_SRC_ALPHA|DEST_ALPHA|INV_DEST_ALPHA|DEST_COLOR|INV_DEST_COLOR|SRC_ALPHA_SAT|BLEND_FACTOR|INV_BLEND_FACTOR|SRC1_COLOR|INV_SRC1_COLOR|SRC1_ALPHA|INV_SRC1_ALPHA)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.fx.blend.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i:ADD|SUBTRACT|REV_SUBTRACT|MIN|MAX)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.fx.blendop.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i:ALL)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.fx.depthwritemask.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i:NEVER|LESS|EQUAL|LESS_EQUAL|GREATER|NOT_EQUAL|GREATER_EQUAL|ALWAYS)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.fx.comparisonfunc.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i:KEEP|REPLACE|INCR_SAT|DECR_SAT|INVERT|INCR|DECR)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.fx.stencilop.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i:WIREFRAME|SOLID)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.fx.fillmode.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i:NONE|FRONT|BACK)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.fx.cullmode.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i:MIN_MAG_MIP_POINT|MIN_MAG_POINT_MIP_LINEAR|MIN_POINT_MAG_LINEAR_MIP_POINT|MIN_POINT_MAG_MIP_LINEAR|MIN_LINEAR_MAG_MIP_POINT|MIN_LINEAR_MAG_POINT_MIP_LINEAR|MIN_MAG_LINEAR_MIP_POINT|MIN_MAG_MIP_LINEAR|ANISOTROPIC|COMPARISON_MIN_MAG_MIP_POINT|COMPARISON_MIN_MAG_POINT_MIP_LINEAR|COMPARISON_MIN_POINT_MAG_LINEAR_MIP_POINT|COMPARISON_MIN_POINT_MAG_MIP_LINEAR|COMPARISON_MIN_LINEAR_MAG_MIP_POINT|COMPARISON_MIN_LINEAR_MAG_POINT_MIP_LINEAR|COMPARISON_MIN_MAG_LINEAR_MIP_POINT|COMPARISON_MIN_MAG_MIP_LINEAR|COMPARISON_ANISOTROPIC|TEXT_1BIT)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.fx.filter.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i:WRAP|MIRROR|CLAMP|BORDER|MIRROR_ONCE)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.fx.textureaddressmode.hlsl\\\"},{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"end\\\":\\\"\\\\\\\"\\\",\\\"name\\\":\\\"string.quoted.double.hlsl\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"constant.character.escape.hlsl\\\"}]}],\\\"scopeName\\\":\\\"source.hlsl\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/hlsl.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/shaderlab.mjs":
/*!********************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/shaderlab.mjs ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _hlsl_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./hlsl.mjs */ \"(app-pages-browser)/./node_modules/@shikijs/langs/dist/hlsl.mjs\");\n\n\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"ShaderLab\\\",\\\"name\\\":\\\"shaderlab\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"//\\\",\\\"end\\\":\\\"$\\\",\\\"name\\\":\\\"comment.line.double-slash.shaderlab\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i:Range|Float|Int|Color|Vector|2D|3D|Cube|Any)\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.basic.shaderlab\\\"},{\\\"include\\\":\\\"#numbers\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i:Shader|Properties|SubShader|Pass|Category)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.structure.shaderlab\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i:Name|Tags|Fallback|CustomEditor|Cull|ZWrite|ZTest|Offset|Blend|BlendOp|ColorMask|AlphaToMask|LOD|Lighting|Stencil|Ref|ReadMask|WriteMask|Comp|CompBack|CompFront|Fail|ZFail|UsePass|GrabPass|Dependency|Material|Diffuse|Ambient|Shininess|Specular|Emission|Fog|Mode|Density|SeparateSpecular|SetTexture|Combine|ConstantColor|Matrix|AlphaTest|ColorMaterial|BindChannels|Bind)\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.propertyname.shaderlab\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i:Back|Front|On|Off|[RGBA]{1,3}|AmbientAndDiffuse|Emission)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.shaderlab\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i:Less|Greater|LEqual|GEqual|Equal|NotEqual|Always|Never)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.comparisonfunction.shaderlab\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i:Keep|Zero|Replace|IncrSat|DecrSat|Invert|IncrWrap|DecrWrap)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.stenciloperation.shaderlab\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i:Previous|Primary|Texture|Constant|Lerp|Double|Quad|Alpha)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.texturecombiners.shaderlab\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i:Global|Linear|Exp2|Exp)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.fog.shaderlab\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i:Vertex|Normal|Tangent|TexCoord0|TexCoord1)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.bindchannels.shaderlab\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i:Add|Sub|RevSub|Min|Max|LogicalClear|LogicalSet|LogicalCopyInverted|LogicalCopy|LogicalNoop|LogicalInvert|LogicalAnd|LogicalNand|LogicalOr|LogicalNor|LogicalXor|LogicalEquiv|LogicalAndReverse|LogicalAndInverted|LogicalOrReverse|LogicalOrInverted)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.blendoperations.shaderlab\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i:One|Zero|SrcColor|SrcAlpha|DstColor|DstAlpha|OneMinusSrcColor|OneMinusSrcAlpha|OneMinusDstColor|OneMinusDstAlpha)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.blendfactors.shaderlab\\\"},{\\\"match\\\":\\\"\\\\\\\\[([a-zA-Z_][a-zA-Z0-9_]*)](?!\\\\\\\\s*[a-zA-Z_][a-zA-Z0-9_]*\\\\\\\\s*\\\\\\\\(\\\\\\\")\\\",\\\"name\\\":\\\"support.variable.reference.shaderlab\\\"},{\\\"begin\\\":\\\"(\\\\\\\\[)\\\",\\\"end\\\":\\\"(])\\\",\\\"name\\\":\\\"meta.attribute.shaderlab\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\G([a-zA-Z]+)\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.attributename.shaderlab\\\"},{\\\"include\\\":\\\"#numbers\\\"}]},{\\\"match\\\":\\\"\\\\\\\\b([a-zA-Z_][a-zA-Z0-9_]*)\\\\\\\\s*\\\\\\\\(\\\",\\\"name\\\":\\\"support.variable.declaration.shaderlab\\\"},{\\\"begin\\\":\\\"\\\\\\\\b(CG(?:PROGRAM|INCLUDE))\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other\\\"}},\\\"end\\\":\\\"\\\\\\\\b(ENDCG)\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other\\\"}},\\\"name\\\":\\\"meta.cgblock\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#hlsl-embedded\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\b(HLSL(?:PROGRAM|INCLUDE))\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other\\\"}},\\\"end\\\":\\\"\\\\\\\\b(ENDHLSL)\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other\\\"}},\\\"name\\\":\\\"meta.hlslblock\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#hlsl-embedded\\\"}]},{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"end\\\":\\\"\\\\\\\"\\\",\\\"name\\\":\\\"string.quoted.double.shaderlab\\\"}],\\\"repository\\\":{\\\"hlsl-embedded\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"source.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(fixed([1-4](x[1-4])?)?)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.basic.shaderlab\\\"},{\\\"match\\\":\\\"\\\\\\\\b(UNITY_MATRIX_MVP|UNITY_MATRIX_MV|UNITY_MATRIX_M|UNITY_MATRIX_V|UNITY_MATRIX_P|UNITY_MATRIX_VP|UNITY_MATRIX_T_MV|UNITY_MATRIX_I_V|UNITY_MATRIX_IT_MV|_Object2World|_World2Object|unity_ObjectToWorld|unity_WorldToObject)\\\\\\\\b\\\",\\\"name\\\":\\\"support.variable.transformations.shaderlab\\\"},{\\\"match\\\":\\\"\\\\\\\\b(_WorldSpaceCameraPos|_ProjectionParams|_ScreenParams|_ZBufferParams|unity_OrthoParams|unity_CameraProjection|unity_CameraInvProjection|unity_CameraWorldClipPlanes)\\\\\\\\b\\\",\\\"name\\\":\\\"support.variable.camera.shaderlab\\\"},{\\\"match\\\":\\\"\\\\\\\\b(_Time|_SinTime|_CosTime|unity_DeltaTime)\\\\\\\\b\\\",\\\"name\\\":\\\"support.variable.time.shaderlab\\\"},{\\\"match\\\":\\\"\\\\\\\\b(_LightColor0|_WorldSpaceLightPos0|_LightMatrix0|unity_4LightPosX0|unity_4LightPosY0|unity_4LightPosZ0|unity_4LightAtten0|unity_LightColor|_LightColor|unity_LightPosition|unity_LightAtten|unity_SpotDirection)\\\\\\\\b\\\",\\\"name\\\":\\\"support.variable.lighting.shaderlab\\\"},{\\\"match\\\":\\\"\\\\\\\\b(unity_AmbientSky|unity_AmbientEquator|unity_AmbientGround|UNITY_LIGHTMODEL_AMBIENT|unity_FogColor|unity_FogParams)\\\\\\\\b\\\",\\\"name\\\":\\\"support.variable.fog.shaderlab\\\"},{\\\"match\\\":\\\"\\\\\\\\b(unity_LODFade)\\\\\\\\b\\\",\\\"name\\\":\\\"support.variable.various.shaderlab\\\"},{\\\"match\\\":\\\"\\\\\\\\b(SHADER_API_(?:D3D9|D3D11|GLCORE|OPENGL|GLES|GLES3|METAL|D3D11_9X|PSSL|XBOXONE|PSP2|WIIU|MOBILE|GLSL))\\\\\\\\b\\\",\\\"name\\\":\\\"support.variable.preprocessor.targetplatform.shaderlab\\\"},{\\\"match\\\":\\\"\\\\\\\\b(SHADER_TARGET)\\\\\\\\b\\\",\\\"name\\\":\\\"support.variable.preprocessor.targetmodel.shaderlab\\\"},{\\\"match\\\":\\\"\\\\\\\\b(UNITY_VERSION)\\\\\\\\b\\\",\\\"name\\\":\\\"support.variable.preprocessor.unityversion.shaderlab\\\"},{\\\"match\\\":\\\"\\\\\\\\b(UNITY_(?:BRANCH|FLATTEN|NO_SCREENSPACE_SHADOWS|NO_LINEAR_COLORSPACE|NO_RGBM|NO_DXT5nm|FRAMEBUFFER_FETCH_AVAILABLE|USE_RGBA_FOR_POINT_SHADOWS|ATTEN_CHANNEL|HALF_TEXEL_OFFSET|UV_STARTS_AT_TOP|MIGHT_NOT_HAVE_DEPTH_Texture|NEAR_CLIP_VALUE|VPOS_TYPE|CAN_COMPILE_TESSELLATION|COMPILER_HLSL|COMPILER_HLSL2GLSL|COMPILER_CG|REVERSED_Z))\\\\\\\\b\\\",\\\"name\\\":\\\"support.variable.preprocessor.platformdifference.shaderlab\\\"},{\\\"match\\\":\\\"\\\\\\\\b(UNITY_PASS_(?:FORWARDBASE|FORWARDADD|DEFERRED|SHADOWCASTER|PREPASSBASE|PREPASSFINAL))\\\\\\\\b\\\",\\\"name\\\":\\\"support.variable.preprocessor.texture2D.shaderlab\\\"},{\\\"match\\\":\\\"\\\\\\\\b(appdata_(?:base|tan|full|img))\\\\\\\\b\\\",\\\"name\\\":\\\"support.class.structures.shaderlab\\\"},{\\\"match\\\":\\\"\\\\\\\\b(SurfaceOutputStandardSpecular|SurfaceOutputStandard|SurfaceOutput|Input)\\\\\\\\b\\\",\\\"name\\\":\\\"support.class.surface.shaderlab\\\"}]},\\\"numbers\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b([0-9]+\\\\\\\\.?[0-9]*)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.shaderlab\\\"}]}},\\\"scopeName\\\":\\\"source.shaderlab\\\",\\\"embeddedLangs\\\":[\\\"hlsl\\\"],\\\"aliases\\\":[\\\"shader\\\"]}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\n..._hlsl_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/shaderlab.mjs\n"));

/***/ })

}]);