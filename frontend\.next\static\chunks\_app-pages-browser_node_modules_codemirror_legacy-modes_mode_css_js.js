"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_codemirror_legacy-modes_mode_css_js"],{

/***/ "(app-pages-browser)/./node_modules/@codemirror/legacy-modes/mode/css.js":
/*!***********************************************************!*\
  !*** ./node_modules/@codemirror/legacy-modes/mode/css.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   css: () => (/* binding */ css),\n/* harmony export */   gss: () => (/* binding */ gss),\n/* harmony export */   keywords: () => (/* binding */ keywords),\n/* harmony export */   less: () => (/* binding */ less),\n/* harmony export */   mkCSS: () => (/* binding */ mkCSS),\n/* harmony export */   sCSS: () => (/* binding */ sCSS)\n/* harmony export */ });\nfunction mkCSS(parserConfig) {\n  parserConfig = {...defaults, ...parserConfig}\n  var inline = parserConfig.inline\n\n  var tokenHooks = parserConfig.tokenHooks,\n      documentTypes = parserConfig.documentTypes || {},\n      mediaTypes = parserConfig.mediaTypes || {},\n      mediaFeatures = parserConfig.mediaFeatures || {},\n      mediaValueKeywords = parserConfig.mediaValueKeywords || {},\n      propertyKeywords = parserConfig.propertyKeywords || {},\n      nonStandardPropertyKeywords = parserConfig.nonStandardPropertyKeywords || {},\n      fontProperties = parserConfig.fontProperties || {},\n      counterDescriptors = parserConfig.counterDescriptors || {},\n      colorKeywords = parserConfig.colorKeywords || {},\n      valueKeywords = parserConfig.valueKeywords || {},\n      allowNested = parserConfig.allowNested,\n      lineComment = parserConfig.lineComment,\n      supportsAtComponent = parserConfig.supportsAtComponent === true,\n      highlightNonStandardPropertyKeywords = parserConfig.highlightNonStandardPropertyKeywords !== false;\n\n  var type, override;\n  function ret(style, tp) { type = tp; return style; }\n\n  // Tokenizers\n\n  function tokenBase(stream, state) {\n    var ch = stream.next();\n    if (tokenHooks[ch]) {\n      var result = tokenHooks[ch](stream, state);\n      if (result !== false) return result;\n    }\n    if (ch == \"@\") {\n      stream.eatWhile(/[\\w\\\\\\-]/);\n      return ret(\"def\", stream.current());\n    } else if (ch == \"=\" || (ch == \"~\" || ch == \"|\") && stream.eat(\"=\")) {\n      return ret(null, \"compare\");\n    } else if (ch == \"\\\"\" || ch == \"'\") {\n      state.tokenize = tokenString(ch);\n      return state.tokenize(stream, state);\n    } else if (ch == \"#\") {\n      stream.eatWhile(/[\\w\\\\\\-]/);\n      return ret(\"atom\", \"hash\");\n    } else if (ch == \"!\") {\n      stream.match(/^\\s*\\w*/);\n      return ret(\"keyword\", \"important\");\n    } else if (/\\d/.test(ch) || ch == \".\" && stream.eat(/\\d/)) {\n      stream.eatWhile(/[\\w.%]/);\n      return ret(\"number\", \"unit\");\n    } else if (ch === \"-\") {\n      if (/[\\d.]/.test(stream.peek())) {\n        stream.eatWhile(/[\\w.%]/);\n        return ret(\"number\", \"unit\");\n      } else if (stream.match(/^-[\\w\\\\\\-]*/)) {\n        stream.eatWhile(/[\\w\\\\\\-]/);\n        if (stream.match(/^\\s*:/, false))\n          return ret(\"def\", \"variable-definition\");\n        return ret(\"variableName\", \"variable\");\n      } else if (stream.match(/^\\w+-/)) {\n        return ret(\"meta\", \"meta\");\n      }\n    } else if (/[,+>*\\/]/.test(ch)) {\n      return ret(null, \"select-op\");\n    } else if (ch == \".\" && stream.match(/^-?[_a-z][_a-z0-9-]*/i)) {\n      return ret(\"qualifier\", \"qualifier\");\n    } else if (/[:;{}\\[\\]\\(\\)]/.test(ch)) {\n      return ret(null, ch);\n    } else if (stream.match(/^[\\w-.]+(?=\\()/)) {\n      if (/^(url(-prefix)?|domain|regexp)$/i.test(stream.current())) {\n        state.tokenize = tokenParenthesized;\n      }\n      return ret(\"variableName.function\", \"variable\");\n    } else if (/[\\w\\\\\\-]/.test(ch)) {\n      stream.eatWhile(/[\\w\\\\\\-]/);\n      return ret(\"property\", \"word\");\n    } else {\n      return ret(null, null);\n    }\n  }\n\n  function tokenString(quote) {\n    return function(stream, state) {\n      var escaped = false, ch;\n      while ((ch = stream.next()) != null) {\n        if (ch == quote && !escaped) {\n          if (quote == \")\") stream.backUp(1);\n          break;\n        }\n        escaped = !escaped && ch == \"\\\\\";\n      }\n      if (ch == quote || !escaped && quote != \")\") state.tokenize = null;\n      return ret(\"string\", \"string\");\n    };\n  }\n\n  function tokenParenthesized(stream, state) {\n    stream.next(); // Must be '('\n    if (!stream.match(/^\\s*[\\\"\\')]/, false))\n      state.tokenize = tokenString(\")\");\n    else\n      state.tokenize = null;\n    return ret(null, \"(\");\n  }\n\n  // Context management\n\n  function Context(type, indent, prev) {\n    this.type = type;\n    this.indent = indent;\n    this.prev = prev;\n  }\n\n  function pushContext(state, stream, type, indent) {\n    state.context = new Context(type, stream.indentation() + (indent === false ? 0 : stream.indentUnit), state.context);\n    return type;\n  }\n\n  function popContext(state) {\n    if (state.context.prev)\n      state.context = state.context.prev;\n    return state.context.type;\n  }\n\n  function pass(type, stream, state) {\n    return states[state.context.type](type, stream, state);\n  }\n  function popAndPass(type, stream, state, n) {\n    for (var i = n || 1; i > 0; i--)\n      state.context = state.context.prev;\n    return pass(type, stream, state);\n  }\n\n  // Parser\n\n  function wordAsValue(stream) {\n    var word = stream.current().toLowerCase();\n    if (valueKeywords.hasOwnProperty(word))\n      override = \"atom\";\n    else if (colorKeywords.hasOwnProperty(word))\n      override = \"keyword\";\n    else\n      override = \"variable\";\n  }\n\n  var states = {};\n\n  states.top = function(type, stream, state) {\n    if (type == \"{\") {\n      return pushContext(state, stream, \"block\");\n    } else if (type == \"}\" && state.context.prev) {\n      return popContext(state);\n    } else if (supportsAtComponent && /@component/i.test(type)) {\n      return pushContext(state, stream, \"atComponentBlock\");\n    } else if (/^@(-moz-)?document$/i.test(type)) {\n      return pushContext(state, stream, \"documentTypes\");\n    } else if (/^@(media|supports|(-moz-)?document|import)$/i.test(type)) {\n      return pushContext(state, stream, \"atBlock\");\n    } else if (/^@(font-face|counter-style)/i.test(type)) {\n      state.stateArg = type;\n      return \"restricted_atBlock_before\";\n    } else if (/^@(-(moz|ms|o|webkit)-)?keyframes$/i.test(type)) {\n      return \"keyframes\";\n    } else if (type && type.charAt(0) == \"@\") {\n      return pushContext(state, stream, \"at\");\n    } else if (type == \"hash\") {\n      override = \"builtin\";\n    } else if (type == \"word\") {\n      override = \"tag\";\n    } else if (type == \"variable-definition\") {\n      return \"maybeprop\";\n    } else if (type == \"interpolation\") {\n      return pushContext(state, stream, \"interpolation\");\n    } else if (type == \":\") {\n      return \"pseudo\";\n    } else if (allowNested && type == \"(\") {\n      return pushContext(state, stream, \"parens\");\n    }\n    return state.context.type;\n  };\n\n  states.block = function(type, stream, state) {\n    if (type == \"word\") {\n      var word = stream.current().toLowerCase();\n      if (propertyKeywords.hasOwnProperty(word)) {\n        override = \"property\";\n        return \"maybeprop\";\n      } else if (nonStandardPropertyKeywords.hasOwnProperty(word)) {\n        override = highlightNonStandardPropertyKeywords ? \"string.special\" : \"property\";\n        return \"maybeprop\";\n      } else if (allowNested) {\n        override = stream.match(/^\\s*:(?:\\s|$)/, false) ? \"property\" : \"tag\";\n        return \"block\";\n      } else {\n        override = \"error\";\n        return \"maybeprop\";\n      }\n    } else if (type == \"meta\") {\n      return \"block\";\n    } else if (!allowNested && (type == \"hash\" || type == \"qualifier\")) {\n      override = \"error\";\n      return \"block\";\n    } else {\n      return states.top(type, stream, state);\n    }\n  };\n\n  states.maybeprop = function(type, stream, state) {\n    if (type == \":\") return pushContext(state, stream, \"prop\");\n    return pass(type, stream, state);\n  };\n\n  states.prop = function(type, stream, state) {\n    if (type == \";\") return popContext(state);\n    if (type == \"{\" && allowNested) return pushContext(state, stream, \"propBlock\");\n    if (type == \"}\" || type == \"{\") return popAndPass(type, stream, state);\n    if (type == \"(\") return pushContext(state, stream, \"parens\");\n\n    if (type == \"hash\" && !/^#([0-9a-fA-F]{3,4}|[0-9a-fA-F]{6}|[0-9a-fA-F]{8})$/.test(stream.current())) {\n      override = \"error\";\n    } else if (type == \"word\") {\n      wordAsValue(stream);\n    } else if (type == \"interpolation\") {\n      return pushContext(state, stream, \"interpolation\");\n    }\n    return \"prop\";\n  };\n\n  states.propBlock = function(type, _stream, state) {\n    if (type == \"}\") return popContext(state);\n    if (type == \"word\") { override = \"property\"; return \"maybeprop\"; }\n    return state.context.type;\n  };\n\n  states.parens = function(type, stream, state) {\n    if (type == \"{\" || type == \"}\") return popAndPass(type, stream, state);\n    if (type == \")\") return popContext(state);\n    if (type == \"(\") return pushContext(state, stream, \"parens\");\n    if (type == \"interpolation\") return pushContext(state, stream, \"interpolation\");\n    if (type == \"word\") wordAsValue(stream);\n    return \"parens\";\n  };\n\n  states.pseudo = function(type, stream, state) {\n    if (type == \"meta\") return \"pseudo\";\n\n    if (type == \"word\") {\n      override = \"variableName.constant\";\n      return state.context.type;\n    }\n    return pass(type, stream, state);\n  };\n\n  states.documentTypes = function(type, stream, state) {\n    if (type == \"word\" && documentTypes.hasOwnProperty(stream.current())) {\n      override = \"tag\";\n      return state.context.type;\n    } else {\n      return states.atBlock(type, stream, state);\n    }\n  };\n\n  states.atBlock = function(type, stream, state) {\n    if (type == \"(\") return pushContext(state, stream, \"atBlock_parens\");\n    if (type == \"}\" || type == \";\") return popAndPass(type, stream, state);\n    if (type == \"{\") return popContext(state) && pushContext(state, stream, allowNested ? \"block\" : \"top\");\n\n    if (type == \"interpolation\") return pushContext(state, stream, \"interpolation\");\n\n    if (type == \"word\") {\n      var word = stream.current().toLowerCase();\n      if (word == \"only\" || word == \"not\" || word == \"and\" || word == \"or\")\n        override = \"keyword\";\n      else if (mediaTypes.hasOwnProperty(word))\n        override = \"attribute\";\n      else if (mediaFeatures.hasOwnProperty(word))\n        override = \"property\";\n      else if (mediaValueKeywords.hasOwnProperty(word))\n        override = \"keyword\";\n      else if (propertyKeywords.hasOwnProperty(word))\n        override = \"property\";\n      else if (nonStandardPropertyKeywords.hasOwnProperty(word))\n        override = highlightNonStandardPropertyKeywords ? \"string.special\" : \"property\";\n      else if (valueKeywords.hasOwnProperty(word))\n        override = \"atom\";\n      else if (colorKeywords.hasOwnProperty(word))\n        override = \"keyword\";\n      else\n        override = \"error\";\n    }\n    return state.context.type;\n  };\n\n  states.atComponentBlock = function(type, stream, state) {\n    if (type == \"}\")\n      return popAndPass(type, stream, state);\n    if (type == \"{\")\n      return popContext(state) && pushContext(state, stream, allowNested ? \"block\" : \"top\", false);\n    if (type == \"word\")\n      override = \"error\";\n    return state.context.type;\n  };\n\n  states.atBlock_parens = function(type, stream, state) {\n    if (type == \")\") return popContext(state);\n    if (type == \"{\" || type == \"}\") return popAndPass(type, stream, state, 2);\n    return states.atBlock(type, stream, state);\n  };\n\n  states.restricted_atBlock_before = function(type, stream, state) {\n    if (type == \"{\")\n      return pushContext(state, stream, \"restricted_atBlock\");\n    if (type == \"word\" && state.stateArg == \"@counter-style\") {\n      override = \"variable\";\n      return \"restricted_atBlock_before\";\n    }\n    return pass(type, stream, state);\n  };\n\n  states.restricted_atBlock = function(type, stream, state) {\n    if (type == \"}\") {\n      state.stateArg = null;\n      return popContext(state);\n    }\n    if (type == \"word\") {\n      if ((state.stateArg == \"@font-face\" && !fontProperties.hasOwnProperty(stream.current().toLowerCase())) ||\n          (state.stateArg == \"@counter-style\" && !counterDescriptors.hasOwnProperty(stream.current().toLowerCase())))\n        override = \"error\";\n      else\n        override = \"property\";\n      return \"maybeprop\";\n    }\n    return \"restricted_atBlock\";\n  };\n\n  states.keyframes = function(type, stream, state) {\n    if (type == \"word\") { override = \"variable\"; return \"keyframes\"; }\n    if (type == \"{\") return pushContext(state, stream, \"top\");\n    return pass(type, stream, state);\n  };\n\n  states.at = function(type, stream, state) {\n    if (type == \";\") return popContext(state);\n    if (type == \"{\" || type == \"}\") return popAndPass(type, stream, state);\n    if (type == \"word\") override = \"tag\";\n    else if (type == \"hash\") override = \"builtin\";\n    return \"at\";\n  };\n\n  states.interpolation = function(type, stream, state) {\n    if (type == \"}\") return popContext(state);\n    if (type == \"{\" || type == \";\") return popAndPass(type, stream, state);\n    if (type == \"word\") override = \"variable\";\n    else if (type != \"variable\" && type != \"(\" && type != \")\") override = \"error\";\n    return \"interpolation\";\n  };\n\n  return {\n    name: parserConfig.name,\n    startState: function() {\n      return {tokenize: null,\n              state: inline ? \"block\" : \"top\",\n              stateArg: null,\n              context: new Context(inline ? \"block\" : \"top\", 0, null)};\n    },\n\n    token: function(stream, state) {\n      if (!state.tokenize && stream.eatSpace()) return null;\n      var style = (state.tokenize || tokenBase)(stream, state);\n      if (style && typeof style == \"object\") {\n        type = style[1];\n        style = style[0];\n      }\n      override = style;\n      if (type != \"comment\")\n        state.state = states[state.state](type, stream, state);\n      return override;\n    },\n\n    indent: function(state, textAfter, iCx) {\n      var cx = state.context, ch = textAfter && textAfter.charAt(0);\n      var indent = cx.indent;\n      if (cx.type == \"prop\" && (ch == \"}\" || ch == \")\")) cx = cx.prev;\n      if (cx.prev) {\n        if (ch == \"}\" && (cx.type == \"block\" || cx.type == \"top\" ||\n                          cx.type == \"interpolation\" || cx.type == \"restricted_atBlock\")) {\n          // Resume indentation from parent context.\n          cx = cx.prev;\n          indent = cx.indent;\n        } else if (ch == \")\" && (cx.type == \"parens\" || cx.type == \"atBlock_parens\") ||\n                   ch == \"{\" && (cx.type == \"at\" || cx.type == \"atBlock\")) {\n          // Dedent relative to current context.\n          indent = Math.max(0, cx.indent - iCx.unit);\n        }\n      }\n      return indent;\n    },\n\n    languageData: {\n      indentOnInput: /^\\s*\\}$/,\n      commentTokens: {line: lineComment, block: {open: \"/*\", close: \"*/\"}},\n      autocomplete: allWords\n    }\n  };\n};\n\nfunction keySet(array) {\n  var keys = {};\n  for (var i = 0; i < array.length; ++i) {\n    keys[array[i].toLowerCase()] = true;\n  }\n  return keys;\n}\n\nvar documentTypes_ = [\n  \"domain\", \"regexp\", \"url\", \"url-prefix\"\n], documentTypes = keySet(documentTypes_);\n\nvar mediaTypes_ = [\n  \"all\", \"aural\", \"braille\", \"handheld\", \"print\", \"projection\", \"screen\",\n  \"tty\", \"tv\", \"embossed\"\n], mediaTypes = keySet(mediaTypes_);\n\nvar mediaFeatures_ = [\n  \"width\", \"min-width\", \"max-width\", \"height\", \"min-height\", \"max-height\",\n  \"device-width\", \"min-device-width\", \"max-device-width\", \"device-height\",\n  \"min-device-height\", \"max-device-height\", \"aspect-ratio\",\n  \"min-aspect-ratio\", \"max-aspect-ratio\", \"device-aspect-ratio\",\n  \"min-device-aspect-ratio\", \"max-device-aspect-ratio\", \"color\", \"min-color\",\n  \"max-color\", \"color-index\", \"min-color-index\", \"max-color-index\",\n  \"monochrome\", \"min-monochrome\", \"max-monochrome\", \"resolution\",\n  \"min-resolution\", \"max-resolution\", \"scan\", \"grid\", \"orientation\",\n  \"device-pixel-ratio\", \"min-device-pixel-ratio\", \"max-device-pixel-ratio\",\n  \"pointer\", \"any-pointer\", \"hover\", \"any-hover\", \"prefers-color-scheme\",\n  \"dynamic-range\", \"video-dynamic-range\"\n], mediaFeatures = keySet(mediaFeatures_);\n\nvar mediaValueKeywords_ = [\n  \"landscape\", \"portrait\", \"none\", \"coarse\", \"fine\", \"on-demand\", \"hover\",\n  \"interlace\", \"progressive\",\n  \"dark\", \"light\",\n  \"standard\", \"high\"\n], mediaValueKeywords = keySet(mediaValueKeywords_);\n\nvar propertyKeywords_ = [\n  \"align-content\", \"align-items\", \"align-self\", \"alignment-adjust\",\n  \"alignment-baseline\", \"all\", \"anchor-point\", \"animation\", \"animation-delay\",\n  \"animation-direction\", \"animation-duration\", \"animation-fill-mode\",\n  \"animation-iteration-count\", \"animation-name\", \"animation-play-state\",\n  \"animation-timing-function\", \"appearance\", \"azimuth\", \"backdrop-filter\",\n  \"backface-visibility\", \"background\", \"background-attachment\",\n  \"background-blend-mode\", \"background-clip\", \"background-color\",\n  \"background-image\", \"background-origin\", \"background-position\",\n  \"background-position-x\", \"background-position-y\", \"background-repeat\",\n  \"background-size\", \"baseline-shift\", \"binding\", \"bleed\", \"block-size\",\n  \"bookmark-label\", \"bookmark-level\", \"bookmark-state\", \"bookmark-target\",\n  \"border\", \"border-bottom\", \"border-bottom-color\", \"border-bottom-left-radius\",\n  \"border-bottom-right-radius\", \"border-bottom-style\", \"border-bottom-width\",\n  \"border-collapse\", \"border-color\", \"border-image\", \"border-image-outset\",\n  \"border-image-repeat\", \"border-image-slice\", \"border-image-source\",\n  \"border-image-width\", \"border-left\", \"border-left-color\", \"border-left-style\",\n  \"border-left-width\", \"border-radius\", \"border-right\", \"border-right-color\",\n  \"border-right-style\", \"border-right-width\", \"border-spacing\", \"border-style\",\n  \"border-top\", \"border-top-color\", \"border-top-left-radius\",\n  \"border-top-right-radius\", \"border-top-style\", \"border-top-width\",\n  \"border-width\", \"bottom\", \"box-decoration-break\", \"box-shadow\", \"box-sizing\",\n  \"break-after\", \"break-before\", \"break-inside\", \"caption-side\", \"caret-color\",\n  \"clear\", \"clip\", \"color\", \"color-profile\", \"column-count\", \"column-fill\",\n  \"column-gap\", \"column-rule\", \"column-rule-color\", \"column-rule-style\",\n  \"column-rule-width\", \"column-span\", \"column-width\", \"columns\", \"contain\",\n  \"content\", \"counter-increment\", \"counter-reset\", \"crop\", \"cue\", \"cue-after\",\n  \"cue-before\", \"cursor\", \"direction\", \"display\", \"dominant-baseline\",\n  \"drop-initial-after-adjust\", \"drop-initial-after-align\",\n  \"drop-initial-before-adjust\", \"drop-initial-before-align\", \"drop-initial-size\",\n  \"drop-initial-value\", \"elevation\", \"empty-cells\", \"fit\", \"fit-content\", \"fit-position\",\n  \"flex\", \"flex-basis\", \"flex-direction\", \"flex-flow\", \"flex-grow\",\n  \"flex-shrink\", \"flex-wrap\", \"float\", \"float-offset\", \"flow-from\", \"flow-into\",\n  \"font\", \"font-family\", \"font-feature-settings\", \"font-kerning\",\n  \"font-language-override\", \"font-optical-sizing\", \"font-size\",\n  \"font-size-adjust\", \"font-stretch\", \"font-style\", \"font-synthesis\",\n  \"font-variant\", \"font-variant-alternates\", \"font-variant-caps\",\n  \"font-variant-east-asian\", \"font-variant-ligatures\", \"font-variant-numeric\",\n  \"font-variant-position\", \"font-variation-settings\", \"font-weight\", \"gap\",\n  \"grid\", \"grid-area\", \"grid-auto-columns\", \"grid-auto-flow\", \"grid-auto-rows\",\n  \"grid-column\", \"grid-column-end\", \"grid-column-gap\", \"grid-column-start\",\n  \"grid-gap\", \"grid-row\", \"grid-row-end\", \"grid-row-gap\", \"grid-row-start\",\n  \"grid-template\", \"grid-template-areas\", \"grid-template-columns\",\n  \"grid-template-rows\", \"hanging-punctuation\", \"height\", \"hyphens\", \"icon\",\n  \"image-orientation\", \"image-rendering\", \"image-resolution\", \"inline-box-align\",\n  \"inset\", \"inset-block\", \"inset-block-end\", \"inset-block-start\", \"inset-inline\",\n  \"inset-inline-end\", \"inset-inline-start\", \"isolation\", \"justify-content\",\n  \"justify-items\", \"justify-self\", \"left\", \"letter-spacing\", \"line-break\",\n  \"line-height\", \"line-height-step\", \"line-stacking\", \"line-stacking-ruby\",\n  \"line-stacking-shift\", \"line-stacking-strategy\", \"list-style\",\n  \"list-style-image\", \"list-style-position\", \"list-style-type\", \"margin\",\n  \"margin-bottom\", \"margin-left\", \"margin-right\", \"margin-top\", \"marks\",\n  \"marquee-direction\", \"marquee-loop\", \"marquee-play-count\", \"marquee-speed\",\n  \"marquee-style\", \"mask-clip\", \"mask-composite\", \"mask-image\", \"mask-mode\",\n  \"mask-origin\", \"mask-position\", \"mask-repeat\", \"mask-size\",\"mask-type\",\n  \"max-block-size\", \"max-height\", \"max-inline-size\",\n  \"max-width\", \"min-block-size\", \"min-height\", \"min-inline-size\", \"min-width\",\n  \"mix-blend-mode\", \"move-to\", \"nav-down\", \"nav-index\", \"nav-left\", \"nav-right\",\n  \"nav-up\", \"object-fit\", \"object-position\", \"offset\", \"offset-anchor\",\n  \"offset-distance\", \"offset-path\", \"offset-position\", \"offset-rotate\",\n  \"opacity\", \"order\", \"orphans\", \"outline\", \"outline-color\", \"outline-offset\",\n  \"outline-style\", \"outline-width\", \"overflow\", \"overflow-style\",\n  \"overflow-wrap\", \"overflow-x\", \"overflow-y\", \"padding\", \"padding-bottom\",\n  \"padding-left\", \"padding-right\", \"padding-top\", \"page\", \"page-break-after\",\n  \"page-break-before\", \"page-break-inside\", \"page-policy\", \"pause\",\n  \"pause-after\", \"pause-before\", \"perspective\", \"perspective-origin\", \"pitch\",\n  \"pitch-range\", \"place-content\", \"place-items\", \"place-self\", \"play-during\",\n  \"position\", \"presentation-level\", \"punctuation-trim\", \"quotes\",\n  \"region-break-after\", \"region-break-before\", \"region-break-inside\",\n  \"region-fragment\", \"rendering-intent\", \"resize\", \"rest\", \"rest-after\",\n  \"rest-before\", \"richness\", \"right\", \"rotate\", \"rotation\", \"rotation-point\",\n  \"row-gap\", \"ruby-align\", \"ruby-overhang\", \"ruby-position\", \"ruby-span\",\n  \"scale\", \"scroll-behavior\", \"scroll-margin\", \"scroll-margin-block\",\n  \"scroll-margin-block-end\", \"scroll-margin-block-start\", \"scroll-margin-bottom\",\n  \"scroll-margin-inline\", \"scroll-margin-inline-end\",\n  \"scroll-margin-inline-start\", \"scroll-margin-left\", \"scroll-margin-right\",\n  \"scroll-margin-top\", \"scroll-padding\", \"scroll-padding-block\",\n  \"scroll-padding-block-end\", \"scroll-padding-block-start\",\n  \"scroll-padding-bottom\", \"scroll-padding-inline\", \"scroll-padding-inline-end\",\n  \"scroll-padding-inline-start\", \"scroll-padding-left\", \"scroll-padding-right\",\n  \"scroll-padding-top\", \"scroll-snap-align\", \"scroll-snap-type\",\n  \"shape-image-threshold\", \"shape-inside\", \"shape-margin\", \"shape-outside\",\n  \"size\", \"speak\", \"speak-as\", \"speak-header\", \"speak-numeral\",\n  \"speak-punctuation\", \"speech-rate\", \"stress\", \"string-set\", \"tab-size\",\n  \"table-layout\", \"target\", \"target-name\", \"target-new\", \"target-position\",\n  \"text-align\", \"text-align-last\", \"text-combine-upright\", \"text-decoration\",\n  \"text-decoration-color\", \"text-decoration-line\", \"text-decoration-skip\",\n  \"text-decoration-skip-ink\", \"text-decoration-style\", \"text-emphasis\",\n  \"text-emphasis-color\", \"text-emphasis-position\", \"text-emphasis-style\",\n  \"text-height\", \"text-indent\", \"text-justify\", \"text-orientation\",\n  \"text-outline\", \"text-overflow\", \"text-rendering\", \"text-shadow\",\n  \"text-size-adjust\", \"text-space-collapse\", \"text-transform\",\n  \"text-underline-position\", \"text-wrap\", \"top\", \"touch-action\", \"transform\", \"transform-origin\",\n  \"transform-style\", \"transition\", \"transition-delay\", \"transition-duration\",\n  \"transition-property\", \"transition-timing-function\", \"translate\",\n  \"unicode-bidi\", \"user-select\", \"vertical-align\", \"visibility\", \"voice-balance\",\n  \"voice-duration\", \"voice-family\", \"voice-pitch\", \"voice-range\", \"voice-rate\",\n  \"voice-stress\", \"voice-volume\", \"volume\", \"white-space\", \"widows\", \"width\",\n  \"will-change\", \"word-break\", \"word-spacing\", \"word-wrap\", \"writing-mode\", \"z-index\",\n  // SVG-specific\n  \"clip-path\", \"clip-rule\", \"mask\", \"enable-background\", \"filter\", \"flood-color\",\n  \"flood-opacity\", \"lighting-color\", \"stop-color\", \"stop-opacity\", \"pointer-events\",\n  \"color-interpolation\", \"color-interpolation-filters\",\n  \"color-rendering\", \"fill\", \"fill-opacity\", \"fill-rule\", \"image-rendering\",\n  \"marker\", \"marker-end\", \"marker-mid\", \"marker-start\", \"paint-order\", \"shape-rendering\", \"stroke\",\n  \"stroke-dasharray\", \"stroke-dashoffset\", \"stroke-linecap\", \"stroke-linejoin\",\n  \"stroke-miterlimit\", \"stroke-opacity\", \"stroke-width\", \"text-rendering\",\n  \"baseline-shift\", \"dominant-baseline\", \"glyph-orientation-horizontal\",\n  \"glyph-orientation-vertical\", \"text-anchor\", \"writing-mode\",\n], propertyKeywords = keySet(propertyKeywords_);\n\nvar nonStandardPropertyKeywords_ = [\n  \"accent-color\", \"aspect-ratio\", \"border-block\", \"border-block-color\", \"border-block-end\",\n  \"border-block-end-color\", \"border-block-end-style\", \"border-block-end-width\",\n  \"border-block-start\", \"border-block-start-color\", \"border-block-start-style\",\n  \"border-block-start-width\", \"border-block-style\", \"border-block-width\",\n  \"border-inline\", \"border-inline-color\", \"border-inline-end\",\n  \"border-inline-end-color\", \"border-inline-end-style\",\n  \"border-inline-end-width\", \"border-inline-start\", \"border-inline-start-color\",\n  \"border-inline-start-style\", \"border-inline-start-width\",\n  \"border-inline-style\", \"border-inline-width\", \"content-visibility\", \"margin-block\",\n  \"margin-block-end\", \"margin-block-start\", \"margin-inline\", \"margin-inline-end\",\n  \"margin-inline-start\", \"overflow-anchor\", \"overscroll-behavior\", \"padding-block\", \"padding-block-end\",\n  \"padding-block-start\", \"padding-inline\", \"padding-inline-end\",\n  \"padding-inline-start\", \"scroll-snap-stop\", \"scrollbar-3d-light-color\",\n  \"scrollbar-arrow-color\", \"scrollbar-base-color\", \"scrollbar-dark-shadow-color\",\n  \"scrollbar-face-color\", \"scrollbar-highlight-color\", \"scrollbar-shadow-color\",\n  \"scrollbar-track-color\", \"searchfield-cancel-button\", \"searchfield-decoration\",\n  \"searchfield-results-button\", \"searchfield-results-decoration\", \"shape-inside\", \"zoom\"\n], nonStandardPropertyKeywords = keySet(nonStandardPropertyKeywords_);\n\nvar fontProperties_ = [\n  \"font-display\", \"font-family\", \"src\", \"unicode-range\", \"font-variant\",\n  \"font-feature-settings\", \"font-stretch\", \"font-weight\", \"font-style\"\n], fontProperties = keySet(fontProperties_);\n\nvar counterDescriptors_ = [\n  \"additive-symbols\", \"fallback\", \"negative\", \"pad\", \"prefix\", \"range\",\n  \"speak-as\", \"suffix\", \"symbols\", \"system\"\n], counterDescriptors = keySet(counterDescriptors_);\n\nvar colorKeywords_ = [\n  \"aliceblue\", \"antiquewhite\", \"aqua\", \"aquamarine\", \"azure\", \"beige\",\n  \"bisque\", \"black\", \"blanchedalmond\", \"blue\", \"blueviolet\", \"brown\",\n  \"burlywood\", \"cadetblue\", \"chartreuse\", \"chocolate\", \"coral\", \"cornflowerblue\",\n  \"cornsilk\", \"crimson\", \"cyan\", \"darkblue\", \"darkcyan\", \"darkgoldenrod\",\n  \"darkgray\", \"darkgreen\", \"darkgrey\", \"darkkhaki\", \"darkmagenta\", \"darkolivegreen\",\n  \"darkorange\", \"darkorchid\", \"darkred\", \"darksalmon\", \"darkseagreen\",\n  \"darkslateblue\", \"darkslategray\", \"darkslategrey\", \"darkturquoise\", \"darkviolet\",\n  \"deeppink\", \"deepskyblue\", \"dimgray\", \"dimgrey\", \"dodgerblue\", \"firebrick\",\n  \"floralwhite\", \"forestgreen\", \"fuchsia\", \"gainsboro\", \"ghostwhite\",\n  \"gold\", \"goldenrod\", \"gray\", \"grey\", \"green\", \"greenyellow\", \"honeydew\",\n  \"hotpink\", \"indianred\", \"indigo\", \"ivory\", \"khaki\", \"lavender\",\n  \"lavenderblush\", \"lawngreen\", \"lemonchiffon\", \"lightblue\", \"lightcoral\",\n  \"lightcyan\", \"lightgoldenrodyellow\", \"lightgray\", \"lightgreen\", \"lightgrey\", \"lightpink\",\n  \"lightsalmon\", \"lightseagreen\", \"lightskyblue\", \"lightslategray\", \"lightslategrey\",\n  \"lightsteelblue\", \"lightyellow\", \"lime\", \"limegreen\", \"linen\", \"magenta\",\n  \"maroon\", \"mediumaquamarine\", \"mediumblue\", \"mediumorchid\", \"mediumpurple\",\n  \"mediumseagreen\", \"mediumslateblue\", \"mediumspringgreen\", \"mediumturquoise\",\n  \"mediumvioletred\", \"midnightblue\", \"mintcream\", \"mistyrose\", \"moccasin\",\n  \"navajowhite\", \"navy\", \"oldlace\", \"olive\", \"olivedrab\", \"orange\", \"orangered\",\n  \"orchid\", \"palegoldenrod\", \"palegreen\", \"paleturquoise\", \"palevioletred\",\n  \"papayawhip\", \"peachpuff\", \"peru\", \"pink\", \"plum\", \"powderblue\",\n  \"purple\", \"rebeccapurple\", \"red\", \"rosybrown\", \"royalblue\", \"saddlebrown\",\n  \"salmon\", \"sandybrown\", \"seagreen\", \"seashell\", \"sienna\", \"silver\", \"skyblue\",\n  \"slateblue\", \"slategray\", \"slategrey\", \"snow\", \"springgreen\", \"steelblue\", \"tan\",\n  \"teal\", \"thistle\", \"tomato\", \"turquoise\", \"violet\", \"wheat\", \"white\",\n  \"whitesmoke\", \"yellow\", \"yellowgreen\"\n], colorKeywords = keySet(colorKeywords_);\n\nvar valueKeywords_ = [\n  \"above\", \"absolute\", \"activeborder\", \"additive\", \"activecaption\", \"afar\",\n  \"after-white-space\", \"ahead\", \"alias\", \"all\", \"all-scroll\", \"alphabetic\", \"alternate\",\n  \"always\", \"amharic\", \"amharic-abegede\", \"antialiased\", \"appworkspace\",\n  \"arabic-indic\", \"armenian\", \"asterisks\", \"attr\", \"auto\", \"auto-flow\", \"avoid\", \"avoid-column\", \"avoid-page\",\n  \"avoid-region\", \"axis-pan\", \"background\", \"backwards\", \"baseline\", \"below\", \"bidi-override\", \"binary\",\n  \"bengali\", \"blink\", \"block\", \"block-axis\", \"blur\", \"bold\", \"bolder\", \"border\", \"border-box\",\n  \"both\", \"bottom\", \"break\", \"break-all\", \"break-word\", \"brightness\", \"bullets\", \"button\",\n  \"buttonface\", \"buttonhighlight\", \"buttonshadow\", \"buttontext\", \"calc\", \"cambodian\",\n  \"capitalize\", \"caps-lock-indicator\", \"caption\", \"captiontext\", \"caret\",\n  \"cell\", \"center\", \"checkbox\", \"circle\", \"cjk-decimal\", \"cjk-earthly-branch\",\n  \"cjk-heavenly-stem\", \"cjk-ideographic\", \"clear\", \"clip\", \"close-quote\",\n  \"col-resize\", \"collapse\", \"color\", \"color-burn\", \"color-dodge\", \"column\", \"column-reverse\",\n  \"compact\", \"condensed\", \"conic-gradient\", \"contain\", \"content\", \"contents\",\n  \"content-box\", \"context-menu\", \"continuous\", \"contrast\", \"copy\", \"counter\", \"counters\", \"cover\", \"crop\",\n  \"cross\", \"crosshair\", \"cubic-bezier\", \"currentcolor\", \"cursive\", \"cyclic\", \"darken\", \"dashed\", \"decimal\",\n  \"decimal-leading-zero\", \"default\", \"default-button\", \"dense\", \"destination-atop\",\n  \"destination-in\", \"destination-out\", \"destination-over\", \"devanagari\", \"difference\",\n  \"disc\", \"discard\", \"disclosure-closed\", \"disclosure-open\", \"document\",\n  \"dot-dash\", \"dot-dot-dash\",\n  \"dotted\", \"double\", \"down\", \"drop-shadow\", \"e-resize\", \"ease\", \"ease-in\", \"ease-in-out\", \"ease-out\",\n  \"element\", \"ellipse\", \"ellipsis\", \"embed\", \"end\", \"ethiopic\", \"ethiopic-abegede\",\n  \"ethiopic-abegede-am-et\", \"ethiopic-abegede-gez\", \"ethiopic-abegede-ti-er\",\n  \"ethiopic-abegede-ti-et\", \"ethiopic-halehame-aa-er\",\n  \"ethiopic-halehame-aa-et\", \"ethiopic-halehame-am-et\",\n  \"ethiopic-halehame-gez\", \"ethiopic-halehame-om-et\",\n  \"ethiopic-halehame-sid-et\", \"ethiopic-halehame-so-et\",\n  \"ethiopic-halehame-ti-er\", \"ethiopic-halehame-ti-et\", \"ethiopic-halehame-tig\",\n  \"ethiopic-numeric\", \"ew-resize\", \"exclusion\", \"expanded\", \"extends\", \"extra-condensed\",\n  \"extra-expanded\", \"fantasy\", \"fast\", \"fill\", \"fill-box\", \"fixed\", \"flat\", \"flex\", \"flex-end\", \"flex-start\", \"footnotes\",\n  \"forwards\", \"from\", \"geometricPrecision\", \"georgian\", \"grayscale\", \"graytext\", \"grid\", \"groove\",\n  \"gujarati\", \"gurmukhi\", \"hand\", \"hangul\", \"hangul-consonant\", \"hard-light\", \"hebrew\",\n  \"help\", \"hidden\", \"hide\", \"higher\", \"highlight\", \"highlighttext\",\n  \"hiragana\", \"hiragana-iroha\", \"horizontal\", \"hsl\", \"hsla\", \"hue\", \"hue-rotate\", \"icon\", \"ignore\",\n  \"inactiveborder\", \"inactivecaption\", \"inactivecaptiontext\", \"infinite\",\n  \"infobackground\", \"infotext\", \"inherit\", \"initial\", \"inline\", \"inline-axis\",\n  \"inline-block\", \"inline-flex\", \"inline-grid\", \"inline-table\", \"inset\", \"inside\", \"intrinsic\", \"invert\",\n  \"italic\", \"japanese-formal\", \"japanese-informal\", \"justify\", \"kannada\",\n  \"katakana\", \"katakana-iroha\", \"keep-all\", \"khmer\",\n  \"korean-hangul-formal\", \"korean-hanja-formal\", \"korean-hanja-informal\",\n  \"landscape\", \"lao\", \"large\", \"larger\", \"left\", \"level\", \"lighter\", \"lighten\",\n  \"line-through\", \"linear\", \"linear-gradient\", \"lines\", \"list-item\", \"listbox\", \"listitem\",\n  \"local\", \"logical\", \"loud\", \"lower\", \"lower-alpha\", \"lower-armenian\",\n  \"lower-greek\", \"lower-hexadecimal\", \"lower-latin\", \"lower-norwegian\",\n  \"lower-roman\", \"lowercase\", \"ltr\", \"luminosity\", \"malayalam\", \"manipulation\", \"match\", \"matrix\", \"matrix3d\",\n  \"media-play-button\", \"media-slider\", \"media-sliderthumb\",\n  \"media-volume-slider\", \"media-volume-sliderthumb\", \"medium\",\n  \"menu\", \"menulist\", \"menulist-button\",\n  \"menutext\", \"message-box\", \"middle\", \"min-intrinsic\",\n  \"mix\", \"mongolian\", \"monospace\", \"move\", \"multiple\", \"multiple_mask_images\", \"multiply\", \"myanmar\", \"n-resize\",\n  \"narrower\", \"ne-resize\", \"nesw-resize\", \"no-close-quote\", \"no-drop\",\n  \"no-open-quote\", \"no-repeat\", \"none\", \"normal\", \"not-allowed\", \"nowrap\",\n  \"ns-resize\", \"numbers\", \"numeric\", \"nw-resize\", \"nwse-resize\", \"oblique\", \"octal\", \"opacity\", \"open-quote\",\n  \"optimizeLegibility\", \"optimizeSpeed\", \"oriya\", \"oromo\", \"outset\",\n  \"outside\", \"outside-shape\", \"overlay\", \"overline\", \"padding\", \"padding-box\",\n  \"painted\", \"page\", \"paused\", \"persian\", \"perspective\", \"pinch-zoom\", \"plus-darker\", \"plus-lighter\",\n  \"pointer\", \"polygon\", \"portrait\", \"pre\", \"pre-line\", \"pre-wrap\", \"preserve-3d\",\n  \"progress\", \"push-button\", \"radial-gradient\", \"radio\", \"read-only\",\n  \"read-write\", \"read-write-plaintext-only\", \"rectangle\", \"region\",\n  \"relative\", \"repeat\", \"repeating-linear-gradient\", \"repeating-radial-gradient\",\n  \"repeating-conic-gradient\", \"repeat-x\", \"repeat-y\", \"reset\", \"reverse\",\n  \"rgb\", \"rgba\", \"ridge\", \"right\", \"rotate\", \"rotate3d\", \"rotateX\", \"rotateY\",\n  \"rotateZ\", \"round\", \"row\", \"row-resize\", \"row-reverse\", \"rtl\", \"run-in\", \"running\",\n  \"s-resize\", \"sans-serif\", \"saturate\", \"saturation\", \"scale\", \"scale3d\", \"scaleX\", \"scaleY\", \"scaleZ\", \"screen\",\n  \"scroll\", \"scrollbar\", \"scroll-position\", \"se-resize\", \"searchfield\",\n  \"searchfield-cancel-button\", \"searchfield-decoration\",\n  \"searchfield-results-button\", \"searchfield-results-decoration\", \"self-start\", \"self-end\",\n  \"semi-condensed\", \"semi-expanded\", \"separate\", \"sepia\", \"serif\", \"show\", \"sidama\",\n  \"simp-chinese-formal\", \"simp-chinese-informal\", \"single\",\n  \"skew\", \"skewX\", \"skewY\", \"skip-white-space\", \"slide\", \"slider-horizontal\",\n  \"slider-vertical\", \"sliderthumb-horizontal\", \"sliderthumb-vertical\", \"slow\",\n  \"small\", \"small-caps\", \"small-caption\", \"smaller\", \"soft-light\", \"solid\", \"somali\",\n  \"source-atop\", \"source-in\", \"source-out\", \"source-over\", \"space\", \"space-around\", \"space-between\", \"space-evenly\", \"spell-out\", \"square\",\n  \"square-button\", \"start\", \"static\", \"status-bar\", \"stretch\", \"stroke\", \"stroke-box\", \"sub\",\n  \"subpixel-antialiased\", \"svg_masks\", \"super\", \"sw-resize\", \"symbolic\", \"symbols\", \"system-ui\", \"table\",\n  \"table-caption\", \"table-cell\", \"table-column\", \"table-column-group\",\n  \"table-footer-group\", \"table-header-group\", \"table-row\", \"table-row-group\",\n  \"tamil\",\n  \"telugu\", \"text\", \"text-bottom\", \"text-top\", \"textarea\", \"textfield\", \"thai\",\n  \"thick\", \"thin\", \"threeddarkshadow\", \"threedface\", \"threedhighlight\",\n  \"threedlightshadow\", \"threedshadow\", \"tibetan\", \"tigre\", \"tigrinya-er\",\n  \"tigrinya-er-abegede\", \"tigrinya-et\", \"tigrinya-et-abegede\", \"to\", \"top\",\n  \"trad-chinese-formal\", \"trad-chinese-informal\", \"transform\",\n  \"translate\", \"translate3d\", \"translateX\", \"translateY\", \"translateZ\",\n  \"transparent\", \"ultra-condensed\", \"ultra-expanded\", \"underline\", \"unidirectional-pan\", \"unset\", \"up\",\n  \"upper-alpha\", \"upper-armenian\", \"upper-greek\", \"upper-hexadecimal\",\n  \"upper-latin\", \"upper-norwegian\", \"upper-roman\", \"uppercase\", \"urdu\", \"url\",\n  \"var\", \"vertical\", \"vertical-text\", \"view-box\", \"visible\", \"visibleFill\", \"visiblePainted\",\n  \"visibleStroke\", \"visual\", \"w-resize\", \"wait\", \"wave\", \"wider\",\n  \"window\", \"windowframe\", \"windowtext\", \"words\", \"wrap\", \"wrap-reverse\", \"x-large\", \"x-small\", \"xor\",\n  \"xx-large\", \"xx-small\"\n], valueKeywords = keySet(valueKeywords_);\n\nvar allWords = documentTypes_.concat(mediaTypes_).concat(mediaFeatures_).concat(mediaValueKeywords_)\n    .concat(propertyKeywords_).concat(nonStandardPropertyKeywords_).concat(colorKeywords_)\n    .concat(valueKeywords_);\n\nconst keywords = {properties: propertyKeywords_, colors: colorKeywords_,\n                         fonts: fontProperties_, values: valueKeywords_, all: allWords}\n\nconst defaults = {\n  documentTypes: documentTypes,\n  mediaTypes: mediaTypes,\n  mediaFeatures: mediaFeatures,\n  mediaValueKeywords: mediaValueKeywords,\n  propertyKeywords: propertyKeywords,\n  nonStandardPropertyKeywords: nonStandardPropertyKeywords,\n  fontProperties: fontProperties,\n  counterDescriptors: counterDescriptors,\n  colorKeywords: colorKeywords,\n  valueKeywords: valueKeywords,\n  tokenHooks: {\n    \"/\": function(stream, state) {\n      if (!stream.eat(\"*\")) return false;\n      state.tokenize = tokenCComment;\n      return tokenCComment(stream, state);\n    }\n  }\n}\n\nconst css = mkCSS({name: \"css\"})\n\nfunction tokenCComment(stream, state) {\n  var maybeEnd = false, ch;\n  while ((ch = stream.next()) != null) {\n    if (maybeEnd && ch == \"/\") {\n      state.tokenize = null;\n      break;\n    }\n    maybeEnd = (ch == \"*\");\n  }\n  return [\"comment\", \"comment\"];\n}\n\nconst sCSS = mkCSS({\n  name: \"scss\",\n  mediaTypes: mediaTypes,\n  mediaFeatures: mediaFeatures,\n  mediaValueKeywords: mediaValueKeywords,\n  propertyKeywords: propertyKeywords,\n  nonStandardPropertyKeywords: nonStandardPropertyKeywords,\n  colorKeywords: colorKeywords,\n  valueKeywords: valueKeywords,\n  fontProperties: fontProperties,\n  allowNested: true,\n  lineComment: \"//\",\n  tokenHooks: {\n    \"/\": function(stream, state) {\n      if (stream.eat(\"/\")) {\n        stream.skipToEnd();\n        return [\"comment\", \"comment\"];\n      } else if (stream.eat(\"*\")) {\n        state.tokenize = tokenCComment;\n        return tokenCComment(stream, state);\n      } else {\n        return [\"operator\", \"operator\"];\n      }\n    },\n    \":\": function(stream) {\n      if (stream.match(/^\\s*\\{/, false))\n        return [null, null]\n      return false;\n    },\n    \"$\": function(stream) {\n      stream.match(/^[\\w-]+/);\n      if (stream.match(/^\\s*:/, false))\n        return [\"def\", \"variable-definition\"];\n      return [\"variableName.special\", \"variable\"];\n    },\n    \"#\": function(stream) {\n      if (!stream.eat(\"{\")) return false;\n      return [null, \"interpolation\"];\n    }\n  }\n})\n\nconst less = mkCSS({\n  name: \"less\",\n  mediaTypes: mediaTypes,\n  mediaFeatures: mediaFeatures,\n  mediaValueKeywords: mediaValueKeywords,\n  propertyKeywords: propertyKeywords,\n  nonStandardPropertyKeywords: nonStandardPropertyKeywords,\n  colorKeywords: colorKeywords,\n  valueKeywords: valueKeywords,\n  fontProperties: fontProperties,\n  allowNested: true,\n  lineComment: \"//\",\n  tokenHooks: {\n    \"/\": function(stream, state) {\n      if (stream.eat(\"/\")) {\n        stream.skipToEnd();\n        return [\"comment\", \"comment\"];\n      } else if (stream.eat(\"*\")) {\n        state.tokenize = tokenCComment;\n        return tokenCComment(stream, state);\n      } else {\n        return [\"operator\", \"operator\"];\n      }\n    },\n    \"@\": function(stream) {\n      if (stream.eat(\"{\")) return [null, \"interpolation\"];\n      if (stream.match(/^(charset|document|font-face|import|(-(moz|ms|o|webkit)-)?keyframes|media|namespace|page|supports)\\b/i, false)) return false;\n      stream.eatWhile(/[\\w\\\\\\-]/);\n      if (stream.match(/^\\s*:/, false))\n        return [\"def\", \"variable-definition\"];\n      return [\"variableName\", \"variable\"];\n    },\n    \"&\": function() {\n      return [\"atom\", \"atom\"];\n    }\n  }\n})\n\nconst gss = mkCSS({\n  name: \"gss\",\n  documentTypes: documentTypes,\n  mediaTypes: mediaTypes,\n  mediaFeatures: mediaFeatures,\n  propertyKeywords: propertyKeywords,\n  nonStandardPropertyKeywords: nonStandardPropertyKeywords,\n  fontProperties: fontProperties,\n  counterDescriptors: counterDescriptors,\n  colorKeywords: colorKeywords,\n  valueKeywords: valueKeywords,\n  supportsAtComponent: true,\n  tokenHooks: {\n    \"/\": function(stream, state) {\n      if (!stream.eat(\"*\")) return false;\n      state.tokenize = tokenCComment;\n      return tokenCComment(stream, state);\n    }\n  }\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@codemirror/legacy-modes/mode/css.js\n"));

/***/ })

}]);