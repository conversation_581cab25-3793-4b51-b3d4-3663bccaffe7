"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_tasl_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/tasl.mjs":
/*!***************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/tasl.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Tasl\\\",\\\"fileTypes\\\":[\\\"tasl\\\"],\\\"name\\\":\\\"tasl\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#namespace\\\"},{\\\"include\\\":\\\"#type\\\"},{\\\"include\\\":\\\"#class\\\"},{\\\"include\\\":\\\"#edge\\\"}],\\\"repository\\\":{\\\"class\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*(class)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.tasl.class\\\"}},\\\"end\\\":\\\"$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#key\\\"},{\\\"include\\\":\\\"#export\\\"},{\\\"include\\\":\\\"#expression\\\"}]},\\\"comment\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.tasl\\\"}},\\\"match\\\":\\\"(#).*$\\\",\\\"name\\\":\\\"comment.line.number-sign.tasl\\\"},\\\"component\\\":{\\\"begin\\\":\\\"->\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.separator.tasl.component\\\"}},\\\"end\\\":\\\"$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},\\\"coproduct\\\":{\\\"begin\\\":\\\"\\\\\\\\[\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.block.tasl.coproduct\\\"}},\\\"end\\\":\\\"]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.block.tasl.coproduct\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#term\\\"},{\\\"include\\\":\\\"#option\\\"}]},\\\"datatype\\\":{\\\"match\\\":\\\"[a-zA-Z][a-zA-Z0-9]*:(?:[A-Za-z0-9\\\\\\\\-._~!$\\\\\\\\&'()*+,;=:@/?]|%\\\\\\\\h{2})+\\\",\\\"name\\\":\\\"string.regexp\\\"},\\\"edge\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*(edge)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.tasl.edge\\\"}},\\\"end\\\":\\\"$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#key\\\"},{\\\"include\\\":\\\"#export\\\"},{\\\"match\\\":\\\"=/\\\",\\\"name\\\":\\\"punctuation.separator.tasl.edge.source\\\"},{\\\"match\\\":\\\"/=>\\\",\\\"name\\\":\\\"punctuation.separator.tasl.edge.target\\\"},{\\\"match\\\":\\\"=>\\\",\\\"name\\\":\\\"punctuation.separator.tasl.edge\\\"},{\\\"include\\\":\\\"#expression\\\"}]},\\\"export\\\":{\\\"match\\\":\\\"::\\\",\\\"name\\\":\\\"keyword.operator.tasl.export\\\"},\\\"expression\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#literal\\\"},{\\\"include\\\":\\\"#uri\\\"},{\\\"include\\\":\\\"#product\\\"},{\\\"include\\\":\\\"#coproduct\\\"},{\\\"include\\\":\\\"#reference\\\"},{\\\"include\\\":\\\"#optional\\\"},{\\\"include\\\":\\\"#identifier\\\"}]},\\\"identifier\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable\\\"}},\\\"match\\\":\\\"([a-zA-Z][a-zA-Z0-9]*)\\\\\\\\b\\\"},\\\"key\\\":{\\\"match\\\":\\\"[a-zA-Z][a-zA-Z0-9]*:(?:[A-Za-z0-9\\\\\\\\-._~!$\\\\\\\\&'()*+,;=:@/?]|%\\\\\\\\h{2})+\\\",\\\"name\\\":\\\"markup.bold entity.name.class\\\"},\\\"literal\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#datatype\\\"}]},\\\"namespace\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.tasl.namespace\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#namespaceURI\\\"},{\\\"match\\\":\\\"[a-zA-Z][a-zA-Z0-9]*\\\\\\\\b\\\",\\\"name\\\":\\\"entity.name\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\s*(namespace)\\\\\\\\b(.*)\\\"},\\\"namespaceURI\\\":{\\\"match\\\":\\\"[a-z]+:[a-zA-Z0-9\\\\\\\\-._~:/?#\\\\\\\\[\\\\\\\\]@!$\\\\\\\\&'()*+,;%=]+\\\",\\\"name\\\":\\\"markup.underline.link\\\"},\\\"option\\\":{\\\"begin\\\":\\\"<-\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.separator.tasl.option\\\"}},\\\"end\\\":\\\"$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},\\\"optional\\\":{\\\"begin\\\":\\\"\\\\\\\\?\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator\\\"}},\\\"end\\\":\\\"$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},\\\"product\\\":{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.block.tasl.product\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.block.tasl.product\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#term\\\"},{\\\"include\\\":\\\"#component\\\"}]},\\\"reference\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"markup.bold keyword.operator\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#key\\\"}]}},\\\"match\\\":\\\"(\\\\\\\\*)\\\\\\\\s*(.*)\\\"},\\\"term\\\":{\\\"match\\\":\\\"[a-zA-Z][a-zA-Z0-9]*:(?:[A-Za-z0-9\\\\\\\\-._~!$\\\\\\\\&'()*+,;=:@/?]|%\\\\\\\\h{2})+\\\",\\\"name\\\":\\\"entity.other.tasl.key\\\"},\\\"type\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*(type)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.tasl.type\\\"}},\\\"end\\\":\\\"$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},\\\"uri\\\":{\\\"match\\\":\\\"<>\\\",\\\"name\\\":\\\"variable.other.constant\\\"}},\\\"scopeName\\\":\\\"source.tasl\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/tasl.mjs\n"));

/***/ })

}]);