"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_codemirror_legacy-modes_mode_asn1_js"],{

/***/ "(app-pages-browser)/./node_modules/@codemirror/legacy-modes/mode/asn1.js":
/*!************************************************************!*\
  !*** ./node_modules/@codemirror/legacy-modes/mode/asn1.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   asn1: () => (/* binding */ asn1)\n/* harmony export */ });\nfunction words(str) {\n  var obj = {}, words = str.split(\" \");\n  for (var i = 0; i < words.length; ++i) obj[words[i]] = true;\n  return obj;\n}\n\nconst defaults = {\n  keywords: words(\"DEFINITIONS OBJECTS IF DERIVED INFORMATION ACTION\" +\n                  \" REPLY ANY NAMED CHARACTERIZED BEHAVIOUR REGISTERED\" +\n                  \" WITH AS IDENTIFIED CONSTRAINED BY PRESENT BEGIN\" +\n                  \" IMPORTS FROM UNITS SYNTAX MIN-ACCESS MAX-ACCESS\" +\n                  \" MINACCESS MAXACCESS REVISION STATUS DESCRIPTION\" +\n                  \" SEQUENCE SET COMPONENTS OF CHOICE DistinguishedName\" +\n                  \" ENUMERATED SIZE MODULE END INDEX AUGMENTS EXTENSIBILITY\" +\n                  \" IMPLIED EXPORTS\"),\n  cmipVerbs: words(\"ACTIONS ADD GET NOTIFICATIONS REPLACE REMOVE\"),\n  compareTypes: words(\"OPTIONAL DEFAULT MANAGED MODULE-TYPE MODULE_IDENTITY\" +\n                      \" MODULE-COMPLIANCE OBJECT-TYPE OBJECT-IDENTITY\" +\n                      \" OBJECT-COMPLIANCE MODE CONFIRMED CONDITIONAL\" +\n                      \" SUBORDINATE SUPERIOR CLASS TRUE FALSE NULL\" +\n                      \" TEXTUAL-CONVENTION\"),\n  status: words(\"current deprecated mandatory obsolete\"),\n  tags: words(\"APPLICATION AUTOMATIC EXPLICIT IMPLICIT PRIVATE TAGS\" +\n              \" UNIVERSAL\"),\n  storage: words(\"BOOLEAN INTEGER OBJECT IDENTIFIER BIT OCTET STRING\" +\n                 \" UTCTime InterfaceIndex IANAifType CMIP-Attribute\" +\n                 \" REAL PACKAGE PACKAGES IpAddress PhysAddress\" +\n                 \" NetworkAddress BITS BMPString TimeStamp TimeTicks\" +\n                 \" TruthValue RowStatus DisplayString GeneralString\" +\n                 \" GraphicString IA5String NumericString\" +\n                 \" PrintableString SnmpAdminString TeletexString\" +\n                 \" UTF8String VideotexString VisibleString StringStore\" +\n                 \" ISO646String T61String UniversalString Unsigned32\" +\n                 \" Integer32 Gauge Gauge32 Counter Counter32 Counter64\"),\n  modifier: words(\"ATTRIBUTE ATTRIBUTES MANDATORY-GROUP MANDATORY-GROUPS\" +\n                  \" GROUP GROUPS ELEMENTS EQUALITY ORDERING SUBSTRINGS\" +\n                  \" DEFINED\"),\n  accessTypes: words(\"not-accessible accessible-for-notify read-only\" +\n                     \" read-create read-write\"),\n  multiLineStrings: true\n}\n\nfunction asn1(parserConfig) {\n  var keywords = parserConfig.keywords || defaults.keywords,\n      cmipVerbs = parserConfig.cmipVerbs || defaults.cmipVerbs,\n      compareTypes = parserConfig.compareTypes || defaults.compareTypes,\n      status = parserConfig.status || defaults.status,\n      tags = parserConfig.tags || defaults.tags,\n      storage = parserConfig.storage || defaults.storage,\n      modifier = parserConfig.modifier || defaults.modifier,\n      accessTypes = parserConfig.accessTypes|| defaults.accessTypes,\n      multiLineStrings = parserConfig.multiLineStrings || defaults.multiLineStrings,\n      indentStatements = parserConfig.indentStatements !== false;\n  var isOperatorChar = /[\\|\\^]/;\n  var curPunc;\n\n  function tokenBase(stream, state) {\n    var ch = stream.next();\n    if (ch == '\"' || ch == \"'\") {\n      state.tokenize = tokenString(ch);\n      return state.tokenize(stream, state);\n    }\n    if (/[\\[\\]\\(\\){}:=,;]/.test(ch)) {\n      curPunc = ch;\n      return \"punctuation\";\n    }\n    if (ch == \"-\"){\n      if (stream.eat(\"-\")) {\n        stream.skipToEnd();\n        return \"comment\";\n      }\n    }\n    if (/\\d/.test(ch)) {\n      stream.eatWhile(/[\\w\\.]/);\n      return \"number\";\n    }\n    if (isOperatorChar.test(ch)) {\n      stream.eatWhile(isOperatorChar);\n      return \"operator\";\n    }\n\n    stream.eatWhile(/[\\w\\-]/);\n    var cur = stream.current();\n    if (keywords.propertyIsEnumerable(cur)) return \"keyword\";\n    if (cmipVerbs.propertyIsEnumerable(cur)) return \"variableName\";\n    if (compareTypes.propertyIsEnumerable(cur)) return \"atom\";\n    if (status.propertyIsEnumerable(cur)) return \"comment\";\n    if (tags.propertyIsEnumerable(cur)) return \"typeName\";\n    if (storage.propertyIsEnumerable(cur)) return \"modifier\";\n    if (modifier.propertyIsEnumerable(cur)) return \"modifier\";\n    if (accessTypes.propertyIsEnumerable(cur)) return \"modifier\";\n\n    return \"variableName\";\n  }\n\n  function tokenString(quote) {\n    return function(stream, state) {\n      var escaped = false, next, end = false;\n      while ((next = stream.next()) != null) {\n        if (next == quote && !escaped){\n          var afterNext = stream.peek();\n          //look if the character if the quote is like the B in '10100010'B\n          if (afterNext){\n            afterNext = afterNext.toLowerCase();\n            if(afterNext == \"b\" || afterNext == \"h\" || afterNext == \"o\")\n              stream.next();\n          }\n          end = true; break;\n        }\n        escaped = !escaped && next == \"\\\\\";\n      }\n      if (end || !(escaped || multiLineStrings))\n        state.tokenize = null;\n      return \"string\";\n    };\n  }\n\n  function Context(indented, column, type, align, prev) {\n    this.indented = indented;\n    this.column = column;\n    this.type = type;\n    this.align = align;\n    this.prev = prev;\n  }\n  function pushContext(state, col, type) {\n    var indent = state.indented;\n    if (state.context && state.context.type == \"statement\")\n      indent = state.context.indented;\n    return state.context = new Context(indent, col, type, null, state.context);\n  }\n  function popContext(state) {\n    var t = state.context.type;\n    if (t == \")\" || t == \"]\" || t == \"}\")\n      state.indented = state.context.indented;\n    return state.context = state.context.prev;\n  }\n\n  //Interface\n  return {\n    name: \"asn1\",\n    startState: function() {\n      return {\n        tokenize: null,\n        context: new Context(-2, 0, \"top\", false),\n        indented: 0,\n        startOfLine: true\n      };\n    },\n\n    token: function(stream, state) {\n      var ctx = state.context;\n      if (stream.sol()) {\n        if (ctx.align == null) ctx.align = false;\n        state.indented = stream.indentation();\n        state.startOfLine = true;\n      }\n      if (stream.eatSpace()) return null;\n      curPunc = null;\n      var style = (state.tokenize || tokenBase)(stream, state);\n      if (style == \"comment\") return style;\n      if (ctx.align == null) ctx.align = true;\n\n      if ((curPunc == \";\" || curPunc == \":\" || curPunc == \",\")\n          && ctx.type == \"statement\"){\n        popContext(state);\n      }\n      else if (curPunc == \"{\") pushContext(state, stream.column(), \"}\");\n      else if (curPunc == \"[\") pushContext(state, stream.column(), \"]\");\n      else if (curPunc == \"(\") pushContext(state, stream.column(), \")\");\n      else if (curPunc == \"}\") {\n        while (ctx.type == \"statement\") ctx = popContext(state);\n        if (ctx.type == \"}\") ctx = popContext(state);\n        while (ctx.type == \"statement\") ctx = popContext(state);\n      }\n      else if (curPunc == ctx.type) popContext(state);\n      else if (indentStatements && (((ctx.type == \"}\" || ctx.type == \"top\")\n                                     && curPunc != ';') || (ctx.type == \"statement\"\n                                                            && curPunc == \"newstatement\")))\n        pushContext(state, stream.column(), \"statement\");\n\n      state.startOfLine = false;\n      return style;\n    },\n\n    languageData: {\n      indentOnInput: /^\\s*[{}]$/,\n      commentTokens: {line: \"--\"}\n    }\n  };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@codemirror/legacy-modes/mode/asn1.js\n"));

/***/ })

}]);