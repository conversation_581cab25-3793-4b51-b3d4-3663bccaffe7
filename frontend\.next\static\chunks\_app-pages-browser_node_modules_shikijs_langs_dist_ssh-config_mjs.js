"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_ssh-config_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/ssh-config.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/ssh-config.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"SSH Config\\\",\\\"fileTypes\\\":[\\\"ssh_config\\\",\\\".ssh/config\\\",\\\"sshd_config\\\"],\\\"name\\\":\\\"ssh-config\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(A(cceptEnv|dd(ressFamily|KeysToAgent)|llow(AgentForwarding|Groups|StreamLocalForwarding|TcpForwarding|Users)|uth(enticationMethods|orized((Keys(Command(User)?|File)|Principals(Command(User)?|File)))))|B(anner|atchMode|ind(Address|Interface))|C(anonical(Domains|ize(FallbackLocal|Hostname|MaxDots|PermittedCNAMEs))|ertificateFile|hallengeResponseAuthentication|heckHostIP|hrootDirectory|iphers?|learAllForwardings|ientAlive(CountMax|Interval)|ompression(Level)?|onnect(Timeout|ionAttempts)|ontrolMaster|ontrolPath|ontrolPersist)|D(eny(Groups|Users)|isableForwarding|ynamicForward)|E(nableSSHKeysign|scapeChar|xitOnForwardFailure|xposeAuthInfo)|F(ingerprintHash|orceCommand|orward(Agent|X11(T(?:imeout|rusted))?))|G(atewayPorts|SSAPI(Authentication|CleanupCredentials|ClientIdentity|DelegateCredentials|KeyExchange|RenewalForcesRekey|ServerIdentity|StrictAcceptorCheck|TrustDns)|atewayPorts|lobalKnownHostsFile)|H(ashKnownHosts|ost(based(AcceptedKeyTypes|Authentication|KeyTypes|UsesNameFromPacketOnly)|Certificate|Key(A(?:gent|lgorithms|lias))?|Name))|I(dentit(iesOnly|y(Agent|File))|gnore(Rhosts|Unknown|UserKnownHosts)|nclude|PQoS)|K(bdInteractive(Authentication|Devices)|erberos(Authentication|GetAFSToken|OrLocalPasswd|TicketCleanup)|exAlgorithms)|L(istenAddress|ocal(Command|Forward)|oginGraceTime|ogLevel)|M(ACs|atch|ax(AuthTries|Sessions|Startups))|N(oHostAuthenticationForLocalhost|umberOfPasswordPrompts)|P(KCS11Provider|asswordAuthentication|ermit(EmptyPasswords|LocalCommand|Open|RootLogin|TTY|Tunnel|User(Environment|RC))|idFile|ort|referredAuthentications|rint(LastLog|Motd)|rotocol|roxy(Command|Jump|UseFdpass)|ubkey(A(?:cceptedKeyTypes|uthentication)))|R(Domain|SAAuthentication|ekeyLimit|emote(Command|Forward)|equestTTY|evoked(HostKeys|Keys)|hostsRSAAuthentication)|S(endEnv|erverAlive(CountMax|Interval)|treamLocalBind(Mask|Unlink)|trict(HostKeyChecking|Modes)|ubsystem|yslogFacility)|T(CPKeepAlive|rustedUserCAKeys|unnel(Device)?)|U(pdateHostKeys|se(BlacklistedKeys|DNS|Keychain|PAM|PrivilegedPort|r(KnownHostsFile)?))|V(erifyHostKeyDNS|ersionAddendum|isualHostKey)|X(11(DisplayOffset|Forwarding|UseLocalhost)|AuthLocation))\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.ssh-config\\\"},{\\\"begin\\\":\\\"(^[ \\\\\\\\t]+)?(?=#)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.comment.leading.ssh-config\\\"}},\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"#\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.ssh-config\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.line.number-sign.ssh-config\\\"}]},{\\\"begin\\\":\\\"(^[ \\\\\\\\t]+)?(?=//)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.comment.leading.ssh-config\\\"}},\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"//\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.ssh-config\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.line.double-slash.ssh-config\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.ssh-config\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.section.ssh-config\\\"},\\\"3\\\":{\\\"name\\\":\\\"meta.toc-list.ssh-config\\\"}},\\\"match\\\":\\\"(?:^|[ \\\\\\\\t])(Host)\\\\\\\\s+((.*))$\\\"},{\\\"match\\\":\\\"\\\\\\\\b(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\\\\\\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\\\\\\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\\\\\\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.ssh-config\\\"},{\\\"match\\\":\\\"\\\\\\\\b[0-9]+\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.ssh-config\\\"},{\\\"match\\\":\\\"\\\\\\\\b(yes|no)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.ssh-config\\\"},{\\\"match\\\":\\\"\\\\\\\\b[A-Z_]+\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.ssh-config\\\"}],\\\"scopeName\\\":\\\"source.ssh-config\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/ssh-config.mjs\n"));

/***/ })

}]);