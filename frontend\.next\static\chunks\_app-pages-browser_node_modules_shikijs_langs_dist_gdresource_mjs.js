"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_gdresource_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/gdresource.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/gdresource.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _gdshader_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./gdshader.mjs */ \"(app-pages-browser)/./node_modules/@shikijs/langs/dist/gdshader.mjs\");\n/* harmony import */ var _gdscript_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./gdscript.mjs */ \"(app-pages-browser)/./node_modules/@shikijs/langs/dist/gdscript.mjs\");\n\n\n\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"GDResource\\\",\\\"name\\\":\\\"gdresource\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#embedded_shader\\\"},{\\\"include\\\":\\\"#embedded_gdscript\\\"},{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#heading\\\"},{\\\"include\\\":\\\"#key_value\\\"}],\\\"repository\\\":{\\\"comment\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.gdresource\\\"}},\\\"match\\\":\\\"(;).*$\\\\\\\\n?\\\",\\\"name\\\":\\\"comment.line.gdresource\\\"},\\\"data\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"begin\\\":\\\"(?<!\\\\\\\\w)(\\\\\\\\{)\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.table.inline.gdresource\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(})(?!\\\\\\\\w)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.table.inline.gdresource\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#key_value\\\"},{\\\"include\\\":\\\"#data\\\"}]},{\\\"begin\\\":\\\"(?<!\\\\\\\\w)(\\\\\\\\[)\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.array.gdresource\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(])(?!\\\\\\\\w)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.array.gdresource\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#data\\\"}]},{\\\"begin\\\":\\\"\\\\\\\"\\\\\\\"\\\\\\\"\\\",\\\"end\\\":\\\"\\\\\\\"\\\\\\\"\\\\\\\"\\\",\\\"name\\\":\\\"string.quoted.triple.basic.block.gdresource\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\([btnfr\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\n/ ]|u\\\\\\\\h{4}|U\\\\\\\\h{8})\\\",\\\"name\\\":\\\"constant.character.escape.gdresource\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\[^btnfr/\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\n]\\\",\\\"name\\\":\\\"invalid.illegal.escape.gdresource\\\"}]},{\\\"match\\\":\\\"\\\\\\\"res://[^\\\\\\\"\\\\\\\\\\\\\\\\]*(?:\\\\\\\\\\\\\\\\.[^\\\\\\\"\\\\\\\\\\\\\\\\]*)*\\\\\\\"\\\",\\\"name\\\":\\\"support.function.any-method.gdresource\\\"},{\\\"match\\\":\\\"(?<=type=)\\\\\\\"[^\\\\\\\"\\\\\\\\\\\\\\\\]*(?:\\\\\\\\\\\\\\\\.[^\\\\\\\"\\\\\\\\\\\\\\\\]*)*\\\\\\\"\\\",\\\"name\\\":\\\"support.class.library.gdresource\\\"},{\\\"match\\\":\\\"(?<=NodePath\\\\\\\\(|parent=|name=)\\\\\\\"[^\\\\\\\"\\\\\\\\\\\\\\\\]*(?:\\\\\\\\\\\\\\\\.[^\\\\\\\"\\\\\\\\\\\\\\\\]*)*\\\\\\\"\\\",\\\"name\\\":\\\"constant.character.escape.gdresource\\\"},{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"end\\\":\\\"\\\\\\\"\\\",\\\"name\\\":\\\"string.quoted.double.basic.line.gdresource\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\([btnfr\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\n/ ]|u\\\\\\\\h{4}|U\\\\\\\\h{8})\\\",\\\"name\\\":\\\"constant.character.escape.gdresource\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\[^btnfr/\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\n]\\\",\\\"name\\\":\\\"invalid.illegal.escape.gdresource\\\"}]},{\\\"match\\\":\\\"'.*?'\\\",\\\"name\\\":\\\"string.quoted.single.literal.line.gdresource\\\"},{\\\"match\\\":\\\"(?<!\\\\\\\\w)(true|false)(?!\\\\\\\\w)\\\",\\\"name\\\":\\\"constant.language.gdresource\\\"},{\\\"match\\\":\\\"(?<!\\\\\\\\w)([+-]?(0|([1-9](([0-9]|_[0-9])+)?))(?:(?:\\\\\\\\.(0|([1-9](([0-9]|_[0-9])+)?)))?[eE][+-]?[1-9]_?[0-9]*|\\\\\\\\.[0-9_]*))(?!\\\\\\\\w)\\\",\\\"name\\\":\\\"constant.numeric.float.gdresource\\\"},{\\\"match\\\":\\\"(?<!\\\\\\\\w)([+-]?(0|([1-9](([0-9]|_[0-9])+)?)))(?!\\\\\\\\w)\\\",\\\"name\\\":\\\"constant.numeric.integer.gdresource\\\"},{\\\"match\\\":\\\"(?<!\\\\\\\\w)([+-]?inf)(?!\\\\\\\\w)\\\",\\\"name\\\":\\\"constant.numeric.inf.gdresource\\\"},{\\\"match\\\":\\\"(?<!\\\\\\\\w)([+-]?nan)(?!\\\\\\\\w)\\\",\\\"name\\\":\\\"constant.numeric.nan.gdresource\\\"},{\\\"match\\\":\\\"(?<!\\\\\\\\w)(0x((\\\\\\\\h((\\\\\\\\h|_\\\\\\\\h)+)?)))(?!\\\\\\\\w)\\\",\\\"name\\\":\\\"constant.numeric.hex.gdresource\\\"},{\\\"match\\\":\\\"(?<!\\\\\\\\w)(0o[0-7](_?[0-7])*)(?!\\\\\\\\w)\\\",\\\"name\\\":\\\"constant.numeric.oct.gdresource\\\"},{\\\"match\\\":\\\"(?<!\\\\\\\\w)(0b[01](_?[01])*)(?!\\\\\\\\w)\\\",\\\"name\\\":\\\"constant.numeric.bin.gdresource\\\"},{\\\"begin\\\":\\\"(?<!\\\\\\\\w)(Vector2|Vector2i|Vector3|Vector3i|Color|Rect2|Rect2i|Array|Basis|Dictionary|Plane|Quat|RID|Rect3|Transform|Transform2D|Transform3D|AABB|String|Color|NodePath|Object|PoolByteArray|PoolIntArray|PoolRealArray|PoolStringArray|PoolVector2Array|PoolVector3Array|PoolColorArray|bool|int|float|StringName|Quaternion|PackedByteArray|PackedInt32Array|PackedInt64Array|PackedFloat32Array|PackedFloat64Array|PackedStringArray|PackedVector2Array|PackedVector2iArray|PackedVector3Array|PackedVector3iArray|PackedColorArray)(\\\\\\\\()\\\\\\\\s?\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.class.library.gdresource\\\"}},\\\"end\\\":\\\"\\\\\\\\s?(\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#key_value\\\"},{\\\"include\\\":\\\"#data\\\"}]},{\\\"begin\\\":\\\"(?<!\\\\\\\\w)(ExtResource|SubResource)(\\\\\\\\()\\\\\\\\s?\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.gdresource\\\"}},\\\"end\\\":\\\"\\\\\\\\s?(\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#key_value\\\"},{\\\"include\\\":\\\"#data\\\"}]}]},\\\"embedded_gdscript\\\":{\\\"begin\\\":\\\"(script/source) = \\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.property.gdresource\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.gdscript\\\"}]},\\\"embedded_shader\\\":{\\\"begin\\\":\\\"(code) = \\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.property.gdresource\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"name\\\":\\\"meta.embedded.block.gdshader\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.gdshader\\\"}]},\\\"heading\\\":{\\\"begin\\\":\\\"\\\\\\\\[([a-z_]*)\\\\\\\\s?\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.gdresource\\\"}},\\\"end\\\":\\\"]\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#heading_properties\\\"},{\\\"include\\\":\\\"#data\\\"}]},\\\"heading_properties\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(\\\\\\\\s*[A-Za-z_-][A-Za-z0-9_-]*\\\\\\\\s*=)(?=\\\\\\\\s*$)\\\",\\\"name\\\":\\\"invalid.illegal.noValue.gdresource\\\"},{\\\"begin\\\":\\\"\\\\\\\\s*([A-Za-z_-]\\\\\\\\S*|\\\\\\\".+\\\\\\\"|'.+'|[0-9]+)\\\\\\\\s*(=)\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.property.gdresource\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyValue.gdresource\\\"}},\\\"end\\\":\\\"($|(?==)|,?|\\\\\\\\s*(?=}))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#data\\\"}]}]},\\\"key_value\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(\\\\\\\\s*[A-Za-z_-][A-Za-z0-9_-]*\\\\\\\\s*=)(?=\\\\\\\\s*$)\\\",\\\"name\\\":\\\"invalid.illegal.noValue.gdresource\\\"},{\\\"begin\\\":\\\"\\\\\\\\s*([A-Za-z_-]\\\\\\\\S*|\\\\\\\".+\\\\\\\"|'.+'|[0-9]+)\\\\\\\\s*(=)\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.property.gdresource\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyValue.gdresource\\\"}},\\\"end\\\":\\\"($|(?==)|,|\\\\\\\\s*(?=}))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#data\\\"}]}]}},\\\"scopeName\\\":\\\"source.gdresource\\\",\\\"embeddedLangs\\\":[\\\"gdshader\\\",\\\"gdscript\\\"]}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\n..._gdshader_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n..._gdscript_mjs__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ac2hpa2lqcy9sYW5ncy9kaXN0L2dkcmVzb3VyY2UubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFxQztBQUNBOztBQUVyQyx3Q0FBd0Msc0VBQXNFLGlDQUFpQyxFQUFFLG1DQUFtQyxFQUFFLHlCQUF5QixFQUFFLHlCQUF5QixFQUFFLDJCQUEyQixrQkFBa0IsYUFBYSxjQUFjLE9BQU8sd0RBQXdELGVBQWUsa0RBQWtELFdBQVcsZUFBZSx5QkFBeUIsRUFBRSw0QkFBNEIsNkJBQTZCLE9BQU8sNkRBQTZELG1CQUFtQiw4QkFBOEIsT0FBTyw2REFBNkQsZ0JBQWdCLDJCQUEyQixFQUFFLHNCQUFzQixFQUFFLEVBQUUseURBQXlELE9BQU8sc0RBQXNELGlEQUFpRCxPQUFPLHNEQUFzRCxnQkFBZ0Isc0JBQXNCLEVBQUUsRUFBRSw0SEFBNEgsdURBQXVELEVBQUUsUUFBUSxFQUFFLHNEQUFzRCxFQUFFLGdHQUFnRyxFQUFFLEVBQUUsK0hBQStILEVBQUUsNkhBQTZILEVBQUUsdUpBQXVKLEVBQUUsMkdBQTJHLHVEQUF1RCxFQUFFLFFBQVEsRUFBRSxzREFBc0QsRUFBRSxnR0FBZ0csRUFBRSxFQUFFLDhFQUE4RSxFQUFFLHdGQUF3RixFQUFFLG1NQUFtTSxFQUFFLHVIQUF1SCxFQUFFLHlGQUF5RixFQUFFLHlGQUF5RixFQUFFLDhHQUE4RyxFQUFFLGtHQUFrRyxFQUFFLGdHQUFnRyxFQUFFLHVqQkFBdWpCLE9BQU8sK0NBQStDLDBDQUEwQywyQkFBMkIsRUFBRSxzQkFBc0IsRUFBRSxFQUFFLGtGQUFrRixPQUFPLHlDQUF5QywwQ0FBMEMsMkJBQTJCLEVBQUUsc0JBQXNCLEVBQUUsRUFBRSx3QkFBd0Isd0RBQXdELE9BQU8saURBQWlELGlDQUFpQyxnQ0FBZ0MsRUFBRSxzQkFBc0IsK0NBQStDLE9BQU8saURBQWlELDJFQUEyRSxnQ0FBZ0MsRUFBRSxjQUFjLHNEQUFzRCxPQUFPLHlDQUF5Qyw4QkFBOEIsb0NBQW9DLEVBQUUsc0JBQXNCLEVBQUUseUJBQXlCLGVBQWUsaUhBQWlILEVBQUUsZ0dBQWdHLE9BQU8sZ0RBQWdELFFBQVEseURBQXlELGlDQUFpQyxvQkFBb0Isc0JBQXNCLEVBQUUsRUFBRSxnQkFBZ0IsZUFBZSxpSEFBaUgsRUFBRSxnR0FBZ0csT0FBTyxnREFBZ0QsUUFBUSx5REFBeUQsZ0NBQWdDLG9CQUFvQixzQkFBc0IsRUFBRSxHQUFHLG1GQUFtRjs7QUFFdGtMLGlFQUFlO0FBQ2YsR0FBRyxxREFBUTtBQUNYLEdBQUcscURBQVE7QUFDWDtBQUNBLENBQUMiLCJzb3VyY2VzIjpbIkQ6XFxzb2Z0XFxTZWFQcm9qZWN0XFxnaXRodWJfb3Blbl9wcm9qZWN0X3N1Y2Nlc3NcXHN1bmFcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXEBzaGlraWpzXFxsYW5nc1xcZGlzdFxcZ2RyZXNvdXJjZS5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGdkc2hhZGVyIGZyb20gJy4vZ2RzaGFkZXIubWpzJ1xuaW1wb3J0IGdkc2NyaXB0IGZyb20gJy4vZ2RzY3JpcHQubWpzJ1xuXG5jb25zdCBsYW5nID0gT2JqZWN0LmZyZWV6ZShKU09OLnBhcnNlKFwie1xcXCJkaXNwbGF5TmFtZVxcXCI6XFxcIkdEUmVzb3VyY2VcXFwiLFxcXCJuYW1lXFxcIjpcXFwiZ2RyZXNvdXJjZVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2VtYmVkZGVkX3NoYWRlclxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNlbWJlZGRlZF9nZHNjcmlwdFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNjb21tZW50XFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2hlYWRpbmdcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIja2V5X3ZhbHVlXFxcIn1dLFxcXCJyZXBvc2l0b3J5XFxcIjp7XFxcImNvbW1lbnRcXFwiOntcXFwiY2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uZGVmaW5pdGlvbi5jb21tZW50LmdkcmVzb3VyY2VcXFwifX0sXFxcIm1hdGNoXFxcIjpcXFwiKDspLiokXFxcXFxcXFxuP1xcXCIsXFxcIm5hbWVcXFwiOlxcXCJjb21tZW50LmxpbmUuZ2RyZXNvdXJjZVxcXCJ9LFxcXCJkYXRhXFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2NvbW1lbnRcXFwifSx7XFxcImJlZ2luXFxcIjpcXFwiKD88IVxcXFxcXFxcdykoXFxcXFxcXFx7KVxcXFxcXFxccypcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24udGFibGUuaW5saW5lLmdkcmVzb3VyY2VcXFwifX0sXFxcImVuZFxcXCI6XFxcIlxcXFxcXFxccyoofSkoPyFcXFxcXFxcXHcpXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uZGVmaW5pdGlvbi50YWJsZS5pbmxpbmUuZ2RyZXNvdXJjZVxcXCJ9fSxcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIja2V5X3ZhbHVlXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2RhdGFcXFwifV19LHtcXFwiYmVnaW5cXFwiOlxcXCIoPzwhXFxcXFxcXFx3KShcXFxcXFxcXFspXFxcXFxcXFxzKlxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uZGVmaW5pdGlvbi5hcnJheS5nZHJlc291cmNlXFxcIn19LFxcXCJlbmRcXFwiOlxcXCJcXFxcXFxcXHMqKF0pKD8hXFxcXFxcXFx3KVxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24uYXJyYXkuZ2RyZXNvdXJjZVxcXCJ9fSxcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjZGF0YVxcXCJ9XX0se1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFwiXFxcXFxcXCJcXFxcXFxcIlxcXCIsXFxcImVuZFxcXCI6XFxcIlxcXFxcXFwiXFxcXFxcXCJcXFxcXFxcIlxcXCIsXFxcIm5hbWVcXFwiOlxcXCJzdHJpbmcucXVvdGVkLnRyaXBsZS5iYXNpYy5ibG9jay5nZHJlc291cmNlXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcIm1hdGNoXFxcIjpcXFwiXFxcXFxcXFxcXFxcXFxcXChbYnRuZnJcXFxcXFxcIlxcXFxcXFxcXFxcXFxcXFxcXFxcXFxcXG4vIF18dVxcXFxcXFxcaHs0fXxVXFxcXFxcXFxoezh9KVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJjb25zdGFudC5jaGFyYWN0ZXIuZXNjYXBlLmdkcmVzb3VyY2VcXFwifSx7XFxcIm1hdGNoXFxcIjpcXFwiXFxcXFxcXFxcXFxcXFxcXFteYnRuZnIvXFxcXFxcXCJcXFxcXFxcXFxcXFxcXFxcXFxcXFxcXFxuXVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJpbnZhbGlkLmlsbGVnYWwuZXNjYXBlLmdkcmVzb3VyY2VcXFwifV19LHtcXFwibWF0Y2hcXFwiOlxcXCJcXFxcXFxcInJlczovL1teXFxcXFxcXCJcXFxcXFxcXFxcXFxcXFxcXSooPzpcXFxcXFxcXFxcXFxcXFxcLlteXFxcXFxcXCJcXFxcXFxcXFxcXFxcXFxcXSopKlxcXFxcXFwiXFxcIixcXFwibmFtZVxcXCI6XFxcInN1cHBvcnQuZnVuY3Rpb24uYW55LW1ldGhvZC5nZHJlc291cmNlXFxcIn0se1xcXCJtYXRjaFxcXCI6XFxcIig/PD10eXBlPSlcXFxcXFxcIlteXFxcXFxcXCJcXFxcXFxcXFxcXFxcXFxcXSooPzpcXFxcXFxcXFxcXFxcXFxcLlteXFxcXFxcXCJcXFxcXFxcXFxcXFxcXFxcXSopKlxcXFxcXFwiXFxcIixcXFwibmFtZVxcXCI6XFxcInN1cHBvcnQuY2xhc3MubGlicmFyeS5nZHJlc291cmNlXFxcIn0se1xcXCJtYXRjaFxcXCI6XFxcIig/PD1Ob2RlUGF0aFxcXFxcXFxcKHxwYXJlbnQ9fG5hbWU9KVxcXFxcXFwiW15cXFxcXFxcIlxcXFxcXFxcXFxcXFxcXFxdKig/OlxcXFxcXFxcXFxcXFxcXFwuW15cXFxcXFxcIlxcXFxcXFxcXFxcXFxcXFxdKikqXFxcXFxcXCJcXFwiLFxcXCJuYW1lXFxcIjpcXFwiY29uc3RhbnQuY2hhcmFjdGVyLmVzY2FwZS5nZHJlc291cmNlXFxcIn0se1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFwiXFxcIixcXFwiZW5kXFxcIjpcXFwiXFxcXFxcXCJcXFwiLFxcXCJuYW1lXFxcIjpcXFwic3RyaW5nLnF1b3RlZC5kb3VibGUuYmFzaWMubGluZS5nZHJlc291cmNlXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcIm1hdGNoXFxcIjpcXFwiXFxcXFxcXFxcXFxcXFxcXChbYnRuZnJcXFxcXFxcIlxcXFxcXFxcXFxcXFxcXFxcXFxcXFxcXG4vIF18dVxcXFxcXFxcaHs0fXxVXFxcXFxcXFxoezh9KVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJjb25zdGFudC5jaGFyYWN0ZXIuZXNjYXBlLmdkcmVzb3VyY2VcXFwifSx7XFxcIm1hdGNoXFxcIjpcXFwiXFxcXFxcXFxcXFxcXFxcXFteYnRuZnIvXFxcXFxcXCJcXFxcXFxcXFxcXFxcXFxcXFxcXFxcXFxuXVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJpbnZhbGlkLmlsbGVnYWwuZXNjYXBlLmdkcmVzb3VyY2VcXFwifV19LHtcXFwibWF0Y2hcXFwiOlxcXCInLio/J1xcXCIsXFxcIm5hbWVcXFwiOlxcXCJzdHJpbmcucXVvdGVkLnNpbmdsZS5saXRlcmFsLmxpbmUuZ2RyZXNvdXJjZVxcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCIoPzwhXFxcXFxcXFx3KSh0cnVlfGZhbHNlKSg/IVxcXFxcXFxcdylcXFwiLFxcXCJuYW1lXFxcIjpcXFwiY29uc3RhbnQubGFuZ3VhZ2UuZ2RyZXNvdXJjZVxcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCIoPzwhXFxcXFxcXFx3KShbKy1dPygwfChbMS05XSgoWzAtOV18X1swLTldKSspPykpKD86KD86XFxcXFxcXFwuKDB8KFsxLTldKChbMC05XXxfWzAtOV0pKyk/KSkpP1tlRV1bKy1dP1sxLTldXz9bMC05XSp8XFxcXFxcXFwuWzAtOV9dKikpKD8hXFxcXFxcXFx3KVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJjb25zdGFudC5udW1lcmljLmZsb2F0LmdkcmVzb3VyY2VcXFwifSx7XFxcIm1hdGNoXFxcIjpcXFwiKD88IVxcXFxcXFxcdykoWystXT8oMHwoWzEtOV0oKFswLTldfF9bMC05XSkrKT8pKSkoPyFcXFxcXFxcXHcpXFxcIixcXFwibmFtZVxcXCI6XFxcImNvbnN0YW50Lm51bWVyaWMuaW50ZWdlci5nZHJlc291cmNlXFxcIn0se1xcXCJtYXRjaFxcXCI6XFxcIig/PCFcXFxcXFxcXHcpKFsrLV0/aW5mKSg/IVxcXFxcXFxcdylcXFwiLFxcXCJuYW1lXFxcIjpcXFwiY29uc3RhbnQubnVtZXJpYy5pbmYuZ2RyZXNvdXJjZVxcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCIoPzwhXFxcXFxcXFx3KShbKy1dP25hbikoPyFcXFxcXFxcXHcpXFxcIixcXFwibmFtZVxcXCI6XFxcImNvbnN0YW50Lm51bWVyaWMubmFuLmdkcmVzb3VyY2VcXFwifSx7XFxcIm1hdGNoXFxcIjpcXFwiKD88IVxcXFxcXFxcdykoMHgoKFxcXFxcXFxcaCgoXFxcXFxcXFxofF9cXFxcXFxcXGgpKyk/KSkpKD8hXFxcXFxcXFx3KVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJjb25zdGFudC5udW1lcmljLmhleC5nZHJlc291cmNlXFxcIn0se1xcXCJtYXRjaFxcXCI6XFxcIig/PCFcXFxcXFxcXHcpKDBvWzAtN10oXz9bMC03XSkqKSg/IVxcXFxcXFxcdylcXFwiLFxcXCJuYW1lXFxcIjpcXFwiY29uc3RhbnQubnVtZXJpYy5vY3QuZ2RyZXNvdXJjZVxcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCIoPzwhXFxcXFxcXFx3KSgwYlswMV0oXz9bMDFdKSopKD8hXFxcXFxcXFx3KVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJjb25zdGFudC5udW1lcmljLmJpbi5nZHJlc291cmNlXFxcIn0se1xcXCJiZWdpblxcXCI6XFxcIig/PCFcXFxcXFxcXHcpKFZlY3RvcjJ8VmVjdG9yMml8VmVjdG9yM3xWZWN0b3IzaXxDb2xvcnxSZWN0MnxSZWN0Mml8QXJyYXl8QmFzaXN8RGljdGlvbmFyeXxQbGFuZXxRdWF0fFJJRHxSZWN0M3xUcmFuc2Zvcm18VHJhbnNmb3JtMkR8VHJhbnNmb3JtM0R8QUFCQnxTdHJpbmd8Q29sb3J8Tm9kZVBhdGh8T2JqZWN0fFBvb2xCeXRlQXJyYXl8UG9vbEludEFycmF5fFBvb2xSZWFsQXJyYXl8UG9vbFN0cmluZ0FycmF5fFBvb2xWZWN0b3IyQXJyYXl8UG9vbFZlY3RvcjNBcnJheXxQb29sQ29sb3JBcnJheXxib29sfGludHxmbG9hdHxTdHJpbmdOYW1lfFF1YXRlcm5pb258UGFja2VkQnl0ZUFycmF5fFBhY2tlZEludDMyQXJyYXl8UGFja2VkSW50NjRBcnJheXxQYWNrZWRGbG9hdDMyQXJyYXl8UGFja2VkRmxvYXQ2NEFycmF5fFBhY2tlZFN0cmluZ0FycmF5fFBhY2tlZFZlY3RvcjJBcnJheXxQYWNrZWRWZWN0b3IyaUFycmF5fFBhY2tlZFZlY3RvcjNBcnJheXxQYWNrZWRWZWN0b3IzaUFycmF5fFBhY2tlZENvbG9yQXJyYXkpKFxcXFxcXFxcKClcXFxcXFxcXHM/XFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJzdXBwb3J0LmNsYXNzLmxpYnJhcnkuZ2RyZXNvdXJjZVxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiXFxcXFxcXFxzPyhcXFxcXFxcXCkpXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIja2V5X3ZhbHVlXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2RhdGFcXFwifV19LHtcXFwiYmVnaW5cXFwiOlxcXCIoPzwhXFxcXFxcXFx3KShFeHRSZXNvdXJjZXxTdWJSZXNvdXJjZSkoXFxcXFxcXFwoKVxcXFxcXFxccz9cXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQuY29udHJvbC5nZHJlc291cmNlXFxcIn19LFxcXCJlbmRcXFwiOlxcXCJcXFxcXFxcXHM/KFxcXFxcXFxcKSlcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNrZXlfdmFsdWVcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjZGF0YVxcXCJ9XX1dfSxcXFwiZW1iZWRkZWRfZ2RzY3JpcHRcXFwiOntcXFwiYmVnaW5cXFwiOlxcXCIoc2NyaXB0L3NvdXJjZSkgPSBcXFxcXFxcIlxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwidmFyaWFibGUub3RoZXIucHJvcGVydHkuZ2RyZXNvdXJjZVxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiXFxcXFxcXCJcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcInNvdXJjZS5nZHNjcmlwdFxcXCJ9XX0sXFxcImVtYmVkZGVkX3NoYWRlclxcXCI6e1xcXCJiZWdpblxcXCI6XFxcIihjb2RlKSA9IFxcXFxcXFwiXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJ2YXJpYWJsZS5vdGhlci5wcm9wZXJ0eS5nZHJlc291cmNlXFxcIn19LFxcXCJlbmRcXFwiOlxcXCJcXFxcXFxcIlxcXCIsXFxcIm5hbWVcXFwiOlxcXCJtZXRhLmVtYmVkZGVkLmJsb2NrLmdkc2hhZGVyXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCJzb3VyY2UuZ2RzaGFkZXJcXFwifV19LFxcXCJoZWFkaW5nXFxcIjp7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXFxbKFthLXpfXSopXFxcXFxcXFxzP1xcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5jb250cm9sLmdkcmVzb3VyY2VcXFwifX0sXFxcImVuZFxcXCI6XFxcIl1cXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNoZWFkaW5nX3Byb3BlcnRpZXNcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjZGF0YVxcXCJ9XX0sXFxcImhlYWRpbmdfcHJvcGVydGllc1xcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwibWF0Y2hcXFwiOlxcXCIoXFxcXFxcXFxzKltBLVphLXpfLV1bQS1aYS16MC05Xy1dKlxcXFxcXFxccyo9KSg/PVxcXFxcXFxccyokKVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJpbnZhbGlkLmlsbGVnYWwubm9WYWx1ZS5nZHJlc291cmNlXFxcIn0se1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxccyooW0EtWmEtel8tXVxcXFxcXFxcUyp8XFxcXFxcXCIuK1xcXFxcXFwifCcuKyd8WzAtOV0rKVxcXFxcXFxccyooPSlcXFxcXFxcXHMqXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJ2YXJpYWJsZS5vdGhlci5wcm9wZXJ0eS5nZHJlc291cmNlXFxcIn0sXFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24ua2V5VmFsdWUuZ2RyZXNvdXJjZVxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiKCR8KD89PSl8LD98XFxcXFxcXFxzKig/PX0pKVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2RhdGFcXFwifV19XX0sXFxcImtleV92YWx1ZVxcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwibWF0Y2hcXFwiOlxcXCIoXFxcXFxcXFxzKltBLVphLXpfLV1bQS1aYS16MC05Xy1dKlxcXFxcXFxccyo9KSg/PVxcXFxcXFxccyokKVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJpbnZhbGlkLmlsbGVnYWwubm9WYWx1ZS5nZHJlc291cmNlXFxcIn0se1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxccyooW0EtWmEtel8tXVxcXFxcXFxcUyp8XFxcXFxcXCIuK1xcXFxcXFwifCcuKyd8WzAtOV0rKVxcXFxcXFxccyooPSlcXFxcXFxcXHMqXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJ2YXJpYWJsZS5vdGhlci5wcm9wZXJ0eS5nZHJlc291cmNlXFxcIn0sXFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24ua2V5VmFsdWUuZ2RyZXNvdXJjZVxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiKCR8KD89PSl8LHxcXFxcXFxcXHMqKD89fSkpXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjZGF0YVxcXCJ9XX1dfX0sXFxcInNjb3BlTmFtZVxcXCI6XFxcInNvdXJjZS5nZHJlc291cmNlXFxcIixcXFwiZW1iZWRkZWRMYW5nc1xcXCI6W1xcXCJnZHNoYWRlclxcXCIsXFxcImdkc2NyaXB0XFxcIl19XCIpKVxuXG5leHBvcnQgZGVmYXVsdCBbXG4uLi5nZHNoYWRlcixcbi4uLmdkc2NyaXB0LFxubGFuZ1xuXVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/gdresource.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/gdscript.mjs":
/*!*******************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/gdscript.mjs ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"GDScript\\\",\\\"fileTypes\\\":[\\\"gd\\\"],\\\"name\\\":\\\"gdscript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#statement\\\"},{\\\"include\\\":\\\"#expression\\\"}],\\\"repository\\\":{\\\"annotated_parameter\\\":{\\\"begin\\\":\\\"\\\\\\\\s*([a-zA-Z_]\\\\\\\\w*)\\\\\\\\s*(:)\\\\\\\\s*([a-zA-Z_]\\\\\\\\w*)?\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.parameter.function.language.gdscript\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.annotation.gdscript\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.type.class.gdscript\\\"}},\\\"end\\\":\\\"(,)|(?=\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.parameters.gdscript\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"},{\\\"match\\\":\\\"=(?!=)\\\",\\\"name\\\":\\\"keyword.operator.assignment.gdscript\\\"}]},\\\"annotations\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.decorator.gdscript\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.decorator.gdscript\\\"}},\\\"match\\\":\\\"(@)(export|export_group|export_color_no_alpha|export_custom|export_dir|export_enum|export_exp_easing|export_file|export_flags|export_flags_2d_navigation|export_flags_2d_physics|export_flags_2d_render|export_flags_3d_navigation|export_flags_3d_physics|export_flags_3d_render|export_global_dir|export_global_file|export_multiline|export_node_path|export_placeholder|export_range|export_storage|icon|onready|rpc|tool|warning_ignore|static_unload)\\\\\\\\b\\\"},\\\"any_method\\\":{\\\"match\\\":\\\"\\\\\\\\b([A-Za-z_]\\\\\\\\w*)\\\\\\\\b(?=\\\\\\\\s*\\\\\\\\()\\\",\\\"name\\\":\\\"entity.name.function.other.gdscript\\\"},\\\"any_property\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.accessor.gdscript\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.language.gdscript\\\"},\\\"3\\\":{\\\"name\\\":\\\"variable.other.property.gdscript\\\"}},\\\"match\\\":\\\"\\\\\\\\b(\\\\\\\\.)\\\\\\\\s*(?<![@$#%])(?:([A-Z_][A-Z_0-9]*)|([A-Za-z_]\\\\\\\\w*))\\\\\\\\b(?!\\\\\\\\()\\\"},\\\"any_variable\\\":{\\\"match\\\":\\\"\\\\\\\\b(?<![@$#%])([A-Za-z_]\\\\\\\\w*)\\\\\\\\b(?!\\\\\\\\()\\\",\\\"name\\\":\\\"variable.other.gdscript\\\"},\\\"arithmetic_operator\\\":{\\\"match\\\":\\\"->|\\\\\\\\+=|-=|\\\\\\\\*\\\\\\\\*=|\\\\\\\\*=|\\\\\\\\^=|/=|%=|&=|~=|\\\\\\\\|=|\\\\\\\\*\\\\\\\\*|[*/%+-]\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.gdscript\\\"},\\\"assignment_operator\\\":{\\\"match\\\":\\\"=\\\",\\\"name\\\":\\\"keyword.operator.assignment.gdscript\\\"},\\\"base_expression\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#builtin_get_node_shorthand\\\"},{\\\"include\\\":\\\"#nodepath_object\\\"},{\\\"include\\\":\\\"#nodepath_function\\\"},{\\\"include\\\":\\\"#strings\\\"},{\\\"include\\\":\\\"#builtin_classes\\\"},{\\\"include\\\":\\\"#const_vars\\\"},{\\\"include\\\":\\\"#keywords\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"include\\\":\\\"#lambda_declaration\\\"},{\\\"include\\\":\\\"#class_declaration\\\"},{\\\"include\\\":\\\"#variable_declaration\\\"},{\\\"include\\\":\\\"#signal_declaration_bare\\\"},{\\\"include\\\":\\\"#signal_declaration\\\"},{\\\"include\\\":\\\"#function_declaration\\\"},{\\\"include\\\":\\\"#statement_keyword\\\"},{\\\"include\\\":\\\"#assignment_operator\\\"},{\\\"include\\\":\\\"#in_keyword\\\"},{\\\"include\\\":\\\"#control_flow\\\"},{\\\"include\\\":\\\"#match_keyword\\\"},{\\\"include\\\":\\\"#curly_braces\\\"},{\\\"include\\\":\\\"#square_braces\\\"},{\\\"include\\\":\\\"#round_braces\\\"},{\\\"include\\\":\\\"#function_call\\\"},{\\\"include\\\":\\\"#region\\\"},{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#self\\\"},{\\\"include\\\":\\\"#func\\\"},{\\\"include\\\":\\\"#letter\\\"},{\\\"include\\\":\\\"#numbers\\\"},{\\\"include\\\":\\\"#pascal_case_class\\\"},{\\\"include\\\":\\\"#line_continuation\\\"}]},\\\"bitwise_operator\\\":{\\\"match\\\":\\\"[\\\\\\\\&|]|<<=|>>=|<<|>>|[\\\\\\\\^~]\\\",\\\"name\\\":\\\"keyword.operator.bitwise.gdscript\\\"},\\\"boolean_operator\\\":{\\\"match\\\":\\\"(&&|\\\\\\\\|\\\\\\\\|)\\\",\\\"name\\\":\\\"keyword.operator.boolean.gdscript\\\"},\\\"builtin_classes\\\":{\\\"match\\\":\\\"(?<![^.]\\\\\\\\.|:)\\\\\\\\b(Vector2|Vector2i|Vector3|Vector3i|Vector4|Vector4i|Color|Rect2|Rect2i|Array|Basis|Dictionary|Plane|Quat|RID|Rect3|Transform|Transform2D|Transform3D|AABB|String|Color|NodePath|PoolByteArray|PoolIntArray|PoolRealArray|PoolStringArray|PoolVector2Array|PoolVector3Array|PoolColorArray|bool|int|float|Signal|Callable|StringName|Quaternion|Projection|PackedByteArray|PackedInt32Array|PackedInt64Array|PackedFloat32Array|PackedFloat64Array|PackedStringArray|PackedVector2Array|PackedVector2iArray|PackedVector3Array|PackedVector3iArray|PackedVector4Array|PackedColorArray|JSON|UPNP|OS|IP|JSONRPC|XRVRS)\\\\\\\\b\\\",\\\"name\\\":\\\"entity.name.type.class.builtin.gdscript\\\"},\\\"builtin_get_node_shorthand\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#builtin_get_node_shorthand_quoted\\\"},{\\\"include\\\":\\\"#builtin_get_node_shorthand_bare\\\"},{\\\"include\\\":\\\"#builtin_get_node_shorthand_bare_multi\\\"}]},\\\"builtin_get_node_shorthand_bare\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.flow.gdscript\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.character.escape.gdscript\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.character.escape.gdscript\\\"},\\\"4\\\":{\\\"name\\\":\\\"constant.character.escape.gdscript\\\"}},\\\"match\\\":\\\"(?<!/\\\\\\\\s*)(\\\\\\\\$\\\\\\\\s*|%|\\\\\\\\$%\\\\\\\\s*)(/\\\\\\\\s*)?([a-zA-Z_]\\\\\\\\w*)\\\\\\\\b(?!\\\\\\\\s*/)\\\",\\\"name\\\":\\\"meta.literal.nodepath.bare.gdscript\\\"},\\\"builtin_get_node_shorthand_bare_multi\\\":{\\\"begin\\\":\\\"(\\\\\\\\$\\\\\\\\s*|%|\\\\\\\\$%\\\\\\\\s*)(/\\\\\\\\s*)?([a-zA-Z_]\\\\\\\\w*)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.flow.gdscript\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.character.escape.gdscript\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.character.escape.gdscript\\\"}},\\\"end\\\":\\\"(?!\\\\\\\\s*/\\\\\\\\s*%?\\\\\\\\s*[a-zA-Z_]\\\\\\\\w*)\\\",\\\"name\\\":\\\"meta.literal.nodepath.bare.gdscript\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.character.escape.gdscript\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.flow.gdscript\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.character.escape.gdscript\\\"}},\\\"match\\\":\\\"(/)\\\\\\\\s*(%)?\\\\\\\\s*([a-zA-Z_]\\\\\\\\w*)\\\\\\\\s*\\\"}]},\\\"builtin_get_node_shorthand_quoted\\\":{\\\"begin\\\":\\\"(?:([$%])|([\\\\\\\\&^@]))([\\\\\\\"'])\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.flow.gdscript\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.other.enummember.gdscript\\\"}},\\\"end\\\":\\\"(\\\\\\\\3)\\\",\\\"name\\\":\\\"string.quoted.gdscript meta.literal.nodepath.gdscript constant.character.escape.gdscript\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"%\\\",\\\"name\\\":\\\"keyword.control.flow\\\"}]},\\\"class_declaration\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.type.class.gdscript\\\"},\\\"2\\\":{\\\"name\\\":\\\"class.other.gdscript\\\"}},\\\"match\\\":\\\"(?<=^class)\\\\\\\\s+([a-zA-Z_]\\\\\\\\w*)\\\\\\\\s*(?=:)\\\"},\\\"class_enum\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.type.class.gdscript\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.other.enummember.gdscript\\\"}},\\\"match\\\":\\\"\\\\\\\\b([A-Z][a-zA-Z_0-9]*)\\\\\\\\.([A-Z_0-9]+)\\\"},\\\"class_is\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.is.gdscript\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.type.class.gdscript\\\"}},\\\"match\\\":\\\"\\\\\\\\s+(is)\\\\\\\\s+([a-zA-Z_]\\\\\\\\w*)\\\"},\\\"class_name\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.type.class.gdscript\\\"},\\\"2\\\":{\\\"name\\\":\\\"class.other.gdscript\\\"}},\\\"match\\\":\\\"(?<=class_name)\\\\\\\\s+([a-zA-Z_]\\\\\\\\w*(\\\\\\\\.([a-zA-Z_]\\\\\\\\w*))?)\\\"},\\\"class_new\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.type.class.gdscript\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.type.new.gdscript\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.parenthesis.begin.gdscript\\\"}},\\\"match\\\":\\\"\\\\\\\\b([a-zA-Z_]\\\\\\\\w*).(new)\\\\\\\\(\\\"},\\\"comment\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.number-sign.gdscript\\\"}},\\\"match\\\":\\\"(#(?:#|)).*$\\\\\\\\n?\\\",\\\"name\\\":\\\"comment.line.number-sign.gdscript\\\"},\\\"compare_operator\\\":{\\\"match\\\":\\\"<=|>=|==|[<>]|!=|!\\\",\\\"name\\\":\\\"keyword.operator.comparison.gdscript\\\"},\\\"const_vars\\\":{\\\"match\\\":\\\"\\\\\\\\b([A-Z_][A-Z_0-9]*)\\\\\\\\b\\\",\\\"name\\\":\\\"variable.other.constant.gdscript\\\"},\\\"control_flow\\\":{\\\"match\\\":\\\"\\\\\\\\b(?:if|elif|else|while|break|continue|pass|return|when|yield|await)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.gdscript\\\"},\\\"curly_braces\\\":{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.dict.begin.gdscript\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.dict.end.gdscript\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#base_expression\\\"},{\\\"include\\\":\\\"#any_variable\\\"}]},\\\"expression\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#getter_setter_godot4\\\"},{\\\"include\\\":\\\"#base_expression\\\"},{\\\"include\\\":\\\"#assignment_operator\\\"},{\\\"include\\\":\\\"#annotations\\\"},{\\\"include\\\":\\\"#class_name\\\"},{\\\"include\\\":\\\"#builtin_classes\\\"},{\\\"include\\\":\\\"#class_new\\\"},{\\\"include\\\":\\\"#class_is\\\"},{\\\"include\\\":\\\"#class_enum\\\"},{\\\"include\\\":\\\"#any_method\\\"},{\\\"include\\\":\\\"#any_variable\\\"},{\\\"include\\\":\\\"#any_property\\\"}]},\\\"extends_statement\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.language.gdscript\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.other.inherited-class.gdscript\\\"}},\\\"match\\\":\\\"(extends)\\\\\\\\s+([a-zA-Z_]\\\\\\\\w*\\\\\\\\.[a-zA-Z_]\\\\\\\\w*)?\\\"},\\\"func\\\":{\\\"match\\\":\\\"\\\\\\\\bfunc\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.language.gdscript\\\"},\\\"function_arguments\\\":{\\\"begin\\\":\\\"(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.gdscript\\\"}},\\\"contentName\\\":\\\"meta.function.parameters.gdscript\\\",\\\"end\\\":\\\"(?=\\\\\\\\))(?!\\\\\\\\)\\\\\\\\s*\\\\\\\\()\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(,)\\\",\\\"name\\\":\\\"punctuation.separator.arguments.gdscript\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.parameter.function-call.gdscript\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.assignment.gdscript\\\"}},\\\"match\\\":\\\"\\\\\\\\b([a-zA-Z_]\\\\\\\\w*)\\\\\\\\s*(=)(?!=)\\\"},{\\\"match\\\":\\\"=(?!=)\\\",\\\"name\\\":\\\"keyword.operator.assignment.gdscript\\\"},{\\\"include\\\":\\\"#base_expression\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.gdscript\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.gdscript\\\"}},\\\"match\\\":\\\"\\\\\\\\s*(\\\\\\\\))\\\\\\\\s*(\\\\\\\\()\\\"},{\\\"include\\\":\\\"#letter\\\"},{\\\"include\\\":\\\"#any_variable\\\"},{\\\"include\\\":\\\"#any_property\\\"},{\\\"include\\\":\\\"#keywords\\\"}]},\\\"function_call\\\":{\\\"begin\\\":\\\"(?=\\\\\\\\b[a-zA-Z_]\\\\\\\\w*\\\\\\\\b\\\\\\\\()\\\",\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.gdscript\\\"}},\\\"name\\\":\\\"meta.function-call.gdscript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function_name\\\"},{\\\"include\\\":\\\"#function_arguments\\\"}]},\\\"function_declaration\\\":{\\\"begin\\\":\\\"\\\\\\\\s*(func)\\\\\\\\s+([a-zA-Z_]\\\\\\\\w*)\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.language.gdscript storage.type.function.gdscript\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.gdscript\\\"}},\\\"end\\\":\\\"(:)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.function.begin.gdscript\\\"}},\\\"name\\\":\\\"meta.function.gdscript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#parameters\\\"},{\\\"include\\\":\\\"#line_continuation\\\"},{\\\"include\\\":\\\"#base_expression\\\"}]},\\\"function_name\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#builtin_classes\\\"},{\\\"match\\\":\\\"\\\\\\\\b(preload)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.language.gdscript\\\"},{\\\"match\\\":\\\"\\\\\\\\b([a-zA-Z_]\\\\\\\\w*)\\\\\\\\b\\\",\\\"name\\\":\\\"entity.name.function.gdscript\\\"}]},\\\"getter_setter_godot4\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.gdscript\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.annotation.gdscript\\\"}},\\\"match\\\":\\\"(get)\\\\\\\\s*(:)\\\",\\\"name\\\":\\\"meta.variable.declaration.getter.gdscript\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.gdscript\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.gdscript\\\"},\\\"3\\\":{\\\"name\\\":\\\"variable.other.gdscript\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.gdscript\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.separator.annotation.gdscript\\\"}},\\\"match\\\":\\\"(set)\\\\\\\\s*(\\\\\\\\()\\\\\\\\s*([A-Za-z_]\\\\\\\\w*)\\\\\\\\s*(\\\\\\\\))\\\\\\\\s*(:)\\\",\\\"name\\\":\\\"meta.variable.declaration.setter.gdscript\\\"}]},\\\"in_keyword\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\b(for)\\\\\\\\b\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.gdscript\\\"}},\\\"end\\\":\\\":\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\bin\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.gdscript\\\"},{\\\"include\\\":\\\"#base_expression\\\"},{\\\"include\\\":\\\"#any_variable\\\"},{\\\"include\\\":\\\"#any_property\\\"}]},{\\\"match\\\":\\\"\\\\\\\\bin\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.operator.wordlike.gdscript\\\"}]},\\\"keywords\\\":{\\\"match\\\":\\\"\\\\\\\\b(?:class|class_name|abstract|is|onready|tool|static|export|as|void|enum|assert|breakpoint|sync|remote|master|puppet|slave|remotesync|mastersync|puppetsync|trait|namespace|super)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.language.gdscript\\\"},\\\"lambda_declaration\\\":{\\\"begin\\\":\\\"(func)\\\\\\\\s?(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.language.gdscript storage.type.function.gdscript\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.gdscript\\\"}},\\\"end\\\":\\\"(:|(?=[#'\\\\\\\"\\\\\\\\n]))\\\",\\\"end2\\\":\\\"(\\\\\\\\s*(\\\\\\\\-\\\\\\\\>)\\\\\\\\s*(void\\\\\\\\w*)|([a-zA-Z_]\\\\\\\\w*)\\\\\\\\s*\\\\\\\\:)\\\",\\\"endCaptures2\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.annotation.result.gdscript\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.language.void.gdscript\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.type.class.gdscript markup.italic\\\"}},\\\"name\\\":\\\"meta.function.gdscript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#parameters\\\"},{\\\"include\\\":\\\"#line_continuation\\\"},{\\\"include\\\":\\\"#base_expression\\\"},{\\\"include\\\":\\\"#any_variable\\\"},{\\\"include\\\":\\\"#any_property\\\"}]},\\\"letter\\\":{\\\"match\\\":\\\"\\\\\\\\b(?:true|false|null)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.gdscript\\\"},\\\"line_continuation\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.continuation.line.gdscript\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.line.continuation.gdscript\\\"}},\\\"match\\\":\\\"(\\\\\\\\\\\\\\\\)\\\\\\\\s*(\\\\\\\\S.*$\\\\\\\\n?)\\\"},{\\\"begin\\\":\\\"(\\\\\\\\\\\\\\\\)\\\\\\\\s*$\\\\\\\\n?\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.continuation.line.gdscript\\\"}},\\\"end\\\":\\\"(?=^\\\\\\\\s*$)|(?!(\\\\\\\\s*[rR]?('''|\\\\\\\"\\\\\\\"\\\\\\\"|['\\\\\\\"]))|(\\\\\\\\G$))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#base_expression\\\"}]}]},\\\"loose_default\\\":{\\\"begin\\\":\\\"(=)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.gdscript\\\"}},\\\"end\\\":\\\"(,)|(?=\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.parameters.gdscript\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},\\\"match_keyword\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.gdscript\\\"}},\\\"match\\\":\\\"^\\\\\\\\n\\\\\\\\s*(match)\\\"},\\\"nodepath_function\\\":{\\\"begin\\\":\\\"(get_node_or_null|has_node|has_node_and_resource|find_node|get_node)\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.gdscript\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.begin.gdscript\\\"}},\\\"contentName\\\":\\\"meta.function.parameters.gdscript\\\",\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.end.gdscript\\\"}},\\\"name\\\":\\\"meta.function.gdscript\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"([\\\\\\\"'])\\\",\\\"end\\\":\\\"\\\\\\\\1\\\",\\\"name\\\":\\\"string.quoted.gdscript meta.literal.nodepath.gdscript constant.character.escape\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"%\\\",\\\"name\\\":\\\"keyword.control.flow\\\"}]},{\\\"include\\\":\\\"#expression\\\"}]},\\\"nodepath_object\\\":{\\\"begin\\\":\\\"(NodePath)\\\\\\\\s*\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.class.library.gdscript\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"name\\\":\\\"meta.literal.nodepath.gdscript\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"([\\\\\\\"'])\\\",\\\"end\\\":\\\"\\\\\\\\1\\\",\\\"name\\\":\\\"string.quoted.gdscript constant.character.escape.gdscript\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"%\\\",\\\"name\\\":\\\"keyword.control.flow.gdscript\\\"}]}]},\\\"numbers\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"0b[01_]+\\\",\\\"name\\\":\\\"constant.numeric.integer.binary.gdscript\\\"},{\\\"match\\\":\\\"0x[_\\\\\\\\h]+\\\",\\\"name\\\":\\\"constant.numeric.integer.hexadecimal.gdscript\\\"},{\\\"match\\\":\\\"\\\\\\\\.[0-9][0-9_]*([eE][+-]?[0-9_]+)?\\\",\\\"name\\\":\\\"constant.numeric.float.gdscript\\\"},{\\\"match\\\":\\\"([0-9][0-9_]*)\\\\\\\\.[0-9_]*([eE][+-]?[0-9_]+)?\\\",\\\"name\\\":\\\"constant.numeric.float.gdscript\\\"},{\\\"match\\\":\\\"([0-9][0-9_]*)?\\\\\\\\.[0-9_]*([eE][+-]?[0-9_]+)\\\",\\\"name\\\":\\\"constant.numeric.float.gdscript\\\"},{\\\"match\\\":\\\"[0-9][0-9_]*[eE][+-]?[0-9_]+\\\",\\\"name\\\":\\\"constant.numeric.float.gdscript\\\"},{\\\"match\\\":\\\"-?[0-9][0-9_]*\\\",\\\"name\\\":\\\"constant.numeric.integer.gdscript\\\"}]},\\\"operators\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#wordlike_operator\\\"},{\\\"include\\\":\\\"#boolean_operator\\\"},{\\\"include\\\":\\\"#arithmetic_operator\\\"},{\\\"include\\\":\\\"#bitwise_operator\\\"},{\\\"include\\\":\\\"#compare_operator\\\"}]},\\\"parameters\\\":{\\\"begin\\\":\\\"(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.begin.gdscript\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.end.gdscript\\\"}},\\\"name\\\":\\\"meta.function.parameters.gdscript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#annotated_parameter\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.parameter.function.language.gdscript\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.parameters.gdscript\\\"}},\\\"match\\\":\\\"([a-zA-Z_]\\\\\\\\w*)\\\\\\\\s*(?:(,)|(?=[)#\\\\\\\\n=]))\\\"},{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#loose_default\\\"}]},\\\"pascal_case_class\\\":{\\\"match\\\":\\\"\\\\\\\\b([A-Z]+[a-z_0-9]*([A-Z]?[a-z_0-9]+)*[A-Z]?)\\\\\\\\b\\\",\\\"name\\\":\\\"entity.name.type.class.gdscript\\\"},\\\"region\\\":{\\\"match\\\":\\\"#(end)?region.*$\\\\\\\\n?\\\",\\\"name\\\":\\\"keyword.language.region.gdscript\\\"},\\\"round_braces\\\":{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.parenthesis.begin.gdscript\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.parenthesis.end.gdscript\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#base_expression\\\"},{\\\"include\\\":\\\"#any_variable\\\"}]},\\\"self\\\":{\\\"match\\\":\\\"\\\\\\\\bself\\\\\\\\b\\\",\\\"name\\\":\\\"variable.language.gdscript\\\"},\\\"signal_declaration\\\":{\\\"begin\\\":\\\"\\\\\\\\s*(signal)\\\\\\\\s+([a-zA-Z_]\\\\\\\\w*)\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.language.gdscript storage.type.function.gdscript\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.gdscript\\\"}},\\\"end\\\":\\\"((?=[#'\\\\\\\"\\\\\\\\n]))\\\",\\\"name\\\":\\\"meta.signal.gdscript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#parameters\\\"},{\\\"include\\\":\\\"#line_continuation\\\"}]},\\\"signal_declaration_bare\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.language.gdscript storage.type.function.gdscript\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.gdscript\\\"}},\\\"match\\\":\\\"\\\\\\\\s*(signal)\\\\\\\\s+([a-zA-Z_]\\\\\\\\w*)(?=[\\\\\\\\n\\\\\\\\s])\\\",\\\"name\\\":\\\"meta.signal.gdscript\\\"},\\\"square_braces\\\":{\\\"begin\\\":\\\"\\\\\\\\[\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.list.begin.gdscript\\\"}},\\\"end\\\":\\\"]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.list.end.gdscript\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#base_expression\\\"},{\\\"include\\\":\\\"#any_variable\\\"}]},\\\"statement\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#extends_statement\\\"}]},\\\"statement_keyword\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(?<!\\\\\\\\.)(continue|assert|break|elif|else|if|pass|return|while)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.flow.gdscript\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?<!\\\\\\\\.)(class)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.class.gdscript\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.flow.gdscript\\\"}},\\\"match\\\":\\\"^\\\\\\\\s*(case|match)(?=\\\\\\\\s*([-+\\\\\\\\w\\\\\\\\d(\\\\\\\\[{'\\\\\\\":#]|$))\\\\\\\\b\\\"}]},\\\"string_bracket_placeholders\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.character.format.placeholder.other.gdscript\\\"},\\\"3\\\":{\\\"name\\\":\\\"storage.type.format.gdscript\\\"},\\\"4\\\":{\\\"name\\\":\\\"storage.type.format.gdscript\\\"}},\\\"match\\\":\\\"(\\\\\\\\{\\\\\\\\{|}}|\\\\\\\\{\\\\\\\\w*(\\\\\\\\.[[:alpha:]_]\\\\\\\\w*|\\\\\\\\[[^\\\\\\\\]'\\\\\\\"]+])*(![rsa])?(:\\\\\\\\w?[<>=^]?[-+ ]?#?\\\\\\\\d*,?(\\\\\\\\.\\\\\\\\d+)?[bcdeEfFgGnosxX%]?)?})\\\",\\\"name\\\":\\\"meta.format.brace.gdscript\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.character.format.placeholder.other.gdscript\\\"},\\\"3\\\":{\\\"name\\\":\\\"storage.type.format.gdscript\\\"},\\\"4\\\":{\\\"name\\\":\\\"storage.type.format.gdscript\\\"}},\\\"match\\\":\\\"(\\\\\\\\{\\\\\\\\w*(\\\\\\\\.[[:alpha:]_]\\\\\\\\w*|\\\\\\\\[[^\\\\\\\\]'\\\\\\\"]+])*(![rsa])?(:)[^'\\\\\\\"{}\\\\\\\\n]*(?:\\\\\\\\{[^'\\\\\\\"}\\\\\\\\n]*?}[^'\\\\\\\"{}\\\\\\\\n]*)*})\\\",\\\"name\\\":\\\"meta.format.brace.gdscript\\\"}]},\\\"string_percent_placeholders\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.character.format.placeholder.other.gdscript\\\"}},\\\"match\\\":\\\"(%(\\\\\\\\([\\\\\\\\w\\\\\\\\s]*\\\\\\\\))?[-+#0 ]*(\\\\\\\\d+|\\\\\\\\*)?(\\\\\\\\.(\\\\\\\\d+|\\\\\\\\*))?([hlL])?[diouxXeEfFgGcrsab%])\\\",\\\"name\\\":\\\"meta.format.percent.gdscript\\\"},\\\"strings\\\":{\\\"begin\\\":\\\"(r)?(\\\\\\\"\\\\\\\"\\\\\\\"|'''|[\\\\\\\"'])\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.character.escape.gdscript\\\"}},\\\"end\\\":\\\"\\\\\\\\2\\\",\\\"name\\\":\\\"string.quoted.gdscript\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"constant.character.escape.gdscript\\\"},{\\\"include\\\":\\\"#string_percent_placeholders\\\"},{\\\"include\\\":\\\"#string_bracket_placeholders\\\"}]},\\\"variable_declaration\\\":{\\\"begin\\\":\\\"\\\\\\\\b(?:(var)|(const))\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.language.gdscript storage.type.var.gdscript\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.language.gdscript storage.type.const.gdscript\\\"}},\\\"end\\\":\\\"$|;\\\",\\\"name\\\":\\\"meta.variable.declaration.gdscript\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.annotation.gdscript\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.gdscript\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.function.gdscript\\\"}},\\\"match\\\":\\\"(:)?\\\\\\\\s*(set|get)\\\\\\\\s+=\\\\\\\\s+([a-zA-Z_]\\\\\\\\w*)\\\"},{\\\"match\\\":\\\":=|=(?!=)\\\",\\\"name\\\":\\\"keyword.operator.assignment.gdscript\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.annotation.gdscript\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.type.class.gdscript\\\"}},\\\"match\\\":\\\"(:)\\\\\\\\s*([a-zA-Z_]\\\\\\\\w*)?\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.language.gdscript\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.gdscript\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.function.gdscript\\\"}},\\\"match\\\":\\\"(setget)\\\\\\\\s+([a-zA-Z_]\\\\\\\\w*)(?:,\\\\\\\\s*([a-zA-Z_]\\\\\\\\w*))?\\\"},{\\\"include\\\":\\\"#expression\\\"},{\\\"include\\\":\\\"#letter\\\"},{\\\"include\\\":\\\"#any_variable\\\"},{\\\"include\\\":\\\"#any_property\\\"},{\\\"include\\\":\\\"#keywords\\\"}]},\\\"wordlike_operator\\\":{\\\"match\\\":\\\"\\\\\\\\b(and|or|not)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.operator.wordlike.gdscript\\\"}},\\\"scopeName\\\":\\\"source.gdscript\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/gdscript.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/gdshader.mjs":
/*!*******************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/gdshader.mjs ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"GDShader\\\",\\\"fileTypes\\\":[\\\"gdshader\\\"],\\\"name\\\":\\\"gdshader\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#any\\\"}],\\\"repository\\\":{\\\"any\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#enclosed\\\"},{\\\"include\\\":\\\"#classifier\\\"},{\\\"include\\\":\\\"#definition\\\"},{\\\"include\\\":\\\"#keyword\\\"},{\\\"include\\\":\\\"#element\\\"},{\\\"include\\\":\\\"#separator\\\"},{\\\"include\\\":\\\"#operator\\\"}]},\\\"arraySize\\\":{\\\"begin\\\":\\\"\\\\\\\\[\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.bracket.gdshader\\\"}},\\\"end\\\":\\\"]\\\",\\\"name\\\":\\\"meta.array-size.gdshader\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#keyword\\\"},{\\\"include\\\":\\\"#element\\\"},{\\\"include\\\":\\\"#separator\\\"}]},\\\"classifier\\\":{\\\"begin\\\":\\\"(?=\\\\\\\\b(?:shader_type|render_mode)\\\\\\\\b)\\\",\\\"end\\\":\\\"(?<=;)\\\",\\\"name\\\":\\\"meta.classifier.gdshader\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#keyword\\\"},{\\\"include\\\":\\\"#identifierClassification\\\"},{\\\"include\\\":\\\"#separator\\\"}]},\\\"classifierKeyword\\\":{\\\"match\\\":\\\"\\\\\\\\b(?:shader_type|render_mode)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.language.classifier.gdshader\\\"},\\\"comment\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#commentLine\\\"},{\\\"include\\\":\\\"#commentBlock\\\"}]},\\\"commentBlock\\\":{\\\"begin\\\":\\\"/\\\\\\\\*\\\",\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"name\\\":\\\"comment.block.gdshader\\\"},\\\"commentLine\\\":{\\\"begin\\\":\\\"//\\\",\\\"end\\\":\\\"$\\\",\\\"name\\\":\\\"comment.line.double-slash.gdshader\\\"},\\\"constantFloat\\\":{\\\"match\\\":\\\"\\\\\\\\b(?:E|PI|TAU)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.float.gdshader\\\"},\\\"constructor\\\":{\\\"match\\\":\\\"\\\\\\\\b(?:[a-zA-Z_]\\\\\\\\w*(?=\\\\\\\\s*\\\\\\\\[\\\\\\\\s*\\\\\\\\w*\\\\\\\\s*]\\\\\\\\s*\\\\\\\\()|[A-Z]\\\\\\\\w*(?=\\\\\\\\s*\\\\\\\\())\\\",\\\"name\\\":\\\"entity.name.type.constructor.gdshader\\\"},\\\"controlKeyword\\\":{\\\"match\\\":\\\"\\\\\\\\b(?:if|else|do|while|for|continue|break|switch|case|default|return|discard)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.gdshader\\\"},\\\"definition\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#structDefinition\\\"}]},\\\"element\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#literalFloat\\\"},{\\\"include\\\":\\\"#literalInt\\\"},{\\\"include\\\":\\\"#literalBool\\\"},{\\\"include\\\":\\\"#identifierType\\\"},{\\\"include\\\":\\\"#constructor\\\"},{\\\"include\\\":\\\"#processorFunction\\\"},{\\\"include\\\":\\\"#identifierFunction\\\"},{\\\"include\\\":\\\"#swizzling\\\"},{\\\"include\\\":\\\"#identifierField\\\"},{\\\"include\\\":\\\"#constantFloat\\\"},{\\\"include\\\":\\\"#languageVariable\\\"},{\\\"include\\\":\\\"#identifierVariable\\\"}]},\\\"enclosed\\\":{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.parenthesis.gdshader\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"name\\\":\\\"meta.parenthesis.gdshader\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#any\\\"}]},\\\"fieldDefinition\\\":{\\\"begin\\\":\\\"\\\\\\\\b[a-zA-Z_]\\\\\\\\w*\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#typeKeyword\\\"},{\\\"match\\\":\\\".+\\\",\\\"name\\\":\\\"entity.name.type.gdshader\\\"}]}},\\\"end\\\":\\\"(?<=;)\\\",\\\"name\\\":\\\"meta.definition.field.gdshader\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#keyword\\\"},{\\\"include\\\":\\\"#arraySize\\\"},{\\\"include\\\":\\\"#fieldName\\\"},{\\\"include\\\":\\\"#any\\\"}]},\\\"fieldName\\\":{\\\"match\\\":\\\"\\\\\\\\b[a-zA-Z_]\\\\\\\\w*\\\\\\\\b\\\",\\\"name\\\":\\\"entity.name.variable.field.gdshader\\\"},\\\"hintKeyword\\\":{\\\"match\\\":\\\"\\\\\\\\b(?:source_color|hint_(?:color|range|(?:black_)?albedo|normal|(?:default_)?(?:white|black)|aniso|anisotropy|roughness_(?:[rgba]|normal|gray))|filter_(?:nearest|linear)(?:_mipmap(?:_anisotropic)?)?|repeat_(?:en|dis)able)\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.annotation.gdshader\\\"},\\\"identifierClassification\\\":{\\\"match\\\":\\\"\\\\\\\\b[a-z_]+\\\\\\\\b\\\",\\\"name\\\":\\\"entity.other.inherited-class.gdshader\\\"},\\\"identifierField\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.accessor.gdshader\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.variable.field.gdshader\\\"}},\\\"match\\\":\\\"(\\\\\\\\.)\\\\\\\\s*([a-zA-Z_]\\\\\\\\w*)\\\\\\\\b(?!\\\\\\\\s*\\\\\\\\()\\\"},\\\"identifierFunction\\\":{\\\"match\\\":\\\"\\\\\\\\b[a-zA-Z_]\\\\\\\\w*(?=(?:\\\\\\\\s|/\\\\\\\\*(?:\\\\\\\\*(?!/)|[^*])*\\\\\\\\*/)*\\\\\\\\()\\\",\\\"name\\\":\\\"entity.name.function.gdshader\\\"},\\\"identifierType\\\":{\\\"match\\\":\\\"\\\\\\\\b[a-zA-Z_]\\\\\\\\w*(?=(?:\\\\\\\\s*\\\\\\\\[\\\\\\\\s*\\\\\\\\w*\\\\\\\\s*])?\\\\\\\\s+[a-zA-Z_]\\\\\\\\w*\\\\\\\\b)\\\",\\\"name\\\":\\\"entity.name.type.gdshader\\\"},\\\"identifierVariable\\\":{\\\"match\\\":\\\"\\\\\\\\b[a-zA-Z_]\\\\\\\\w*\\\\\\\\b\\\",\\\"name\\\":\\\"variable.name.gdshader\\\"},\\\"keyword\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#classifierKeyword\\\"},{\\\"include\\\":\\\"#structKeyword\\\"},{\\\"include\\\":\\\"#controlKeyword\\\"},{\\\"include\\\":\\\"#modifierKeyword\\\"},{\\\"include\\\":\\\"#precisionKeyword\\\"},{\\\"include\\\":\\\"#typeKeyword\\\"},{\\\"include\\\":\\\"#hintKeyword\\\"}]},\\\"languageVariable\\\":{\\\"match\\\":\\\"\\\\\\\\b[A-Z][A-Z_0-9]*\\\\\\\\b\\\",\\\"name\\\":\\\"variable.language.gdshader\\\"},\\\"literalBool\\\":{\\\"match\\\":\\\"\\\\\\\\b(?:false|true)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.boolean.gdshader\\\"},\\\"literalFloat\\\":{\\\"match\\\":\\\"\\\\\\\\b(?:\\\\\\\\d+[eE][-+]?\\\\\\\\d+|(?:\\\\\\\\d*\\\\\\\\.\\\\\\\\d+|\\\\\\\\d+\\\\\\\\.)(?:[eE][-+]?\\\\\\\\d+)?)[fF]?\\\",\\\"name\\\":\\\"constant.numeric.float.gdshader\\\"},\\\"literalInt\\\":{\\\"match\\\":\\\"\\\\\\\\b(?:0[xX]\\\\\\\\h+|\\\\\\\\d+[uU]?)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.integer.gdshader\\\"},\\\"modifierKeyword\\\":{\\\"match\\\":\\\"\\\\\\\\b(?:const|global|instance|uniform|varying|in|out|inout|flat|smooth)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.modifier.gdshader\\\"},\\\"operator\\\":{\\\"match\\\":\\\"<<=?|>>=?|[-+*/\\\\\\\\&|<>=!]=|&&|\\\\\\\\|\\\\\\\\||[-+~!*/%<>\\\\\\\\&^|=]\\\",\\\"name\\\":\\\"keyword.operator.gdshader\\\"},\\\"precisionKeyword\\\":{\\\"match\\\":\\\"\\\\\\\\b(?:low|medium|high)p\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.built-in.primitive.precision.gdshader\\\"},\\\"processorFunction\\\":{\\\"match\\\":\\\"\\\\\\\\b(?:vertex|fragment|light|start|process|sky|fog)(?=(?:\\\\\\\\s|/\\\\\\\\*(?:\\\\\\\\*(?!/)|[^*])*\\\\\\\\*/)*\\\\\\\\()\\\",\\\"name\\\":\\\"support.function.gdshader\\\"},\\\"separator\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\.\\\",\\\"name\\\":\\\"punctuation.accessor.gdshader\\\"},{\\\"include\\\":\\\"#separatorComma\\\"},{\\\"match\\\":\\\";\\\",\\\"name\\\":\\\"punctuation.terminator.statement.gdshader\\\"},{\\\"match\\\":\\\":\\\",\\\"name\\\":\\\"keyword.operator.type.annotation.gdshader\\\"}]},\\\"separatorComma\\\":{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.separator.comma.gdshader\\\"},\\\"structDefinition\\\":{\\\"begin\\\":\\\"(?=\\\\\\\\bstruct\\\\\\\\b)\\\",\\\"end\\\":\\\"(?<=;)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#keyword\\\"},{\\\"include\\\":\\\"#structName\\\"},{\\\"include\\\":\\\"#structDefinitionBlock\\\"},{\\\"include\\\":\\\"#separator\\\"}]},\\\"structDefinitionBlock\\\":{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.block.struct.gdshader\\\"}},\\\"end\\\":\\\"}\\\",\\\"name\\\":\\\"meta.definition.block.struct.gdshader\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#precisionKeyword\\\"},{\\\"include\\\":\\\"#fieldDefinition\\\"},{\\\"include\\\":\\\"#keyword\\\"},{\\\"include\\\":\\\"#any\\\"}]},\\\"structKeyword\\\":{\\\"match\\\":\\\"\\\\\\\\bstruct\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.struct.gdshader\\\"},\\\"structName\\\":{\\\"match\\\":\\\"\\\\\\\\b[a-zA-Z_]\\\\\\\\w*\\\\\\\\b\\\",\\\"name\\\":\\\"entity.name.type.struct.gdshader\\\"},\\\"swizzling\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.accessor.gdshader\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.other.property.gdshader\\\"}},\\\"match\\\":\\\"(\\\\\\\\.)\\\\\\\\s*([xyzw]{2,4}|[rgba]{2,4}|[stpq]{2,4})\\\\\\\\b\\\"},\\\"typeKeyword\\\":{\\\"match\\\":\\\"\\\\\\\\b(?:void|bool|[biu]?vec[234]|u?int|float|mat[234]|[iu]?sampler(?:3D|2D(?:Array)?)|samplerCube)\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.gdshader\\\"}},\\\"scopeName\\\":\\\"source.gdshader\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/gdshader.mjs\n"));

/***/ })

}]);