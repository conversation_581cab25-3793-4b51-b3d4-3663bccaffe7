阶段一：准备工作
部署的第一阶段是准备工作，我们需要完成以下这些工作。
- 下载Suna源码并解压缩
- 安装Anaconda、node.js和docker
- 一键下载依赖。
首先，我们需要将课件网盘中的Suna项目源码包下载并解压缩，
[图片]
        然后在课件的软件包中找到Anaconda、node.js和docker三个软件安装包，下载并进行安装，这三个软件安装没有任何难度，一路点击下一步即可，此外，部署阶段建议全程开启科学网络工具，确保一些Saas服务能顺利运行。
        紧接着，让我们进入Suna项目主目录内，点击进入后端（backend）文件夹，
[图片]
然后右键点击空白处，选择在当前文件夹内开启终端，
[图片]
然后使用conda create命令，创建一个Suna后端运行的虚拟环境，
# 1. 创建虚拟环境
conda create -n suna python=3.11

# 2. 初始化 conda（只需一次）
conda init

# ⚠️ 3. 重启命令行

# 4. 激活环境（PowerShell / cmd / Git Bash 都支持）
conda activate suna
安装完成后重启命令行，输入conda activate命令进入虚拟环境，
conda activate suna
然后使用pip install，安装Suna项目依赖
pip install -r requirements.txt
安装完成后，我们进入项目前端文件夹（frontend），
[图片]
同样在空白处点击右键，打开当前目录的命令行，然后使用npm install安装前端依赖，
# 可以设置代理环境加速安装
# set HTTP_PROXY=http://127.0.0.1:10080
# set HTTPS_PROXY=http://127.0.0.1:10080
npm install
等待完成后，我们第一阶段准备工作就全部完成了。
阶段二：配置Suna项目后端
        然后进入第二阶段，让我们来完成Suna的后端配置。Suna的后端配置总共需要完成以下四步，主要是进行此前介绍的部分核心组件的配置，
- 配置tavily API-KEY：开启网络搜索功能
- 配置firecrawl API-KEY：开启网络爬虫功能
- 配置Daytona：开启沙盒环境功能
- 配置supabase：开启完整后端支持
第一步需要获取搜索引擎tavily的API KEY，我们需要登录tavily官网（https://www.tavily.com/），完成注册并获取API-KEY，
[图片]
[图片]
然后进入Suna项目的后端（backend）文件夹，用文本编辑器打开.env文件，该文件是后端配置文件，
[图片]
然后将刚刚复制的API-KEY写入TAVILY_API_KEY中，记得要随时保存。
[图片]
        接下来继续获取网络爬虫firecrawl的API KEY，同样需要登录firecrawl官网（https://firecrawl.dev），完成注册后在dashboard页面复制API-KEY，
[图片]
[图片]
然后同样是在.env文件中写入FIRECRAWL_API_KEY。
[图片]
        接下来继续设置沙盒环境工具Daytona，Daytona的设置稍微比较复杂，我们需要先进入Daytona官网（https://www.daytona.io/）并进行注册，
[图片]
        然后Daytona需要搭配一个Suna镜像才能顺利运行，因此我们需要点击左侧的Image（镜像）选项，然后点击右上角的Create Image（创建镜像），然后输入既定的Image Name和Entrypoint，然后点击创建。这段文本较为复杂，大家可以领取文字版课件后直接复制。
[图片]
- Image name: kortix/suna:0.1.2
- Entrypoint: /usr/bin/supervisord -n -cc/etc/supervisor/conf.d/supervisord.conf
然后等待镜像导入完成即可。
[图片]
接下来点击左侧Keys选项并创建API-Key，
[图片]
最后，将创建好的Daytona API-KEY写入.env配置文件。到这里，第三项配置就完成了。
[图片]
最后一项，是配置后端服务工具supabase。还是一样，需要登录supabase官网，并根据引导完成注册和项目创建，
[图片]
例如这里我创建了一个名为suna-test的项目，
[图片]
然后在项目主页左侧选择Project setting，然后点击Data API，往下翻找到schemas选项，确保选择了如图所示的三种格式。
[图片]
然后在当前页面往上翻，找到如图所示三项核心信息，并分别复制填入.env文件中箭头所示这三个变量里。
[图片]
然后保存.env文件并退出。
接下来回到后端文件夹的命令行中，输入如下三项命令，第一条命令是是登陆supabase，输入后会自动弹出确认登陆的网页，
[图片]
[图片]
输入账号密码即可。第二条link命令是在本地设置默认项目，需要关联到supabase对应的项目ID，
[图片]
而第三条push命令则是将本地数据表格式同步到关联的项目中。
[图片]
npx supabase login
npx supabase link --project-ref <your-project-ref>
npx supabase db push
全部执行完后，Suna后端配置全部完成。
阶段三：配置Suna底层大模型
        最后第三个阶段，让我们设置Suna的基础模型配置。这里强烈推荐大家分别为Suna的前端和后端配置不同的模型，能够大幅加快Suna的响应速度。
- 配置Suna后端大模型API：Claude 3.7模型
- 配置Suna前端大模型API：DeepSeek模型
        首先在后端配置中，推荐使用Claude 3.7模型，这是目前Agent能力最强的模型，同时也是Suna的默认模型。我们可以直接在某宝上购买Claude官方API-KEY，也可以自行注册，然后在后端的配置文件.env文件中写入Claude API-KEY即可。
[图片]
此外，也可以输入OpenRouter的API-KEY，来调用包括DeepSeek模型在内的各项主流模型。
        紧接着，我们用文本编辑器打开前端配置文件.env.local，
[图片]
然后如图所示，把部分后端配置复制写入前端配置文件中，并在最后一行OPENAI_API_KEY一栏写入DeepSeek官方的API-KEY，这就是全部的前端配置了。
[图片]
然后记得保存并退出。
至此，准备工作全部完成，接下来即可按照如下流程启动Suna了！
- 第一步：借助docker启动Redis
- 第二步：启动Suna后端
- 第三步：启动Suna前端
激动人心的时刻来了，首先需要借助docker启动Redis。我们需要确保之前安装的docker已经启动，然后在后端文件夹中打开命令行，输入Redis启动命令。
conda activate suna
docker compose up redis
然后同样在后端文件夹中再打开一个命令行，输入如下命令开启Suna的后端服务。
conda activate suna
python api.py
最后，在前端文件夹中打开命令行，输入如下命令开启Suna前端。
npm run dev
前端启动后，我们就能本地浏览器输入localhost:3000，即可使用Suna了！使用前会要求先注册，使用任意邮箱注册即可，
[图片]
[图片]
然后即可登录到对话页面，开始进行对话了！suna支持普通对话聊天，也可以执行各类复杂任务，大家现在看到的就是一个完整的复杂任务执行任务流程，整个过程Suan会先进行任务规划，然后一步步执行，执行过程中能够调用命令行、操作浏览器、编写Python代码、并且还能在沙盒环境中创建和编写相关文件等等等等。