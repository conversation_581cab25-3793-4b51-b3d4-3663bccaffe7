"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_turtle_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/turtle.mjs":
/*!*****************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/turtle.mjs ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Turtle\\\",\\\"fileTypes\\\":[\\\"turtle\\\",\\\"ttl\\\",\\\"acl\\\"],\\\"name\\\":\\\"turtle\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#rule-constraint\\\"},{\\\"include\\\":\\\"#iriref\\\"},{\\\"include\\\":\\\"#prefix\\\"},{\\\"include\\\":\\\"#prefixed-name\\\"},{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#special-predicate\\\"},{\\\"include\\\":\\\"#literals\\\"},{\\\"include\\\":\\\"#language-tag\\\"}],\\\"repository\\\":{\\\"boolean\\\":{\\\"match\\\":\\\"\\\\\\\\b(?i:true|false)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.sparql\\\"},\\\"comment\\\":{\\\"match\\\":\\\"#.*$\\\",\\\"name\\\":\\\"comment.line.number-sign.turtle\\\"},\\\"integer\\\":{\\\"match\\\":\\\"[+-]?(?:\\\\\\\\d+|[0-9]+\\\\\\\\.[0-9]*|\\\\\\\\.[0-9]+(?:[eE][+-]?\\\\\\\\d+)?)\\\",\\\"name\\\":\\\"constant.numeric.turtle\\\"},\\\"iriref\\\":{\\\"match\\\":\\\"<[^ - <>\\\\\\\"{}|^`\\\\\\\\\\\\\\\\]*>\\\",\\\"name\\\":\\\"entity.name.type.iriref.turtle\\\"},\\\"language-tag\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.class.turtle\\\"}},\\\"match\\\":\\\"@(\\\\\\\\w+)\\\",\\\"name\\\":\\\"meta.string-literal-language-tag.turtle\\\"},\\\"literals\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#string\\\"},{\\\"include\\\":\\\"#numeric\\\"},{\\\"include\\\":\\\"#boolean\\\"}]},\\\"numeric\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#integer\\\"}]},\\\"prefix\\\":{\\\"match\\\":\\\"(?i:@?base|@?prefix)\\\\\\\\s\\\",\\\"name\\\":\\\"keyword.operator.turtle\\\"},\\\"prefixed-name\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.PNAME_NS.turtle\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.variable.PN_LOCAL.turtle\\\"}},\\\"match\\\":\\\"(\\\\\\\\w*:)(\\\\\\\\w*)\\\",\\\"name\\\":\\\"constant.complex.turtle\\\"},\\\"rule-constraint\\\":{\\\"begin\\\":\\\"(rule:content) (\\\\\\\"\\\\\\\"\\\\\\\")\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#prefixed-name\\\"}]},\\\"2\\\":{\\\"name\\\":\\\"string.quoted.triple.turtle\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\\\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"string.quoted.triple.turtle\\\"}},\\\"name\\\":\\\"meta.rule-constraint.turtle\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.srs\\\"}]},\\\"single-dquote-string-literal\\\":{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.turtle\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.turtle\\\"}},\\\"name\\\":\\\"string.quoted.double.turtle\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string-character-escape\\\"}]},\\\"single-squote-string-literal\\\":{\\\"begin\\\":\\\"'\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.turtle\\\"}},\\\"end\\\":\\\"'\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.turtle\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.turtle\\\"}},\\\"name\\\":\\\"string.quoted.single.turtle\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string-character-escape\\\"}]},\\\"special-predicate\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.turtle\\\"}},\\\"match\\\":\\\"\\\\\\\\s(a)\\\\\\\\s\\\",\\\"name\\\":\\\"meta.specialPredicate.turtle\\\"},\\\"string\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#triple-squote-string-literal\\\"},{\\\"include\\\":\\\"#triple-dquote-string-literal\\\"},{\\\"include\\\":\\\"#single-squote-string-literal\\\"},{\\\"include\\\":\\\"#single-dquote-string-literal\\\"},{\\\"include\\\":\\\"#triple-tick-string-literal\\\"}]},\\\"string-character-escape\\\":{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\(x\\\\\\\\h{2}|[0-2][0-7]{0,2}|3[0-6][0-7]?|37[0-7]?|[4-7][0-7]?|.|$)\\\",\\\"name\\\":\\\"constant.character.escape.turtle\\\"},\\\"triple-dquote-string-literal\\\":{\\\"begin\\\":\\\"\\\\\\\"\\\\\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.turtle\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\\\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.turtle\\\"}},\\\"name\\\":\\\"string.quoted.triple.turtle\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string-character-escape\\\"}]},\\\"triple-squote-string-literal\\\":{\\\"begin\\\":\\\"'''\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.turtle\\\"}},\\\"end\\\":\\\"'''\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.turtle\\\"}},\\\"name\\\":\\\"string.quoted.triple.turtle\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string-character-escape\\\"}]},\\\"triple-tick-string-literal\\\":{\\\"begin\\\":\\\"```\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.turtle\\\"}},\\\"end\\\":\\\"```\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.turtle\\\"}},\\\"name\\\":\\\"string.quoted.triple.turtle\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string-character-escape\\\"}]}},\\\"scopeName\\\":\\\"source.turtle\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/turtle.mjs\n"));

/***/ })

}]);