"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_tsv_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/tsv.mjs":
/*!**************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/tsv.mjs ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"TSV\\\",\\\"fileTypes\\\":[\\\"tsv\\\",\\\"tab\\\"],\\\"name\\\":\\\"tsv\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"rainbow1\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.rainbow2\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.function.rainbow3\\\"},\\\"4\\\":{\\\"name\\\":\\\"comment.rainbow4\\\"},\\\"5\\\":{\\\"name\\\":\\\"string.rainbow5\\\"},\\\"6\\\":{\\\"name\\\":\\\"variable.parameter.rainbow6\\\"},\\\"7\\\":{\\\"name\\\":\\\"constant.numeric.rainbow7\\\"},\\\"8\\\":{\\\"name\\\":\\\"entity.name.type.rainbow8\\\"},\\\"9\\\":{\\\"name\\\":\\\"markup.bold.rainbow9\\\"},\\\"10\\\":{\\\"name\\\":\\\"invalid.rainbow10\\\"}},\\\"match\\\":\\\"([^\\\\\\\\t]*\\\\\\\\t?)([^\\\\\\\\t]*\\\\\\\\t?)([^\\\\\\\\t]*\\\\\\\\t?)([^\\\\\\\\t]*\\\\\\\\t?)([^\\\\\\\\t]*\\\\\\\\t?)([^\\\\\\\\t]*\\\\\\\\t?)([^\\\\\\\\t]*\\\\\\\\t?)([^\\\\\\\\t]*\\\\\\\\t?)([^\\\\\\\\t]*\\\\\\\\t?)([^\\\\\\\\t]*\\\\\\\\t?)\\\",\\\"name\\\":\\\"rainbowgroup\\\"}],\\\"scopeName\\\":\\\"text.tsv\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/tsv.mjs\n"));

/***/ })

}]);