"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_plsql_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/plsql.mjs":
/*!****************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/plsql.mjs ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"PL/SQL\\\",\\\"fileTypes\\\":[\\\"sql\\\",\\\"ddl\\\",\\\"dml\\\",\\\"pkh\\\",\\\"pks\\\",\\\"pkb\\\",\\\"pck\\\",\\\"pls\\\",\\\"plb\\\"],\\\"foldingStartMarker\\\":\\\"(?i)^\\\\\\\\s*(begin|if|loop)\\\\\\\\b\\\",\\\"foldingStopMarker\\\":\\\"(?i)^\\\\\\\\s*(end)\\\\\\\\b\\\",\\\"name\\\":\\\"plsql\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"/\\\\\\\\*\\\",\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"name\\\":\\\"comment.block.oracle\\\"},{\\\"match\\\":\\\"--.*$\\\",\\\"name\\\":\\\"comment.line.double-dash.oracle\\\"},{\\\"match\\\":\\\"(?i)^\\\\\\\\s*rem\\\\\\\\s+.*$\\\",\\\"name\\\":\\\"comment.line.sqlplus.oracle\\\"},{\\\"match\\\":\\\"(?i)^\\\\\\\\s*prompt\\\\\\\\s+.*$\\\",\\\"name\\\":\\\"comment.line.sqlplus-prompt.oracle\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.oracle\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.oracle\\\"}},\\\"match\\\":\\\"(?i)^\\\\\\\\s*(create)(\\\\\\\\s+or\\\\\\\\s+replace)?\\\\\\\\s+\\\",\\\"name\\\":\\\"meta.create.oracle\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.oracle\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.oracle\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.type.oracle\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(package)(\\\\\\\\s+body)?\\\\\\\\s+(\\\\\\\\S+)\\\",\\\"name\\\":\\\"meta.package.oracle\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.oracle\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.type.oracle\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(type)\\\\\\\\s+\\\\\\\"([^\\\\\\\"]+)\\\\\\\"\\\",\\\"name\\\":\\\"meta.type.oracle\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.oracle\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.oracle\\\"}},\\\"match\\\":\\\"(?i)^\\\\\\\\s*(function|procedure)\\\\\\\\s+\\\\\\\"?([-a-z0-9_]+)\\\\\\\"?\\\",\\\"name\\\":\\\"meta.procedure.oracle\\\"},{\\\"match\\\":\\\"[!<>:]?=|<>|[<>+]|(?<!\\\\\\\\.)\\\\\\\\*|-|(?<!^)/|\\\\\\\\|\\\\\\\\|\\\",\\\"name\\\":\\\"keyword.operator.oracle\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\b(true|false|null|is\\\\\\\\s+(not\\\\\\\\s+)?null)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.oracle\\\"},{\\\"match\\\":\\\"\\\\\\\\b\\\\\\\\d+(\\\\\\\\.\\\\\\\\d+)?\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.oracle\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\b(if|elsif|else|end\\\\\\\\s+if|loop|end\\\\\\\\s+loop|for|while|case|end\\\\\\\\s+case|continue|return|goto)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.oracle\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\b(or|and|not|like)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.oracle\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\b(%(isopen|found|notfound|rowcount)|commit|rollback|sqlerrm)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.oracle\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\b(sql(?:|code))\\\\\\\\b\\\",\\\"name\\\":\\\"variable.language.oracle\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\b(ascii|asciistr|chr|compose|concat|convert|decompose|dump|initcap|instr|instrb|instrc|instr2|instr4|unistr|length|lengthb|lengthc|length2|length4|lower|lpad|ltrim|nchr|replace|rpad|rtrim|soundex|substr|translate|trim|upper|vsize)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.builtin.char.oracle\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\b(add_months|current_date|current_timestamp|dbtimezone|last_day|localtimestamp|months_between|new_time|next_day|round|sessiontimezone|sysdate|tz_offset|systimestamp)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.builtin.date.oracle\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\b(avg|count|sum|max|min|median|corr|corr_\\\\\\\\w+|covar_(pop|samp)|cume_dist|dense_rank|first|group_id|grouping|grouping_id|last|percentile_cont|percentile_disc|percent_rank|rank|regr_\\\\\\\\w+|row_number|stats_binomial_test|stats_crosstab|stats_f_test|stats_ks_test|stats_mode|stats_mw_test|stats_one_way_anova|stats_t_test_\\\\\\\\w+|stats_wsr_test|stddev|stddev_pop|stddev_samp|var_pop|var_samp|variance)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.builtin.aggregate.oracle\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\b(bfilename|cardinality|coalesce|decode|empty_(blob|clob)|lag|lead|listagg|lnnvl|nanvl|nullif|nvl|nvl2|sys_(context|guid|typeid|connect_by_path|extract_utc)|uid|(current\\\\\\\\s+)?user|userenv|cardinality|(bulk\\\\\\\\s+)?collect|powermultiset(_by_cardinality)?|ora_hash|standard_hash|execute\\\\\\\\s+immediate|alter\\\\\\\\s+session)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.builtin.advanced.oracle\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\b(bin_to_num|cast|chartorowid|from_tz|hextoraw|numtodsinterval|numtoyminterval|rawtohex|rawtonhex|to_char|to_clob|to_date|to_dsinterval|to_lob|to_multi_byte|to_nclob|to_number|to_single_byte|to_timestamp|to_timestamp_tz|to_yminterval|scn_to_timestamp|timestamp_to_scn|rowidtochar|rowidtonchar|to_binary_double|to_binary_float|to_blob|to_nchar|con_dbid_to_id|con_guid_to_id|con_name_to_id|con_uid_to_id)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.builtin.convert.oracle\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\b(abs|acos|asin|atan|atan2|bit_(and|or|xor)|ceil|cos|cosh|exp|extract|floor|greatest|least|ln|log|mod|power|remainder|round|sign|sin|sinh|sqrt|tan|tanh|trunc)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.builtin.math.oracle\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\b(\\\\\\\\.(count|delete|exists|extend|first|last|limit|next|prior|trim|reverse))\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.builtin.collection.oracle\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\b(cluster_details|cluster_distance|cluster_id|cluster_probability|cluster_set|feature_details|feature_id|feature_set|feature_value|prediction|prediction_bounds|prediction_cost|prediction_details|prediction_probability|prediction_set)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.builtin.data_mining.oracle\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\b(appendchildxml|deletexml|depth|extract|existsnode|extractvalue|insertchildxml|insertxmlbefore|xmlcast|xmldiff|xmlelement|xmlexists|xmlisvalid|insertchildxmlafter|insertchildxmlbefore|path|sys_dburigen|sys_xmlagg|sys_xmlgen|updatexml|xmlagg|xmlcdata|xmlcolattval|xmlcomment|xmlconcat|xmlforest|xmlparse|xmlpi|xmlquery|xmlroot|xmlsequence|xmlserialize|xmltable|xmltransform)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.builtin.xml.oracle\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\b(pragma\\\\\\\\s+(autonomous_transaction|serially_reusable|restrict_references|exception_init|inline))\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.pragma.oracle\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\b(p([io]|io)_[-a-z0-9_]+)\\\\\\\\b\\\",\\\"name\\\":\\\"variable.parameter.oracle\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\b(l_[-a-z0-9_]+)\\\\\\\\b\\\",\\\"name\\\":\\\"variable.other.oracle\\\"},{\\\"match\\\":\\\"(?i):\\\\\\\\b(new|old)\\\\\\\\b\\\",\\\"name\\\":\\\"variable.trigger.oracle\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\b(connect\\\\\\\\s+by\\\\\\\\s+(nocycle\\\\\\\\s+)?(prior|level)|connect_by_(root|icycle)|level|start\\\\\\\\s+with)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.hierarchical.sql.oracle\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\b(language|name|java|c)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.wrapper.oracle\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\b(end|then|deterministic|exception|when|declare|begin|in|out|nocopy|is|as|exit|open|fetch|into|close|subtype|type|rowtype|default|exclusive|mode|lock|record|index\\\\\\\\s+by|result_cache|constant|comment|\\\\\\\\.(nextval|currval))\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.oracle\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\b(grant|revoke|alter|drop|force|add|check|constraint|primary\\\\\\\\s+key|foreign\\\\\\\\s+key|references|unique(\\\\\\\\s+index)?|column|sequence|increment\\\\\\\\s+by|cache|(materialized\\\\\\\\s+)?view|trigger|storage|tablespace|pct(free|used)|(init|max)trans|logging)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.ddl.oracle\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\b(with|select|from|where|order\\\\\\\\s+(siblings\\\\\\\\s+)?by|group\\\\\\\\s+by|rollup|cube|((left|right|cross|natural)\\\\\\\\s+(outer\\\\\\\\s+)?)?join|on|asc|desc|update|set|insert|into|values|delete|distinct|union|minus|intersect|having|limit|table|between|like|of|row|(r(?:ange|ows))\\\\\\\\s+between|nulls\\\\\\\\s+first|nulls\\\\\\\\s+last|before|after|all|any|exists|rownum|cursor|returning|over|partition\\\\\\\\s+by|merge|using|matched|pivot|unpivot)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.sql.oracle\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\b(define|whenever\\\\\\\\s+sqlerror|exec|timing\\\\\\\\s+start|timing\\\\\\\\s+stop)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.sqlplus.oracle\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\b(access_into_null|case_not_found|collection_is_null|cursor_already_open|dup_val_on_index|invalid_cursor|invalid_number|login_denied|no_data_found|not_logged_on|program_error|rowtype_mismatch|self_is_null|storage_error|subscript_beyond_count|subscript_outside_limit|sys_invalid_rowid|timeout_on_resource|too_many_rows|value_error|zero_divide|others)\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.exception.oracle\\\"},{\\\"captures\\\":{\\\"3\\\":{\\\"name\\\":\\\"support.class.oracle\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b((dbms|utl|owa|apex)_\\\\\\\\w+\\\\\\\\.(\\\\\\\\w+))\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.oracle\\\"},{\\\"captures\\\":{\\\"3\\\":{\\\"name\\\":\\\"support.class.oracle\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b((ht[fp])\\\\\\\\.(\\\\\\\\w+))\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.oracle\\\"},{\\\"captures\\\":{\\\"3\\\":{\\\"name\\\":\\\"support.class.user-defined.oracle\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b((\\\\\\\\w+_pkg|pkg_\\\\\\\\w+)\\\\\\\\.(\\\\\\\\w+))\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.user-defined.oracle\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\b(raise(?:|_application_error))\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.oracle\\\"},{\\\"begin\\\":\\\"'\\\",\\\"end\\\":\\\"'\\\",\\\"name\\\":\\\"string.quoted.single.oracle\\\"},{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"end\\\":\\\"\\\\\\\"\\\",\\\"name\\\":\\\"string.quoted.double.oracle\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\b(char|varchar|varchar2|nchar|nvarchar2|boolean|date|timestamp(\\\\\\\\s+with(\\\\\\\\s+local)?\\\\\\\\s+time\\\\\\\\s+zone)?|interval\\\\\\\\s*day(\\\\\\\\(\\\\\\\\d*\\\\\\\\))?\\\\\\\\s*to\\\\\\\\s*month|interval\\\\\\\\s*year(\\\\\\\\(\\\\\\\\d*\\\\\\\\))?\\\\\\\\s*to\\\\\\\\s*second(\\\\\\\\(\\\\\\\\d*\\\\\\\\))?|xmltype|blob|clob|nclob|bfile|long|long\\\\\\\\s+raw|raw|number|integer|decimal|smallint|float|binary_(float|double|integer)|pls_(float|double|integer)|rowid|urowid|vararray|natural|naturaln|positive|positiven|signtype|simple_(float|double|integer))\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.oracle\\\"}],\\\"scopeName\\\":\\\"source.plsql.oracle\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/plsql.mjs\n"));

/***/ })

}]);