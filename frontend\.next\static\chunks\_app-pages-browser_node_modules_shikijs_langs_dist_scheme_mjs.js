"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_scheme_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/scheme.mjs":
/*!*****************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/scheme.mjs ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Scheme\\\",\\\"fileTypes\\\":[\\\"scm\\\",\\\"ss\\\",\\\"sch\\\",\\\"rkt\\\"],\\\"name\\\":\\\"scheme\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#block-comment\\\"},{\\\"include\\\":\\\"#sexp\\\"},{\\\"include\\\":\\\"#string\\\"},{\\\"include\\\":\\\"#language-functions\\\"},{\\\"include\\\":\\\"#quote\\\"},{\\\"include\\\":\\\"#illegal\\\"}],\\\"repository\\\":{\\\"block-comment\\\":{\\\"begin\\\":\\\"#\\\\\\\\|\\\",\\\"contentName\\\":\\\"comment\\\",\\\"end\\\":\\\"\\\\\\\\|#\\\",\\\"name\\\":\\\"comment\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-comment\\\",\\\"name\\\":\\\"comment\\\"}]},\\\"comment\\\":{\\\"begin\\\":\\\"(^[ \\\\\\\\t]+)?(?=;)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.comment.leading.scheme\\\"}},\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\";\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.scheme\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.line.semicolon.scheme\\\"}]},\\\"constants\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"#[t|f]\\\",\\\"name\\\":\\\"constant.language.boolean.scheme\\\"},{\\\"match\\\":\\\"(?<=[(\\\\\\\\s])((#[ei])?[0-9]+(\\\\\\\\.[0-9]+)?|(#x)\\\\\\\\h+|(#o)[0-7]+|(#b)[01]+)(?=[\\\\\\\\s;()'\\\\\\\",\\\\\\\\[\\\\\\\\]])\\\",\\\"name\\\":\\\"constant.numeric.scheme\\\"}]},\\\"illegal\\\":{\\\"match\\\":\\\"[()\\\\\\\\[\\\\\\\\]]\\\",\\\"name\\\":\\\"invalid.illegal.parenthesis.scheme\\\"},\\\"language-functions\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=([\\\\\\\\s(\\\\\\\\[]))(do|or|and|else|quasiquote|begin|if|case|set!|cond|let|unquote|define|let\\\\\\\\*|unquote-splicing|delay|letrec)(?=([\\\\\\\\s(]))\\\",\\\"name\\\":\\\"keyword.control.scheme\\\"},{\\\"match\\\":\\\"(?<=([\\\\\\\\s(]))(char-alphabetic|char-lower-case|char-numeric|char-ready|char-upper-case|char-whitespace|(?:char|string)(?:-ci)?(?:=|<=?|>=?)|atom|boolean|bound-identifier=|char|complex|identifier|integer|symbol|free-identifier=|inexact|eof-object|exact|list|(?:input|output)-port|pair|real|rational|zero|vector|negative|odd|null|string|eq|equal|eqv|even|number|positive|procedure)(\\\\\\\\?)(?=([\\\\\\\\s(]))\\\",\\\"name\\\":\\\"support.function.boolean-test.scheme\\\"},{\\\"match\\\":\\\"(?<=([\\\\\\\\s(]))(char->integer|exact->inexact|inexact->exact|integer->char|symbol->string|list->vector|list->string|identifier->symbol|vector->list|string->list|string->number|string->symbol|number->string)(?=([\\\\\\\\s(]))\\\",\\\"name\\\":\\\"support.function.convert-type.scheme\\\"},{\\\"match\\\":\\\"(?<=([\\\\\\\\s(]))(set-c(?:ar|dr)|(?:vector|string)-(?:fill|set))(!)(?=([\\\\\\\\s(]))\\\",\\\"name\\\":\\\"support.function.with-side-effects.scheme\\\"},{\\\"match\\\":\\\"(?<=([\\\\\\\\s(]))(>=?|<=?|[=*/+-])(?=([\\\\\\\\s(]))\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.scheme\\\"},{\\\"match\\\":\\\"(?<=([\\\\\\\\s(]))(append|apply|approximate|call-with-current-continuation|call/cc|catch|construct-identifier|define-syntax|display|foo|for-each|force|format|cd|gen-counter|gen-loser|generate-identifier|last-pair|length|let-syntax|letrec-syntax|list|list-ref|list-tail|load|log|macro|magnitude|map|map-streams|max|member|memq|memv|min|newline|nil|not|peek-char|rationalize|read|read-char|return|reverse|sequence|substring|syntax|syntax-rules|transcript-off|transcript-on|truncate|unwrap-syntax|values-list|write|write-char|cons|c([ad]){1,4}r|abs|acos|angle|asin|assoc|assq|assv|atan|ceiling|cos|floor|round|sin|sqrt|tan|(?:real|imag)-part|numerator|denominatormodulo|exp|expt|remainder|quotient|lcm|call-with-(?:input|output)-file|c(?:lose|urrent)-(?:input|output)-port|with-(?:input|output)-from-file|open-(?:input|output)-file|char-(?:downcase|upcase|ready)|make-(?:polar|promise|rectangular|string|vector)string(?:-(?:append|copy|length|ref))?|vector-(?:length|ref))(?=([\\\\\\\\s(]))\\\",\\\"name\\\":\\\"support.function.general.scheme\\\"}]},\\\"quote\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.quoted.symbol.scheme\\\"}},\\\"match\\\":\\\"(')\\\\\\\\s*(\\\\\\\\p{alnum}[[:alnum:]!$%\\\\\\\\&*+-./:<=>?@^_~]*)\\\",\\\"name\\\":\\\"constant.other.symbol.scheme\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.quoted.empty-list.scheme\\\"},\\\"2\\\":{\\\"name\\\":\\\"meta.expression.scheme\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.section.expression.begin.scheme\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.section.expression.end.scheme\\\"}},\\\"match\\\":\\\"(')\\\\\\\\s*((\\\\\\\\()\\\\\\\\s*(\\\\\\\\)))\\\",\\\"name\\\":\\\"constant.other.empty-list.schem\\\"},{\\\"begin\\\":\\\"(')\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.quoted.scheme\\\"}},\\\"end\\\":\\\"(?=[\\\\\\\\s()])|(?<=\\\\\\\\n)\\\",\\\"name\\\":\\\"string.other.quoted-object.scheme\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#quoted\\\"}]}]},\\\"quote-sexp\\\":{\\\"begin\\\":\\\"(?<=\\\\\\\\()\\\\\\\\s*(quote)\\\\\\\\s+\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.quote.scheme\\\"}},\\\"contentName\\\":\\\"string.other.quote.scheme\\\",\\\"end\\\":\\\"(?=[\\\\\\\\s)])|(?<=\\\\\\\\n)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#quoted\\\"}]},\\\"quoted\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#string\\\"},{\\\"begin\\\":\\\"(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.expression.begin.scheme\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.expression.end.scheme\\\"}},\\\"name\\\":\\\"meta.expression.scheme\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#quoted\\\"}]},{\\\"include\\\":\\\"#quote\\\"},{\\\"include\\\":\\\"#illegal\\\"}]},\\\"sexp\\\":{\\\"begin\\\":\\\"(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.expression.begin.scheme\\\"}},\\\"end\\\":\\\"(\\\\\\\\))(\\\\\\\\n)?\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.expression.end.scheme\\\"},\\\"2\\\":{\\\"name\\\":\\\"meta.after-expression.scheme\\\"}},\\\"name\\\":\\\"meta.expression.scheme\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"begin\\\":\\\"(?<=\\\\\\\\()(define)\\\\\\\\s+(\\\\\\\\()(\\\\\\\\p{alnum}[[:alnum:]!$%\\\\\\\\&*+-./:<=>?@^_~]*)((\\\\\\\\s+(\\\\\\\\p{alnum}[[:alnum:]!$%\\\\\\\\&*+-./:<=>?@^_~]*|[._]))*)\\\\\\\\s*(\\\\\\\\))\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.scheme\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.function.scheme\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.function.scheme\\\"},\\\"4\\\":{\\\"name\\\":\\\"variable.parameter.function.scheme\\\"},\\\"7\\\":{\\\"name\\\":\\\"punctuation.definition.function.scheme\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"name\\\":\\\"meta.declaration.procedure.scheme\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#sexp\\\"},{\\\"include\\\":\\\"#illegal\\\"}]},{\\\"begin\\\":\\\"(?<=\\\\\\\\()(lambda)\\\\\\\\s+(\\\\\\\\()((?:(\\\\\\\\p{alnum}[[:alnum:]!$%\\\\\\\\&*+-./:<=>?@^_~]*|[._])\\\\\\\\s+)*(\\\\\\\\p{alnum}[[:alnum:]!$%\\\\\\\\&*+-./:<=>?@^_~]*|[._])?)(\\\\\\\\))\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.scheme\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.variable.scheme\\\"},\\\"3\\\":{\\\"name\\\":\\\"variable.parameter.scheme\\\"},\\\"6\\\":{\\\"name\\\":\\\"punctuation.definition.variable.scheme\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"name\\\":\\\"meta.declaration.procedure.scheme\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#sexp\\\"},{\\\"include\\\":\\\"#illegal\\\"}]},{\\\"begin\\\":\\\"(?<=\\\\\\\\()(define)\\\\\\\\s(\\\\\\\\p{alnum}[[:alnum:]!$%\\\\\\\\&*+-./:<=>?@^_~]*)\\\\\\\\s*.*?\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.scheme\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.other.scheme\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"name\\\":\\\"meta.declaration.variable.scheme\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#sexp\\\"},{\\\"include\\\":\\\"#illegal\\\"}]},{\\\"include\\\":\\\"#quote-sexp\\\"},{\\\"include\\\":\\\"#quote\\\"},{\\\"include\\\":\\\"#language-functions\\\"},{\\\"include\\\":\\\"#string\\\"},{\\\"include\\\":\\\"#constants\\\"},{\\\"match\\\":\\\"(?<=[(\\\\\\\\s])(#\\\\\\\\\\\\\\\\)(space|newline|tab)(?=[\\\\\\\\s)])\\\",\\\"name\\\":\\\"constant.character.named.scheme\\\"},{\\\"match\\\":\\\"(?<=[(\\\\\\\\s])(#\\\\\\\\\\\\\\\\)x[0-9A-F]{2,4}(?=[\\\\\\\\s)])\\\",\\\"name\\\":\\\"constant.character.hex-literal.scheme\\\"},{\\\"match\\\":\\\"(?<=[(\\\\\\\\s])(#\\\\\\\\\\\\\\\\).(?=[\\\\\\\\s)])\\\",\\\"name\\\":\\\"constant.character.escape.scheme\\\"},{\\\"match\\\":\\\"(?<=[ ()])\\\\\\\\.(?=[ ()])\\\",\\\"name\\\":\\\"punctuation.separator.cons.scheme\\\"},{\\\"include\\\":\\\"#sexp\\\"},{\\\"include\\\":\\\"#illegal\\\"}]},\\\"string\\\":{\\\"begin\\\":\\\"(\\\\\\\")\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.scheme\\\"}},\\\"end\\\":\\\"(\\\\\\\")\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.scheme\\\"}},\\\"name\\\":\\\"string.quoted.double.scheme\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"constant.character.escape.scheme\\\"}]}},\\\"scopeName\\\":\\\"source.scheme\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ac2hpa2lqcy9sYW5ncy9kaXN0L3NjaGVtZS5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLHdDQUF3Qyw2R0FBNkcseUJBQXlCLEVBQUUsK0JBQStCLEVBQUUsc0JBQXNCLEVBQUUsd0JBQXdCLEVBQUUsb0NBQW9DLEVBQUUsdUJBQXVCLEVBQUUseUJBQXlCLGtCQUFrQixtQkFBbUIsd0dBQXdHLG9EQUFvRCxFQUFFLGNBQWMsNkJBQTZCLHVCQUF1QixPQUFPLDREQUE0RCxzQ0FBc0MsYUFBYSxzQkFBc0IsT0FBTyxvREFBb0QsOERBQThELEVBQUUsZ0JBQWdCLGVBQWUsbUVBQW1FLEVBQUUsaUdBQWlHLDREQUE0RCxFQUFFLGNBQWMsNkVBQTZFLHlCQUF5QixlQUFlLGtNQUFrTSxFQUFFLGtkQUFrZCxFQUFFLDRSQUE0UixFQUFFLHFKQUFxSixFQUFFLDZHQUE2RyxFQUFFLGlpQkFBaWlCLElBQUksMmVBQTJlLEVBQUUsWUFBWSxlQUFlLGNBQWMsT0FBTyx1REFBdUQsNkJBQTZCLE1BQU0sZ0ZBQWdGLEVBQUUsY0FBYyxPQUFPLDBEQUEwRCxRQUFRLG9DQUFvQyxRQUFRLHlEQUF5RCxRQUFRLHdEQUF3RCw0RkFBNEYsRUFBRSwyQ0FBMkMsT0FBTyxnREFBZ0Qsb0dBQW9HLHdCQUF3QixFQUFFLEVBQUUsaUJBQWlCLCtEQUErRCxPQUFPLDJDQUEyQyxrR0FBa0csd0JBQXdCLEVBQUUsYUFBYSxlQUFlLHdCQUF3QixFQUFFLHlDQUF5QyxPQUFPLDBEQUEwRCxzQ0FBc0MsT0FBTyx3REFBd0Qsb0RBQW9ELHdCQUF3QixFQUFFLEVBQUUsdUJBQXVCLEVBQUUseUJBQXlCLEVBQUUsV0FBVyx5Q0FBeUMsT0FBTywwREFBMEQsOENBQThDLE9BQU8sdURBQXVELFFBQVEsMkNBQTJDLG9EQUFvRCx5QkFBeUIsRUFBRSxrREFBa0QsTUFBTSxrREFBa0QsTUFBTSx5RUFBeUUsT0FBTyxvQ0FBb0MsUUFBUSxvREFBb0QsUUFBUSx5Q0FBeUMsUUFBUSxnREFBZ0QsUUFBUSxxREFBcUQscUZBQXFGLHlCQUF5QixFQUFFLHNCQUFzQixFQUFFLHlCQUF5QixFQUFFLEVBQUUsc0RBQXNELE1BQU0sdURBQXVELE1BQU0sa0VBQWtFLE9BQU8sb0NBQW9DLFFBQVEsb0RBQW9ELFFBQVEsdUNBQXVDLFFBQVEscURBQXFELHFGQUFxRix5QkFBeUIsRUFBRSxzQkFBc0IsRUFBRSx5QkFBeUIsRUFBRSxFQUFFLDBDQUEwQyxNQUFNLDZEQUE2RCxPQUFPLG9DQUFvQyxRQUFRLG9DQUFvQyxvRkFBb0YseUJBQXlCLEVBQUUsc0JBQXNCLEVBQUUseUJBQXlCLEVBQUUsRUFBRSw0QkFBNEIsRUFBRSx1QkFBdUIsRUFBRSxvQ0FBb0MsRUFBRSx3QkFBd0IsRUFBRSwyQkFBMkIsRUFBRSxtSEFBbUgsRUFBRSw4Q0FBOEMsSUFBSSxrRUFBa0UsRUFBRSxrR0FBa0csRUFBRSxzRkFBc0YsRUFBRSxzQkFBc0IsRUFBRSx5QkFBeUIsRUFBRSxhQUFhLHdDQUF3QyxPQUFPLHlEQUF5RCxxQ0FBcUMsT0FBTyx1REFBdUQseURBQXlELHNFQUFzRSxHQUFHLGlDQUFpQzs7QUFFNXFQLGlFQUFlO0FBQ2Y7QUFDQSxDQUFDIiwic291cmNlcyI6WyJEOlxcc29mdFxcU2VhUHJvamVjdFxcZ2l0aHViX29wZW5fcHJvamVjdF9zdWNjZXNzXFxzdW5hXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxAc2hpa2lqc1xcbGFuZ3NcXGRpc3RcXHNjaGVtZS5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgbGFuZyA9IE9iamVjdC5mcmVlemUoSlNPTi5wYXJzZShcIntcXFwiZGlzcGxheU5hbWVcXFwiOlxcXCJTY2hlbWVcXFwiLFxcXCJmaWxlVHlwZXNcXFwiOltcXFwic2NtXFxcIixcXFwic3NcXFwiLFxcXCJzY2hcXFwiLFxcXCJya3RcXFwiXSxcXFwibmFtZVxcXCI6XFxcInNjaGVtZVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2NvbW1lbnRcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjYmxvY2stY29tbWVudFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNzZXhwXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3N0cmluZ1xcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNsYW5ndWFnZS1mdW5jdGlvbnNcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjcXVvdGVcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjaWxsZWdhbFxcXCJ9XSxcXFwicmVwb3NpdG9yeVxcXCI6e1xcXCJibG9jay1jb21tZW50XFxcIjp7XFxcImJlZ2luXFxcIjpcXFwiI1xcXFxcXFxcfFxcXCIsXFxcImNvbnRlbnROYW1lXFxcIjpcXFwiY29tbWVudFxcXCIsXFxcImVuZFxcXCI6XFxcIlxcXFxcXFxcfCNcXFwiLFxcXCJuYW1lXFxcIjpcXFwiY29tbWVudFxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2Jsb2NrLWNvbW1lbnRcXFwiLFxcXCJuYW1lXFxcIjpcXFwiY29tbWVudFxcXCJ9XX0sXFxcImNvbW1lbnRcXFwiOntcXFwiYmVnaW5cXFwiOlxcXCIoXlsgXFxcXFxcXFx0XSspPyg/PTspXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi53aGl0ZXNwYWNlLmNvbW1lbnQubGVhZGluZy5zY2hlbWVcXFwifX0sXFxcImVuZFxcXCI6XFxcIig/IVxcXFxcXFxcRylcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiYmVnaW5cXFwiOlxcXCI7XFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLmNvbW1lbnQuc2NoZW1lXFxcIn19LFxcXCJlbmRcXFwiOlxcXCJcXFxcXFxcXG5cXFwiLFxcXCJuYW1lXFxcIjpcXFwiY29tbWVudC5saW5lLnNlbWljb2xvbi5zY2hlbWVcXFwifV19LFxcXCJjb25zdGFudHNcXFwiOntcXFwicGF0dGVybnNcXFwiOlt7XFxcIm1hdGNoXFxcIjpcXFwiI1t0fGZdXFxcIixcXFwibmFtZVxcXCI6XFxcImNvbnN0YW50Lmxhbmd1YWdlLmJvb2xlYW4uc2NoZW1lXFxcIn0se1xcXCJtYXRjaFxcXCI6XFxcIig/PD1bKFxcXFxcXFxcc10pKCgjW2VpXSk/WzAtOV0rKFxcXFxcXFxcLlswLTldKyk/fCgjeClcXFxcXFxcXGgrfCgjbylbMC03XSt8KCNiKVswMV0rKSg/PVtcXFxcXFxcXHM7KCknXFxcXFxcXCIsXFxcXFxcXFxbXFxcXFxcXFxdXSlcXFwiLFxcXCJuYW1lXFxcIjpcXFwiY29uc3RhbnQubnVtZXJpYy5zY2hlbWVcXFwifV19LFxcXCJpbGxlZ2FsXFxcIjp7XFxcIm1hdGNoXFxcIjpcXFwiWygpXFxcXFxcXFxbXFxcXFxcXFxdXVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJpbnZhbGlkLmlsbGVnYWwucGFyZW50aGVzaXMuc2NoZW1lXFxcIn0sXFxcImxhbmd1YWdlLWZ1bmN0aW9uc1xcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwibWF0Y2hcXFwiOlxcXCIoPzw9KFtcXFxcXFxcXHMoXFxcXFxcXFxbXSkpKGRvfG9yfGFuZHxlbHNlfHF1YXNpcXVvdGV8YmVnaW58aWZ8Y2FzZXxzZXQhfGNvbmR8bGV0fHVucXVvdGV8ZGVmaW5lfGxldFxcXFxcXFxcKnx1bnF1b3RlLXNwbGljaW5nfGRlbGF5fGxldHJlYykoPz0oW1xcXFxcXFxccyhdKSlcXFwiLFxcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5jb250cm9sLnNjaGVtZVxcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCIoPzw9KFtcXFxcXFxcXHMoXSkpKGNoYXItYWxwaGFiZXRpY3xjaGFyLWxvd2VyLWNhc2V8Y2hhci1udW1lcmljfGNoYXItcmVhZHl8Y2hhci11cHBlci1jYXNlfGNoYXItd2hpdGVzcGFjZXwoPzpjaGFyfHN0cmluZykoPzotY2kpPyg/Oj18PD0/fD49Pyl8YXRvbXxib29sZWFufGJvdW5kLWlkZW50aWZpZXI9fGNoYXJ8Y29tcGxleHxpZGVudGlmaWVyfGludGVnZXJ8c3ltYm9sfGZyZWUtaWRlbnRpZmllcj18aW5leGFjdHxlb2Ytb2JqZWN0fGV4YWN0fGxpc3R8KD86aW5wdXR8b3V0cHV0KS1wb3J0fHBhaXJ8cmVhbHxyYXRpb25hbHx6ZXJvfHZlY3RvcnxuZWdhdGl2ZXxvZGR8bnVsbHxzdHJpbmd8ZXF8ZXF1YWx8ZXF2fGV2ZW58bnVtYmVyfHBvc2l0aXZlfHByb2NlZHVyZSkoXFxcXFxcXFw/KSg/PShbXFxcXFxcXFxzKF0pKVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJzdXBwb3J0LmZ1bmN0aW9uLmJvb2xlYW4tdGVzdC5zY2hlbWVcXFwifSx7XFxcIm1hdGNoXFxcIjpcXFwiKD88PShbXFxcXFxcXFxzKF0pKShjaGFyLT5pbnRlZ2VyfGV4YWN0LT5pbmV4YWN0fGluZXhhY3QtPmV4YWN0fGludGVnZXItPmNoYXJ8c3ltYm9sLT5zdHJpbmd8bGlzdC0+dmVjdG9yfGxpc3QtPnN0cmluZ3xpZGVudGlmaWVyLT5zeW1ib2x8dmVjdG9yLT5saXN0fHN0cmluZy0+bGlzdHxzdHJpbmctPm51bWJlcnxzdHJpbmctPnN5bWJvbHxudW1iZXItPnN0cmluZykoPz0oW1xcXFxcXFxccyhdKSlcXFwiLFxcXCJuYW1lXFxcIjpcXFwic3VwcG9ydC5mdW5jdGlvbi5jb252ZXJ0LXR5cGUuc2NoZW1lXFxcIn0se1xcXCJtYXRjaFxcXCI6XFxcIig/PD0oW1xcXFxcXFxccyhdKSkoc2V0LWMoPzphcnxkcil8KD86dmVjdG9yfHN0cmluZyktKD86ZmlsbHxzZXQpKSghKSg/PShbXFxcXFxcXFxzKF0pKVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJzdXBwb3J0LmZ1bmN0aW9uLndpdGgtc2lkZS1lZmZlY3RzLnNjaGVtZVxcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCIoPzw9KFtcXFxcXFxcXHMoXSkpKD49P3w8PT98Wz0qLystXSkoPz0oW1xcXFxcXFxccyhdKSlcXFwiLFxcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5vcGVyYXRvci5hcml0aG1ldGljLnNjaGVtZVxcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCIoPzw9KFtcXFxcXFxcXHMoXSkpKGFwcGVuZHxhcHBseXxhcHByb3hpbWF0ZXxjYWxsLXdpdGgtY3VycmVudC1jb250aW51YXRpb258Y2FsbC9jY3xjYXRjaHxjb25zdHJ1Y3QtaWRlbnRpZmllcnxkZWZpbmUtc3ludGF4fGRpc3BsYXl8Zm9vfGZvci1lYWNofGZvcmNlfGZvcm1hdHxjZHxnZW4tY291bnRlcnxnZW4tbG9zZXJ8Z2VuZXJhdGUtaWRlbnRpZmllcnxsYXN0LXBhaXJ8bGVuZ3RofGxldC1zeW50YXh8bGV0cmVjLXN5bnRheHxsaXN0fGxpc3QtcmVmfGxpc3QtdGFpbHxsb2FkfGxvZ3xtYWNyb3xtYWduaXR1ZGV8bWFwfG1hcC1zdHJlYW1zfG1heHxtZW1iZXJ8bWVtcXxtZW12fG1pbnxuZXdsaW5lfG5pbHxub3R8cGVlay1jaGFyfHJhdGlvbmFsaXplfHJlYWR8cmVhZC1jaGFyfHJldHVybnxyZXZlcnNlfHNlcXVlbmNlfHN1YnN0cmluZ3xzeW50YXh8c3ludGF4LXJ1bGVzfHRyYW5zY3JpcHQtb2ZmfHRyYW5zY3JpcHQtb258dHJ1bmNhdGV8dW53cmFwLXN5bnRheHx2YWx1ZXMtbGlzdHx3cml0ZXx3cml0ZS1jaGFyfGNvbnN8YyhbYWRdKXsxLDR9cnxhYnN8YWNvc3xhbmdsZXxhc2lufGFzc29jfGFzc3F8YXNzdnxhdGFufGNlaWxpbmd8Y29zfGZsb29yfHJvdW5kfHNpbnxzcXJ0fHRhbnwoPzpyZWFsfGltYWcpLXBhcnR8bnVtZXJhdG9yfGRlbm9taW5hdG9ybW9kdWxvfGV4cHxleHB0fHJlbWFpbmRlcnxxdW90aWVudHxsY218Y2FsbC13aXRoLSg/OmlucHV0fG91dHB1dCktZmlsZXxjKD86bG9zZXx1cnJlbnQpLSg/OmlucHV0fG91dHB1dCktcG9ydHx3aXRoLSg/OmlucHV0fG91dHB1dCktZnJvbS1maWxlfG9wZW4tKD86aW5wdXR8b3V0cHV0KS1maWxlfGNoYXItKD86ZG93bmNhc2V8dXBjYXNlfHJlYWR5KXxtYWtlLSg/OnBvbGFyfHByb21pc2V8cmVjdGFuZ3VsYXJ8c3RyaW5nfHZlY3RvcilzdHJpbmcoPzotKD86YXBwZW5kfGNvcHl8bGVuZ3RofHJlZikpP3x2ZWN0b3ItKD86bGVuZ3RofHJlZikpKD89KFtcXFxcXFxcXHMoXSkpXFxcIixcXFwibmFtZVxcXCI6XFxcInN1cHBvcnQuZnVuY3Rpb24uZ2VuZXJhbC5zY2hlbWVcXFwifV19LFxcXCJxdW90ZVxcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiY2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uc2VjdGlvbi5xdW90ZWQuc3ltYm9sLnNjaGVtZVxcXCJ9fSxcXFwibWF0Y2hcXFwiOlxcXCIoJylcXFxcXFxcXHMqKFxcXFxcXFxccHthbG51bX1bWzphbG51bTpdISQlXFxcXFxcXFwmKistLi86PD0+P0BeX35dKilcXFwiLFxcXCJuYW1lXFxcIjpcXFwiY29uc3RhbnQub3RoZXIuc3ltYm9sLnNjaGVtZVxcXCJ9LHtcXFwiY2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uc2VjdGlvbi5xdW90ZWQuZW1wdHktbGlzdC5zY2hlbWVcXFwifSxcXFwiMlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwibWV0YS5leHByZXNzaW9uLnNjaGVtZVxcXCJ9LFxcXCIzXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5zZWN0aW9uLmV4cHJlc3Npb24uYmVnaW4uc2NoZW1lXFxcIn0sXFxcIjRcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLnNlY3Rpb24uZXhwcmVzc2lvbi5lbmQuc2NoZW1lXFxcIn19LFxcXCJtYXRjaFxcXCI6XFxcIignKVxcXFxcXFxccyooKFxcXFxcXFxcKClcXFxcXFxcXHMqKFxcXFxcXFxcKSkpXFxcIixcXFwibmFtZVxcXCI6XFxcImNvbnN0YW50Lm90aGVyLmVtcHR5LWxpc3Quc2NoZW1cXFwifSx7XFxcImJlZ2luXFxcIjpcXFwiKCcpXFxcXFxcXFxzKlxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uc2VjdGlvbi5xdW90ZWQuc2NoZW1lXFxcIn19LFxcXCJlbmRcXFwiOlxcXCIoPz1bXFxcXFxcXFxzKCldKXwoPzw9XFxcXFxcXFxuKVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJzdHJpbmcub3RoZXIucXVvdGVkLW9iamVjdC5zY2hlbWVcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNxdW90ZWRcXFwifV19XX0sXFxcInF1b3RlLXNleHBcXFwiOntcXFwiYmVnaW5cXFwiOlxcXCIoPzw9XFxcXFxcXFwoKVxcXFxcXFxccyoocXVvdGUpXFxcXFxcXFxzK1xcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5jb250cm9sLnF1b3RlLnNjaGVtZVxcXCJ9fSxcXFwiY29udGVudE5hbWVcXFwiOlxcXCJzdHJpbmcub3RoZXIucXVvdGUuc2NoZW1lXFxcIixcXFwiZW5kXFxcIjpcXFwiKD89W1xcXFxcXFxccyldKXwoPzw9XFxcXFxcXFxuKVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3F1b3RlZFxcXCJ9XX0sXFxcInF1b3RlZFxcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNzdHJpbmdcXFwifSx7XFxcImJlZ2luXFxcIjpcXFwiKFxcXFxcXFxcKClcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLnNlY3Rpb24uZXhwcmVzc2lvbi5iZWdpbi5zY2hlbWVcXFwifX0sXFxcImVuZFxcXCI6XFxcIihcXFxcXFxcXCkpXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uc2VjdGlvbi5leHByZXNzaW9uLmVuZC5zY2hlbWVcXFwifX0sXFxcIm5hbWVcXFwiOlxcXCJtZXRhLmV4cHJlc3Npb24uc2NoZW1lXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjcXVvdGVkXFxcIn1dfSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjcXVvdGVcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjaWxsZWdhbFxcXCJ9XX0sXFxcInNleHBcXFwiOntcXFwiYmVnaW5cXFwiOlxcXCIoXFxcXFxcXFwoKVxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uc2VjdGlvbi5leHByZXNzaW9uLmJlZ2luLnNjaGVtZVxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiKFxcXFxcXFxcKSkoXFxcXFxcXFxuKT9cXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5zZWN0aW9uLmV4cHJlc3Npb24uZW5kLnNjaGVtZVxcXCJ9LFxcXCIyXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJtZXRhLmFmdGVyLWV4cHJlc3Npb24uc2NoZW1lXFxcIn19LFxcXCJuYW1lXFxcIjpcXFwibWV0YS5leHByZXNzaW9uLnNjaGVtZVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2NvbW1lbnRcXFwifSx7XFxcImJlZ2luXFxcIjpcXFwiKD88PVxcXFxcXFxcKCkoZGVmaW5lKVxcXFxcXFxccysoXFxcXFxcXFwoKShcXFxcXFxcXHB7YWxudW19W1s6YWxudW06XSEkJVxcXFxcXFxcJiorLS4vOjw9Pj9AXl9+XSopKChcXFxcXFxcXHMrKFxcXFxcXFxccHthbG51bX1bWzphbG51bTpdISQlXFxcXFxcXFwmKistLi86PD0+P0BeX35dKnxbLl9dKSkqKVxcXFxcXFxccyooXFxcXFxcXFwpKVxcXCIsXFxcImNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQuY29udHJvbC5zY2hlbWVcXFwifSxcXFwiMlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uZGVmaW5pdGlvbi5mdW5jdGlvbi5zY2hlbWVcXFwifSxcXFwiM1xcXCI6e1xcXCJuYW1lXFxcIjpcXFwiZW50aXR5Lm5hbWUuZnVuY3Rpb24uc2NoZW1lXFxcIn0sXFxcIjRcXFwiOntcXFwibmFtZVxcXCI6XFxcInZhcmlhYmxlLnBhcmFtZXRlci5mdW5jdGlvbi5zY2hlbWVcXFwifSxcXFwiN1xcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uZGVmaW5pdGlvbi5mdW5jdGlvbi5zY2hlbWVcXFwifX0sXFxcImVuZFxcXCI6XFxcIig/PVxcXFxcXFxcKSlcXFwiLFxcXCJuYW1lXFxcIjpcXFwibWV0YS5kZWNsYXJhdGlvbi5wcm9jZWR1cmUuc2NoZW1lXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjY29tbWVudFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNzZXhwXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2lsbGVnYWxcXFwifV19LHtcXFwiYmVnaW5cXFwiOlxcXCIoPzw9XFxcXFxcXFwoKShsYW1iZGEpXFxcXFxcXFxzKyhcXFxcXFxcXCgpKCg/OihcXFxcXFxcXHB7YWxudW19W1s6YWxudW06XSEkJVxcXFxcXFxcJiorLS4vOjw9Pj9AXl9+XSp8Wy5fXSlcXFxcXFxcXHMrKSooXFxcXFxcXFxwe2FsbnVtfVtbOmFsbnVtOl0hJCVcXFxcXFxcXCYqKy0uLzo8PT4/QF5ffl0qfFsuX10pPykoXFxcXFxcXFwpKVxcXCIsXFxcImNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQuY29udHJvbC5zY2hlbWVcXFwifSxcXFwiMlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uZGVmaW5pdGlvbi52YXJpYWJsZS5zY2hlbWVcXFwifSxcXFwiM1xcXCI6e1xcXCJuYW1lXFxcIjpcXFwidmFyaWFibGUucGFyYW1ldGVyLnNjaGVtZVxcXCJ9LFxcXCI2XFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLnZhcmlhYmxlLnNjaGVtZVxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiKD89XFxcXFxcXFwpKVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJtZXRhLmRlY2xhcmF0aW9uLnByb2NlZHVyZS5zY2hlbWVcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNjb21tZW50XFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3NleHBcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjaWxsZWdhbFxcXCJ9XX0se1xcXCJiZWdpblxcXCI6XFxcIig/PD1cXFxcXFxcXCgpKGRlZmluZSlcXFxcXFxcXHMoXFxcXFxcXFxwe2FsbnVtfVtbOmFsbnVtOl0hJCVcXFxcXFxcXCYqKy0uLzo8PT4/QF5ffl0qKVxcXFxcXFxccyouKj9cXFwiLFxcXCJjYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLmNvbnRyb2wuc2NoZW1lXFxcIn0sXFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcInZhcmlhYmxlLm90aGVyLnNjaGVtZVxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiKD89XFxcXFxcXFwpKVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJtZXRhLmRlY2xhcmF0aW9uLnZhcmlhYmxlLnNjaGVtZVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2NvbW1lbnRcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjc2V4cFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNpbGxlZ2FsXFxcIn1dfSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjcXVvdGUtc2V4cFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNxdW90ZVxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNsYW5ndWFnZS1mdW5jdGlvbnNcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjc3RyaW5nXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2NvbnN0YW50c1xcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCIoPzw9WyhcXFxcXFxcXHNdKSgjXFxcXFxcXFxcXFxcXFxcXCkoc3BhY2V8bmV3bGluZXx0YWIpKD89W1xcXFxcXFxccyldKVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJjb25zdGFudC5jaGFyYWN0ZXIubmFtZWQuc2NoZW1lXFxcIn0se1xcXCJtYXRjaFxcXCI6XFxcIig/PD1bKFxcXFxcXFxcc10pKCNcXFxcXFxcXFxcXFxcXFxcKXhbMC05QS1GXXsyLDR9KD89W1xcXFxcXFxccyldKVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJjb25zdGFudC5jaGFyYWN0ZXIuaGV4LWxpdGVyYWwuc2NoZW1lXFxcIn0se1xcXCJtYXRjaFxcXCI6XFxcIig/PD1bKFxcXFxcXFxcc10pKCNcXFxcXFxcXFxcXFxcXFxcKS4oPz1bXFxcXFxcXFxzKV0pXFxcIixcXFwibmFtZVxcXCI6XFxcImNvbnN0YW50LmNoYXJhY3Rlci5lc2NhcGUuc2NoZW1lXFxcIn0se1xcXCJtYXRjaFxcXCI6XFxcIig/PD1bICgpXSlcXFxcXFxcXC4oPz1bICgpXSlcXFwiLFxcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uc2VwYXJhdG9yLmNvbnMuc2NoZW1lXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3NleHBcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjaWxsZWdhbFxcXCJ9XX0sXFxcInN0cmluZ1xcXCI6e1xcXCJiZWdpblxcXCI6XFxcIihcXFxcXFxcIilcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24uc3RyaW5nLmJlZ2luLnNjaGVtZVxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiKFxcXFxcXFwiKVxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24uc3RyaW5nLmVuZC5zY2hlbWVcXFwifX0sXFxcIm5hbWVcXFwiOlxcXCJzdHJpbmcucXVvdGVkLmRvdWJsZS5zY2hlbWVcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwibWF0Y2hcXFwiOlxcXCJcXFxcXFxcXFxcXFxcXFxcLlxcXCIsXFxcIm5hbWVcXFwiOlxcXCJjb25zdGFudC5jaGFyYWN0ZXIuZXNjYXBlLnNjaGVtZVxcXCJ9XX19LFxcXCJzY29wZU5hbWVcXFwiOlxcXCJzb3VyY2Uuc2NoZW1lXFxcIn1cIikpXG5cbmV4cG9ydCBkZWZhdWx0IFtcbmxhbmdcbl1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/scheme.mjs\n"));

/***/ })

}]);