"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_codemirror_legacy-modes_mode_clojure_js"],{

/***/ "(app-pages-browser)/./node_modules/@codemirror/legacy-modes/mode/clojure.js":
/*!***************************************************************!*\
  !*** ./node_modules/@codemirror/legacy-modes/mode/clojure.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clojure: () => (/* binding */ clojure)\n/* harmony export */ });\nvar atoms = [\"false\", \"nil\", \"true\"];\nvar specialForms = [\".\", \"catch\", \"def\", \"do\", \"if\", \"monitor-enter\",\n                    \"monitor-exit\", \"new\", \"quote\", \"recur\", \"set!\", \"throw\", \"try\", \"var\"];\nvar coreSymbols = [\"*\", \"*'\", \"*1\", \"*2\", \"*3\", \"*agent*\",\n                   \"*allow-unresolved-vars*\", \"*assert*\", \"*clojure-version*\",\n                   \"*command-line-args*\", \"*compile-files*\", \"*compile-path*\",\n                   \"*compiler-options*\", \"*data-readers*\", \"*default-data-reader-fn*\", \"*e\",\n                   \"*err*\", \"*file*\", \"*flush-on-newline*\", \"*fn-loader*\", \"*in*\",\n                   \"*math-context*\", \"*ns*\", \"*out*\", \"*print-dup*\", \"*print-length*\",\n                   \"*print-level*\", \"*print-meta*\", \"*print-namespace-maps*\",\n                   \"*print-readably*\", \"*read-eval*\", \"*reader-resolver*\", \"*source-path*\",\n                   \"*suppress-read*\", \"*unchecked-math*\", \"*use-context-classloader*\",\n                   \"*verbose-defrecords*\", \"*warn-on-reflection*\", \"+\", \"+'\", \"-\", \"-'\",\n                   \"->\", \"->>\", \"->ArrayChunk\", \"->Eduction\", \"->Vec\", \"->VecNode\",\n                   \"->VecSeq\", \"-cache-protocol-fn\", \"-reset-methods\", \"..\", \"/\", \"<\", \"<=\",\n                   \"=\", \"==\", \">\", \">=\", \"EMPTY-NODE\", \"Inst\", \"StackTraceElement->vec\",\n                   \"Throwable->map\", \"accessor\", \"aclone\", \"add-classpath\", \"add-watch\",\n                   \"agent\", \"agent-error\", \"agent-errors\", \"aget\", \"alength\", \"alias\",\n                   \"all-ns\", \"alter\", \"alter-meta!\", \"alter-var-root\", \"amap\", \"ancestors\",\n                   \"and\", \"any?\", \"apply\", \"areduce\", \"array-map\", \"as->\", \"aset\",\n                   \"aset-boolean\", \"aset-byte\", \"aset-char\", \"aset-double\", \"aset-float\",\n                   \"aset-int\", \"aset-long\", \"aset-short\", \"assert\", \"assoc\", \"assoc!\",\n                   \"assoc-in\", \"associative?\", \"atom\", \"await\", \"await-for\", \"await1\",\n                   \"bases\", \"bean\", \"bigdec\", \"bigint\", \"biginteger\", \"binding\", \"bit-and\",\n                   \"bit-and-not\", \"bit-clear\", \"bit-flip\", \"bit-not\", \"bit-or\", \"bit-set\",\n                   \"bit-shift-left\", \"bit-shift-right\", \"bit-test\", \"bit-xor\", \"boolean\",\n                   \"boolean-array\", \"boolean?\", \"booleans\", \"bound-fn\", \"bound-fn*\",\n                   \"bound?\", \"bounded-count\", \"butlast\", \"byte\", \"byte-array\", \"bytes\",\n                   \"bytes?\", \"case\", \"cast\", \"cat\", \"char\", \"char-array\",\n                   \"char-escape-string\", \"char-name-string\", \"char?\", \"chars\", \"chunk\",\n                   \"chunk-append\", \"chunk-buffer\", \"chunk-cons\", \"chunk-first\", \"chunk-next\",\n                   \"chunk-rest\", \"chunked-seq?\", \"class\", \"class?\", \"clear-agent-errors\",\n                   \"clojure-version\", \"coll?\", \"comment\", \"commute\", \"comp\", \"comparator\",\n                   \"compare\", \"compare-and-set!\", \"compile\", \"complement\", \"completing\",\n                   \"concat\", \"cond\", \"cond->\", \"cond->>\", \"condp\", \"conj\", \"conj!\", \"cons\",\n                   \"constantly\", \"construct-proxy\", \"contains?\", \"count\", \"counted?\",\n                   \"create-ns\", \"create-struct\", \"cycle\", \"dec\", \"dec'\", \"decimal?\",\n                   \"declare\", \"dedupe\", \"default-data-readers\", \"definline\", \"definterface\",\n                   \"defmacro\", \"defmethod\", \"defmulti\", \"defn\", \"defn-\", \"defonce\",\n                   \"defprotocol\", \"defrecord\", \"defstruct\", \"deftype\", \"delay\", \"delay?\",\n                   \"deliver\", \"denominator\", \"deref\", \"derive\", \"descendants\", \"destructure\",\n                   \"disj\", \"disj!\", \"dissoc\", \"dissoc!\", \"distinct\", \"distinct?\", \"doall\",\n                   \"dorun\", \"doseq\", \"dosync\", \"dotimes\", \"doto\", \"double\", \"double-array\",\n                   \"double?\", \"doubles\", \"drop\", \"drop-last\", \"drop-while\", \"eduction\",\n                   \"empty\", \"empty?\", \"ensure\", \"ensure-reduced\", \"enumeration-seq\",\n                   \"error-handler\", \"error-mode\", \"eval\", \"even?\", \"every-pred\", \"every?\",\n                   \"ex-data\", \"ex-info\", \"extend\", \"extend-protocol\", \"extend-type\",\n                   \"extenders\", \"extends?\", \"false?\", \"ffirst\", \"file-seq\", \"filter\",\n                   \"filterv\", \"find\", \"find-keyword\", \"find-ns\", \"find-protocol-impl\",\n                   \"find-protocol-method\", \"find-var\", \"first\", \"flatten\", \"float\",\n                   \"float-array\", \"float?\", \"floats\", \"flush\", \"fn\", \"fn?\", \"fnext\", \"fnil\",\n                   \"for\", \"force\", \"format\", \"frequencies\", \"future\", \"future-call\",\n                   \"future-cancel\", \"future-cancelled?\", \"future-done?\", \"future?\",\n                   \"gen-class\", \"gen-interface\", \"gensym\", \"get\", \"get-in\", \"get-method\",\n                   \"get-proxy-class\", \"get-thread-bindings\", \"get-validator\", \"group-by\",\n                   \"halt-when\", \"hash\", \"hash-combine\", \"hash-map\", \"hash-ordered-coll\",\n                   \"hash-set\", \"hash-unordered-coll\", \"ident?\", \"identical?\", \"identity\",\n                   \"if-let\", \"if-not\", \"if-some\", \"ifn?\", \"import\", \"in-ns\", \"inc\", \"inc'\",\n                   \"indexed?\", \"init-proxy\", \"inst-ms\", \"inst-ms*\", \"inst?\", \"instance?\",\n                   \"int\", \"int-array\", \"int?\", \"integer?\", \"interleave\", \"intern\",\n                   \"interpose\", \"into\", \"into-array\", \"ints\", \"io!\", \"isa?\", \"iterate\",\n                   \"iterator-seq\", \"juxt\", \"keep\", \"keep-indexed\", \"key\", \"keys\", \"keyword\",\n                   \"keyword?\", \"last\", \"lazy-cat\", \"lazy-seq\", \"let\", \"letfn\", \"line-seq\",\n                   \"list\", \"list*\", \"list?\", \"load\", \"load-file\", \"load-reader\",\n                   \"load-string\", \"loaded-libs\", \"locking\", \"long\", \"long-array\", \"longs\",\n                   \"loop\", \"macroexpand\", \"macroexpand-1\", \"make-array\", \"make-hierarchy\",\n                   \"map\", \"map-entry?\", \"map-indexed\", \"map?\", \"mapcat\", \"mapv\", \"max\",\n                   \"max-key\", \"memfn\", \"memoize\", \"merge\", \"merge-with\", \"meta\",\n                   \"method-sig\", \"methods\", \"min\", \"min-key\", \"mix-collection-hash\", \"mod\",\n                   \"munge\", \"name\", \"namespace\", \"namespace-munge\", \"nat-int?\", \"neg-int?\",\n                   \"neg?\", \"newline\", \"next\", \"nfirst\", \"nil?\", \"nnext\", \"not\", \"not-any?\",\n                   \"not-empty\", \"not-every?\", \"not=\", \"ns\", \"ns-aliases\", \"ns-imports\",\n                   \"ns-interns\", \"ns-map\", \"ns-name\", \"ns-publics\", \"ns-refers\",\n                   \"ns-resolve\", \"ns-unalias\", \"ns-unmap\", \"nth\", \"nthnext\", \"nthrest\",\n                   \"num\", \"number?\", \"numerator\", \"object-array\", \"odd?\", \"or\", \"parents\",\n                   \"partial\", \"partition\", \"partition-all\", \"partition-by\", \"pcalls\", \"peek\",\n                   \"persistent!\", \"pmap\", \"pop\", \"pop!\", \"pop-thread-bindings\", \"pos-int?\",\n                   \"pos?\", \"pr\", \"pr-str\", \"prefer-method\", \"prefers\",\n                   \"primitives-classnames\", \"print\", \"print-ctor\", \"print-dup\",\n                   \"print-method\", \"print-simple\", \"print-str\", \"printf\", \"println\",\n                   \"println-str\", \"prn\", \"prn-str\", \"promise\", \"proxy\",\n                   \"proxy-call-with-super\", \"proxy-mappings\", \"proxy-name\", \"proxy-super\",\n                   \"push-thread-bindings\", \"pvalues\", \"qualified-ident?\",\n                   \"qualified-keyword?\", \"qualified-symbol?\", \"quot\", \"rand\", \"rand-int\",\n                   \"rand-nth\", \"random-sample\", \"range\", \"ratio?\", \"rational?\",\n                   \"rationalize\", \"re-find\", \"re-groups\", \"re-matcher\", \"re-matches\",\n                   \"re-pattern\", \"re-seq\", \"read\", \"read-line\", \"read-string\",\n                   \"reader-conditional\", \"reader-conditional?\", \"realized?\", \"record?\",\n                   \"reduce\", \"reduce-kv\", \"reduced\", \"reduced?\", \"reductions\", \"ref\",\n                   \"ref-history-count\", \"ref-max-history\", \"ref-min-history\", \"ref-set\",\n                   \"refer\", \"refer-clojure\", \"reify\", \"release-pending-sends\", \"rem\",\n                   \"remove\", \"remove-all-methods\", \"remove-method\", \"remove-ns\",\n                   \"remove-watch\", \"repeat\", \"repeatedly\", \"replace\", \"replicate\", \"require\",\n                   \"reset!\", \"reset-meta!\", \"reset-vals!\", \"resolve\", \"rest\",\n                   \"restart-agent\", \"resultset-seq\", \"reverse\", \"reversible?\", \"rseq\",\n                   \"rsubseq\", \"run!\", \"satisfies?\", \"second\", \"select-keys\", \"send\",\n                   \"send-off\", \"send-via\", \"seq\", \"seq?\", \"seqable?\", \"seque\", \"sequence\",\n                   \"sequential?\", \"set\", \"set-agent-send-executor!\",\n                   \"set-agent-send-off-executor!\", \"set-error-handler!\", \"set-error-mode!\",\n                   \"set-validator!\", \"set?\", \"short\", \"short-array\", \"shorts\", \"shuffle\",\n                   \"shutdown-agents\", \"simple-ident?\", \"simple-keyword?\", \"simple-symbol?\",\n                   \"slurp\", \"some\", \"some->\", \"some->>\", \"some-fn\", \"some?\", \"sort\",\n                   \"sort-by\", \"sorted-map\", \"sorted-map-by\", \"sorted-set\", \"sorted-set-by\",\n                   \"sorted?\", \"special-symbol?\", \"spit\", \"split-at\", \"split-with\", \"str\",\n                   \"string?\", \"struct\", \"struct-map\", \"subs\", \"subseq\", \"subvec\", \"supers\",\n                   \"swap!\", \"swap-vals!\", \"symbol\", \"symbol?\", \"sync\", \"tagged-literal\",\n                   \"tagged-literal?\", \"take\", \"take-last\", \"take-nth\", \"take-while\", \"test\",\n                   \"the-ns\", \"thread-bound?\", \"time\", \"to-array\", \"to-array-2d\",\n                   \"trampoline\", \"transduce\", \"transient\", \"tree-seq\", \"true?\", \"type\",\n                   \"unchecked-add\", \"unchecked-add-int\", \"unchecked-byte\", \"unchecked-char\",\n                   \"unchecked-dec\", \"unchecked-dec-int\", \"unchecked-divide-int\",\n                   \"unchecked-double\", \"unchecked-float\", \"unchecked-inc\",\n                   \"unchecked-inc-int\", \"unchecked-int\", \"unchecked-long\",\n                   \"unchecked-multiply\", \"unchecked-multiply-int\", \"unchecked-negate\",\n                   \"unchecked-negate-int\", \"unchecked-remainder-int\", \"unchecked-short\",\n                   \"unchecked-subtract\", \"unchecked-subtract-int\", \"underive\", \"unquote\",\n                   \"unquote-splicing\", \"unreduced\", \"unsigned-bit-shift-right\", \"update\",\n                   \"update-in\", \"update-proxy\", \"uri?\", \"use\", \"uuid?\", \"val\", \"vals\",\n                   \"var-get\", \"var-set\", \"var?\", \"vary-meta\", \"vec\", \"vector\", \"vector-of\",\n                   \"vector?\", \"volatile!\", \"volatile?\", \"vreset!\", \"vswap!\", \"when\",\n                   \"when-first\", \"when-let\", \"when-not\", \"when-some\", \"while\",\n                   \"with-bindings\", \"with-bindings*\", \"with-in-str\", \"with-loading-context\",\n                   \"with-local-vars\", \"with-meta\", \"with-open\", \"with-out-str\",\n                   \"with-precision\", \"with-redefs\", \"with-redefs-fn\", \"xml-seq\", \"zero?\",\n                   \"zipmap\"];\nvar haveBodyParameter = [\n  \"->\", \"->>\", \"as->\", \"binding\", \"bound-fn\", \"case\", \"catch\", \"comment\",\n  \"cond\", \"cond->\", \"cond->>\", \"condp\", \"def\", \"definterface\", \"defmethod\",\n  \"defn\", \"defmacro\", \"defprotocol\", \"defrecord\", \"defstruct\", \"deftype\",\n  \"do\", \"doseq\", \"dotimes\", \"doto\", \"extend\", \"extend-protocol\",\n  \"extend-type\", \"fn\", \"for\", \"future\", \"if\", \"if-let\", \"if-not\", \"if-some\",\n  \"let\", \"letfn\", \"locking\", \"loop\", \"ns\", \"proxy\", \"reify\", \"struct-map\",\n  \"some->\", \"some->>\", \"try\", \"when\", \"when-first\", \"when-let\", \"when-not\",\n  \"when-some\", \"while\", \"with-bindings\", \"with-bindings*\", \"with-in-str\",\n  \"with-loading-context\", \"with-local-vars\", \"with-meta\", \"with-open\",\n  \"with-out-str\", \"with-precision\", \"with-redefs\", \"with-redefs-fn\"];\n\nvar atom = createLookupMap(atoms);\nvar specialForm = createLookupMap(specialForms);\nvar coreSymbol = createLookupMap(coreSymbols);\nvar hasBodyParameter = createLookupMap(haveBodyParameter);\nvar delimiter = /^(?:[\\\\\\[\\]\\s\"(),;@^`{}~]|$)/;\nvar numberLiteral = /^(?:[+\\-]?\\d+(?:(?:N|(?:[eE][+\\-]?\\d+))|(?:\\.?\\d*(?:M|(?:[eE][+\\-]?\\d+))?)|\\/\\d+|[xX][0-9a-fA-F]+|r[0-9a-zA-Z]+)?(?=[\\\\\\[\\]\\s\"#'(),;@^`{}~]|$))/;\nvar characterLiteral = /^(?:\\\\(?:backspace|formfeed|newline|return|space|tab|o[0-7]{3}|u[0-9A-Fa-f]{4}|x[0-9A-Fa-f]{4}|.)?(?=[\\\\\\[\\]\\s\"(),;@^`{}~]|$))/;\n\n// simple-namespace := /^[^\\\\\\/\\[\\]\\d\\s\"#'(),;@^`{}~.][^\\\\\\[\\]\\s\"(),;@^`{}~.\\/]*/\n// simple-symbol    := /^(?:\\/|[^\\\\\\/\\[\\]\\d\\s\"#'(),;@^`{}~][^\\\\\\[\\]\\s\"(),;@^`{}~]*)/\n// qualified-symbol := (<simple-namespace>(<.><simple-namespace>)*</>)?<simple-symbol>\nvar qualifiedSymbol = /^(?:(?:[^\\\\\\/\\[\\]\\d\\s\"#'(),;@^`{}~.][^\\\\\\[\\]\\s\"(),;@^`{}~.\\/]*(?:\\.[^\\\\\\/\\[\\]\\d\\s\"#'(),;@^`{}~.][^\\\\\\[\\]\\s\"(),;@^`{}~.\\/]*)*\\/)?(?:\\/|[^\\\\\\/\\[\\]\\d\\s\"#'(),;@^`{}~][^\\\\\\[\\]\\s\"(),;@^`{}~]*)*(?=[\\\\\\[\\]\\s\"(),;@^`{}~]|$))/;\n\nfunction base(stream, state) {\n  if (stream.eatSpace() || stream.eat(\",\")) return [\"space\", null];\n  if (stream.match(numberLiteral)) return [null, \"number\"];\n  if (stream.match(characterLiteral)) return [null, \"string.special\"];\n  if (stream.eat(/^\"/)) return (state.tokenize = inString)(stream, state);\n  if (stream.eat(/^[(\\[{]/)) return [\"open\", \"bracket\"];\n  if (stream.eat(/^[)\\]}]/)) return [\"close\", \"bracket\"];\n  if (stream.eat(/^;/)) {stream.skipToEnd(); return [\"space\", \"comment\"];}\n  if (stream.eat(/^[#'@^`~]/)) return [null, \"meta\"];\n\n  var matches = stream.match(qualifiedSymbol);\n  var symbol = matches && matches[0];\n\n  if (!symbol) {\n    // advance stream by at least one character so we don't get stuck.\n    stream.next();\n    stream.eatWhile(function (c) {return !is(c, delimiter);});\n    return [null, \"error\"];\n  }\n\n  if (symbol === \"comment\" && state.lastToken === \"(\")\n    return (state.tokenize = inComment)(stream, state);\n  if (is(symbol, atom) || symbol.charAt(0) === \":\") return [\"symbol\", \"atom\"];\n  if (is(symbol, specialForm) || is(symbol, coreSymbol)) return [\"symbol\", \"keyword\"];\n  if (state.lastToken === \"(\") return [\"symbol\", \"builtin\"]; // other operator\n\n  return [\"symbol\", \"variable\"];\n}\n\nfunction inString(stream, state) {\n  var escaped = false, next;\n\n  while (next = stream.next()) {\n    if (next === \"\\\"\" && !escaped) {state.tokenize = base; break;}\n    escaped = !escaped && next === \"\\\\\";\n  }\n\n  return [null, \"string\"];\n}\n\nfunction inComment(stream, state) {\n  var parenthesisCount = 1;\n  var next;\n\n  while (next = stream.next()) {\n    if (next === \")\") parenthesisCount--;\n    if (next === \"(\") parenthesisCount++;\n    if (parenthesisCount === 0) {\n      stream.backUp(1);\n      state.tokenize = base;\n      break;\n    }\n  }\n\n  return [\"space\", \"comment\"];\n}\n\nfunction createLookupMap(words) {\n  var obj = {};\n\n  for (var i = 0; i < words.length; ++i) obj[words[i]] = true;\n\n  return obj;\n}\n\nfunction is(value, test) {\n  if (test instanceof RegExp) return test.test(value);\n  if (test instanceof Object) return test.propertyIsEnumerable(value);\n}\n\nconst clojure = {\n  name: \"clojure\",\n  startState: function () {\n    return {\n      ctx: {prev: null, start: 0, indentTo: 0},\n      lastToken: null,\n      tokenize: base\n    };\n  },\n\n  token: function (stream, state) {\n    if (stream.sol() && (typeof state.ctx.indentTo !== \"number\"))\n      state.ctx.indentTo = state.ctx.start + 1;\n\n    var typeStylePair = state.tokenize(stream, state);\n    var type = typeStylePair[0];\n    var style = typeStylePair[1];\n    var current = stream.current();\n\n    if (type !== \"space\") {\n      if (state.lastToken === \"(\" && state.ctx.indentTo === null) {\n        if (type === \"symbol\" && is(current, hasBodyParameter))\n          state.ctx.indentTo = state.ctx.start + stream.indentUnit;\n        else state.ctx.indentTo = \"next\";\n      } else if (state.ctx.indentTo === \"next\") {\n        state.ctx.indentTo = stream.column();\n      }\n\n      state.lastToken = current;\n    }\n\n    if (type === \"open\")\n      state.ctx = {prev: state.ctx, start: stream.column(), indentTo: null};\n    else if (type === \"close\") state.ctx = state.ctx.prev || state.ctx;\n\n    return style;\n  },\n\n  indent: function (state) {\n    var i = state.ctx.indentTo;\n\n    return (typeof i === \"number\") ?\n      i :\n      state.ctx.start + 1;\n  },\n\n  languageData: {\n    closeBrackets: {brackets: [\"(\", \"[\", \"{\", '\"']},\n    commentTokens: {line: \";;\"},\n    autocomplete: [].concat(atoms, specialForms, coreSymbols)\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@codemirror/legacy-modes/mode/clojure.js\n"));

/***/ })

}]);