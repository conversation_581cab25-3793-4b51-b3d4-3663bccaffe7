"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_fortran-free-form_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/fortran-free-form.mjs":
/*!****************************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/fortran-free-form.mjs ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Fortran (Free Form)\\\",\\\"fileTypes\\\":[\\\"f90\\\",\\\"F90\\\",\\\"f95\\\",\\\"F95\\\",\\\"f03\\\",\\\"F03\\\",\\\"f08\\\",\\\"F08\\\",\\\"f18\\\",\\\"F18\\\",\\\"fpp\\\",\\\"FPP\\\",\\\".pf\\\",\\\".PF\\\"],\\\"firstLineMatch\\\":\\\"(?i)-\\\\\\\\*- mode: fortran free -\\\\\\\\*-\\\",\\\"injections\\\":{\\\"source.fortran.free - ( string | comment | meta.preprocessor )\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#line-continuation-operator\\\"},{\\\"include\\\":\\\"#preprocessor\\\"}]},\\\"string.quoted.double.fortran\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#string-line-continuation-operator\\\"}]},\\\"string.quoted.single.fortran\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#string-line-continuation-operator\\\"}]}},\\\"name\\\":\\\"fortran-free-form\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"include\\\":\\\"#array-constructor\\\"},{\\\"include\\\":\\\"#parentheses\\\"},{\\\"include\\\":\\\"#include-statement\\\"},{\\\"include\\\":\\\"#import-statement\\\"},{\\\"include\\\":\\\"#block-data-definition\\\"},{\\\"include\\\":\\\"#function-definition\\\"},{\\\"include\\\":\\\"#module-definition\\\"},{\\\"include\\\":\\\"#program-definition\\\"},{\\\"include\\\":\\\"#submodule-definition\\\"},{\\\"include\\\":\\\"#subroutine-definition\\\"},{\\\"include\\\":\\\"#procedure-definition\\\"},{\\\"include\\\":\\\"#derived-type-definition\\\"},{\\\"include\\\":\\\"#enum-block-construct\\\"},{\\\"include\\\":\\\"#interface-block-constructs\\\"},{\\\"include\\\":\\\"#procedure-specification-statement\\\"},{\\\"include\\\":\\\"#type-specification-statements\\\"},{\\\"include\\\":\\\"#specification-statements\\\"},{\\\"include\\\":\\\"#control-constructs\\\"},{\\\"include\\\":\\\"#control-statements\\\"},{\\\"include\\\":\\\"#execution-statements\\\"},{\\\"include\\\":\\\"#intrinsic-functions\\\"},{\\\"include\\\":\\\"#variable\\\"}],\\\"repository\\\":{\\\"IO-item-list\\\":{\\\"begin\\\":\\\"(?i)(?=\\\\\\\\s*[a-z0-9\\\\\\\"'])\\\",\\\"contentName\\\":\\\"meta.name-list.fortran\\\",\\\"end\\\":\\\"(?=[);!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"include\\\":\\\"#intrinsic-functions\\\"},{\\\"include\\\":\\\"#array-constructor\\\"},{\\\"include\\\":\\\"#parentheses\\\"},{\\\"include\\\":\\\"#brackets\\\"},{\\\"include\\\":\\\"#assignment-keyword\\\"},{\\\"include\\\":\\\"#operator-keyword\\\"},{\\\"include\\\":\\\"#variable\\\"}]},\\\"IO-keywords\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(?:(read)|(write))\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.generic-spec.read.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.generic-spec.write.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.generic-spec.formatted.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.generic-spec.unformatted.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(?:(formatted)|(unformatted))\\\\\\\\b\\\"},{\\\"include\\\":\\\"#invalid-word\\\"}]},\\\"IO-statements\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\b(?:(backspace)|(close)|(endfile)|(format)|(inquire)|(open)|(read)|(rewind)|(write))\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.backspace.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.close.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.control.endfile.fortran\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.control.format.fortran\\\"},\\\"5\\\":{\\\"name\\\":\\\"keyword.control.inquire.fortran\\\"},\\\"6\\\":{\\\"name\\\":\\\"keyword.control.open.fortran\\\"},\\\"7\\\":{\\\"name\\\":\\\"keyword.control.read.fortran\\\"},\\\"8\\\":{\\\"name\\\":\\\"keyword.control.rewind.fortran\\\"},\\\"9\\\":{\\\"name\\\":\\\"keyword.control.write.fortran\\\"},\\\"10\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"name\\\":\\\"meta.statement.IO.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"},{\\\"include\\\":\\\"#IO-item-list\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.backspace.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.endfile.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.control.format.fortran\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.control.print.fortran\\\"},\\\"5\\\":{\\\"name\\\":\\\"keyword.control.read.fortran\\\"},\\\"6\\\":{\\\"name\\\":\\\"keyword.control.rewind.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(?:(backspace)|(endfile)|(format)|(print)|(read)|(rewind))\\\\\\\\b\\\"},{\\\"begin\\\":\\\"(?i)\\\\\\\\b(?:(flush)|(wait))\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.flush.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.wait.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.flush.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(flush)\\\\\\\\b\\\"}]},\\\"abstract-attribute\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.fortran.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(abstract)\\\\\\\\b\\\"},\\\"abstract-interface-block-construct\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(abstract)\\\\\\\\s+(interface)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.attribute.fortran.modern\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.interface.fortran\\\"}},\\\"end\\\":\\\"(?i)\\\\\\\\b(end\\\\\\\\s*interface)\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.endinterface.fortran.modern\\\"}},\\\"name\\\":\\\"meta.interface.abstract.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$base\\\"}]},\\\"access-attribute\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#private-attribute\\\"},{\\\"include\\\":\\\"#public-attribute\\\"}]},\\\"allocatable-attribute\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.allocatable.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(allocatable)\\\\\\\\b\\\"},\\\"allocate-statement\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(allocate)\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.allocate.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"name\\\":\\\"meta.statement.allocate.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]},\\\"arithmetic-operators\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.subtraction.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.addition.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.operator.division.fortran\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.operator.power.fortran\\\"},\\\"5\\\":{\\\"name\\\":\\\"keyword.operator.multiplication.fortran\\\"}},\\\"match\\\":\\\"(-)|(\\\\\\\\+)|/(?![/=\\\\\\\\\\\\\\\\])|(\\\\\\\\*\\\\\\\\*)|(\\\\\\\\*)\\\"},\\\"array-constructor\\\":{\\\"begin\\\":\\\"(?=\\\\\\\\s*(\\\\\\\\[|\\\\\\\\(/))\\\",\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"name\\\":\\\"meta.contructor.array\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#brackets\\\"},{\\\"begin\\\":\\\"\\\\\\\\s*(\\\\\\\\(/)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.bracket.left.fortran\\\"}},\\\"end\\\":\\\"(/\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.bracket.left.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"include\\\":\\\"#array-constructor\\\"},{\\\"include\\\":\\\"#parentheses\\\"},{\\\"include\\\":\\\"#intrinsic-functions\\\"},{\\\"include\\\":\\\"#variable\\\"}]}]},\\\"assign-statement\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\b(assign)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.assign.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.statement.control.assign.fortran\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.to.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(to)\\\\\\\\b\\\"},{\\\"include\\\":\\\"$base\\\"}]}]},\\\"assignment-keyword\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(assignment)\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.generic-spec.assignment.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#assignment-operator\\\"},{\\\"include\\\":\\\"#invalid-word\\\"}]},\\\"assignment-operator\\\":{\\\"match\\\":\\\"(?<![/=<>])(=)(?![=>])\\\",\\\"name\\\":\\\"keyword.operator.assignment.fortran\\\"},\\\"associate-construct\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(associate)\\\\\\\\b(?=\\\\\\\\s*\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.associate.fortran\\\"}},\\\"contentName\\\":\\\"meta.block.associate.fortran\\\",\\\"end\\\":\\\"(?i)\\\\\\\\b(end\\\\\\\\s*associate)\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.endassociate.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"$base\\\"}]},\\\"asynchronous-attribute\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.asynchronous.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(asynchronous)\\\\\\\\b\\\"},\\\"attribute-specification-statement\\\":{\\\"begin\\\":\\\"(?i)(?=\\\\\\\\b(?:allocatable|asynchronous|contiguous|external|intrinsic|optional|parameter|pointer|private|protected|public|save|target|value|volatile)\\\\\\\\b|(bind|dimension|intent)\\\\\\\\s*\\\\\\\\(|(codimension)\\\\\\\\s*\\\\\\\\[)\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.statement.attribute-specification.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#access-attribute\\\"},{\\\"include\\\":\\\"#allocatable-attribute\\\"},{\\\"include\\\":\\\"#asynchronous-attribute\\\"},{\\\"include\\\":\\\"#codimension-attribute\\\"},{\\\"include\\\":\\\"#contiguous-attribute\\\"},{\\\"include\\\":\\\"#dimension-attribute\\\"},{\\\"include\\\":\\\"#external-attribute\\\"},{\\\"include\\\":\\\"#intent-attribute\\\"},{\\\"include\\\":\\\"#intrinsic-attribute\\\"},{\\\"include\\\":\\\"#language-binding-attribute\\\"},{\\\"include\\\":\\\"#optional-attribute\\\"},{\\\"include\\\":\\\"#parameter-attribute\\\"},{\\\"include\\\":\\\"#pointer-attribute\\\"},{\\\"include\\\":\\\"#protected-attribute\\\"},{\\\"include\\\":\\\"#save-attribute\\\"},{\\\"include\\\":\\\"#target-attribute\\\"},{\\\"include\\\":\\\"#value-attribute\\\"},{\\\"include\\\":\\\"#volatile-attribute\\\"},{\\\"begin\\\":\\\"(?=\\\\\\\\s*::)\\\",\\\"contentName\\\":\\\"meta.attribute-list.normal.fortran\\\",\\\"end\\\":\\\"(::)|(?=[;!\\\\\\\\n])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.double-colon.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#invalid-word\\\"}]},{\\\"include\\\":\\\"#name-list\\\"}]},\\\"block-construct\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(block)\\\\\\\\b(?!\\\\\\\\s*\\\\\\\\bdata\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.associate.fortran\\\"}},\\\"contentName\\\":\\\"meta.block.block.fortran\\\",\\\"end\\\":\\\"(?i)\\\\\\\\b(end\\\\\\\\s*block)\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.endassociate.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"$base\\\"}]},\\\"block-data-definition\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(block\\\\\\\\s*data)\\\\\\\\b(?:\\\\\\\\s+([a-z]\\\\\\\\w*)\\\\\\\\b)?\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.block-data.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.block-data.fortran\\\"}},\\\"end\\\":\\\"(?i)\\\\\\\\b(?:(end\\\\\\\\s*block\\\\\\\\s*data)(?:\\\\\\\\s+(\\\\\\\\2))?|(end))\\\\\\\\b(?:\\\\\\\\s*(\\\\\\\\S((?!\\\\\\\\n).)*))?\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.end-block-data.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.block-data.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.control.end-block-data.fortran\\\"},\\\"4\\\":{\\\"name\\\":\\\"invalid.error.block-data-definition.fortran\\\"}},\\\"name\\\":\\\"meta.block-data.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$base\\\"}]},\\\"brackets\\\":{\\\"begin\\\":\\\"\\\\\\\\s*(\\\\\\\\[)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.bracket.left.fortran\\\"}},\\\"end\\\":\\\"(])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.bracket.left.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"include\\\":\\\"#array-constructor\\\"},{\\\"include\\\":\\\"#parentheses\\\"},{\\\"include\\\":\\\"#intrinsic-functions\\\"},{\\\"include\\\":\\\"#variable\\\"}]},\\\"call-statement\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(call)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.call.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.statement.control.call.fortran\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*([a-z]\\\\\\\\w*)(%)([a-z]\\\\\\\\w*)\\\\\\\\b\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.accessor.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.function.subroutine.fortran\\\"}},\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]},{\\\"include\\\":\\\"#intrinsic-subroutines\\\"},{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b([a-z]\\\\\\\\w*)\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.subroutine.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.subroutine.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b([a-z]\\\\\\\\w*)\\\\\\\\b(?=\\\\\\\\s*[;!\\\\\\\\n])\\\"},{\\\"include\\\":\\\"$base\\\"}]}]},\\\"character-type\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\b(character)\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.character.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"contentName\\\":\\\"meta.type-spec.fortran\\\",\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.character.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.multiplication.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.numeric.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(character)\\\\\\\\b(?:\\\\\\\\s*(\\\\\\\\*)\\\\\\\\s*(\\\\\\\\d*))?\\\"}]},\\\"codimension-attribute\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(codimension)(?=\\\\\\\\s*\\\\\\\\[)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.codimension.fortran\\\"}},\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#brackets\\\"}]},\\\"comments\\\":{\\\"begin\\\":\\\"!\\\",\\\"end\\\":\\\"(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"comment.line.fortran\\\"},\\\"common-statement\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(common)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.common.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$base\\\"}]},\\\"concurrent-attribute\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(concurrent)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.while.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses\\\"},{\\\"include\\\":\\\"#invalid-word\\\"}]},\\\"constants\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#logical-constant\\\"},{\\\"include\\\":\\\"#numeric-constant\\\"},{\\\"include\\\":\\\"#string-constant\\\"}]},\\\"contiguous-attribute\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.contigous.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(contiguous)\\\\\\\\b\\\"},\\\"continue-statement\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(continue)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.continue.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.statement.control.continue.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#invalid-character\\\"}]}]},\\\"control-constructs\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#named-control-constructs\\\"},{\\\"include\\\":\\\"#unnamed-control-constructs\\\"}]},\\\"control-statements\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#assign-statement\\\"},{\\\"include\\\":\\\"#call-statement\\\"},{\\\"include\\\":\\\"#continue-statement\\\"},{\\\"include\\\":\\\"#cycle-statement\\\"},{\\\"include\\\":\\\"#entry-statement\\\"},{\\\"include\\\":\\\"#error-stop-statement\\\"},{\\\"include\\\":\\\"#exit-statement\\\"},{\\\"include\\\":\\\"#goto-statement\\\"},{\\\"include\\\":\\\"#pause-statement\\\"},{\\\"include\\\":\\\"#return-statement\\\"},{\\\"include\\\":\\\"#stop-statement\\\"},{\\\"include\\\":\\\"#where-statement\\\"},{\\\"include\\\":\\\"#image-control-statement\\\"}]},\\\"cpp-numeric-constant\\\":{\\\"captures\\\":{\\\"0\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?=.)\\\",\\\"beginCaptures\\\":{},\\\"end\\\":\\\"$\\\",\\\"endCaptures\\\":{},\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.unit.hexadecimal.cpp\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.numeric.hexadecimal.cpp\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=\\\\\\\\h)'(?=\\\\\\\\h)\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.cpp\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric.cpp\\\"},\\\"4\\\":{\\\"name\\\":\\\"constant.numeric.hexadecimal.cpp\\\"},\\\"5\\\":{\\\"name\\\":\\\"constant.numeric.hexadecimal.cpp\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=\\\\\\\\h)'(?=\\\\\\\\h)\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.cpp\\\"}]},\\\"6\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric.cpp\\\"},\\\"7\\\":{\\\"name\\\":\\\"keyword.other.unit.exponent.hexadecimal.cpp\\\"},\\\"8\\\":{\\\"name\\\":\\\"keyword.operator.plus.exponent.hexadecimal.cpp\\\"},\\\"9\\\":{\\\"name\\\":\\\"keyword.operator.minus.exponent.hexadecimal.cpp\\\"},\\\"10\\\":{\\\"name\\\":\\\"constant.numeric.exponent.hexadecimal.cpp\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=\\\\\\\\h)'(?=\\\\\\\\h)\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.cpp\\\"}]},\\\"11\\\":{\\\"name\\\":\\\"keyword.other.unit.suffix.floating-point.cpp\\\"},\\\"12\\\":{\\\"name\\\":\\\"keyword.other.unit.user-defined.cpp\\\"}},\\\"match\\\":\\\"(\\\\\\\\G0[xX])(\\\\\\\\h(?:\\\\\\\\h|((?<=\\\\\\\\h)'(?=\\\\\\\\h)))*)?((?:(?<=\\\\\\\\h)\\\\\\\\.|\\\\\\\\.(?=\\\\\\\\h)))(\\\\\\\\h(?:\\\\\\\\h|((?<=\\\\\\\\h)'(?=\\\\\\\\h)))*)?(?:(?<!')([pP])(\\\\\\\\+?)(-?)([0-9](?:[0-9]|(?<=\\\\\\\\h)'(?=\\\\\\\\h))*))?([lLfF](?!\\\\\\\\w))?((?:\\\\\\\\w(?<![pP\\\\\\\\h])\\\\\\\\w*)?$)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.numeric.decimal.cpp\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=\\\\\\\\h)'(?=\\\\\\\\h)\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.cpp\\\"}]},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric.cpp\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.numeric.decimal.point.cpp\\\"},\\\"4\\\":{\\\"name\\\":\\\"constant.numeric.decimal.cpp\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=\\\\\\\\h)'(?=\\\\\\\\h)\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.cpp\\\"}]},\\\"5\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric.cpp\\\"},\\\"6\\\":{\\\"name\\\":\\\"keyword.other.unit.exponent.decimal.cpp\\\"},\\\"7\\\":{\\\"name\\\":\\\"keyword.operator.plus.exponent.decimal.cpp\\\"},\\\"8\\\":{\\\"name\\\":\\\"keyword.operator.minus.exponent.decimal.cpp\\\"},\\\"9\\\":{\\\"name\\\":\\\"constant.numeric.exponent.decimal.cpp\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=\\\\\\\\h)'(?=\\\\\\\\h)\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.cpp\\\"}]},\\\"10\\\":{\\\"name\\\":\\\"keyword.other.unit.suffix.floating-point.cpp\\\"},\\\"11\\\":{\\\"name\\\":\\\"keyword.other.unit.user-defined.cpp\\\"}},\\\"match\\\":\\\"\\\\\\\\G(?=[0-9.])(?!0[xXbB])([0-9](?:[0-9]|((?<=\\\\\\\\h)'(?=\\\\\\\\h)))*)?((?:(?<=[0-9])\\\\\\\\.|\\\\\\\\.(?=[0-9])))([0-9](?:[0-9]|((?<=\\\\\\\\h)'(?=\\\\\\\\h)))*)?(?:(?<!')([eE])(\\\\\\\\+?)(-?)([0-9](?:[0-9]|(?<=\\\\\\\\h)'(?=\\\\\\\\h))*))?([lLfF](?!\\\\\\\\w))?((?:\\\\\\\\w(?<![0-9eE])\\\\\\\\w*)?$)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.unit.binary.cpp\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.numeric.binary.cpp\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=\\\\\\\\h)'(?=\\\\\\\\h)\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.cpp\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric.cpp\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.other.unit.suffix.integer.cpp\\\"},\\\"5\\\":{\\\"name\\\":\\\"keyword.other.unit.user-defined.cpp\\\"}},\\\"match\\\":\\\"(\\\\\\\\G0[bB])([01](?:[01]|((?<=\\\\\\\\h)'(?=\\\\\\\\h)))*)((?:[uU]|[uU]ll?|[uU]LL?|ll?[uU]?|LL?[uU]?|[fF])(?!\\\\\\\\w))?((?:\\\\\\\\w(?<![0-9])\\\\\\\\w*)?$)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.unit.octal.cpp\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.numeric.octal.cpp\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=\\\\\\\\h)'(?=\\\\\\\\h)\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.cpp\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric.cpp\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.other.unit.suffix.integer.cpp\\\"},\\\"5\\\":{\\\"name\\\":\\\"keyword.other.unit.user-defined.cpp\\\"}},\\\"match\\\":\\\"(\\\\\\\\G0)((?:[0-7]|((?<=\\\\\\\\h)'(?=\\\\\\\\h)))+)((?:[uU]|[uU]ll?|[uU]LL?|ll?[uU]?|LL?[uU]?|[fF])(?!\\\\\\\\w))?((?:\\\\\\\\w(?<![0-9])\\\\\\\\w*)?$)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.unit.hexadecimal.cpp\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.numeric.hexadecimal.cpp\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=\\\\\\\\h)'(?=\\\\\\\\h)\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.cpp\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric.cpp\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.other.unit.exponent.hexadecimal.cpp\\\"},\\\"5\\\":{\\\"name\\\":\\\"keyword.operator.plus.exponent.hexadecimal.cpp\\\"},\\\"6\\\":{\\\"name\\\":\\\"keyword.operator.minus.exponent.hexadecimal.cpp\\\"},\\\"7\\\":{\\\"name\\\":\\\"constant.numeric.exponent.hexadecimal.cpp\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=\\\\\\\\h)'(?=\\\\\\\\h)\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.cpp\\\"}]},\\\"8\\\":{\\\"name\\\":\\\"keyword.other.unit.suffix.integer.cpp\\\"},\\\"9\\\":{\\\"name\\\":\\\"keyword.other.unit.user-defined.cpp\\\"}},\\\"match\\\":\\\"(\\\\\\\\G0[xX])(\\\\\\\\h(?:\\\\\\\\h|((?<=\\\\\\\\h)'(?=\\\\\\\\h)))*)(?:(?<!')([pP])(\\\\\\\\+?)(-?)([0-9](?:[0-9]|(?<=\\\\\\\\h)'(?=\\\\\\\\h))*))?((?:[uU]|[uU]ll?|[uU]LL?|ll?[uU]?|LL?[uU]?|[fF])(?!\\\\\\\\w))?((?:\\\\\\\\w(?<![pP\\\\\\\\h])\\\\\\\\w*)?$)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.numeric.decimal.cpp\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=\\\\\\\\h)'(?=\\\\\\\\h)\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.cpp\\\"}]},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric.cpp\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.other.unit.exponent.decimal.cpp\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.operator.plus.exponent.decimal.cpp\\\"},\\\"5\\\":{\\\"name\\\":\\\"keyword.operator.minus.exponent.decimal.cpp\\\"},\\\"6\\\":{\\\"name\\\":\\\"constant.numeric.exponent.decimal.cpp\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=\\\\\\\\h)'(?=\\\\\\\\h)\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.cpp\\\"}]},\\\"7\\\":{\\\"name\\\":\\\"keyword.other.unit.suffix.integer.cpp\\\"},\\\"8\\\":{\\\"name\\\":\\\"keyword.other.unit.user-defined.cpp\\\"}},\\\"match\\\":\\\"\\\\\\\\G(?=[0-9.])(?!0[xXbB])([0-9](?:[0-9]|((?<=\\\\\\\\h)'(?=\\\\\\\\h)))*)(?:(?<!')([eE])(\\\\\\\\+?)(-?)([0-9](?:[0-9]|(?<=\\\\\\\\h)'(?=\\\\\\\\h))*))?((?:[uU]|[uU]ll?|[uU]LL?|ll?[uU]?|LL?[uU]?|[fF])(?!\\\\\\\\w))?((?:\\\\\\\\w(?<![0-9eE])\\\\\\\\w*)?$)\\\"},{\\\"match\\\":\\\"(?:[0-9a-zA-Z_.']|(?<=[eEpP])[+-])+\\\",\\\"name\\\":\\\"invalid.illegal.constant.numeric.cpp\\\"}]}]}},\\\"match\\\":\\\"(?<!\\\\\\\\w)\\\\\\\\.?\\\\\\\\d(?:[0-9a-zA-Z_.']|(?<=[eEpP])[+-])*\\\"},\\\"critical-construct\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(critical)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.associate.fortran\\\"}},\\\"contentName\\\":\\\"meta.block.critical.fortran\\\",\\\"end\\\":\\\"(?i)\\\\\\\\b(end\\\\\\\\s*critical)\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.endassociate.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"$base\\\"}]},\\\"cycle-statement\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(cycle)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.cycle.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.statement.control.fortran\\\",\\\"patterns\\\":[]}]},\\\"data-statement\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(data)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.data.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$base\\\"}]},\\\"deallocate-statement\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(deallocate)\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.deallocate.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"name\\\":\\\"meta.statement.deallocate.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]},\\\"deferred-attribute\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.deferred.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(deferred)\\\\\\\\b\\\"},\\\"derived-type\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(?:(class)|(type))\\\\\\\\s*(\\\\\\\\()\\\\\\\\s*(([a-z]\\\\\\\\w*)|\\\\\\\\*)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.class.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.type.type.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"},\\\"4\\\":{\\\"name\\\":\\\"entity.name.type.fortran\\\"}},\\\"contentName\\\":\\\"meta.type-spec.fortran\\\",\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"name\\\":\\\"meta.specification.type.derived.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]},\\\"derived-type-component-attribute-specification\\\":{\\\"begin\\\":\\\"(?i)(?=\\\\\\\\s*\\\\\\\\b(?:private|sequence)\\\\\\\\b)\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.statement.attribute-specification.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#access-attribute\\\"},{\\\"include\\\":\\\"#sequence-attribute\\\"},{\\\"include\\\":\\\"#invalid-character\\\"}]},\\\"derived-type-component-parameter-specification\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.integer.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.comma.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.other.attribute.derived-type.parameter.fortran\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.operator.double-colon.fortran\\\"},\\\"5\\\":{\\\"name\\\":\\\"entity.name.derived-type.parameter.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(integer)\\\\\\\\s*(,)\\\\\\\\s*(kind|len)\\\\\\\\s*(?:(::)\\\\\\\\s*([a-z]\\\\\\\\w*)?)?\\\\\\\\s*(?=[;!\\\\\\\\n])\\\"},\\\"derived-type-component-procedure-specification\\\":{\\\"begin\\\":\\\"(?i)(?=\\\\\\\\bprocedure\\\\\\\\b)\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.specification.procedure.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#procedure-type\\\"},{\\\"begin\\\":\\\"(?=\\\\\\\\s*(,|::|\\\\\\\\())\\\",\\\"contentName\\\":\\\"meta.attribute-list.derived-type-component-procedure.fortran\\\",\\\"end\\\":\\\"(::)|(?=[;!\\\\\\\\n])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.double-colon.fortran\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"(,)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.comma.fortran\\\"}},\\\"end\\\":\\\"(?=::|[,;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#access-attribute\\\"},{\\\"include\\\":\\\"#pass-attribute\\\"},{\\\"include\\\":\\\"#nopass-attribute\\\"},{\\\"include\\\":\\\"#invalid-word\\\"},{\\\"include\\\":\\\"#pointer-attribute\\\"}]}]},{\\\"include\\\":\\\"#procedure-name-list\\\"}]},\\\"derived-type-component-type-specification\\\":{\\\"begin\\\":\\\"(?i)(?=\\\\\\\\b(?:character|class|complex|double\\\\\\\\s*precision|double\\\\\\\\s*complex|integer|logical|real|type)\\\\\\\\b(?![^:'\\\\\\\";!\\\\\\\\n]*\\\\\\\\bfunction\\\\\\\\b))\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.specification.derived-type.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#types\\\"},{\\\"include\\\":\\\"#line-continuation-operator\\\"},{\\\"begin\\\":\\\"(?=\\\\\\\\s*(,|::))\\\",\\\"contentName\\\":\\\"meta.attribute-list.derived-type-component-type.fortran\\\",\\\"end\\\":\\\"(::)|(?=[;!\\\\\\\\n])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.double-colon.fortran\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"(,)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.comma.fortran\\\"}},\\\"end\\\":\\\"(?=::|[,;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#access-attribute\\\"},{\\\"include\\\":\\\"#allocatable-attribute\\\"},{\\\"include\\\":\\\"#codimension-attribute\\\"},{\\\"include\\\":\\\"#contiguous-attribute\\\"},{\\\"include\\\":\\\"#dimension-attribute\\\"},{\\\"include\\\":\\\"#pointer-attribute\\\"},{\\\"include\\\":\\\"#invalid-word\\\"}]}]},{\\\"include\\\":\\\"#name-list\\\"}]},\\\"derived-type-contains-attribute-specification\\\":{\\\"begin\\\":\\\"(?i)(?=\\\\\\\\bprivate\\\\\\\\b)\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.statement.attribute-specification.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#access-attribute\\\"},{\\\"include\\\":\\\"#invalid-character\\\"}]},\\\"derived-type-contains-final-procedure-specification\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(final)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.final-procedure.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.specification.procedure.final.fortran\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?=\\\\\\\\s*(::))\\\",\\\"end\\\":\\\"(::)|(?=[;!\\\\\\\\n])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.double-colon.fortran\\\"}},\\\"name\\\":\\\"meta.attribute-list.derived-type-contains-final-procedure.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#invalid-word\\\"}]},{\\\"include\\\":\\\"#procedure-name\\\"}]},\\\"derived-type-contains-generic-procedure-specification\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(generic)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.procedure.generic.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.specification.procedure.generic.fortran\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?=\\\\\\\\s*(,|::|\\\\\\\\())\\\",\\\"contentName\\\":\\\"meta.attribute-list.derived-type-contains-generic-procedure.fortran\\\",\\\"end\\\":\\\"(::)|(?=[;!\\\\\\\\n])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.double-colon.fortran\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"(,)|^|(?<=&)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.comma.fortran\\\"}},\\\"end\\\":\\\"(?=::|[,\\\\\\\\&;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#access-attribute\\\"},{\\\"include\\\":\\\"#invalid-word\\\"}]}]},{\\\"begin\\\":\\\"(?=\\\\\\\\s*[a-z])\\\",\\\"contentName\\\":\\\"meta.name-list.fortran\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#IO-keywords\\\"},{\\\"include\\\":\\\"#assignment-keyword\\\"},{\\\"include\\\":\\\"#operator-keyword\\\"},{\\\"include\\\":\\\"#procedure-name\\\"},{\\\"include\\\":\\\"#pointer-operators\\\"}]}]},\\\"derived-type-contains-procedure-specification\\\":{\\\"begin\\\":\\\"(?i)(?=\\\\\\\\bprocedure\\\\\\\\b)\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.specification.procedure.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#procedure-type\\\"},{\\\"begin\\\":\\\"(?=\\\\\\\\s*(,|::|\\\\\\\\())\\\",\\\"contentName\\\":\\\"meta.attribute-list.derived-type-contains-procedure.fortran\\\",\\\"end\\\":\\\"(::)|(?=[;!\\\\\\\\n])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.double-colon.fortran\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"(,)|^|(?<=&)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.comma.fortran\\\"}},\\\"end\\\":\\\"(?=::|[,\\\\\\\\&;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.something.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#access-attribute\\\"},{\\\"include\\\":\\\"#deferred-attribute\\\"},{\\\"include\\\":\\\"#non-overridable-attribute\\\"},{\\\"include\\\":\\\"#nopass-attribute\\\"},{\\\"include\\\":\\\"#pass-attribute\\\"},{\\\"include\\\":\\\"#invalid-word\\\"}]}]},{\\\"include\\\":\\\"#procedure-name-list\\\"}]},\\\"derived-type-definition\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(type)\\\\\\\\b(?!\\\\\\\\s*(\\\\\\\\(|is\\\\\\\\b|=))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.type.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.derived-type.definition.fortran\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(?=\\\\\\\\s*(,|::))\\\",\\\"contentName\\\":\\\"meta.attribute-list.derived-type.fortran\\\",\\\"end\\\":\\\"(::)|(?=[;!\\\\\\\\n])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.double-colon.fortran\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"(,)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.comma.fortran\\\"}},\\\"end\\\":\\\"(?=::|[,;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#access-attribute\\\"},{\\\"include\\\":\\\"#abstract-attribute\\\"},{\\\"include\\\":\\\"#language-binding-attribute\\\"},{\\\"include\\\":\\\"#extends-attribute\\\"},{\\\"include\\\":\\\"#invalid-word\\\"}]}]},{\\\"begin\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b([a-z]\\\\\\\\w*)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.type.fortran\\\"}},\\\"end\\\":\\\"(?i)(?:^|(?<=;))\\\\\\\\s*(end\\\\\\\\s*type)(?:\\\\\\\\s+(?:(\\\\\\\\1)|(\\\\\\\\w+)))?\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.endtype.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.type.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"invalid.error.derived-type.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#dummy-variable-list\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"begin\\\":\\\"(?i)^(?!\\\\\\\\s*\\\\\\\\b(?:contains|end\\\\\\\\s*type)\\\\\\\\b)\\\",\\\"end\\\":\\\"(?i)^(?=\\\\\\\\s*\\\\\\\\b(?:contains|end\\\\\\\\s*type)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.block.specification.derived-type.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#derived-type-component-attribute-specification\\\"},{\\\"include\\\":\\\"#derived-type-component-parameter-specification\\\"},{\\\"include\\\":\\\"#derived-type-component-procedure-specification\\\"},{\\\"include\\\":\\\"#derived-type-component-type-specification\\\"}]},{\\\"begin\\\":\\\"(?i)\\\\\\\\b(contains)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.contains.fortran\\\"}},\\\"end\\\":\\\"(?i)(?=\\\\\\\\s*end\\\\\\\\s*type\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.block.contains.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#derived-type-contains-attribute-specification\\\"},{\\\"include\\\":\\\"#derived-type-contains-final-procedure-specification\\\"},{\\\"include\\\":\\\"#derived-type-contains-generic-procedure-specification\\\"},{\\\"include\\\":\\\"#derived-type-contains-procedure-specification\\\"}]}]}]},\\\"derived-type-operators\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.selector.fortran\\\"}},\\\"match\\\":\\\"\\\\\\\\s*(%)\\\"},\\\"dimension-attribute\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(dimension)(?=\\\\\\\\s*\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.dimension.fortran\\\"}},\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]},\\\"do-construct\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.enddo.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(end\\\\\\\\s*do)\\\\\\\\b\\\"},{\\\"begin\\\":\\\"(?i)\\\\\\\\b(do)\\\\\\\\s+(\\\\\\\\d{1,5})\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.do.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.numeric.fortran\\\"}},\\\"end\\\":\\\"(?i)(?:^|(?<=;))(?=\\\\\\\\s*\\\\\\\\b\\\\\\\\2\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.do.labeled.fortran\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\G(?:\\\\\\\\s*(,)|(?!\\\\\\\\s*[;!\\\\\\\\n]))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.comma.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#concurrent-attribute\\\"},{\\\"include\\\":\\\"#while-attribute\\\"},{\\\"include\\\":\\\"$base\\\"}]},{\\\"include\\\":\\\"$base\\\"}]},{\\\"begin\\\":\\\"(?i)\\\\\\\\b(do)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.do.fortran\\\"}},\\\"end\\\":\\\"(?i)\\\\\\\\b(?:(continue)|(end\\\\\\\\s*do))\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.continue.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.enddo.fortran\\\"}},\\\"name\\\":\\\"meta.block.do.unlabeled.fortran\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\G(?:\\\\\\\\s*(,)|(?!\\\\\\\\s*[;!\\\\\\\\n]))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.comma.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.loop-control.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#concurrent-attribute\\\"},{\\\"include\\\":\\\"#while-attribute\\\"},{\\\"include\\\":\\\"$base\\\"}]},{\\\"begin\\\":\\\"(?i)(?!\\\\\\\\s*\\\\\\\\b(continue|end\\\\\\\\s*do)\\\\\\\\b)\\\",\\\"end\\\":\\\"(?i)(?=\\\\\\\\s*\\\\\\\\b(continue|end\\\\\\\\s*do)\\\\\\\\b)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$base\\\"}]}]}]},\\\"dummy-variable\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.parameter.fortran\\\"}},\\\"match\\\":\\\"(?i)(?:^|(?<=[\\\\\\\\&,(]))\\\\\\\\s*([a-z]\\\\\\\\w*)\\\"},\\\"dummy-variable-list\\\":{\\\"begin\\\":\\\"\\\\\\\\G\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.begin.fortran\\\"}},\\\"end\\\":\\\"\\\\\\\\)|(?=\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.end.fortran\\\"}},\\\"name\\\":\\\"meta.dummy-variable-list\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#dummy-variable\\\"}]},\\\"elemental-attribute\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.elemental.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(elemental)\\\\\\\\b\\\"},\\\"entry-statement\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(entry)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.entry.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.statement.control.entry.fortran\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b([a-z]\\\\\\\\w*)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.entry.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#dummy-variable-list\\\"},{\\\"include\\\":\\\"#result-statement\\\"},{\\\"include\\\":\\\"#language-binding-attribute\\\"}]}]}]},\\\"enum-block-construct\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(enum)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.enum.fortran\\\"}},\\\"end\\\":\\\"(?i)\\\\\\\\b(end\\\\\\\\s*enum)\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.end-enum.fortran\\\"}},\\\"name\\\":\\\"meta.enum.fortran\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G\\\\\\\\s*(,)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.comma.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#language-binding-attribute\\\"},{\\\"include\\\":\\\"#invalid-word\\\"}]},{\\\"begin\\\":\\\"(?i)(?!\\\\\\\\s*\\\\\\\\b(end\\\\\\\\s*enum)\\\\\\\\b)\\\",\\\"end\\\":\\\"(?i)(?=\\\\\\\\b(end\\\\\\\\s*enum)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.block.specification.enum.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"begin\\\":\\\"(?i)\\\\\\\\b(enumerator)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.enumerator.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.statement.enumerator-specification.fortran\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?=\\\\\\\\s*(,|::))\\\",\\\"contentName\\\":\\\"meta.attribute-list.enum.fortran\\\",\\\"end\\\":\\\"(::)|(?=[;!\\\\\\\\n])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.double-colon.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#invalid-word\\\"}]},{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#name-list\\\"}]}]}]},\\\"equivalence-statement\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(equivalence)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.common.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?:\\\\\\\\G|(,))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"puntuation.comma.fortran\\\"}},\\\"end\\\":\\\"(?=[,;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]}]},\\\"error-stop-statement\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(error\\\\\\\\s+stop)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.errorstop.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.statement.control.errorstop.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#string-operators\\\"},{\\\"include\\\":\\\"#variable\\\"},{\\\"include\\\":\\\"#invalid-character\\\"}]},\\\"event-statement\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(event (?:post|wait))\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.event.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"name\\\":\\\"meta.statement.event.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]},\\\"execution-statements\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#allocate-statement\\\"},{\\\"include\\\":\\\"#deallocate-statement\\\"},{\\\"include\\\":\\\"#IO-statements\\\"},{\\\"include\\\":\\\"#nullify-statement\\\"}]},\\\"exit-statement\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(exit)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.exit.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.statement.control.exit.fortran\\\",\\\"patterns\\\":[]},\\\"explicit-interface-block-construct\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(interface)\\\\\\\\b(?=\\\\\\\\s*[;!\\\\\\\\n])\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.interface.fortran\\\"}},\\\"end\\\":\\\"(?i)\\\\\\\\b(end\\\\\\\\s*interface)\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.endinterface.fortran.modern\\\"}},\\\"name\\\":\\\"meta.interface.explicit.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$base\\\"}]},\\\"extends-attribute\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(extends)\\\\\\\\s*\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.extends.fortran\\\"}},\\\"end\\\":\\\"(?:\\\\\\\\)|(?=\\\\\\\\n))\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b([a-z]\\\\\\\\w*)\\\\\\\\b\\\",\\\"name\\\":\\\"entity.name.type.fortran\\\"}]},\\\"external-attribute\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.external.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(external)\\\\\\\\b\\\"},\\\"fail-image-statement\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.fail-image.fortran\\\"}},\\\"match\\\":\\\"\\\\\\\\b(fail image)\\\\\\\\b\\\",\\\"name\\\":\\\"meta.statement.fail-image.fortran\\\"},\\\"forall-construct\\\":{\\\"applyEndPatternLast\\\":1,\\\"begin\\\":\\\"(?i)\\\\\\\\b(forall)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.forall.fortran\\\"}},\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\G(?!\\\\\\\\s*[;!\\\\\\\\n])\\\",\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"name\\\":\\\"meta.loop-control.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses\\\"},{\\\"include\\\":\\\"#invalid-word\\\"}]},{\\\"begin\\\":\\\"(?<=\\\\\\\\))(?=\\\\\\\\s*[;!\\\\\\\\n])\\\",\\\"end\\\":\\\"(?i)\\\\\\\\b(end\\\\\\\\s*forall)\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.endforall.fortran\\\"}},\\\"name\\\":\\\"meta.block.forall.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$base\\\"}]},{\\\"begin\\\":\\\"(?i)(?<=\\\\\\\\))(?!\\\\\\\\s*[;!\\\\\\\\n])\\\",\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"meta.statement.control.forall.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$base\\\"}]}]},\\\"form-team-statement\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(form team)\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.form-team.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"name\\\":\\\"meta.statement.form-team.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]},\\\"function-definition\\\":{\\\"begin\\\":\\\"(?i)(?=([^:'\\\\\\\";!\\\\\\\\n](?!\\\\\\\\bend)(?!\\\\\\\\bsubroutine\\\\\\\\b))*\\\\\\\\bfunction\\\\\\\\b)\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.function.fortran\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)(?=\\\\\\\\G\\\\\\\\s*(?!\\\\\\\\bfunction\\\\\\\\b))\\\",\\\"end\\\":\\\"(?i)(?=\\\\\\\\bfunction\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.attribute-list.function.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#elemental-attribute\\\"},{\\\"include\\\":\\\"#module-attribute\\\"},{\\\"include\\\":\\\"#pure-attribute\\\"},{\\\"include\\\":\\\"#recursive-attribute\\\"},{\\\"include\\\":\\\"#types\\\"},{\\\"include\\\":\\\"#invalid-word\\\"}]},{\\\"begin\\\":\\\"(?i)\\\\\\\\b(function)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.function.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b([a-z]\\\\\\\\w*)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.fortran\\\"}},\\\"end\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(?:(end\\\\\\\\s*function)(?:\\\\\\\\s+([a-z_]\\\\\\\\w*))?|(end))\\\\\\\\b\\\\\\\\s*([^;!\\\\\\\\n]+)?(?=[;!\\\\\\\\n])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.endfunction.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.other.endfunction.fortran\\\"},\\\"4\\\":{\\\"name\\\":\\\"invalid.error.function.fortran\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(?!\\\\\\\\s*[;!\\\\\\\\n])\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.function.first-line.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#dummy-variable-list\\\"},{\\\"include\\\":\\\"#result-statement\\\"},{\\\"include\\\":\\\"#language-binding-attribute\\\"}]},{\\\"begin\\\":\\\"(?i)(?!\\\\\\\\bend(?:\\\\\\\\s*[;!\\\\\\\\n]|\\\\\\\\s*function\\\\\\\\b))\\\",\\\"end\\\":\\\"(?i)(?=\\\\\\\\bend(?:\\\\\\\\s*[;!\\\\\\\\n]|\\\\\\\\s*function\\\\\\\\b))\\\",\\\"name\\\":\\\"meta.block.specification.function.fortran\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\b(contains)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.contains.fortran\\\"}},\\\"end\\\":\\\"(?i)(?=end(?:\\\\\\\\s*[;!\\\\\\\\n]|\\\\\\\\s*function\\\\\\\\b))\\\",\\\"name\\\":\\\"meta.block.contains.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$base\\\"}]},{\\\"include\\\":\\\"$base\\\"}]}]}]}]},\\\"generic-interface-block-construct\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(interface)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.interface.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.interface.generic.fortran\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(assignment)\\\\\\\\s*(\\\\\\\\()\\\\\\\\s*(?:(=)|(\\\\\\\\S.*))\\\\\\\\s*(\\\\\\\\))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.assignment.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.operator.assignment.fortran\\\"},\\\"4\\\":{\\\"name\\\":\\\"invalid.error.generic-interface.fortran\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"end\\\":\\\"(?i)\\\\\\\\b(end\\\\\\\\s*interface)\\\\\\\\b(?:\\\\\\\\s*\\\\\\\\b(\\\\\\\\1)\\\\\\\\b\\\\\\\\s*(\\\\\\\\()\\\\\\\\s*(?:(\\\\\\\\3)|(\\\\\\\\S.*))\\\\\\\\s*(\\\\\\\\)))?\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.endinterface.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.assignment.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.operator.assignment.fortran\\\"},\\\"5\\\":{\\\"name\\\":\\\"invalid.error.generic-interface-end.fortran\\\"},\\\"6\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#interface-procedure-statement\\\"},{\\\"include\\\":\\\"$base\\\"}]},{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(operator)\\\\\\\\s*(\\\\\\\\()\\\\\\\\s*(?:(\\\\\\\\.[a-z]+\\\\\\\\.|==|/=|>=|[><]|<=|[-+/]|//|\\\\\\\\*\\\\\\\\*|\\\\\\\\*)|(\\\\\\\\S.*))\\\\\\\\s*(\\\\\\\\))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.operator.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.operator.fortran\\\"},\\\"4\\\":{\\\"name\\\":\\\"invalid.error.generic-interface-block-op.fortran\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"end\\\":\\\"(?i)\\\\\\\\b(end\\\\\\\\s*interface)\\\\\\\\b(?:\\\\\\\\s*\\\\\\\\b(\\\\\\\\1)\\\\\\\\b\\\\\\\\s*(\\\\\\\\()\\\\\\\\s*(?:(\\\\\\\\3)|(\\\\\\\\S.*))\\\\\\\\s*(\\\\\\\\)))?\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.endinterface.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.operator.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.operator.fortran\\\"},\\\"5\\\":{\\\"name\\\":\\\"invalid.error.generic-interface-block-op-end.fortran\\\"},\\\"6\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#interface-procedure-statement\\\"},{\\\"include\\\":\\\"$base\\\"}]},{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(?:(read)|(write))\\\\\\\\s*(\\\\\\\\()\\\\\\\\s*(?:(formatted)|(unformatted)|(\\\\\\\\S.*))\\\\\\\\s*(\\\\\\\\))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.read.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.write.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.other.formatted.fortran\\\"},\\\"5\\\":{\\\"name\\\":\\\"keyword.other.unformatted.fortran\\\"},\\\"6\\\":{\\\"name\\\":\\\"invalid.error.generic-interface-block.fortran\\\"},\\\"7\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"end\\\":\\\"(?i)\\\\\\\\b(end\\\\\\\\s*interface)\\\\\\\\b(?:\\\\\\\\s*\\\\\\\\b(?:(\\\\\\\\2)|(\\\\\\\\3))\\\\\\\\b\\\\\\\\s*(\\\\\\\\()\\\\\\\\s*(?:(\\\\\\\\4)|(\\\\\\\\5)|(\\\\\\\\S.*))\\\\\\\\s*(\\\\\\\\)))?\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.endinterface.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.read.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.other.write.fortran\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"},\\\"5\\\":{\\\"name\\\":\\\"keyword.other.formatted.fortran\\\"},\\\"6\\\":{\\\"name\\\":\\\"keyword.other.unformatted.fortran\\\"},\\\"7\\\":{\\\"name\\\":\\\"invalid.error.generic-interface-block-end.fortran\\\"},\\\"8\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#interface-procedure-statement\\\"},{\\\"include\\\":\\\"$base\\\"}]},{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b([a-z]\\\\\\\\w*)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.fortran\\\"}},\\\"end\\\":\\\"(?i)\\\\\\\\b(end\\\\\\\\s*interface)\\\\\\\\b(?:\\\\\\\\s*\\\\\\\\b(\\\\\\\\1)\\\\\\\\b)?\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.endinterface.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#interface-procedure-statement\\\"},{\\\"include\\\":\\\"$base\\\"}]}]},\\\"goto-statement\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(go\\\\\\\\s*to)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.goto.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.statement.control.goto.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$base\\\"}]},\\\"if-construct\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\b(if)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.if.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#logical-control-expression\\\"},{\\\"begin\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(then)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.then.fortran\\\"}},\\\"contentName\\\":\\\"meta.block.if.fortran\\\",\\\"end\\\":\\\"(?i)\\\\\\\\b(end\\\\\\\\s*if)\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.endif.fortran\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\b(else\\\\\\\\s*if)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.elseif.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.then.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"meta.label.elseif.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(then)\\\\\\\\b(\\\\\\\\s*[a-z]\\\\\\\\w*)?\\\"},{\\\"include\\\":\\\"#invalid-word\\\"}]},{\\\"begin\\\":\\\"(?i)\\\\\\\\b(else)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.else.fortran\\\"}},\\\"end\\\":\\\"(?i)(?=\\\\\\\\b(end\\\\\\\\s*if)\\\\\\\\b)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?!(\\\\\\\\s*([;!\\\\\\\\n])))\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.label.else.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.error.label.else.fortran\\\"}},\\\"match\\\":\\\"\\\\\\\\s*([a-z]\\\\\\\\w*)?\\\\\\\\s*\\\\\\\\b(\\\\\\\\w*)\\\\\\\\b\\\"},{\\\"include\\\":\\\"#invalid-word\\\"}]},{\\\"begin\\\":\\\"(?i)(?!\\\\\\\\b(end\\\\\\\\s*if)\\\\\\\\b)\\\",\\\"end\\\":\\\"(?i)(?=\\\\\\\\b(end\\\\\\\\s*if)\\\\\\\\b)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$base\\\"}]}]},{\\\"include\\\":\\\"$base\\\"}]},{\\\"begin\\\":\\\"(?i)(?=\\\\\\\\s*[a-z])\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.statement.control.if.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$base\\\"}]}]}]},\\\"image-control-statement\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#sync-all-statement\\\"},{\\\"include\\\":\\\"#sync-statement\\\"},{\\\"include\\\":\\\"#event-statement\\\"},{\\\"include\\\":\\\"#form-team-statement\\\"},{\\\"include\\\":\\\"#fail-image-statement\\\"}]},\\\"implicit-statement\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(implicit)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.implicit.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.statement.implicit.fortran\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.none.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(none)\\\\\\\\b\\\"},{\\\"include\\\":\\\"$base\\\"}]},\\\"import-statement\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(import)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.include.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.statement.include.fortran\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*(?:(::)|(?=[a-z]))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.double-colon.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#name-list\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\G\\\\\\\\s*(,)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.comma.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.all.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(all)\\\\\\\\b\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.none.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(none)\\\\\\\\b\\\"},{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(only)\\\\\\\\s*(:)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.only.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.colon.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#name-list\\\"}]},{\\\"include\\\":\\\"#invalid-word\\\"}]}]},\\\"include-statement\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(include)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.include.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.statement.include.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string-constant\\\"},{\\\"include\\\":\\\"#invalid-character\\\"}]},\\\"intent-attribute\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(intent)\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.intent.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"end\\\":\\\"(\\\\\\\\))|(?=[;!\\\\\\\\n])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.intent.in-out.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.modifier.intent.in.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"storage.modifier.intent.out.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(?:(in\\\\\\\\s*out)|(in)|(out))\\\\\\\\b\\\"},{\\\"include\\\":\\\"#invalid-word\\\"}]},\\\"interface-block-constructs\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#abstract-interface-block-construct\\\"},{\\\"include\\\":\\\"#explicit-interface-block-construct\\\"},{\\\"include\\\":\\\"#generic-interface-block-construct\\\"}]},\\\"interface-procedure-statement\\\":{\\\"begin\\\":\\\"(?i)(?=[^'\\\\\\\";!\\\\\\\\n]*\\\\\\\\bprocedure\\\\\\\\b)\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.statement.procedure.fortran\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)(?=\\\\\\\\G\\\\\\\\s*(?!\\\\\\\\bprocedure\\\\\\\\b))\\\",\\\"end\\\":\\\"(?i)(?=\\\\\\\\bprocedure\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.attribute-list.interface.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#module-attribute\\\"},{\\\"include\\\":\\\"#invalid-word\\\"}]},{\\\"begin\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(procedure)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.procedure.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.double-colon.fortran\\\"}},\\\"match\\\":\\\"\\\\\\\\G\\\\\\\\s*(::)\\\"},{\\\"include\\\":\\\"#procedure-name-list\\\"}]}]},\\\"intrinsic-attribute\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.intrinsic.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(intrinsic)\\\\\\\\b\\\"},\\\"intrinsic-functions\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\b(acosh|asinh|atanh|bge|bgt|ble|blt|dshiftl|dshiftr|findloc|hypot|iall|iany|image_index|iparity|is_contiguous|lcobound|leadz|mask[lr]|merge_bits|norm2|num_images|parity|popcnt|poppar|shift[alr]|storage_size|this_image|trailz|ucobound)\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.intrinsic.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]},{\\\"begin\\\":\\\"(?i)\\\\\\\\b(bessel_[jy][01n]|erf(c(_scaled)?)?|gamma|log_gamma)\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.intrinsic.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]},{\\\"begin\\\":\\\"(?i)\\\\\\\\b(command_argument_count|extends_type_of|is_iostat_end|is_iostat_eor|new_line|same_type_as|selected_char_kind)\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.intrinsic.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]},{\\\"begin\\\":\\\"(?i)\\\\\\\\b(ieee_(class|copy_sign|is_(finite|nan|negative|normal)|logb|next_after|rem|rint|scalb|selected_real_kind|support_(datatype|denormal|divide|inf|io|nan|rounding|sqrt|standard|underflow_control)|unordered|value))\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.intrinsic.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]},{\\\"begin\\\":\\\"(?i)\\\\\\\\b(ieee_support_(flag|halting))\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.intrinsic.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]},{\\\"begin\\\":\\\"(?i)\\\\\\\\b(c_(associated|funloc|loc|sizeof))\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.intrinsic.fortran\\\"}},\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]},{\\\"begin\\\":\\\"(?i)\\\\\\\\b(compiler_(options|version))\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.intrinsic.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]},{\\\"begin\\\":\\\"(?i)\\\\\\\\b(null)\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.intrinsic.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]},{\\\"begin\\\":\\\"(?i)\\\\\\\\b(achar|adjustl|adjustr|all|allocated|associated|any|bit_size|btest|ceiling|count|cshift|digits|dot_product|eoshift|epsilon|exponent|floor|fraction|huge|iachar|iand|ibclr|ibits|ibset|ieor|ior|ishftc?|kind|lbound|len_trim|logical|matmul|maxexponent|maxloc|maxval|merge|minexponent|minloc|minval|modulo|nearest|not|pack|precision|present|product|radix|range|repeat|reshape|rrspacing|scale|scan|selected_(int|real)_kind|set_exponent|shape|size|spacing|spread|sum|tiny|transfer|transpose|trim|ubound|unpack|verify)\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.intrinsic.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]},{\\\"begin\\\":\\\"(?i)\\\\\\\\b([icd]?abs|acos|[ad]int|[ad]nint|aimag|amax[01]|amin[01]|d?asin|d?atan|d?atan2|char|conjg|[cd]?cos|d?cosh|cmplx|dble|i?dim|dmax1|dmin1|dprod|[cd]?exp|float|ichar|idint|ifix|index|int|len|lge|lgt|lle|llt|[acd]?log|[ad]?log10|max[01]?|min[01]?|[ad]?mod|(id)?nint|real|[di]?sign|[cd]?sin|d?sinh|sngl|[cd]?sqrt|d?tan|d?tanh)\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.intrinsic.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]}]},\\\"intrinsic-subroutines\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(date_and_time|mvbits|random_number|random_seed|system_clock)\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.subroutine.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]},{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(cpu_time)\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.subroutine.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]},{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(ieee_(get|set)_(rounding|underflow)_mode)\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.subroutine.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]},{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(ieee_(get|set)_(flag|halting_mode|status))\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.subroutine.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]},{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(c_f_(p(?:ointer|rocpointer)))\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.subroutine.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]},{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(execute_command_line|get_command|get_command_argument|get_environment_variable|move_alloc)\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.subroutine.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]}]},\\\"invalid-character\\\":{\\\"match\\\":\\\"(?i)[^\\\\\\\\s;!\\\\\\\\n]+\\\",\\\"name\\\":\\\"invalid.error.character.fortran\\\"},\\\"invalid-word\\\":{\\\"match\\\":\\\"(?i)\\\\\\\\b\\\\\\\\w+\\\\\\\\b\\\",\\\"name\\\":\\\"invalid.error.word.fortran\\\"},\\\"language-binding-attribute\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(bind)\\\\\\\\s*\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.bind.fortran\\\"}},\\\"end\\\":\\\"(?:\\\\\\\\)|(?=\\\\\\\\n))\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?i)\\\\\\\\b(c)\\\\\\\\b\\\",\\\"name\\\":\\\"variable.parameter.fortran\\\"},{\\\"include\\\":\\\"#dummy-variable\\\"},{\\\"include\\\":\\\"$base\\\"}]},\\\"line-continuation-operator\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.line-continuation.fortran\\\"}},\\\"match\\\":\\\"(?:^|(?<=;))\\\\\\\\s*(&)\\\"},{\\\"begin\\\":\\\"\\\\\\\\s*(&)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.line-continuation.fortran\\\"}},\\\"contentName\\\":\\\"meta.line-continuation.fortran\\\",\\\"end\\\":\\\"(?i)^(?:\\\\\\\\s*(&))?\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.line-continuation.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"match\\\":\\\"\\\\\\\\S[^!]*\\\",\\\"name\\\":\\\"invalid.error.line-cont.fortran\\\"}]}]},\\\"logical-constant\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.language.logical.false.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.language.logical.true.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\s*(?:(\\\\\\\\.false\\\\\\\\.)|(\\\\\\\\.true\\\\\\\\.))\\\"},\\\"logical-control-expression\\\":{\\\"begin\\\":\\\"\\\\\\\\G(?=\\\\\\\\s*\\\\\\\\()\\\",\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"name\\\":\\\"meta.expression.control.logical.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses\\\"}]},\\\"logical-operators\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?i)(\\\\\\\\s*\\\\\\\\.(and|eq|eqv|le|lt|ge|gt|ne|neqv|not|or)\\\\\\\\.)\\\",\\\"name\\\":\\\"keyword.logical.fortran\\\"},{\\\"match\\\":\\\"(==|/=|>=|(?<!=)>|<=|<)\\\",\\\"name\\\":\\\"keyword.logical.fortran.modern\\\"}]},\\\"logical-type\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\b(logical)\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.logical.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"contentName\\\":\\\"meta.type-spec.fortran\\\",\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.character.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.multiplication.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.numeric.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(logical)\\\\\\\\b(?:\\\\\\\\s*(\\\\\\\\*)\\\\\\\\s*(\\\\\\\\d*))?\\\"}]},\\\"module-attribute\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.module.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(module)\\\\\\\\b(?=\\\\\\\\s*(?:[;!\\\\\\\\n]|[^'\\\\\\\";!\\\\\\\\n]*\\\\\\\\b(?:function|procedure|subroutine)\\\\\\\\b))\\\"},\\\"module-definition\\\":{\\\"begin\\\":\\\"(?i)(?=\\\\\\\\b(module)\\\\\\\\b)(?![^'\\\\\\\";!\\\\\\\\n]*\\\\\\\\b(?:function|procedure|subroutine)\\\\\\\\b)\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.module.fortran\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.program.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(module)\\\\\\\\b\\\"},{\\\"applyEndPatternLast\\\":1,\\\"begin\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b([a-z]\\\\\\\\w*)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.class.module.fortran\\\"}},\\\"end\\\":\\\"(?i)\\\\\\\\b(?:(end\\\\\\\\s*module)(?:\\\\\\\\s+([a-z_]\\\\\\\\w*))?|(end))\\\\\\\\b\\\\\\\\s*([^;!\\\\\\\\n]+)?(?=[;!\\\\\\\\n])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.endmodule.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.class.module.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.other.endmodule.fortran\\\"},\\\"4\\\":{\\\"name\\\":\\\"invalid.error.module-definition.fortran\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G\\\",\\\"end\\\":\\\"(?i)(?=\\\\\\\\bend(?:\\\\\\\\s*[;!\\\\\\\\n]|\\\\\\\\s*module\\\\\\\\b))\\\",\\\"name\\\":\\\"meta.block.specification.module.fortran\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\b(contains)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.contains.fortran\\\"}},\\\"end\\\":\\\"(?i)(?=\\\\\\\\s*end(?:\\\\\\\\s*[;!\\\\\\\\n]|\\\\\\\\s*module\\\\\\\\b))\\\",\\\"name\\\":\\\"meta.block.contains.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$base\\\"}]},{\\\"include\\\":\\\"$base\\\"}]}]}]},\\\"name-list\\\":{\\\"begin\\\":\\\"(?i)(?=\\\\\\\\s*[a-z])\\\",\\\"contentName\\\":\\\"meta.name-list.fortran\\\",\\\"end\\\":\\\"(?=[);!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"include\\\":\\\"#intrinsic-functions\\\"},{\\\"include\\\":\\\"#array-constructor\\\"},{\\\"include\\\":\\\"#parentheses\\\"},{\\\"include\\\":\\\"#brackets\\\"},{\\\"include\\\":\\\"#assignment-keyword\\\"},{\\\"include\\\":\\\"#operator-keyword\\\"},{\\\"include\\\":\\\"#variable\\\"}]},\\\"named-control-constructs\\\":{\\\"applyEndPatternLast\\\":1,\\\"begin\\\":\\\"(?i)([a-z]\\\\\\\\w*)\\\\\\\\s*(:)(?=\\\\\\\\s*(?:associate|block(?!\\\\\\\\s*data)|critical|do|forall|if|select\\\\\\\\s*case|select\\\\\\\\s*type|select\\\\\\\\s*rank|where)\\\\\\\\b)\\\",\\\"contentName\\\":\\\"meta.named-construct.fortran.modern\\\",\\\"end\\\":\\\"(?i)(?!\\\\\\\\s*\\\\\\\\b(?:associate|block(?!\\\\\\\\s*data)|critical|do|forall|if|select\\\\\\\\s*case|select\\\\\\\\s*type|select\\\\\\\\s*rank|where)\\\\\\\\b)(?:\\\\\\\\b(\\\\\\\\1)\\\\\\\\b)?([^\\\\\\\\s;!\\\\\\\\n]*?)?(?=\\\\\\\\s*[;!\\\\\\\\n])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.label.end.name.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.error.named-control-constructs.fortran.modern\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#unnamed-control-constructs\\\"}]},\\\"namelist-statement\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(namelist)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.namelist.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$base\\\"}]},\\\"non-intrinsic-attribute\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.non-intrinsic.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(non_intrinsic)\\\\\\\\b\\\"},\\\"non-overridable-attribute\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.non-overridable.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(non_overridable)\\\\\\\\b\\\"},\\\"nopass-attribute\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.nopass.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(nopass)\\\\\\\\b\\\"},\\\"nullify-statement\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(nullify)\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.nullify.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"name\\\":\\\"meta.statement.nullify.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]},\\\"numeric-constant\\\":{\\\"match\\\":\\\"(?i)[+-]?(\\\\\\\\b\\\\\\\\d+\\\\\\\\.?\\\\\\\\d*|\\\\\\\\.\\\\\\\\d+)(_\\\\\\\\w+|d[+-]?\\\\\\\\d+|e[+-]?\\\\\\\\d+(_\\\\\\\\w+)?)?(?![a-z_])\\\",\\\"name\\\":\\\"constant.numeric.fortran\\\"},\\\"numeric-type\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\b(?:(complex)|(double\\\\\\\\s*precision)|(double\\\\\\\\s*complex)|(integer)|(real))\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.complex.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.type.double.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"storage.type.doublecomplex.fortran\\\"},\\\"4\\\":{\\\"name\\\":\\\"storage.type.integer.fortran\\\"},\\\"5\\\":{\\\"name\\\":\\\"storage.type.real.fortran\\\"},\\\"6\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"contentName\\\":\\\"meta.type-spec.fortran\\\",\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.complex.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.type.double.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"storage.type.doublecomplex.fortran\\\"},\\\"4\\\":{\\\"name\\\":\\\"storage.type.integer.fortran\\\"},\\\"5\\\":{\\\"name\\\":\\\"storage.type.real.fortran\\\"},\\\"6\\\":{\\\"name\\\":\\\"storage.type.dimension.fortran\\\"},\\\"7\\\":{\\\"name\\\":\\\"keyword.operator.multiplication.fortran\\\"},\\\"8\\\":{\\\"name\\\":\\\"constant.numeric.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(?:(complex)|(double\\\\\\\\s*precision)|(double\\\\\\\\s*complex)|(integer)|(real)|(dimension))\\\\\\\\b(?:\\\\\\\\s*(\\\\\\\\*)\\\\\\\\s*(\\\\\\\\d*))?\\\"}]},\\\"operator-keyword\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(operator)\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.generic-spec.operator.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#arithmetic-operators\\\"},{\\\"include\\\":\\\"#logical-operators\\\"},{\\\"include\\\":\\\"#user-defined-operators\\\"},{\\\"include\\\":\\\"#invalid-word\\\"}]},\\\"operators\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#arithmetic-operators\\\"},{\\\"include\\\":\\\"#assignment-operator\\\"},{\\\"include\\\":\\\"#derived-type-operators\\\"},{\\\"include\\\":\\\"#logical-operators\\\"},{\\\"include\\\":\\\"#pointer-operators\\\"},{\\\"include\\\":\\\"#string-operators\\\"},{\\\"include\\\":\\\"#user-defined-operators\\\"}]},\\\"optional-attribute\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.optional.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(optional)\\\\\\\\b\\\"},\\\"parameter-attribute\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.parameter.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(parameter)\\\\\\\\b\\\"},\\\"parentheses\\\":{\\\"begin\\\":\\\"\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"include\\\":\\\"#array-constructor\\\"},{\\\"include\\\":\\\"#parentheses\\\"},{\\\"include\\\":\\\"#intrinsic-functions\\\"},{\\\"include\\\":\\\"#variable\\\"}]},\\\"parentheses-dummy-variables\\\":{\\\"begin\\\":\\\"\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#procedure-call-dummy-variable\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"include\\\":\\\"#array-constructor\\\"},{\\\"include\\\":\\\"#parentheses\\\"},{\\\"include\\\":\\\"#intrinsic-functions\\\"},{\\\"include\\\":\\\"#variable\\\"}]},\\\"pass-attribute\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(pass)\\\\\\\\s*\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.pass.fortran\\\"}},\\\"end\\\":\\\"\\\\\\\\)|(?=\\\\\\\\n)\\\",\\\"patterns\\\":[]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.pass.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(pass)\\\\\\\\b\\\"}]},\\\"pause-statement\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(pause)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.pause.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.statement.control.pause.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#invalid-character\\\"}]},\\\"pointer-attribute\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.pointer.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(pointer)\\\\\\\\b\\\"},\\\"pointer-operators\\\":{\\\"match\\\":\\\"(=>)\\\",\\\"name\\\":\\\"keyword.other.point.fortran\\\"},\\\"preprocessor\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*(#:?)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.preprocessor.indicator.fortran\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"meta.preprocessor\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-if-construct\\\"},{\\\"include\\\":\\\"#preprocessor-statements\\\"}]},\\\"preprocessor-arithmetic-operators\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.subtraction.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.addition.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.operator.division.fortran\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.operator.multiplication.fortran\\\"}},\\\"match\\\":\\\"(-)|(\\\\\\\\+)|(/)|(\\\\\\\\*)\\\"},\\\"preprocessor-assignment-operator\\\":{\\\"match\\\":\\\"(?<!=)(=)(?!=)\\\",\\\"name\\\":\\\"keyword.operator.assignment.preprocessor.fortran\\\"},\\\"preprocessor-comments\\\":{\\\"begin\\\":\\\"/\\\\\\\\*\\\",\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"name\\\":\\\"comment.preprocessor\\\"},\\\"preprocessor-constants\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#cpp-numeric-constant\\\"},{\\\"include\\\":\\\"#preprocessor-string-constant\\\"}]},\\\"preprocessor-define-statement\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(define)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.preprocessor.define.fortran\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.macro.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-comments\\\"},{\\\"include\\\":\\\"#preprocessor-constants\\\"},{\\\"include\\\":\\\"#preprocessor-line-continuation-operator\\\"}]},\\\"preprocessor-defined-function\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.preprocessor.defined.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(defined)\\\\\\\\b\\\"},\\\"preprocessor-error-statement\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*(error)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.preprocessor.error.fortran\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.macro.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-comments\\\"},{\\\"include\\\":\\\"#preprocessor-string-constant\\\"},{\\\"include\\\":\\\"#preprocessor-line-continuation-operator\\\"}]},\\\"preprocessor-if-construct\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(if)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.preprocessor.if.fortran\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.conditional.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-comments\\\"},{\\\"include\\\":\\\"#cpp-numeric-constant\\\"},{\\\"include\\\":\\\"#preprocessor-logical-operators\\\"},{\\\"include\\\":\\\"#preprocessor-arithmetic-operators\\\"},{\\\"include\\\":\\\"#preprocessor-defined-function\\\"},{\\\"include\\\":\\\"#preprocessor-line-continuation-operator\\\"}]},{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(ifdef)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.preprocessor.ifdef.fortran\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\n)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-comments\\\"},{\\\"include\\\":\\\"#cpp-numeric-constant\\\"},{\\\"include\\\":\\\"#preprocessor-logical-operators\\\"},{\\\"include\\\":\\\"#preprocessor-arithmetic-operators\\\"},{\\\"include\\\":\\\"#preprocessor-line-continuation-operator\\\"}]},{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(ifndef)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.preprocessor.ifndef.fortran\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\n)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-comments\\\"},{\\\"include\\\":\\\"#cpp-numeric-constant\\\"},{\\\"include\\\":\\\"#preprocessor-logical-operators\\\"},{\\\"include\\\":\\\"#preprocessor-arithmetic-operators\\\"},{\\\"include\\\":\\\"#preprocessor-line-continuation-operator\\\"}]},{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(else)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.preprocessor.else.fortran\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\n)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-comments\\\"},{\\\"include\\\":\\\"#cpp-numeric-constant\\\"}]},{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(elif)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.preprocessor.elif.fortran\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\n)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-comments\\\"},{\\\"include\\\":\\\"#cpp-numeric-constant\\\"},{\\\"include\\\":\\\"#preprocessor-logical-operators\\\"},{\\\"include\\\":\\\"#preprocessor-arithmetic-operators\\\"},{\\\"include\\\":\\\"#preprocessor-defined-function\\\"},{\\\"include\\\":\\\"#preprocessor-line-continuation-operator\\\"}]},{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(endif)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.preprocessor.endif.fortran\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\n)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-comments\\\"}]}]},\\\"preprocessor-include-statement\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*(include)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.preprocessor.include.fortran\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.include.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-comments\\\"},{\\\"include\\\":\\\"#preprocessor-string-constant\\\"},{\\\"begin\\\":\\\"<\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.preprocessor.fortran\\\"}},\\\"end\\\":\\\">\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.preprocessor.fortran\\\"}},\\\"name\\\":\\\"string.quoted.other.lt-gt.include.preprocessor.fortran\\\"},{\\\"include\\\":\\\"#line-continuation-operator\\\"}]},\\\"preprocessor-line-continuation-operator\\\":{\\\"begin\\\":\\\"\\\\\\\\s*(\\\\\\\\\\\\\\\\)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.character.escape.line-continuation.preprocessor.fortran\\\"}},\\\"end\\\":\\\"(?i)^\\\"},\\\"preprocessor-logical-operators\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.logical.preprocessor.and.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.logical.preprocessor.equals.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.operator.logical.preprocessor.not_equals.fortran\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.operator.logical.preprocessor.or.fortran\\\"},\\\"5\\\":{\\\"name\\\":\\\"keyword.operator.logical.preprocessor.less_eq.fortran\\\"},\\\"6\\\":{\\\"name\\\":\\\"keyword.operator.logical.preprocessor.more_eq.fortran\\\"},\\\"7\\\":{\\\"name\\\":\\\"keyword.operator.logical.preprocessor.less.fortran\\\"},\\\"8\\\":{\\\"name\\\":\\\"keyword.operator.logical.preprocessor.more.fortran\\\"},\\\"9\\\":{\\\"name\\\":\\\"keyword.operator.logical.preprocessor.complementary.fortran\\\"},\\\"10\\\":{\\\"name\\\":\\\"keyword.operator.logical.preprocessor.xor.fortran\\\"},\\\"11\\\":{\\\"name\\\":\\\"keyword.operator.logical.preprocessor.bitand.fortran\\\"},\\\"12\\\":{\\\"name\\\":\\\"keyword.operator.logical.preprocessor.not.fortran\\\"},\\\"13\\\":{\\\"name\\\":\\\"keyword.operator.logical.preprocessor.bitor.fortran\\\"}},\\\"match\\\":\\\"(&&)|(==)|(!=)|(\\\\\\\\|\\\\\\\\|)|(<=)|(>=)|(<)|(>)|(~)|(\\\\\\\\^)|(&)|(!)|(\\\\\\\\|)\\\",\\\"name\\\":\\\"keyword.operator.logical.preprocessor.fortran\\\"},\\\"preprocessor-operators\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-line-continuation-operator\\\"},{\\\"include\\\":\\\"#preprocessor-logical-operators\\\"},{\\\"include\\\":\\\"#preprocessor-arithmetic-operators\\\"}]},\\\"preprocessor-pragma-statement\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(pragma)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.preprocessor.pragma.fortran\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.pragma.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-comments\\\"},{\\\"include\\\":\\\"#preprocessor-string-constant\\\"}]},\\\"preprocessor-statements\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-define-statement\\\"},{\\\"include\\\":\\\"#preprocessor-error-statement\\\"},{\\\"include\\\":\\\"#preprocessor-include-statement\\\"},{\\\"include\\\":\\\"#preprocessor-preprocessor-pragma-statement\\\"},{\\\"include\\\":\\\"#preprocessor-undefine-statement\\\"}]},\\\"preprocessor-string-constant\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.preprocessor.fortran\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.preprocessor.fortran\\\"}},\\\"name\\\":\\\"string.quoted.double.include.preprocessor.fortran\\\"},{\\\"begin\\\":\\\"'\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.preprocessor.fortran\\\"}},\\\"end\\\":\\\"'\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.preprocessor.fortran\\\"}},\\\"name\\\":\\\"string.quoted.single.include.preprocessor.fortran\\\"}]},\\\"preprocessor-undefine-statement\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(undef)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.preprocessor.undef.fortran\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.undef.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-comments\\\"},{\\\"include\\\":\\\"#preprocessor-line-continuation-operator\\\"}]},\\\"private-attribute\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.private.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(private)\\\\\\\\b\\\"},\\\"procedure-call-dummy-variable\\\":{\\\"match\\\":\\\"(?i)\\\\\\\\s*([a-z]\\\\\\\\w*)(?=\\\\\\\\s*=)(?!\\\\\\\\s*==)\\\",\\\"name\\\":\\\"variable.parameter.dummy-variable.fortran.modern\\\"},\\\"procedure-definition\\\":{\\\"begin\\\":\\\"(?i)(?=[^'\\\\\\\";!\\\\\\\\n]*\\\\\\\\bmodule\\\\\\\\s+procedure\\\\\\\\b)\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.procedure.fortran\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(module\\\\\\\\s+procedure)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.procedure.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b([a-z]\\\\\\\\w*)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.procedure.fortran\\\"}},\\\"end\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(?:(end\\\\\\\\s*procedure)(?:\\\\\\\\s+([a-z_]\\\\\\\\w*))?|(end))\\\\\\\\b\\\\\\\\s*([^;!\\\\\\\\n]+)?(?=[;!\\\\\\\\n])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.endprocedure.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.procedure.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.other.endprocedure.fortran\\\"},\\\"4\\\":{\\\"name\\\":\\\"invalid.error.procedure-definition.fortran\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(?!\\\\\\\\s*[;!\\\\\\\\n])\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.first-line.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#invalid-character\\\"}]},{\\\"begin\\\":\\\"(?i)(?!\\\\\\\\s*(?:contains\\\\\\\\b|end\\\\\\\\s*[;!\\\\\\\\n]|end\\\\\\\\s*procedure\\\\\\\\b))\\\",\\\"end\\\":\\\"(?i)(?=\\\\\\\\s*(?:contains\\\\\\\\b|end\\\\\\\\s*[;!\\\\\\\\n]|end\\\\\\\\s*procedure\\\\\\\\b))\\\",\\\"name\\\":\\\"meta.block.specification.procedure.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"(?i)\\\\\\\\s*(contains)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.contains.fortran\\\"}},\\\"end\\\":\\\"(?i)(?=\\\\\\\\s*end(?:\\\\\\\\s*[;!\\\\\\\\n]|\\\\\\\\s*procedure\\\\\\\\b))\\\",\\\"name\\\":\\\"meta.block.contains.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]}]}]}]},\\\"procedure-name\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.procedure.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b([a-z]\\\\\\\\w*)\\\\\\\\b\\\"},\\\"procedure-name-list\\\":{\\\"begin\\\":\\\"(?i)(?=\\\\\\\\s*[a-z])\\\",\\\"contentName\\\":\\\"meta.name-list.fortran\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?!\\\\\\\\s*\\\\\\\\n)\\\",\\\"end\\\":\\\"(,)|(?=[!;\\\\\\\\n])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.comma.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#procedure-name\\\"},{\\\"include\\\":\\\"#pointer-operators\\\"}]}]},\\\"procedure-specification-statement\\\":{\\\"begin\\\":\\\"(?i)(?=\\\\\\\\bprocedure\\\\\\\\b)\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.specification.procedure.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#procedure-type\\\"},{\\\"begin\\\":\\\"(?=\\\\\\\\s*(,|::|\\\\\\\\())\\\",\\\"contentName\\\":\\\"meta.attribute-list.procedure.fortran\\\",\\\"end\\\":\\\"(::)|(?=[;!\\\\\\\\n])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.double-colon.fortran\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"(,)|^|(?<=&)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.comma.fortran\\\"}},\\\"end\\\":\\\"(?=::|[,\\\\\\\\&;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#access-attribute\\\"},{\\\"include\\\":\\\"#intent-attribute\\\"},{\\\"include\\\":\\\"#optional-attribute\\\"},{\\\"include\\\":\\\"#pointer-attribute\\\"},{\\\"include\\\":\\\"#protected-attribute\\\"},{\\\"include\\\":\\\"#save-attribute\\\"},{\\\"include\\\":\\\"#invalid-word\\\"}]}]},{\\\"include\\\":\\\"#procedure-name-list\\\"}]},\\\"procedure-type\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\b(procedure)\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.procedure.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"contentName\\\":\\\"meta.type-spec.fortran\\\",\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#types\\\"},{\\\"include\\\":\\\"#procedure-name\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.procedure.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(procedure)\\\\\\\\b\\\"}]},\\\"program-definition\\\":{\\\"begin\\\":\\\"(?i)(?=\\\\\\\\b(program)\\\\\\\\b)\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.program.fortran\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.program.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(program)\\\\\\\\b\\\"},{\\\"applyEndPatternLast\\\":1,\\\"begin\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b([a-z]\\\\\\\\w*)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.program.fortran\\\"}},\\\"end\\\":\\\"(?i)\\\\\\\\b(?:(end\\\\\\\\s*program)(?:\\\\\\\\s+([a-z_]\\\\\\\\w*))?|(end))\\\\\\\\b\\\\\\\\s*([^;!\\\\\\\\n]+)?(?=[;!\\\\\\\\n])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.endprogram.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.program.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.control.endprogram.fortran\\\"},\\\"4\\\":{\\\"name\\\":\\\"invalid.error.program-definition.fortran\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G\\\",\\\"end\\\":\\\"(?i)(?=\\\\\\\\bend(?:\\\\\\\\s*[;!\\\\\\\\n]|\\\\\\\\s*program\\\\\\\\b))\\\",\\\"name\\\":\\\"meta.block.specification.program.fortran\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\b(contains)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.contains.fortran\\\"}},\\\"end\\\":\\\"(?i)(?=end(?:\\\\\\\\s*[;!\\\\\\\\n]|\\\\\\\\s*program\\\\\\\\b))\\\",\\\"name\\\":\\\"meta.block.contains.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$base\\\"}]},{\\\"include\\\":\\\"$base\\\"}]}]}]},\\\"protected-attribute\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.protected.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(protected)\\\\\\\\b\\\"},\\\"public-attribute\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.public.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(public)\\\\\\\\b\\\"},\\\"pure-attribute\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.impure.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.modifier.pure.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(?:(impure)|(pure))\\\\\\\\b\\\"},\\\"recursive-attribute\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.non_recursive.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.modifier.recursive.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(?:(non_recursive)|(recursive))\\\\\\\\b\\\"},\\\"result-statement\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(result)\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.result.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#dummy-variable\\\"}]},\\\"return-statement\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(return)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.return.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.statement.control.return.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#invalid-character\\\"}]},\\\"save-attribute\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.save.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(save)\\\\\\\\b\\\"},\\\"select-case-construct\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(select\\\\\\\\s*case)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.selectcase.fortran\\\"}},\\\"end\\\":\\\"(?i)\\\\\\\\b(end\\\\\\\\s*select)\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.endselect.fortran\\\"}},\\\"name\\\":\\\"meta.block.select.case.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses\\\"},{\\\"begin\\\":\\\"(?i)\\\\\\\\b(case)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.case.fortran\\\"}},\\\"end\\\":\\\"(?i)(?=[;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.default.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(default)\\\\\\\\b\\\"},{\\\"include\\\":\\\"#parentheses\\\"},{\\\"include\\\":\\\"#invalid-word\\\"}]},{\\\"include\\\":\\\"$base\\\"}]},\\\"select-rank-construct\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(select\\\\\\\\s*rank)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.selectrank.fortran\\\"}},\\\"end\\\":\\\"(?i)\\\\\\\\b(end\\\\\\\\s*select)\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.endselect.fortran\\\"}},\\\"name\\\":\\\"meta.block.select.rank.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses\\\"},{\\\"begin\\\":\\\"(?i)\\\\\\\\b(rank)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.rank.fortran\\\"}},\\\"end\\\":\\\"(?i)(?=[;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.default.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(default)\\\\\\\\b\\\"},{\\\"include\\\":\\\"#parentheses\\\"},{\\\"include\\\":\\\"#invalid-word\\\"}]},{\\\"include\\\":\\\"$base\\\"}]},\\\"select-type-construct\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(select\\\\\\\\s*type)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.selecttype.fortran\\\"}},\\\"end\\\":\\\"(?i)\\\\\\\\b(end\\\\\\\\s*select)\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.endselect.fortran\\\"}},\\\"name\\\":\\\"meta.block.select.type.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses\\\"},{\\\"begin\\\":\\\"(?i)\\\\\\\\b(?:(class)|(type))\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.class.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.type.fortran\\\"}},\\\"end\\\":\\\"(?i)(?=[;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.default.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(default)\\\\\\\\b\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.is.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(is)\\\\\\\\b\\\"},{\\\"include\\\":\\\"#parentheses\\\"},{\\\"include\\\":\\\"#invalid-word\\\"}]},{\\\"include\\\":\\\"$base\\\"}]},\\\"sequence-attribute\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.sequence.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(sequence)\\\\\\\\b\\\"},\\\"specification-statements\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#attribute-specification-statement\\\"},{\\\"include\\\":\\\"#common-statement\\\"},{\\\"include\\\":\\\"#data-statement\\\"},{\\\"include\\\":\\\"#equivalence-statement\\\"},{\\\"include\\\":\\\"#implicit-statement\\\"},{\\\"include\\\":\\\"#namelist-statement\\\"},{\\\"include\\\":\\\"#use-statement\\\"}]},\\\"stop-statement\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(stop)\\\\\\\\b(?:\\\\\\\\s*\\\\\\\\b([a-z]\\\\\\\\w*)\\\\\\\\b)?\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.stop.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"meta.label.stop.stop\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.statement.control.stop.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#string-operators\\\"},{\\\"include\\\":\\\"#invalid-character\\\"}]},\\\"string-constant\\\":{\\\"patterns\\\":[{\\\"applyEndPatternLast\\\":1,\\\"begin\\\":\\\"'\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.fortran\\\"}},\\\"end\\\":\\\"'\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.fortran\\\"}},\\\"name\\\":\\\"string.quoted.single.fortran\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"''\\\",\\\"name\\\":\\\"constant.character.escape.apostrophe.fortran\\\"}]},{\\\"applyEndPatternLast\\\":1,\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.fortran\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.fortran\\\"}},\\\"name\\\":\\\"string.quoted.double.fortran\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\"\\\\\\\"\\\",\\\"name\\\":\\\"constant.character.escape.quote.fortran\\\"}]}]},\\\"string-line-continuation-operator\\\":{\\\"begin\\\":\\\"(&)(?=\\\\\\\\s*\\\\\\\\n)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.line-continuation.fortran\\\"}},\\\"end\\\":\\\"(?i)^(?:(?=\\\\\\\\s*[^\\\\\\\\s!\\\\\\\\&])|\\\\\\\\s*(&))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.line-continuation.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"match\\\":\\\"\\\\\\\\S.*\\\",\\\"name\\\":\\\"invalid.error.string-line-cont.fortran\\\"}]},\\\"string-operators\\\":{\\\"match\\\":\\\"(//)\\\",\\\"name\\\":\\\"keyword.other.concatination.fortran\\\"},\\\"submodule-definition\\\":{\\\"begin\\\":\\\"(?i)(?=\\\\\\\\b(submodule)\\\\\\\\s*\\\\\\\\()\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.submodule.fortran\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(submodule)\\\\\\\\s*(\\\\\\\\()\\\\\\\\s*(\\\\\\\\w+)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.submodule.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.class.submodule.fortran\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"patterns\\\":[]},{\\\"applyEndPatternLast\\\":1,\\\"begin\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b([a-z]\\\\\\\\w*)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.module.submodule.fortran\\\"}},\\\"end\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(?:(end\\\\\\\\s*submodule)(?:\\\\\\\\s+([a-z_]\\\\\\\\w*))?|(end))\\\\\\\\b\\\\\\\\s*([^;!\\\\\\\\n]+)?(?=[;!\\\\\\\\n])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.endsubmodule.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.module.submodule.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.other.endsubmodule.fortran\\\"},\\\"4\\\":{\\\"name\\\":\\\"invalid.error.submodule.fortran\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G\\\",\\\"end\\\":\\\"(?i)(?=\\\\\\\\bend(?:\\\\\\\\s*[;!\\\\\\\\n]|\\\\\\\\s*submodule\\\\\\\\b))\\\",\\\"name\\\":\\\"meta.block.specification.submodule.fortran\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\b(contains)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.contains.fortran\\\"}},\\\"end\\\":\\\"(?i)(?=\\\\\\\\s*end(?:\\\\\\\\s*[;!\\\\\\\\n]|\\\\\\\\s*submodule\\\\\\\\b))\\\",\\\"name\\\":\\\"meta.block.contains.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$base\\\"}]},{\\\"include\\\":\\\"$base\\\"}]}]}]},\\\"subroutine-definition\\\":{\\\"begin\\\":\\\"(?i)(?=([^:'\\\\\\\";!\\\\\\\\n](?!\\\\\\\\bend))*\\\\\\\\bsubroutine\\\\\\\\b)\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.subroutine.fortran\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)(?=\\\\\\\\G\\\\\\\\s*(?!\\\\\\\\bsubroutine\\\\\\\\b))\\\",\\\"end\\\":\\\"(?i)(?=\\\\\\\\bsubroutine\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.attribute-list.subroutine.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#elemental-attribute\\\"},{\\\"include\\\":\\\"#module-attribute\\\"},{\\\"include\\\":\\\"#pure-attribute\\\"},{\\\"include\\\":\\\"#recursive-attribute\\\"},{\\\"include\\\":\\\"#invalid-word\\\"}]},{\\\"begin\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(subroutine)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.subroutine.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b([a-z]\\\\\\\\w*)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.subroutine.fortran\\\"}},\\\"end\\\":\\\"(?i)\\\\\\\\b(?:(end\\\\\\\\s*subroutine)(?:\\\\\\\\s+([a-z_]\\\\\\\\w*))?|(end))\\\\\\\\b\\\\\\\\s*([^;!\\\\\\\\n]+)?(?=[;!\\\\\\\\n])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.endsubroutine.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.subroutine.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.other.endsubroutine.fortran\\\"},\\\"4\\\":{\\\"name\\\":\\\"invalid.error.subroutine.fortran\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(?!\\\\\\\\s*[;!\\\\\\\\n])\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.first-line.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#dummy-variable-list\\\"},{\\\"include\\\":\\\"#language-binding-attribute\\\"}]},{\\\"begin\\\":\\\"(?i)(?!\\\\\\\\bend(?:\\\\\\\\s*[;!\\\\\\\\n]|\\\\\\\\s*subroutine\\\\\\\\b))\\\",\\\"end\\\":\\\"(?i)(?=\\\\\\\\bend(?:\\\\\\\\s*[;!\\\\\\\\n]|\\\\\\\\s*subroutine\\\\\\\\b))\\\",\\\"name\\\":\\\"meta.block.specification.subroutine.fortran\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\b(contains)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.contains.fortran\\\"}},\\\"end\\\":\\\"(?i)(?=end(?:\\\\\\\\s*[;!\\\\\\\\n]|\\\\\\\\s*subroutine\\\\\\\\b))\\\",\\\"name\\\":\\\"meta.block.contains.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$base\\\"}]},{\\\"include\\\":\\\"$base\\\"}]}]}]}]},\\\"sync-all-statement\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(sync (?:all|memory))(\\\\\\\\s*(?=\\\\\\\\())?\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.sync-all-memory.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"name\\\":\\\"meta.statement.sync-all-memory.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]},\\\"sync-statement\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(sync (?:images|team))\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.sync-images-team.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"name\\\":\\\"meta.statement.sync-images-team.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]},\\\"target-attribute\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.target.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(target)\\\\\\\\b\\\"},\\\"type-specification-statements\\\":{\\\"begin\\\":\\\"(?i)(?=\\\\\\\\b(?:character|class|complex|double\\\\\\\\s*precision|double\\\\\\\\s*complex|integer|logical|real|type|dimension)\\\\\\\\b(?![^'\\\\\\\";!\\\\\\\\n:]*\\\\\\\\bfunction\\\\\\\\b))\\\",\\\"end\\\":\\\"(?=[);!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.specification.type.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#types\\\"},{\\\"begin\\\":\\\"(?=\\\\\\\\s*(,|::))\\\",\\\"contentName\\\":\\\"meta.attribute-list.type-specification-statements.fortran\\\",\\\"end\\\":\\\"(::)|(?=[;!\\\\\\\\n])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.double-colon.fortran\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"(,)|^|(?<=&)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.comma.fortran\\\"}},\\\"end\\\":\\\"(?=::|[,\\\\\\\\&;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#access-attribute\\\"},{\\\"include\\\":\\\"#allocatable-attribute\\\"},{\\\"include\\\":\\\"#asynchronous-attribute\\\"},{\\\"include\\\":\\\"#codimension-attribute\\\"},{\\\"include\\\":\\\"#contiguous-attribute\\\"},{\\\"include\\\":\\\"#dimension-attribute\\\"},{\\\"include\\\":\\\"#external-attribute\\\"},{\\\"include\\\":\\\"#intent-attribute\\\"},{\\\"include\\\":\\\"#intrinsic-attribute\\\"},{\\\"include\\\":\\\"#language-binding-attribute\\\"},{\\\"include\\\":\\\"#optional-attribute\\\"},{\\\"include\\\":\\\"#parameter-attribute\\\"},{\\\"include\\\":\\\"#pointer-attribute\\\"},{\\\"include\\\":\\\"#protected-attribute\\\"},{\\\"include\\\":\\\"#save-attribute\\\"},{\\\"include\\\":\\\"#target-attribute\\\"},{\\\"include\\\":\\\"#value-attribute\\\"},{\\\"include\\\":\\\"#volatile-attribute\\\"},{\\\"include\\\":\\\"#invalid-word\\\"}]}]},{\\\"include\\\":\\\"#name-list\\\"}]},\\\"types\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#character-type\\\"},{\\\"include\\\":\\\"#derived-type\\\"},{\\\"include\\\":\\\"#logical-type\\\"},{\\\"include\\\":\\\"#numeric-type\\\"}]},\\\"unnamed-control-constructs\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#associate-construct\\\"},{\\\"include\\\":\\\"#block-construct\\\"},{\\\"include\\\":\\\"#critical-construct\\\"},{\\\"include\\\":\\\"#do-construct\\\"},{\\\"include\\\":\\\"#forall-construct\\\"},{\\\"include\\\":\\\"#if-construct\\\"},{\\\"include\\\":\\\"#select-case-construct\\\"},{\\\"include\\\":\\\"#select-type-construct\\\"},{\\\"include\\\":\\\"#select-rank-construct\\\"},{\\\"include\\\":\\\"#where-construct\\\"}]},\\\"use-statement\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(use)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.use.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.statement.use.fortran\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?=\\\\\\\\s*(,|::|\\\\\\\\())\\\",\\\"contentName\\\":\\\"meta.attribute-list.namelist.fortran\\\",\\\"end\\\":\\\"(::)|(?=[;!\\\\\\\\n])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.double-colon.fortran\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"(,)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.comma.fortran\\\"}},\\\"end\\\":\\\"(?=::|[,;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#intrinsic-attribute\\\"},{\\\"include\\\":\\\"#non-intrinsic-attribute\\\"},{\\\"include\\\":\\\"#invalid-word\\\"}]}]},{\\\"begin\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b([a-z]\\\\\\\\w*)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.class.module.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(,)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.comma.fortran\\\"}},\\\"end\\\":\\\"(?=::|[;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(only\\\\\\\\s*:)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.only.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#operator-keyword\\\"},{\\\"include\\\":\\\"$base\\\"}]},{\\\"begin\\\":\\\"(?i)(?=\\\\\\\\s*[a-z])\\\",\\\"contentName\\\":\\\"meta.name-list.fortran\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#operator-keyword\\\"},{\\\"include\\\":\\\"$base\\\"}]}]}]}]},\\\"user-defined-operators\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.user-defined.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\s*(\\\\\\\\.[a-z]+\\\\\\\\.)\\\"},\\\"value-attribute\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.value.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(value)\\\\\\\\b\\\"},\\\"variable\\\":{\\\"applyEndPatternLast\\\":1,\\\"begin\\\":\\\"(?i)\\\\\\\\b(?=[a-z])\\\",\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"name\\\":\\\"meta.parameter.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#brackets\\\"},{\\\"include\\\":\\\"#derived-type-operators\\\"},{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"},{\\\"include\\\":\\\"#word\\\"}]},\\\"volatile-attribute\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.volatile.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(volatile)\\\\\\\\b\\\"},\\\"where-construct\\\":{\\\"patterns\\\":[{\\\"applyEndPatternLast\\\":1,\\\"begin\\\":\\\"(?i)\\\\\\\\b(where)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.where.fortran\\\"}},\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#logical-control-expression\\\"},{\\\"begin\\\":\\\"(?<=\\\\\\\\))(?=\\\\\\\\s*[;!\\\\\\\\n])\\\",\\\"end\\\":\\\"(?i)\\\\\\\\b(end\\\\\\\\s*where)\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.endwhere.fortran\\\"}},\\\"name\\\":\\\"meta.block.where.fortran\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(else\\\\\\\\s*where)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.elsewhere.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses\\\"},{\\\"include\\\":\\\"#invalid-word\\\"}]},{\\\"include\\\":\\\"$base\\\"}]},{\\\"begin\\\":\\\"(?i)(?<=\\\\\\\\))(?!\\\\\\\\s*[;!\\\\\\\\n])\\\",\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"meta.statement.control.where.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$base\\\"}]}]}]},\\\"while-attribute\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(while)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.while.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses\\\"},{\\\"include\\\":\\\"#invalid-word\\\"}]},\\\"word\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?i)(?:\\\\\\\\G|(?<=%))\\\\\\\\s*\\\\\\\\b([a-z]\\\\\\\\w*)\\\\\\\\b\\\"}]}},\\\"scopeName\\\":\\\"source.fortran.free\\\",\\\"aliases\\\":[\\\"f90\\\",\\\"f95\\\",\\\"f03\\\",\\\"f08\\\",\\\"f18\\\"]}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/fortran-free-form.mjs\n"));

/***/ })

}]);