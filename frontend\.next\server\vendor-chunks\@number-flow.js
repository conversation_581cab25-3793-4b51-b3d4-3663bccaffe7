"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@number-flow";
exports.ids = ["vendor-chunks/@number-flow"];
exports.modules = {

/***/ "(ssr)/./node_modules/@number-flow/react/dist/NumberFlow-client-48rw3j0J.mjs":
/*!*****************************************************************************!*\
  !*** ./node_modules/@number-flow/react/dist/NumberFlow-client-48rw3j0J.mjs ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   N: () => (/* binding */ NumberFlow),\n/* harmony export */   a: () => (/* binding */ NumberFlowElement),\n/* harmony export */   b: () => (/* binding */ NumberFlowGroup)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var number_flow_lite__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! number-flow/lite */ \"(ssr)/./node_modules/number-flow/dist/lite.mjs\");\n/* harmony import */ var esm_env__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! esm-env */ \"(ssr)/./node_modules/esm-env/index.js\");\n/* __next_internal_client_entry_do_not_use__ N,a,b auto */ \n\n\nconst REACT_MAJOR = parseInt(react__WEBPACK_IMPORTED_MODULE_0__.version.match(/^(\\d+)\\./)?.[1]);\nconst isReact19 = REACT_MAJOR >= 19;\n// Can't wait to not have to do this in React 19:\nconst OBSERVED_ATTRIBUTES = [\n    'data',\n    'digits'\n];\nclass NumberFlowElement extends number_flow_lite__WEBPACK_IMPORTED_MODULE_1__[\"default\"] {\n    attributeChangedCallback(attr, _oldValue, newValue) {\n        this[attr] = JSON.parse(newValue);\n    }\n}\nNumberFlowElement.observedAttributes = isReact19 ? [] : OBSERVED_ATTRIBUTES;\n(0,number_flow_lite__WEBPACK_IMPORTED_MODULE_1__.define)('number-flow-react', NumberFlowElement);\n// You're supposed to cache these between uses:\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number/toLocaleString\n// Serialize to strings b/c React:\nconst formatters = {};\n// Tiny workaround to support React 19 until it's released:\nconst serialize = isReact19 ? (p)=>p : JSON.stringify;\nfunction splitProps(props) {\n    const { transformTiming, spinTiming, opacityTiming, animated, respectMotionPreference, trend, plugins, ...rest } = props;\n    return [\n        {\n            transformTiming,\n            spinTiming,\n            opacityTiming,\n            animated,\n            respectMotionPreference,\n            trend,\n            plugins\n        },\n        rest\n    ];\n}\n// We need a class component to use getSnapshotBeforeUpdate:\nclass NumberFlowImpl extends react__WEBPACK_IMPORTED_MODULE_0__.Component {\n    // Update the non-`data` props to avoid JSON serialization\n    // Data needs to be set in render still:\n    updateProperties(prevProps) {\n        if (!this.el) return;\n        this.el.batched = !this.props.isolate;\n        const [nonData] = splitProps(this.props);\n        Object.entries(nonData).forEach(([k, v])=>{\n            // @ts-ignore\n            this.el[k] = v ?? NumberFlowElement.defaultProps[k];\n        });\n        if (prevProps?.onAnimationsStart) this.el.removeEventListener('animationsstart', prevProps.onAnimationsStart);\n        if (this.props.onAnimationsStart) this.el.addEventListener('animationsstart', this.props.onAnimationsStart);\n        if (prevProps?.onAnimationsFinish) this.el.removeEventListener('animationsfinish', prevProps.onAnimationsFinish);\n        if (this.props.onAnimationsFinish) this.el.addEventListener('animationsfinish', this.props.onAnimationsFinish);\n    }\n    componentDidMount() {\n        this.updateProperties();\n        if (isReact19 && this.el) {\n            // React 19 needs this because the attributeChangedCallback isn't called:\n            this.el.digits = this.props.digits;\n            this.el.data = this.props.data;\n        }\n    }\n    getSnapshotBeforeUpdate(prevProps) {\n        this.updateProperties(prevProps);\n        if (prevProps.data !== this.props.data) {\n            if (this.props.group) {\n                this.props.group.willUpdate();\n                return ()=>this.props.group?.didUpdate();\n            }\n            if (!this.props.isolate) {\n                this.el?.willUpdate();\n                return ()=>this.el?.didUpdate();\n            }\n        }\n        return null;\n    }\n    componentDidUpdate(_, __, didUpdate) {\n        didUpdate?.();\n    }\n    handleRef(el) {\n        if (this.props.innerRef) this.props.innerRef.current = el;\n        this.el = el;\n    }\n    render() {\n        const [_, { innerRef, className, data, willChange, isolate, group, digits, onAnimationsStart, onAnimationsFinish, ...rest }] = splitProps(this.props);\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"number-flow-react\", {\n            ref: this.handleRef,\n            \"data-will-change\": willChange ? '' : undefined,\n            // Have to rename this:\n            class: className,\n            ...rest,\n            dangerouslySetInnerHTML: {\n                __html: esm_env__WEBPACK_IMPORTED_MODULE_2__.BROWSER ? '' : (0,number_flow_lite__WEBPACK_IMPORTED_MODULE_1__.renderInnerHTML)(data)\n            },\n            suppressHydrationWarning: true,\n            digits: serialize(digits),\n            // Make sure data is set last, everything else is updated:\n            data: serialize(data)\n        });\n    }\n    constructor(props){\n        super(props);\n        this.handleRef = this.handleRef.bind(this);\n    }\n}\nconst NumberFlow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function NumberFlow({ value, locales, format, prefix, suffix, ...props }, _ref) {\n    react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle(_ref, {\n        \"NumberFlow.NumberFlow.useImperativeHandle\": ()=>ref.current\n    }[\"NumberFlow.NumberFlow.useImperativeHandle\"], []);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef();\n    const group = react__WEBPACK_IMPORTED_MODULE_0__.useContext(NumberFlowGroupContext);\n    group?.useRegister(ref);\n    const localesString = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"NumberFlow.NumberFlow.useMemo[localesString]\": ()=>locales ? JSON.stringify(locales) : ''\n    }[\"NumberFlow.NumberFlow.useMemo[localesString]\"], [\n        locales\n    ]);\n    const formatString = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"NumberFlow.NumberFlow.useMemo[formatString]\": ()=>format ? JSON.stringify(format) : ''\n    }[\"NumberFlow.NumberFlow.useMemo[formatString]\"], [\n        format\n    ]);\n    const data = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"NumberFlow.NumberFlow.useMemo[data]\": ()=>{\n            const formatter = formatters[`${localesString}:${formatString}`] ??= new Intl.NumberFormat(locales, format);\n            return (0,number_flow_lite__WEBPACK_IMPORTED_MODULE_1__.formatToData)(value, formatter, prefix, suffix);\n        }\n    }[\"NumberFlow.NumberFlow.useMemo[data]\"], [\n        value,\n        localesString,\n        formatString,\n        prefix,\n        suffix\n    ]);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(NumberFlowImpl, {\n        ...props,\n        group: group,\n        data: data,\n        innerRef: ref\n    });\n});\nconst NumberFlowGroupContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(undefined);\nfunction NumberFlowGroup({ children }) {\n    const flows = react__WEBPACK_IMPORTED_MODULE_0__.useRef(new Set());\n    const updating = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const pending = react__WEBPACK_IMPORTED_MODULE_0__.useRef(new WeakMap());\n    const value = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"NumberFlowGroup.useMemo[value]\": ()=>({\n                useRegister (ref) {\n                    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n                        \"NumberFlowGroup.useMemo[value]\": ()=>{\n                            flows.current.add(ref);\n                            return ({\n                                \"NumberFlowGroup.useMemo[value]\": ()=>{\n                                    flows.current.delete(ref);\n                                }\n                            })[\"NumberFlowGroup.useMemo[value]\"];\n                        }\n                    }[\"NumberFlowGroup.useMemo[value]\"], []);\n                },\n                willUpdate () {\n                    if (updating.current) return;\n                    updating.current = true;\n                    flows.current.forEach({\n                        \"NumberFlowGroup.useMemo[value]\": (ref)=>{\n                            const f = ref.current;\n                            if (!f || !f.created) return;\n                            f.willUpdate();\n                            pending.current.set(f, true);\n                        }\n                    }[\"NumberFlowGroup.useMemo[value]\"]);\n                },\n                didUpdate () {\n                    flows.current.forEach({\n                        \"NumberFlowGroup.useMemo[value]\": (ref)=>{\n                            const f = ref.current;\n                            if (!f || !pending.current.get(f)) return;\n                            f.didUpdate();\n                            pending.current.delete(f);\n                        }\n                    }[\"NumberFlowGroup.useMemo[value]\"]);\n                    updating.current = false;\n                }\n            })\n    }[\"NumberFlowGroup.useMemo[value]\"], []);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(NumberFlowGroupContext.Provider, {\n        value: value\n    }, children);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@number-flow/react/dist/NumberFlow-client-48rw3j0J.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@number-flow/react/dist/index.mjs":
/*!********************************************************!*\
  !*** ./node_modules/@number-flow/react/dist/index.mjs ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NumberFlowElement: () => (/* reexport safe */ _NumberFlow_client_48rw3j0J_mjs__WEBPACK_IMPORTED_MODULE_3__.a),\n/* harmony export */   NumberFlowGroup: () => (/* reexport safe */ _NumberFlow_client_48rw3j0J_mjs__WEBPACK_IMPORTED_MODULE_3__.b),\n/* harmony export */   continuous: () => (/* reexport safe */ number_flow_plugins__WEBPACK_IMPORTED_MODULE_2__.continuous),\n/* harmony export */   \"default\": () => (/* reexport safe */ _NumberFlow_client_48rw3j0J_mjs__WEBPACK_IMPORTED_MODULE_3__.N),\n/* harmony export */   useCanAnimate: () => (/* binding */ useCanAnimate),\n/* harmony export */   useIsSupported: () => (/* binding */ useIsSupported),\n/* harmony export */   usePrefersReducedMotion: () => (/* binding */ usePrefersReducedMotion)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var number_flow_lite__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! number-flow/lite */ \"(ssr)/./node_modules/number-flow/dist/lite.mjs\");\n/* harmony import */ var number_flow_plugins__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! number-flow/plugins */ \"(ssr)/./node_modules/number-flow/dist/plugins.mjs\");\n/* harmony import */ var _NumberFlow_client_48rw3j0J_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./NumberFlow-client-48rw3j0J.mjs */ \"(ssr)/./node_modules/@number-flow/react/dist/NumberFlow-client-48rw3j0J.mjs\");\n\n\n\n\n\nconst useIsSupported = ()=>react__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore(()=>()=>{}, ()=>number_flow_lite__WEBPACK_IMPORTED_MODULE_1__.canAnimate, ()=>false);\nconst usePrefersReducedMotion = ()=>react__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore((cb)=>{\n        number_flow_lite__WEBPACK_IMPORTED_MODULE_1__.prefersReducedMotion?.addEventListener('change', cb);\n        return ()=>number_flow_lite__WEBPACK_IMPORTED_MODULE_1__.prefersReducedMotion?.removeEventListener('change', cb);\n    }, ()=>number_flow_lite__WEBPACK_IMPORTED_MODULE_1__.prefersReducedMotion.matches, ()=>false);\nfunction useCanAnimate({ respectMotionPreference = true } = {}) {\n    const isSupported = useIsSupported();\n    const reducedMotion = usePrefersReducedMotion();\n    return isSupported && (!respectMotionPreference || !reducedMotion);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG51bWJlci1mbG93L3JlYWN0L2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQStCO0FBQ3FDO0FBQ2hDO0FBQzBFOztBQUU5RywyQkFBMkIsdURBQTBCLFdBQVcsTUFBTSx3REFBVTtBQUNoRixvQ0FBb0MsdURBQTBCO0FBQzlELFFBQVEsa0VBQW9CO0FBQzVCLG1CQUFtQixrRUFBb0I7QUFDdkMsS0FBSyxNQUFNLGtFQUFvQjtBQUMvQix5QkFBeUIsaUNBQWlDLElBQUk7QUFDOUQ7QUFDQTtBQUNBO0FBQ0E7O0FBRWtFIiwic291cmNlcyI6WyJEOlxcc29mdFxcU2VhUHJvamVjdFxcZ2l0aHViX29wZW5fcHJvamVjdF9zdWNjZXNzXFxzdW5hXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxAbnVtYmVyLWZsb3dcXHJlYWN0XFxkaXN0XFxpbmRleC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgY2FuQW5pbWF0ZSwgcHJlZmVyc1JlZHVjZWRNb3Rpb24gfSBmcm9tICdudW1iZXItZmxvdy9saXRlJztcbmV4cG9ydCAqIGZyb20gJ251bWJlci1mbG93L3BsdWdpbnMnO1xuZXhwb3J0IHsgYSBhcyBOdW1iZXJGbG93RWxlbWVudCwgYiBhcyBOdW1iZXJGbG93R3JvdXAsIE4gYXMgZGVmYXVsdCB9IGZyb20gJy4vTnVtYmVyRmxvdy1jbGllbnQtNDhydzNqMEoubWpzJztcblxuY29uc3QgdXNlSXNTdXBwb3J0ZWQgPSAoKT0+UmVhY3QudXNlU3luY0V4dGVybmFsU3RvcmUoKCk9PigpPT57fSwgKCk9PmNhbkFuaW1hdGUsICgpPT5mYWxzZSk7XG5jb25zdCB1c2VQcmVmZXJzUmVkdWNlZE1vdGlvbiA9ICgpPT5SZWFjdC51c2VTeW5jRXh0ZXJuYWxTdG9yZSgoY2IpPT57XG4gICAgICAgIHByZWZlcnNSZWR1Y2VkTW90aW9uPy5hZGRFdmVudExpc3RlbmVyKCdjaGFuZ2UnLCBjYik7XG4gICAgICAgIHJldHVybiAoKT0+cHJlZmVyc1JlZHVjZWRNb3Rpb24/LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2NoYW5nZScsIGNiKTtcbiAgICB9LCAoKT0+cHJlZmVyc1JlZHVjZWRNb3Rpb24ubWF0Y2hlcywgKCk9PmZhbHNlKTtcbmZ1bmN0aW9uIHVzZUNhbkFuaW1hdGUoeyByZXNwZWN0TW90aW9uUHJlZmVyZW5jZSA9IHRydWUgfSA9IHt9KSB7XG4gICAgY29uc3QgaXNTdXBwb3J0ZWQgPSB1c2VJc1N1cHBvcnRlZCgpO1xuICAgIGNvbnN0IHJlZHVjZWRNb3Rpb24gPSB1c2VQcmVmZXJzUmVkdWNlZE1vdGlvbigpO1xuICAgIHJldHVybiBpc1N1cHBvcnRlZCAmJiAoIXJlc3BlY3RNb3Rpb25QcmVmZXJlbmNlIHx8ICFyZWR1Y2VkTW90aW9uKTtcbn1cblxuZXhwb3J0IHsgdXNlQ2FuQW5pbWF0ZSwgdXNlSXNTdXBwb3J0ZWQsIHVzZVByZWZlcnNSZWR1Y2VkTW90aW9uIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@number-flow/react/dist/index.mjs\n");

/***/ })

};
;