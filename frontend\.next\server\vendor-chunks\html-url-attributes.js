"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/html-url-attributes";
exports.ids = ["vendor-chunks/html-url-attributes"];
exports.modules = {

/***/ "(ssr)/./node_modules/html-url-attributes/lib/index.js":
/*!*******************************************************!*\
  !*** ./node_modules/html-url-attributes/lib/index.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   urlAttributes: () => (/* binding */ urlAttributes)\n/* harmony export */ });\n/**\n * HTML URL properties.\n *\n * Each key is a property name and each value is a list of tag names it applies\n * to or `null` if it applies to all elements.\n *\n * @type {Record<string, Array<string> | null>}\n */\nconst urlAttributes = {\n  action: ['form'],\n  cite: ['blockquote', 'del', 'ins', 'q'],\n  data: ['object'],\n  formAction: ['button', 'input'],\n  href: ['a', 'area', 'base', 'link'],\n  icon: ['menuitem'],\n  itemId: null,\n  manifest: ['html'],\n  ping: ['a', 'area'],\n  poster: ['video'],\n  src: [\n    'audio',\n    'embed',\n    'iframe',\n    'img',\n    'input',\n    'script',\n    'source',\n    'track',\n    'video'\n  ]\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaHRtbC11cmwtYXR0cmlidXRlcy9saWIvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxzb2Z0XFxTZWFQcm9qZWN0XFxnaXRodWJfb3Blbl9wcm9qZWN0X3N1Y2Nlc3NcXHN1bmFcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXGh0bWwtdXJsLWF0dHJpYnV0ZXNcXGxpYlxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBIVE1MIFVSTCBwcm9wZXJ0aWVzLlxuICpcbiAqIEVhY2gga2V5IGlzIGEgcHJvcGVydHkgbmFtZSBhbmQgZWFjaCB2YWx1ZSBpcyBhIGxpc3Qgb2YgdGFnIG5hbWVzIGl0IGFwcGxpZXNcbiAqIHRvIG9yIGBudWxsYCBpZiBpdCBhcHBsaWVzIHRvIGFsbCBlbGVtZW50cy5cbiAqXG4gKiBAdHlwZSB7UmVjb3JkPHN0cmluZywgQXJyYXk8c3RyaW5nPiB8IG51bGw+fVxuICovXG5leHBvcnQgY29uc3QgdXJsQXR0cmlidXRlcyA9IHtcbiAgYWN0aW9uOiBbJ2Zvcm0nXSxcbiAgY2l0ZTogWydibG9ja3F1b3RlJywgJ2RlbCcsICdpbnMnLCAncSddLFxuICBkYXRhOiBbJ29iamVjdCddLFxuICBmb3JtQWN0aW9uOiBbJ2J1dHRvbicsICdpbnB1dCddLFxuICBocmVmOiBbJ2EnLCAnYXJlYScsICdiYXNlJywgJ2xpbmsnXSxcbiAgaWNvbjogWydtZW51aXRlbSddLFxuICBpdGVtSWQ6IG51bGwsXG4gIG1hbmlmZXN0OiBbJ2h0bWwnXSxcbiAgcGluZzogWydhJywgJ2FyZWEnXSxcbiAgcG9zdGVyOiBbJ3ZpZGVvJ10sXG4gIHNyYzogW1xuICAgICdhdWRpbycsXG4gICAgJ2VtYmVkJyxcbiAgICAnaWZyYW1lJyxcbiAgICAnaW1nJyxcbiAgICAnaW5wdXQnLFxuICAgICdzY3JpcHQnLFxuICAgICdzb3VyY2UnLFxuICAgICd0cmFjaycsXG4gICAgJ3ZpZGVvJ1xuICBdXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/html-url-attributes/lib/index.js\n");

/***/ })

};
;