"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_terraform_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/terraform.mjs":
/*!********************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/terraform.mjs ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Terraform\\\",\\\"fileTypes\\\":[\\\"tf\\\",\\\"tfvars\\\"],\\\"name\\\":\\\"terraform\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#attribute_definition\\\"},{\\\"include\\\":\\\"#block\\\"},{\\\"include\\\":\\\"#expressions\\\"}],\\\"repository\\\":{\\\"attribute_access\\\":{\\\"begin\\\":\\\"\\\\\\\\.(?!\\\\\\\\*)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.accessor.hcl\\\"}},\\\"end\\\":\\\"\\\\\\\\p{alpha}[\\\\\\\\w-]*|\\\\\\\\d*\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?!null|false|true)\\\\\\\\p{alpha}[\\\\\\\\w-]*\\\",\\\"name\\\":\\\"variable.other.member.hcl\\\"},{\\\"match\\\":\\\"\\\\\\\\d+\\\",\\\"name\\\":\\\"constant.numeric.integer.hcl\\\"}]}}},\\\"attribute_definition\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.parens.begin.hcl\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.other.readwrite.hcl\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.section.parens.end.hcl\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.operator.assignment.hcl\\\"}},\\\"match\\\":\\\"(\\\\\\\\()?(\\\\\\\\b(?!null\\\\\\\\b|false\\\\\\\\b|true\\\\\\\\b)\\\\\\\\p{alpha}[[:alnum:]_-]*)(\\\\\\\\))?\\\\\\\\s*(=(?![=>]))\\\\\\\\s*\\\",\\\"name\\\":\\\"variable.declaration.hcl\\\"},\\\"attribute_splat\\\":{\\\"begin\\\":\\\"\\\\\\\\.\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.accessor.hcl\\\"}},\\\"end\\\":\\\"\\\\\\\\*\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.splat.hcl\\\"}}},\\\"block\\\":{\\\"begin\\\":\\\"(\\\\\\\\w[-\\\\\\\\w]*)([\\\\\\\\s\\\\\\\"\\\\\\\\-\\\\\\\\w]*)(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\bdata|check|import|locals|module|output|provider|resource|terraform|variable\\\\\\\\b\\\",\\\"name\\\":\\\"entity.name.type.terraform\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?!null|false|true)\\\\\\\\p{alpha}[[:alnum:]_-]*\\\\\\\\b\\\",\\\"name\\\":\\\"entity.name.type.hcl\\\"}]},\\\"2\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"[\\\\\\\"\\\\\\\\-\\\\\\\\w]+\\\",\\\"name\\\":\\\"variable.other.enummember.hcl\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"punctuation.section.block.begin.hcl\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.section.block.begin.hcl\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.block.end.hcl\\\"}},\\\"name\\\":\\\"meta.block.hcl\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#attribute_definition\\\"},{\\\"include\\\":\\\"#block\\\"},{\\\"include\\\":\\\"#expressions\\\"}]},\\\"block_inline_comments\\\":{\\\"begin\\\":\\\"/\\\\\\\\*\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.hcl\\\"}},\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"name\\\":\\\"comment.block.hcl\\\"},\\\"brackets\\\":{\\\"begin\\\":\\\"\\\\\\\\[\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.brackets.begin.hcl\\\"}},\\\"end\\\":\\\"]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.brackets.end.hcl\\\"}},\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\*\\\",\\\"name\\\":\\\"keyword.operator.splat.hcl\\\"},{\\\"include\\\":\\\"#comma\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#inline_for_expression\\\"},{\\\"include\\\":\\\"#inline_if_expression\\\"},{\\\"include\\\":\\\"#expressions\\\"},{\\\"include\\\":\\\"#local_identifiers\\\"}]},\\\"char_escapes\\\":{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\(?:[nrt\\\\\\\"\\\\\\\\\\\\\\\\]|u(\\\\\\\\h{8}|\\\\\\\\h{4}))\\\",\\\"name\\\":\\\"constant.character.escape.hcl\\\"},\\\"comma\\\":{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.separator.hcl\\\"},\\\"comments\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#hash_line_comments\\\"},{\\\"include\\\":\\\"#double_slash_line_comments\\\"},{\\\"include\\\":\\\"#block_inline_comments\\\"}]},\\\"double_slash_line_comments\\\":{\\\"begin\\\":\\\"//\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.hcl\\\"}},\\\"end\\\":\\\"$\\\\\\\\n?\\\",\\\"name\\\":\\\"comment.line.double-slash.hcl\\\"},\\\"expressions\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#literal_values\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"include\\\":\\\"#tuple_for_expression\\\"},{\\\"include\\\":\\\"#object_for_expression\\\"},{\\\"include\\\":\\\"#brackets\\\"},{\\\"include\\\":\\\"#objects\\\"},{\\\"include\\\":\\\"#attribute_access\\\"},{\\\"include\\\":\\\"#attribute_splat\\\"},{\\\"include\\\":\\\"#functions\\\"},{\\\"include\\\":\\\"#parens\\\"}]},\\\"for_expression_body\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\bin\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.operator.word.hcl\\\"},{\\\"match\\\":\\\"\\\\\\\\bif\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.conditional.hcl\\\"},{\\\"match\\\":\\\":\\\",\\\"name\\\":\\\"keyword.operator.hcl\\\"},{\\\"include\\\":\\\"#expressions\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#comma\\\"},{\\\"include\\\":\\\"#local_identifiers\\\"}]},\\\"functions\\\":{\\\"begin\\\":\\\"([:\\\\\\\\-\\\\\\\\w]+)(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(core::)?(abs|abspath|alltrue|anytrue|base64decode|base64encode|base64gzip|base64sha256|base64sha512|basename|bcrypt|can|ceil|chomp|chunklist|cidrhost|cidrnetmask|cidrsubnet|cidrsubnets|coalesce|coalescelist|compact|concat|contains|csvdecode|dirname|distinct|element|endswith|file|filebase64|filebase64sha256|filebase64sha512|fileexists|filemd5|fileset|filesha1|filesha256|filesha512|flatten|floor|format|formatdate|formatlist|indent|index|join|jsondecode|jsonencode|keys|length|log|lookup|lower|matchkeys|max|md5|merge|min|nonsensitive|one|parseint|pathexpand|plantimestamp|pow|range|regex|regexall|replace|reverse|rsadecrypt|sensitive|setintersection|setproduct|setsubtract|setunion|sha1|sha256|sha512|signum|slice|sort|split|startswith|strcontains|strrev|substr|sum|templatefile|textdecodebase64|textencodebase64|timeadd|timecmp|timestamp|title|tobool|tolist|tomap|tonumber|toset|tostring|transpose|trim|trimprefix|trimspace|trimsuffix|try|upper|urlencode|uuid|uuidv5|values|yamldecode|yamlencode|zipmap)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.builtin.terraform\\\"},{\\\"match\\\":\\\"\\\\\\\\bprovider::\\\\\\\\p{alpha}[\\\\\\\\w_-]*::\\\\\\\\p{alpha}[\\\\\\\\w_-]*\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.provider.terraform\\\"}]},\\\"2\\\":{\\\"name\\\":\\\"punctuation.section.parens.begin.hcl\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.end.hcl\\\"}},\\\"name\\\":\\\"meta.function-call.hcl\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#expressions\\\"},{\\\"include\\\":\\\"#comma\\\"}]},\\\"hash_line_comments\\\":{\\\"begin\\\":\\\"#\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.hcl\\\"}},\\\"end\\\":\\\"$\\\\\\\\n?\\\",\\\"name\\\":\\\"comment.line.number-sign.hcl\\\"},\\\"hcl_type_keywords\\\":{\\\"match\\\":\\\"\\\\\\\\b(any|string|number|bool|list|set|map|tuple|object)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.hcl\\\"},\\\"heredoc\\\":{\\\"begin\\\":\\\"(<<-?)\\\\\\\\s*(\\\\\\\\w+)\\\\\\\\s*$\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.heredoc.hcl\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.heredoc.hcl\\\"}},\\\"end\\\":\\\"^\\\\\\\\s*\\\\\\\\2\\\\\\\\s*$\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.control.heredoc.hcl\\\"}},\\\"name\\\":\\\"string.unquoted.heredoc.hcl\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_interpolation\\\"}]},\\\"inline_for_expression\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.hcl\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"=>\\\",\\\"name\\\":\\\"storage.type.function.hcl\\\"},{\\\"include\\\":\\\"#for_expression_body\\\"}]}},\\\"match\\\":\\\"(for)\\\\\\\\b(.*)\\\\\\\\n\\\"},\\\"inline_if_expression\\\":{\\\"begin\\\":\\\"(if)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.conditional.hcl\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expressions\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#comma\\\"},{\\\"include\\\":\\\"#local_identifiers\\\"}]},\\\"language_constants\\\":{\\\"match\\\":\\\"\\\\\\\\b(true|false|null)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.hcl\\\"},\\\"literal_values\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#numeric_literals\\\"},{\\\"include\\\":\\\"#language_constants\\\"},{\\\"include\\\":\\\"#string_literals\\\"},{\\\"include\\\":\\\"#heredoc\\\"},{\\\"include\\\":\\\"#hcl_type_keywords\\\"},{\\\"include\\\":\\\"#named_value_references\\\"}]},\\\"local_identifiers\\\":{\\\"match\\\":\\\"\\\\\\\\b(?!null|false|true)\\\\\\\\p{alpha}[[:alnum:]_-]*\\\\\\\\b\\\",\\\"name\\\":\\\"variable.other.readwrite.hcl\\\"},\\\"named_value_references\\\":{\\\"match\\\":\\\"\\\\\\\\b(var|local|module|data|path|terraform)\\\\\\\\b\\\",\\\"name\\\":\\\"variable.other.readwrite.terraform\\\"},\\\"numeric_literals\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.exponent.hcl\\\"}},\\\"match\\\":\\\"\\\\\\\\b\\\\\\\\d+([Ee][+-]?)\\\\\\\\d+\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.float.hcl\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.decimal.hcl\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.exponent.hcl\\\"}},\\\"match\\\":\\\"\\\\\\\\b\\\\\\\\d+(\\\\\\\\.)\\\\\\\\d+(?:([Ee][+-]?)\\\\\\\\d+)?\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.float.hcl\\\"},{\\\"match\\\":\\\"\\\\\\\\b\\\\\\\\d+\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.integer.hcl\\\"}]},\\\"object_for_expression\\\":{\\\"begin\\\":\\\"(\\\\\\\\{)\\\\\\\\s?(for)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.braces.begin.hcl\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.hcl\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.braces.end.hcl\\\"}},\\\"patterns\\\":[{\\\"match\\\":\\\"=>\\\",\\\"name\\\":\\\"storage.type.function.hcl\\\"},{\\\"include\\\":\\\"#for_expression_body\\\"}]},\\\"object_key_values\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#literal_values\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"include\\\":\\\"#tuple_for_expression\\\"},{\\\"include\\\":\\\"#object_for_expression\\\"},{\\\"include\\\":\\\"#heredoc\\\"},{\\\"include\\\":\\\"#functions\\\"}]},\\\"objects\\\":{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.braces.begin.hcl\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.braces.end.hcl\\\"}},\\\"name\\\":\\\"meta.braces.hcl\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#objects\\\"},{\\\"include\\\":\\\"#inline_for_expression\\\"},{\\\"include\\\":\\\"#inline_if_expression\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.mapping.key.hcl variable.other.readwrite.hcl\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.assignment.hcl\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"=>\\\",\\\"name\\\":\\\"storage.type.function.hcl\\\"}]}},\\\"match\\\":\\\"\\\\\\\\b((?!null|false|true)\\\\\\\\p{alpha}[[:alnum:]_-]*)\\\\\\\\s*(=>?)\\\\\\\\s*\\\"},{\\\"captures\\\":{\\\"0\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#named_value_references\\\"}]},\\\"1\\\":{\\\"name\\\":\\\"meta.mapping.key.hcl string.quoted.double.hcl\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.hcl\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.hcl\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.operator.hcl\\\"}},\\\"match\\\":\\\"\\\\\\\\b((\\\\\\\").*(\\\\\\\"))\\\\\\\\s*(=)\\\\\\\\s*\\\"},{\\\"begin\\\":\\\"^\\\\\\\\s*\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.begin.hcl\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\\\\\\s*([=:])\\\\\\\\s*\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.parens.end.hcl\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.hcl\\\"}},\\\"name\\\":\\\"meta.mapping.key.hcl\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#named_value_references\\\"},{\\\"include\\\":\\\"#attribute_access\\\"}]},{\\\"include\\\":\\\"#object_key_values\\\"}]},\\\"operators\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\">=\\\",\\\"name\\\":\\\"keyword.operator.hcl\\\"},{\\\"match\\\":\\\"<=\\\",\\\"name\\\":\\\"keyword.operator.hcl\\\"},{\\\"match\\\":\\\"==\\\",\\\"name\\\":\\\"keyword.operator.hcl\\\"},{\\\"match\\\":\\\"!=\\\",\\\"name\\\":\\\"keyword.operator.hcl\\\"},{\\\"match\\\":\\\"\\\\\\\\+\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.hcl\\\"},{\\\"match\\\":\\\"-\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.hcl\\\"},{\\\"match\\\":\\\"\\\\\\\\*\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.hcl\\\"},{\\\"match\\\":\\\"/\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.hcl\\\"},{\\\"match\\\":\\\"%\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.hcl\\\"},{\\\"match\\\":\\\"&&\\\",\\\"name\\\":\\\"keyword.operator.logical.hcl\\\"},{\\\"match\\\":\\\"\\\\\\\\|\\\\\\\\|\\\",\\\"name\\\":\\\"keyword.operator.logical.hcl\\\"},{\\\"match\\\":\\\"!\\\",\\\"name\\\":\\\"keyword.operator.logical.hcl\\\"},{\\\"match\\\":\\\">\\\",\\\"name\\\":\\\"keyword.operator.hcl\\\"},{\\\"match\\\":\\\"<\\\",\\\"name\\\":\\\"keyword.operator.hcl\\\"},{\\\"match\\\":\\\"\\\\\\\\?\\\",\\\"name\\\":\\\"keyword.operator.hcl\\\"},{\\\"match\\\":\\\"\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\",\\\"name\\\":\\\"keyword.operator.hcl\\\"},{\\\"match\\\":\\\":\\\",\\\"name\\\":\\\"keyword.operator.hcl\\\"},{\\\"match\\\":\\\"=>\\\",\\\"name\\\":\\\"keyword.operator.hcl\\\"}]},\\\"parens\\\":{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.begin.hcl\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.end.hcl\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#expressions\\\"}]},\\\"string_interpolation\\\":{\\\"begin\\\":\\\"(?<![%$])([%$]\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.interpolation.begin.hcl\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.other.interpolation.end.hcl\\\"}},\\\"name\\\":\\\"meta.interpolation.hcl\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"~\\\\\\\\s\\\",\\\"name\\\":\\\"keyword.operator.template.left.trim.hcl\\\"},{\\\"match\\\":\\\"\\\\\\\\s~\\\",\\\"name\\\":\\\"keyword.operator.template.right.trim.hcl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(if|else|endif|for|in|endfor)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.hcl\\\"},{\\\"include\\\":\\\"#expressions\\\"},{\\\"include\\\":\\\"#local_identifiers\\\"}]},\\\"string_literals\\\":{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.hcl\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.hcl\\\"}},\\\"name\\\":\\\"string.quoted.double.hcl\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_interpolation\\\"},{\\\"include\\\":\\\"#char_escapes\\\"}]},\\\"tuple_for_expression\\\":{\\\"begin\\\":\\\"(\\\\\\\\[)\\\\\\\\s?(for)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.brackets.begin.hcl\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.hcl\\\"}},\\\"end\\\":\\\"]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.brackets.end.hcl\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#for_expression_body\\\"}]}},\\\"scopeName\\\":\\\"source.hcl.terraform\\\",\\\"aliases\\\":[\\\"tf\\\",\\\"tfvars\\\"]}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/terraform.mjs\n"));

/***/ })

}]);