"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_codemirror_legacy-modes_mode_rpm_js"],{

/***/ "(app-pages-browser)/./node_modules/@codemirror/legacy-modes/mode/rpm.js":
/*!***********************************************************!*\
  !*** ./node_modules/@codemirror/legacy-modes/mode/rpm.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   rpmChanges: () => (/* binding */ rpmChanges),\n/* harmony export */   rpmSpec: () => (/* binding */ rpmSpec)\n/* harmony export */ });\nvar headerSeparator = /^-+$/;\nvar headerLine = /^(Mon|Tue|Wed|Thu|Fri|Sat|Sun) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)  ?\\d{1,2} \\d{2}:\\d{2}(:\\d{2})? [A-Z]{3,4} \\d{4} - /;\nvar simpleEmail = /^[\\w+.-]+@[\\w.-]+/;\n\nconst rpmChanges = {\n  name: \"rpmchanges\",\n  token: function(stream) {\n    if (stream.sol()) {\n      if (stream.match(headerSeparator)) { return 'tag'; }\n      if (stream.match(headerLine)) { return 'tag'; }\n    }\n    if (stream.match(simpleEmail)) { return 'string'; }\n    stream.next();\n    return null;\n  }\n}\n\n// Quick and dirty spec file highlighting\n\nvar arch = /^(i386|i586|i686|x86_64|ppc64le|ppc64|ppc|ia64|s390x|s390|sparc64|sparcv9|sparc|noarch|alphaev6|alpha|hppa|mipsel)/;\n\nvar preamble = /^[a-zA-Z0-9()]+:/;\nvar section = /^%(debug_package|package|description|prep|build|install|files|clean|changelog|preinstall|preun|postinstall|postun|pretrans|posttrans|pre|post|triggerin|triggerun|verifyscript|check|triggerpostun|triggerprein|trigger)/;\nvar control_flow_complex = /^%(ifnarch|ifarch|if)/; // rpm control flow macros\nvar control_flow_simple = /^%(else|endif)/; // rpm control flow macros\nvar operators = /^(\\!|\\?|\\<\\=|\\<|\\>\\=|\\>|\\=\\=|\\&\\&|\\|\\|)/; // operators in control flow macros\n\nconst rpmSpec = {\n  name: \"rpmspec\",\n  startState: function () {\n    return {\n      controlFlow: false,\n      macroParameters: false,\n      section: false\n    };\n  },\n  token: function (stream, state) {\n    var ch = stream.peek();\n    if (ch == \"#\") { stream.skipToEnd(); return \"comment\"; }\n\n    if (stream.sol()) {\n      if (stream.match(preamble)) { return \"header\"; }\n      if (stream.match(section)) { return \"atom\"; }\n    }\n\n    if (stream.match(/^\\$\\w+/)) { return \"def\"; } // Variables like '$RPM_BUILD_ROOT'\n    if (stream.match(/^\\$\\{\\w+\\}/)) { return \"def\"; } // Variables like '${RPM_BUILD_ROOT}'\n\n    if (stream.match(control_flow_simple)) { return \"keyword\"; }\n    if (stream.match(control_flow_complex)) {\n      state.controlFlow = true;\n      return \"keyword\";\n    }\n    if (state.controlFlow) {\n      if (stream.match(operators)) { return \"operator\"; }\n      if (stream.match(/^(\\d+)/)) { return \"number\"; }\n      if (stream.eol()) { state.controlFlow = false; }\n    }\n\n    if (stream.match(arch)) {\n      if (stream.eol()) { state.controlFlow = false; }\n      return \"number\";\n    }\n\n    // Macros like '%make_install' or '%attr(0775,root,root)'\n    if (stream.match(/^%[\\w]+/)) {\n      if (stream.match('(')) { state.macroParameters = true; }\n      return \"keyword\";\n    }\n    if (state.macroParameters) {\n      if (stream.match(/^\\d+/)) { return \"number\";}\n      if (stream.match(')')) {\n        state.macroParameters = false;\n        return \"keyword\";\n      }\n    }\n\n    // Macros like '%{defined fedora}'\n    if (stream.match(/^%\\{\\??[\\w \\-\\:\\!]+\\}/)) {\n      if (stream.eol()) { state.controlFlow = false; }\n      return \"def\";\n    }\n\n    stream.next();\n    return null;\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@codemirror/legacy-modes/mode/rpm.js\n"));

/***/ })

}]);