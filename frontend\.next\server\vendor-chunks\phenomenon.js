"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/phenomenon";
exports.ids = ["vendor-chunks/phenomenon"];
exports.modules = {

/***/ "(ssr)/./node_modules/phenomenon/dist/phenomenon.mjs":
/*!*****************************************************!*\
  !*** ./node_modules/phenomenon/dist/phenomenon.mjs ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nvar t=[\"x\",\"y\",\"z\"],e=function(t){Object.assign(this,{uniforms:{},geometry:{vertices:[{x:0,y:0,z:0}]},mode:0,modifiers:{},attributes:[],multiplier:1,buffers:[]}),Object.assign(this,t),this.prepareProgram(),this.prepareUniforms(),this.prepareAttributes()};e.prototype.compileShader=function(t,e){var i=this.gl.createShader(t);return this.gl.shaderSource(i,e),this.gl.compileShader(i),i},e.prototype.prepareProgram=function(){var t=this.gl,e=this.vertex,i=this.fragment,r=t.createProgram();t.attachShader(r,this.compileShader(35633,e)),t.attachShader(r,this.compileShader(35632,i)),t.linkProgram(r),t.useProgram(r),this.program=r},e.prototype.prepareUniforms=function(){for(var t=Object.keys(this.uniforms),e=0;e<t.length;e+=1){var i=this.gl.getUniformLocation(this.program,t[e]);this.uniforms[t[e]].location=i}},e.prototype.prepareAttributes=function(){void 0!==this.geometry.vertices&&this.attributes.push({name:\"aPosition\",size:3}),void 0!==this.geometry.normal&&this.attributes.push({name:\"aNormal\",size:3}),this.attributeKeys=[];for(var t=0;t<this.attributes.length;t+=1)this.attributeKeys.push(this.attributes[t].name),this.prepareAttribute(this.attributes[t])},e.prototype.prepareAttribute=function(e){for(var i=this.geometry,r=this.multiplier,s=i.vertices,n=i.normal,a=new Float32Array(r*s.length*e.size),o=0;o<r;o+=1)for(var h=e.data&&e.data(o,r),u=o*s.length*e.size,f=0;f<s.length;f+=1)for(var c=0;c<e.size;c+=1){var l=this.modifiers[e.name];a[u]=void 0!==l?l(h,f,c,this):\"aPosition\"===e.name?s[f][t[c]]:\"aNormal\"===e.name?n[f][t[c]]:h[c],u+=1}this.attributes[this.attributeKeys.indexOf(e.name)].data=a,this.prepareBuffer(this.attributes[this.attributeKeys.indexOf(e.name)])},e.prototype.prepareBuffer=function(t){var e=t.data,i=t.name,r=t.size,s=this.gl.createBuffer();this.gl.bindBuffer(34962,s),this.gl.bufferData(34962,e,35044);var n=this.gl.getAttribLocation(this.program,i);this.gl.enableVertexAttribArray(n),this.gl.vertexAttribPointer(n,r,5126,!1,0,0),this.buffers[this.attributeKeys.indexOf(t.name)]={buffer:s,location:n,size:r}},e.prototype.render=function(t){var e=this,i=this.uniforms,r=this.multiplier,s=this.gl;s.useProgram(this.program);for(var n=0;n<this.buffers.length;n+=1){var a=this.buffers[n],o=a.location,h=a.buffer,u=a.size;s.enableVertexAttribArray(o),s.bindBuffer(34962,h),s.vertexAttribPointer(o,u,5126,!1,0,0)}Object.keys(t).forEach(function(e){i[e].value=t[e].value}),Object.keys(i).forEach(function(t){var r=i[t];e.uniformMap[r.type](r.location,r.value)}),s.drawArrays(this.mode,0,r*this.geometry.vertices.length),this.onRender&&this.onRender(this)},e.prototype.destroy=function(){for(var t=0;t<this.buffers.length;t+=1)this.gl.deleteBuffer(this.buffers[t].buffer);this.gl.deleteProgram(this.program),this.gl=null};var i=function(t){var e=this,i=t||{},r=i.canvas;void 0===r&&(r=document.querySelector(\"canvas\"));var s=i.context;void 0===s&&(s={});var n=i.contextType;void 0===n&&(n=\"experimental-webgl\");var a=i.settings;void 0===a&&(a={});var o=r.getContext(n,Object.assign({alpha:!1,antialias:!1},s));Object.assign(this,{gl:o,canvas:r,uniforms:{},instances:new Map,shouldRender:!0}),Object.assign(this,{devicePixelRatio:1,clearColor:[1,1,1,1],position:{x:0,y:0,z:2},clip:[.001,100]}),Object.assign(this,a),this.uniformMap={float:function(t,e){return o.uniform1f(t,e)},vec2:function(t,e){return o.uniform2fv(t,e)},vec3:function(t,e){return o.uniform3fv(t,e)},vec4:function(t,e){return o.uniform4fv(t,e)},mat2:function(t,e){return o.uniformMatrix2fv(t,!1,e)},mat3:function(t,e){return o.uniformMatrix3fv(t,!1,e)},mat4:function(t,e){return o.uniformMatrix4fv(t,!1,e)}},o.enable(o.DEPTH_TEST),o.depthFunc(o.LEQUAL),!1===o.getContextAttributes().alpha&&(o.clearColor.apply(o,this.clearColor),o.clearDepth(1)),this.onSetup&&this.onSetup(o),window.addEventListener(\"resize\",function(){return e.resize()}),this.resize(),this.render()};i.prototype.resize=function(){var t=this.gl,e=this.canvas,i=this.devicePixelRatio,r=this.position;e.width=e.clientWidth*i,e.height=e.clientHeight*i;var s=t.drawingBufferWidth,n=t.drawingBufferHeight,a=s/n;t.viewport(0,0,s,n);var o=Math.tan(Math.PI/180*22.5),h=[1,0,0,0,0,1,0,0,0,0,1,0,r.x,r.y,(a<1?1:a)*-r.z,1];this.uniforms.uProjectionMatrix={type:\"mat4\",value:[.5/o,0,0,0,0,a/o*.5,0,0,0,0,-(this.clip[1]+this.clip[0])/(this.clip[1]-this.clip[0]),-1,0,0,-2*this.clip[1]*(this.clip[0]/(this.clip[1]-this.clip[0])),0]},this.uniforms.uViewMatrix={type:\"mat4\",value:[1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1]},this.uniforms.uModelMatrix={type:\"mat4\",value:h}},i.prototype.toggle=function(t){t!==this.shouldRender&&(this.shouldRender=void 0!==t?t:!this.shouldRender,this.shouldRender&&this.render())},i.prototype.render=function(){var t=this;this.gl.clear(16640),this.instances.forEach(function(e){e.render(t.uniforms)}),this.onRender&&this.onRender(this),this.shouldRender&&requestAnimationFrame(function(){return t.render()})},i.prototype.add=function(t,i){void 0===i&&(i={uniforms:{}}),void 0===i.uniforms&&(i.uniforms={}),Object.assign(i.uniforms,JSON.parse(JSON.stringify(this.uniforms))),Object.assign(i,{gl:this.gl,uniformMap:this.uniformMap});var r=new e(i);return this.instances.set(t,r),r},i.prototype.remove=function(t){var e=this.instances.get(t);void 0!==e&&(e.destroy(),this.instances.delete(t))},i.prototype.destroy=function(){var t=this;this.instances.forEach(function(e,i){e.destroy(),t.instances.delete(i)}),this.toggle(!1)};/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (i);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/phenomenon/dist/phenomenon.mjs\n");

/***/ })

};
;