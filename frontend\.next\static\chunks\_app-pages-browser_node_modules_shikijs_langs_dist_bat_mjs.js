"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_bat_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/bat.mjs":
/*!**************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/bat.mjs ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Batch File\\\",\\\"injections\\\":{\\\"L:meta.block.repeat.batchfile\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#repeatParameter\\\"}]}},\\\"name\\\":\\\"bat\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#commands\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#controls\\\"},{\\\"include\\\":\\\"#escaped_characters\\\"},{\\\"include\\\":\\\"#labels\\\"},{\\\"include\\\":\\\"#numbers\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"include\\\":\\\"#parens\\\"},{\\\"include\\\":\\\"#strings\\\"},{\\\"include\\\":\\\"#variables\\\"}],\\\"repository\\\":{\\\"command_set\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=^|[\\\\\\\\s@])(?i:SET)(?=$|\\\\\\\\s)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.command.batchfile\\\"}},\\\"end\\\":\\\"(?=$\\\\\\\\n|[\\\\\\\\&|><)])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#command_set_inside\\\"}]}]},\\\"command_set_group\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.group.begin.batchfile\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.group.end.batchfile\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#command_set_inside_arithmetic\\\"}]}]},\\\"command_set_inside\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#escaped_characters\\\"},{\\\"include\\\":\\\"#variables\\\"},{\\\"include\\\":\\\"#numbers\\\"},{\\\"include\\\":\\\"#parens\\\"},{\\\"include\\\":\\\"#command_set_strings\\\"},{\\\"include\\\":\\\"#strings\\\"},{\\\"begin\\\":\\\"([^ ][^=]*)(=)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.readwrite.batchfile\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.assignment.batchfile\\\"}},\\\"end\\\":\\\"(?=$\\\\\\\\n|[\\\\\\\\&|><)])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#escaped_characters\\\"},{\\\"include\\\":\\\"#variables\\\"},{\\\"include\\\":\\\"#numbers\\\"},{\\\"include\\\":\\\"#parens\\\"},{\\\"include\\\":\\\"#strings\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\s+/[aA]\\\\\\\\s+\\\",\\\"end\\\":\\\"(?=$\\\\\\\\n|[\\\\\\\\&|><)])\\\",\\\"name\\\":\\\"meta.expression.set.batchfile\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.batchfile\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.batchfile\\\"}},\\\"name\\\":\\\"string.quoted.double.batchfile\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#command_set_inside_arithmetic\\\"},{\\\"include\\\":\\\"#command_set_group\\\"},{\\\"include\\\":\\\"#variables\\\"}]},{\\\"include\\\":\\\"#command_set_inside_arithmetic\\\"},{\\\"include\\\":\\\"#command_set_group\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\s+/[pP]\\\\\\\\s+\\\",\\\"end\\\":\\\"(?=$\\\\\\\\n|[\\\\\\\\&|><)])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#command_set_strings\\\"},{\\\"begin\\\":\\\"([^ ][^=]*)(=)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.readwrite.batchfile\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.assignment.batchfile\\\"}},\\\"end\\\":\\\"(?=$\\\\\\\\n|[\\\\\\\\&|><)])\\\",\\\"name\\\":\\\"meta.prompt.set.batchfile\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#strings\\\"}]}]}]},\\\"command_set_inside_arithmetic\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#command_set_operators\\\"},{\\\"include\\\":\\\"#numbers\\\"},{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.separator.batchfile\\\"}]},\\\"command_set_operators\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.readwrite.batchfile\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.assignment.augmented.batchfile\\\"}},\\\"match\\\":\\\"([^ ]*)(\\\\\\\\+=|-=|\\\\\\\\*=|/=|%%=|&=|\\\\\\\\|=|\\\\\\\\^=|<<=|>>=)\\\"},{\\\"match\\\":\\\"[+\\\\\\\\-/*]|%%|[|\\\\\\\\&^]|<<|>>|~\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.batchfile\\\"},{\\\"match\\\":\\\"!\\\",\\\"name\\\":\\\"keyword.operator.logical.batchfile\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.readwrite.batchfile\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.assignment.batchfile\\\"}},\\\"match\\\":\\\"([^ =]*)(=)\\\"}]},\\\"command_set_strings\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(\\\\\\\")\\\\\\\\s*([^ ][^=]*)(=)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.batchfile\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.other.readwrite.batchfile\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.operator.assignment.batchfile\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.batchfile\\\"}},\\\"name\\\":\\\"string.quoted.double.batchfile\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#variables\\\"},{\\\"include\\\":\\\"#numbers\\\"},{\\\"include\\\":\\\"#escaped_characters\\\"}]}]},\\\"commands\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=^|[\\\\\\\\s@])(?i:adprep|append|arp|assoc|at|atmadm|attrib|auditpol|autochk|autoconv|autofmt|bcdboot|bcdedit|bdehdcfg|bitsadmin|bootcfg|brea|cacls|cd|certreq|certutil|change|chcp|chdir|chglogon|chgport|chgusr|chkdsk|chkntfs|choice|cipher|clip|cls|clscluadmin|cluster|cmd|cmdkey|cmstp|color|comp|compact|convert|copy|cprofile|cscript|csvde|date|dcdiag|dcgpofix|dcpromo|defra|del|dfscmd|dfsdiag|dfsrmig|diantz|dir|dirquota|diskcomp|diskcopy|diskpart|diskperf|diskraid|diskshadow|dispdiag|doin|dnscmd|doskey|driverquery|dsacls|dsadd|dsamain|dsdbutil|dsget|dsmgmt|dsmod|dsmove|dsquery|dsrm|edit|endlocal|eraseesentutl|eventcreate|eventquery|eventtriggers|evntcmd|expand|extract|fc|filescrn|find|findstr|finger|flattemp|fonde|forfiles|format|freedisk|fsutil|ftp|ftype|fveupdate|getmac|gettype|gpfixup|gpresult|gpupdate|graftabl|hashgen|hep|helpctr|hostname|icacls|iisreset|inuse|ipconfig|ipxroute|irftp|ismserv|jetpack|klist|ksetup|ktmutil|ktpass|label|ldifd|ldp|lodctr|logman|logoff|lpq|lpr|macfile|makecab|manage-bde|mapadmin|md|mkdir|mklink|mmc|mode|more|mount|mountvol|move|mqbup|mqsvc|mqtgsvc|msdt|msg|msiexec|msinfo32|mstsc|nbtstat|net computer|net group|net localgroup|net print|net session|net share|net start|net stop|net use|net user|net view|net|netcfg|netdiag|netdom|netsh|netstat|nfsadmin|nfsshare|nfsstat|nlb|nlbmgr|nltest|nslookup|ntackup|ntcmdprompt|ntdsutil|ntfrsutl|openfiles|pagefileconfig|path|pathping|pause|pbadmin|pentnt|perfmon|ping|pnpunatten|pnputil|popd|powercfg|powershell|powershell_ise|print|prncnfg|prndrvr|prnjobs|prnmngr|prnport|prnqctl|prompt|pubprn|pushd|pushprinterconnections|pwlauncher|qappsrv|qprocess|query|quser|qwinsta|rasdial|rcp|rd|rdpsign|regentc|recover|redircmp|redirusr|reg|regini|regsvr32|relog|ren|rename|rendom|repadmin|repair-bde|replace|reset session|rxec|risetup|rmdir|robocopy|route|rpcinfo|rpcping|rsh|runas|rundll32|rwinsta|sc|schtasks|scp|scwcmd|secedit|serverceipoptin|servrmanagercmd|serverweroptin|setspn|setx|sfc|sftp|shadow|shift|showmount|shutdown|sort|ssh|ssh-add|ssh-agent|ssh-keygen|ssh-keyscan|start|storrept|subst|sxstrace|ysocmgr|systeminfo|takeown|tapicfg|taskkill|tasklist|tcmsetup|telnet|tftp|time|timeout|title|tlntadmn|tpmvscmgr|tpmvscmgr|tacerpt|tracert|tree|tscon|tsdiscon|tsecimp|tskill|tsprof|type|typeperf|tzutil|uddiconfig|umount|unlodctr|ver|verifier|verif|vol|vssadmin|w32tm|waitfor|wbadmin|wdsutil|wecutil|wevtutil|where|whoami|winnt|winnt32|winpop|winrm|winrs|winsat|wlbs|wmic|wscript|wsl|xcopy)(?=$|\\\\\\\\s)\\\",\\\"name\\\":\\\"keyword.command.batchfile\\\"},{\\\"begin\\\":\\\"(?i)(?<=^|[\\\\\\\\s@])(echo)(?:(?=$|[.:])|\\\\\\\\s+(?:(o(?:n|ff))(?=\\\\\\\\s*$))?)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.command.batchfile\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.special-method.batchfile\\\"}},\\\"end\\\":\\\"(?=$\\\\\\\\n|[\\\\\\\\&|><)])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#escaped_characters\\\"},{\\\"include\\\":\\\"#variables\\\"},{\\\"include\\\":\\\"#numbers\\\"},{\\\"include\\\":\\\"#strings\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.command.batchfile\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.special-method.batchfile\\\"}},\\\"match\\\":\\\"(?i)(?<=^|[\\\\\\\\s@])(setlocal)(?:\\\\\\\\s*$|\\\\\\\\s+((?:Enable|Disable)(?:Extensions|DelayedExpansion))(?=\\\\\\\\s*$))\\\"},{\\\"include\\\":\\\"#command_set\\\"}]},\\\"comments\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?:^|(&))\\\\\\\\s*(?=(:[+=,;: ]))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.conditional.batchfile\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(:[+=,;: ])\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.batchfile\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"comment.line.colon.batchfile\\\"}]},{\\\"begin\\\":\\\"(?<=^|[\\\\\\\\s@])(?i)(REM)(\\\\\\\\.)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.command.rem.batchfile\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.batchfile\\\"}},\\\"end\\\":\\\"(?=$\\\\\\\\n|[\\\\\\\\&|><)])\\\",\\\"name\\\":\\\"comment.line.rem.batchfile\\\"},{\\\"begin\\\":\\\"(?<=^|[\\\\\\\\s@])(?i:rem)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.command.rem.batchfile\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.line.rem.batchfile\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"[><|]\\\",\\\"name\\\":\\\"invalid.illegal.unexpected-character.batchfile\\\"}]}]},\\\"constants\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(?i:NUL)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.batchfile\\\"}]},\\\"controls\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?i)(?<=^|\\\\\\\\s)(?:call|exit(?=$|\\\\\\\\s)|goto(?=$|[\\\\\\\\s:]))\\\",\\\"name\\\":\\\"keyword.control.statement.batchfile\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.conditional.batchfile\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.logical.batchfile\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.other.special-method.batchfile\\\"}},\\\"match\\\":\\\"(?<=^|\\\\\\\\s)(?i)(if)\\\\\\\\s+(?:(not)\\\\\\\\s+)?(exist|defined|errorlevel|cmdextversion)(?=\\\\\\\\s)\\\"},{\\\"match\\\":\\\"(?<=^|\\\\\\\\s)(?i)(?:if|else)(?=$|\\\\\\\\s)\\\",\\\"name\\\":\\\"keyword.control.conditional.batchfile\\\"},{\\\"begin\\\":\\\"(?<=^|[\\\\\\\\s(\\\\\\\\&^])(?i)for(?=\\\\\\\\s)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.control.repeat.batchfile\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"meta.block.repeat.batchfile\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=[\\\\\\\\s^])(?i)in(?=\\\\\\\\s)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.control.repeat.in.batchfile\\\"}},\\\"end\\\":\\\"(?<=[\\\\\\\\s)^])(?i)do(?=\\\\\\\\s)|\\\\\\\\n\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.control.repeat.do.batchfile\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"include\\\":\\\"$self\\\"}]}]},\\\"escaped_characters\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"%%|\\\\\\\\^\\\\\\\\^!|\\\\\\\\^(?=.)|\\\\\\\\^\\\\\\\\n\\\",\\\"name\\\":\\\"constant.character.escape.batchfile\\\"}]},\\\"labels\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.batchfile\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.special-method.batchfile\\\"}},\\\"match\\\":\\\"(?i)(?:^\\\\\\\\s*|(?<=call|goto)\\\\\\\\s*)(:)([^+=,;:\\\\\\\\s]\\\\\\\\S*)\\\"}]},\\\"numbers\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=^|[\\\\\\\\s=])(0[xX]\\\\\\\\h*|[+-]?\\\\\\\\d+)(?=$|[\\\\\\\\s<>])\\\",\\\"name\\\":\\\"constant.numeric.batchfile\\\"}]},\\\"operators\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"@(?=\\\\\\\\S)\\\",\\\"name\\\":\\\"keyword.operator.at.batchfile\\\"},{\\\"match\\\":\\\"(?<=\\\\\\\\s)(?i:EQU|NEQ|LSS|LEQ|GTR|GEQ)(?=\\\\\\\\s)|==\\\",\\\"name\\\":\\\"keyword.operator.comparison.batchfile\\\"},{\\\"match\\\":\\\"(?<=\\\\\\\\s)(?i)(NOT)(?=\\\\\\\\s)\\\",\\\"name\\\":\\\"keyword.operator.logical.batchfile\\\"},{\\\"match\\\":\\\"(?<!\\\\\\\\^)&&?|\\\\\\\\|\\\\\\\\|\\\",\\\"name\\\":\\\"keyword.operator.conditional.batchfile\\\"},{\\\"match\\\":\\\"(?<!\\\\\\\\^)\\\\\\\\|\\\",\\\"name\\\":\\\"keyword.operator.pipe.batchfile\\\"},{\\\"match\\\":\\\"<&?|>[\\\\\\\\&>]?\\\",\\\"name\\\":\\\"keyword.operator.redirection.batchfile\\\"}]},\\\"parens\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.group.begin.batchfile\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.group.end.batchfile\\\"}},\\\"name\\\":\\\"meta.group.batchfile\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"[,;]\\\",\\\"name\\\":\\\"punctuation.separator.batchfile\\\"},{\\\"include\\\":\\\"$self\\\"}]}]},\\\"repeatParameter\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.variable.batchfile\\\"}},\\\"match\\\":\\\"(%%)(?i:~[fdpnxsatz]*(?:\\\\\\\\$PATH:)?)?[a-zA-Z]\\\",\\\"name\\\":\\\"variable.parameter.repeat.batchfile\\\"}]},\\\"strings\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.batchfile\\\"}},\\\"end\\\":\\\"(\\\\\\\")|(\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.batchfile\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.batchfile\\\"}},\\\"name\\\":\\\"string.quoted.double.batchfile\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"%%\\\",\\\"name\\\":\\\"constant.character.escape.batchfile\\\"},{\\\"include\\\":\\\"#variables\\\"}]}]},\\\"variable\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"%(?=[^%]+%)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.variable.begin.batchfile\\\"}},\\\"end\\\":\\\"(%)|\\\\\\\\n\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.variable.end.batchfile\\\"}},\\\"name\\\":\\\"variable.other.readwrite.batchfile\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\":~\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.separator.batchfile\\\"}},\\\"end\\\":\\\"(?=[%\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.variable.substring.batchfile\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#variable_substring\\\"}]},{\\\"begin\\\":\\\":\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.separator.batchfile\\\"}},\\\"end\\\":\\\"(?=[%\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.variable.substitution.batchfile\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#variable_replace\\\"},{\\\"begin\\\":\\\"=\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.separator.batchfile\\\"}},\\\"end\\\":\\\"(?=[%\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#variable_delayed_expansion\\\"},{\\\"match\\\":\\\"[^%]+\\\",\\\"name\\\":\\\"string.unquoted.batchfile\\\"}]}]}]}]},\\\"variable_delayed_expansion\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"!(?=[^!]+!)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.variable.begin.batchfile\\\"}},\\\"end\\\":\\\"(!)|\\\\\\\\n\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.variable.end.batchfile\\\"}},\\\"name\\\":\\\"variable.other.readwrite.batchfile\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\":~\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.separator.batchfile\\\"}},\\\"end\\\":\\\"(?=[!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.variable.substring.batchfile\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#variable_substring\\\"}]},{\\\"begin\\\":\\\":\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.separator.batchfile\\\"}},\\\"end\\\":\\\"(?=[!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.variable.substitution.batchfile\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#escaped_characters\\\"},{\\\"include\\\":\\\"#variable_replace\\\"},{\\\"include\\\":\\\"#variable\\\"},{\\\"begin\\\":\\\"=\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.separator.batchfile\\\"}},\\\"end\\\":\\\"(?=[!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#variable\\\"},{\\\"match\\\":\\\"[^!]+\\\",\\\"name\\\":\\\"string.unquoted.batchfile\\\"}]}]}]}]},\\\"variable_replace\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"[^=%!\\\\\\\\n]+\\\",\\\"name\\\":\\\"string.unquoted.batchfile\\\"}]},\\\"variable_substring\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.numeric.batchfile\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.batchfile\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.numeric.batchfile\\\"}},\\\"match\\\":\\\"([+-]?\\\\\\\\d+)(?:(,)([+-]?\\\\\\\\d+))?\\\"}]},\\\"variables\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.variable.batchfile\\\"}},\\\"match\\\":\\\"(%)(?:(?i:~[fdpnxsatz]*(?:\\\\\\\\$PATH:)?)?\\\\\\\\d|\\\\\\\\*)\\\",\\\"name\\\":\\\"variable.parameter.batchfile\\\"},{\\\"include\\\":\\\"#variable\\\"},{\\\"include\\\":\\\"#variable_delayed_expansion\\\"}]}},\\\"scopeName\\\":\\\"source.batchfile\\\",\\\"aliases\\\":[\\\"batch\\\"]}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/bat.mjs\n"));

/***/ })

}]);