"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_diff_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/diff.mjs":
/*!***************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/diff.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Diff\\\",\\\"name\\\":\\\"diff\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.separator.diff\\\"}},\\\"match\\\":\\\"^((\\\\\\\\*{15})|(={67})|(-{3}))$\\\\\\\\n?\\\",\\\"name\\\":\\\"meta.separator.diff\\\"},{\\\"match\\\":\\\"^\\\\\\\\d+(,\\\\\\\\d+)*([adc])\\\\\\\\d+(,\\\\\\\\d+)*$\\\\\\\\n?\\\",\\\"name\\\":\\\"meta.diff.range.normal\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.range.diff\\\"},\\\"2\\\":{\\\"name\\\":\\\"meta.toc-list.line-number.diff\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.range.diff\\\"}},\\\"match\\\":\\\"^(@@)\\\\\\\\s*(.+?)\\\\\\\\s*(@@)($\\\\\\\\n?)?\\\",\\\"name\\\":\\\"meta.diff.range.unified\\\"},{\\\"captures\\\":{\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.range.diff\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.range.diff\\\"},\\\"6\\\":{\\\"name\\\":\\\"punctuation.definition.range.diff\\\"},\\\"7\\\":{\\\"name\\\":\\\"punctuation.definition.range.diff\\\"}},\\\"match\\\":\\\"^(((-{3}) .+ (-{4}))|((\\\\\\\\*{3}) .+ (\\\\\\\\*{4})))$\\\\\\\\n?\\\",\\\"name\\\":\\\"meta.diff.range.context\\\"},{\\\"match\\\":\\\"^diff --git a/.*$\\\\\\\\n?\\\",\\\"name\\\":\\\"meta.diff.header.git\\\"},{\\\"match\\\":\\\"^diff (-|\\\\\\\\S+\\\\\\\\s+\\\\\\\\S+).*$\\\\\\\\n?\\\",\\\"name\\\":\\\"meta.diff.header.command\\\"},{\\\"captures\\\":{\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.from-file.diff\\\"},\\\"6\\\":{\\\"name\\\":\\\"punctuation.definition.from-file.diff\\\"},\\\"7\\\":{\\\"name\\\":\\\"punctuation.definition.from-file.diff\\\"}},\\\"match\\\":\\\"(^(?:(((-{3}) .+)|((\\\\\\\\*{3}) .+))$\\\\\\\\n?|(={4}) .+(?= - )))\\\",\\\"name\\\":\\\"meta.diff.header.from-file\\\"},{\\\"captures\\\":{\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.to-file.diff\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.to-file.diff\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.to-file.diff\\\"}},\\\"match\\\":\\\"(^(\\\\\\\\+{3}) .+$\\\\\\\\n?| (-) .* (={4})$\\\\\\\\n?)\\\",\\\"name\\\":\\\"meta.diff.header.to-file\\\"},{\\\"captures\\\":{\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.inserted.diff\\\"},\\\"6\\\":{\\\"name\\\":\\\"punctuation.definition.inserted.diff\\\"}},\\\"match\\\":\\\"^(((>)( .*)?)|((\\\\\\\\+).*))$\\\\\\\\n?\\\",\\\"name\\\":\\\"markup.inserted.diff\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.changed.diff\\\"}},\\\"match\\\":\\\"^(!).*$\\\\\\\\n?\\\",\\\"name\\\":\\\"markup.changed.diff\\\"},{\\\"captures\\\":{\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.deleted.diff\\\"},\\\"6\\\":{\\\"name\\\":\\\"punctuation.definition.deleted.diff\\\"}},\\\"match\\\":\\\"^(((<)( .*)?)|((-).*))$\\\\\\\\n?\\\",\\\"name\\\":\\\"markup.deleted.diff\\\"},{\\\"begin\\\":\\\"^(#)\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.diff\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.line.number-sign.diff\\\"},{\\\"match\\\":\\\"^index [0-9a-f]{7,40}\\\\\\\\.\\\\\\\\.[0-9a-f]{7,40}.*$\\\\\\\\n?\\\",\\\"name\\\":\\\"meta.diff.index.git\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.key-value.diff\\\"},\\\"2\\\":{\\\"name\\\":\\\"meta.toc-list.file-name.diff\\\"}},\\\"match\\\":\\\"^Index(:) (.+)$\\\\\\\\n?\\\",\\\"name\\\":\\\"meta.diff.index\\\"},{\\\"match\\\":\\\"^Only in .*: .*$\\\\\\\\n?\\\",\\\"name\\\":\\\"meta.diff.only-in\\\"}],\\\"scopeName\\\":\\\"source.diff\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/diff.mjs\n"));

/***/ })

}]);