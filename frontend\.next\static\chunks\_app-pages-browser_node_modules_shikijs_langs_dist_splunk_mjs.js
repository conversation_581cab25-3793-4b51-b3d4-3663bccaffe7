"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_splunk_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/splunk.mjs":
/*!*****************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/splunk.mjs ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Splunk Query Language\\\",\\\"fileTypes\\\":[\\\"splunk\\\",\\\"spl\\\"],\\\"name\\\":\\\"splunk\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=([|\\\\\\\\[]))(\\\\\\\\s*)\\\\\\\\b(abstract|accum|addcoltotals|addinfo|addtotals|analyzefields|anomalies|anomalousvalue|append|appendcols|appendpipe|arules|associate|audit|autoregress|bucket|bucketdir|chart|cluster|collect|concurrency|contingency|convert|correlate|crawl|datamodel|dbinspect|dbxquery|dbxlookup|dedup|delete|delta|diff|dispatch|erex|eval|eventcount|eventstats|extract|fieldformat|fields|fieldsummary|file|filldown|fillnull|findtypes|folderize|foreach|format|from|gauge|gentimes|geostats|head|highlight|history|input|inputcsv|inputlookup|iplocation|join|kmeans|kvform|loadjob|localize|localop|lookup|makecontinuous|makemv|makeresults|map|metadata|metasearch|multikv|multisearch|mvcombine|mvexpand|nomv|outlier|outputcsv|outputlookup|outputtext|overlap|pivot|predict|rangemap|rare|regex|relevancy|reltime|rename|replace|rest|return|reverse|rex|rtorder|run|savedsearch|script|scrub|search|searchtxn|selfjoin|sendemail|set|setfields|sichart|sirare|sistats|sitimechart|sitop|sort|spath|stats|strcat|streamstats|table|tags|tail|timechart|top|transaction|transpose|trendline|tscollect|tstats|typeahead|typelearner|typer|uniq|untable|where|x11|xmlkv|xmlunescape|xpath|xyseries)\\\\\\\\b(?=\\\\\\\\s)\\\",\\\"name\\\":\\\"support.class.splunk_search\\\"},{\\\"match\\\":\\\"\\\\\\\\b(abs|acos|acosh|asin|asinh|atan|atan2|atanh|case|cidrmatch|ceiling|coalesce|commands|cos|cosh|exact|exp|floor|hypot|if|in|isbool|isint|isnotnull|isnull|isnum|isstr|len|like|ln|log|lower|ltrim|match|max|md5|min|mvappend|mvcount|mvdedup|mvfilter|mvfind|mvindex|mvjoin|mvrange|mvsort|mvzip|now|null|nullif|pi|pow|printf|random|relative_time|replace|round|rtrim|searchmatch|sha1|sha256|sha512|sigfig|sin|sinh|spath|split|sqrt|strftime|strptime|substr|tan|tanh|time|tonumber|tostring|trim|typeof|upper|urldecode|validate)(?=\\\\\\\\()\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.splunk_search\\\"},{\\\"match\\\":\\\"\\\\\\\\b(avg|count|distinct_count|estdc|estdc_error|eval|max|mean|median|min|mode|percentile|range|stdev|stdevp|sum|sumsq|var|varp|first|last|list|values|earliest|earliest_time|latest|latest_time|per_day|per_hour|per_minute|per_second|rate)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.splunk_search\\\"},{\\\"match\\\":\\\"(?<=`)\\\\\\\\w+(?=[(`])\\\",\\\"name\\\":\\\"entity.name.function.splunk_search\\\"},{\\\"match\\\":\\\"\\\\\\\\b(\\\\\\\\d+)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.splunk_search\\\"},{\\\"match\\\":\\\"(\\\\\\\\\\\\\\\\[\\\\\\\\\\\\\\\\|*=])\\\",\\\"name\\\":\\\"contant.character.escape.splunk_search\\\"},{\\\"match\\\":\\\"(\\\\\\\\|,)\\\",\\\"name\\\":\\\"keyword.operator.splunk_search\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\b(as|by|or|and|over|where|output|outputnew)\\\\\\\\b|(?-i)\\\\\\\\b(NOT|true|false)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.splunk_search\\\"},{\\\"match\\\":\\\"(?<=[(,]|[^=]\\\\\\\\s{300})([^()\\\\\\\",=]+)(?=[),])\\\",\\\"name\\\":\\\"variable.parameter.splunk_search\\\"},{\\\"match\\\":\\\"([\\\\\\\\w.]+)(\\\\\\\\[]|\\\\\\\\{})?(\\\\\\\\s*)(?==)\\\",\\\"name\\\":\\\"variable.splunk_search\\\"},{\\\"match\\\":\\\"=\\\",\\\"name\\\":\\\"keyword.operator.splunk_search\\\"},{\\\"begin\\\":\\\"(?<!\\\\\\\\\\\\\\\\)\\\\\\\"\\\",\\\"end\\\":\\\"(?<!\\\\\\\\\\\\\\\\)\\\\\\\"\\\",\\\"name\\\":\\\"string.quoted.double.splunk_search\\\"},{\\\"begin\\\":\\\"(?<!\\\\\\\\\\\\\\\\)'\\\",\\\"end\\\":\\\"(?<!\\\\\\\\\\\\\\\\)'\\\",\\\"name\\\":\\\"string.quoted.single.splunk_search\\\"},{\\\"begin\\\":\\\"query=\\\\\\\"\\\",\\\"end\\\":\\\"(?<!\\\\\\\\\\\\\\\\)\\\\\\\"\\\",\\\"name\\\":\\\"meta.embedded.block.sql\\\"},{\\\"begin\\\":\\\"(?<!\\\\\\\\\\\\\\\\)```\\\",\\\"end\\\":\\\"(?<!\\\\\\\\\\\\\\\\)```\\\",\\\"name\\\":\\\"comment.block.splunk_search\\\"},{\\\"begin\\\":\\\"`comment\\\\\\\\(\\\",\\\"end\\\":\\\"\\\\\\\\)`\\\",\\\"name\\\":\\\"comment.block.splunk_search\\\"}],\\\"scopeName\\\":\\\"source.splunk_search\\\",\\\"aliases\\\":[\\\"spl\\\"]}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/splunk.mjs\n"));

/***/ })

}]);