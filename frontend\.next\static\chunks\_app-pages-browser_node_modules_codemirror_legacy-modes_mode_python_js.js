"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_codemirror_legacy-modes_mode_python_js"],{

/***/ "(app-pages-browser)/./node_modules/@codemirror/legacy-modes/mode/python.js":
/*!**************************************************************!*\
  !*** ./node_modules/@codemirror/legacy-modes/mode/python.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cython: () => (/* binding */ cython),\n/* harmony export */   mkPython: () => (/* binding */ mkPython),\n/* harmony export */   python: () => (/* binding */ python)\n/* harmony export */ });\nfunction wordRegexp(words) {\n  return new RegExp(\"^((\" + words.join(\")|(\") + \"))\\\\b\");\n}\n\nvar wordOperators = wordRegexp([\"and\", \"or\", \"not\", \"is\"]);\nvar commonKeywords = [\"as\", \"assert\", \"break\", \"class\", \"continue\",\n                      \"def\", \"del\", \"elif\", \"else\", \"except\", \"finally\",\n                      \"for\", \"from\", \"global\", \"if\", \"import\",\n                      \"lambda\", \"pass\", \"raise\", \"return\",\n                      \"try\", \"while\", \"with\", \"yield\", \"in\", \"False\", \"True\"];\nvar commonBuiltins = [\"abs\", \"all\", \"any\", \"bin\", \"bool\", \"bytearray\", \"callable\", \"chr\",\n                      \"classmethod\", \"compile\", \"complex\", \"delattr\", \"dict\", \"dir\", \"divmod\",\n                      \"enumerate\", \"eval\", \"filter\", \"float\", \"format\", \"frozenset\",\n                      \"getattr\", \"globals\", \"hasattr\", \"hash\", \"help\", \"hex\", \"id\",\n                      \"input\", \"int\", \"isinstance\", \"issubclass\", \"iter\", \"len\",\n                      \"list\", \"locals\", \"map\", \"max\", \"memoryview\", \"min\", \"next\",\n                      \"object\", \"oct\", \"open\", \"ord\", \"pow\", \"property\", \"range\",\n                      \"repr\", \"reversed\", \"round\", \"set\", \"setattr\", \"slice\",\n                      \"sorted\", \"staticmethod\", \"str\", \"sum\", \"super\", \"tuple\",\n                      \"type\", \"vars\", \"zip\", \"__import__\", \"NotImplemented\",\n                      \"Ellipsis\", \"__debug__\"];\n\nfunction top(state) {\n  return state.scopes[state.scopes.length - 1];\n}\n\nfunction mkPython(parserConf) {\n  var ERRORCLASS = \"error\";\n\n  var delimiters = parserConf.delimiters || parserConf.singleDelimiters || /^[\\(\\)\\[\\]\\{\\}@,:`=;\\.\\\\]/;\n  //               (Backwards-compatibility with old, cumbersome config system)\n  var operators = [parserConf.singleOperators, parserConf.doubleOperators, parserConf.doubleDelimiters, parserConf.tripleDelimiters,\n                   parserConf.operators || /^([-+*/%\\/&|^]=?|[<>=]+|\\/\\/=?|\\*\\*=?|!=|[~!@]|\\.\\.\\.)/]\n  for (var i = 0; i < operators.length; i++) if (!operators[i]) operators.splice(i--, 1)\n\n  var hangingIndent = parserConf.hangingIndent;\n\n  var myKeywords = commonKeywords, myBuiltins = commonBuiltins;\n  if (parserConf.extra_keywords != undefined)\n    myKeywords = myKeywords.concat(parserConf.extra_keywords);\n\n  if (parserConf.extra_builtins != undefined)\n    myBuiltins = myBuiltins.concat(parserConf.extra_builtins);\n\n  var py3 = !(parserConf.version && Number(parserConf.version) < 3)\n  if (py3) {\n    // since http://legacy.python.org/dev/peps/pep-0465/ @ is also an operator\n    var identifiers = parserConf.identifiers|| /^[_A-Za-z\\u00A1-\\uFFFF][_A-Za-z0-9\\u00A1-\\uFFFF]*/;\n    myKeywords = myKeywords.concat([\"nonlocal\", \"None\", \"aiter\", \"anext\", \"async\", \"await\", \"breakpoint\", \"match\", \"case\"]);\n    myBuiltins = myBuiltins.concat([\"ascii\", \"bytes\", \"exec\", \"print\"]);\n    var stringPrefixes = new RegExp(\"^(([rbuf]|(br)|(rb)|(fr)|(rf))?('{3}|\\\"{3}|['\\\"]))\", \"i\");\n  } else {\n    var identifiers = parserConf.identifiers|| /^[_A-Za-z][_A-Za-z0-9]*/;\n    myKeywords = myKeywords.concat([\"exec\", \"print\"]);\n    myBuiltins = myBuiltins.concat([\"apply\", \"basestring\", \"buffer\", \"cmp\", \"coerce\", \"execfile\",\n                                    \"file\", \"intern\", \"long\", \"raw_input\", \"reduce\", \"reload\",\n                                    \"unichr\", \"unicode\", \"xrange\", \"None\"]);\n    var stringPrefixes = new RegExp(\"^(([rubf]|(ur)|(br))?('{3}|\\\"{3}|['\\\"]))\", \"i\");\n  }\n  var keywords = wordRegexp(myKeywords);\n  var builtins = wordRegexp(myBuiltins);\n\n  // tokenizers\n  function tokenBase(stream, state) {\n    var sol = stream.sol() && state.lastToken != \"\\\\\"\n    if (sol) state.indent = stream.indentation()\n    // Handle scope changes\n    if (sol && top(state).type == \"py\") {\n      var scopeOffset = top(state).offset;\n      if (stream.eatSpace()) {\n        var lineOffset = stream.indentation();\n        if (lineOffset > scopeOffset)\n          pushPyScope(stream, state);\n        else if (lineOffset < scopeOffset && dedent(stream, state) && stream.peek() != \"#\")\n          state.errorToken = true;\n        return null;\n      } else {\n        var style = tokenBaseInner(stream, state);\n        if (scopeOffset > 0 && dedent(stream, state))\n          style += \" \" + ERRORCLASS;\n        return style;\n      }\n    }\n    return tokenBaseInner(stream, state);\n  }\n\n  function tokenBaseInner(stream, state, inFormat) {\n    if (stream.eatSpace()) return null;\n\n    // Handle Comments\n    if (!inFormat && stream.match(/^#.*/)) return \"comment\";\n\n    // Handle Number Literals\n    if (stream.match(/^[0-9\\.]/, false)) {\n      var floatLiteral = false;\n      // Floats\n      if (stream.match(/^[\\d_]*\\.\\d+(e[\\+\\-]?\\d+)?/i)) { floatLiteral = true; }\n      if (stream.match(/^[\\d_]+\\.\\d*/)) { floatLiteral = true; }\n      if (stream.match(/^\\.\\d+/)) { floatLiteral = true; }\n      if (floatLiteral) {\n        // Float literals may be \"imaginary\"\n        stream.eat(/J/i);\n        return \"number\";\n      }\n      // Integers\n      var intLiteral = false;\n      // Hex\n      if (stream.match(/^0x[0-9a-f_]+/i)) intLiteral = true;\n      // Binary\n      if (stream.match(/^0b[01_]+/i)) intLiteral = true;\n      // Octal\n      if (stream.match(/^0o[0-7_]+/i)) intLiteral = true;\n      // Decimal\n      if (stream.match(/^[1-9][\\d_]*(e[\\+\\-]?[\\d_]+)?/)) {\n        // Decimal literals may be \"imaginary\"\n        stream.eat(/J/i);\n        // TODO - Can you have imaginary longs?\n        intLiteral = true;\n      }\n      // Zero by itself with no other piece of number.\n      if (stream.match(/^0(?![\\dx])/i)) intLiteral = true;\n      if (intLiteral) {\n        // Integer literals may be \"long\"\n        stream.eat(/L/i);\n        return \"number\";\n      }\n    }\n\n    // Handle Strings\n    if (stream.match(stringPrefixes)) {\n      var isFmtString = stream.current().toLowerCase().indexOf('f') !== -1;\n      if (!isFmtString) {\n        state.tokenize = tokenStringFactory(stream.current(), state.tokenize);\n        return state.tokenize(stream, state);\n      } else {\n        state.tokenize = formatStringFactory(stream.current(), state.tokenize);\n        return state.tokenize(stream, state);\n      }\n    }\n\n    for (var i = 0; i < operators.length; i++)\n      if (stream.match(operators[i])) return \"operator\"\n\n    if (stream.match(delimiters)) return \"punctuation\";\n\n    if (state.lastToken == \".\" && stream.match(identifiers))\n      return \"property\";\n\n    if (stream.match(keywords) || stream.match(wordOperators))\n      return \"keyword\";\n\n    if (stream.match(builtins))\n      return \"builtin\";\n\n    if (stream.match(/^(self|cls)\\b/))\n      return \"self\";\n\n    if (stream.match(identifiers)) {\n      if (state.lastToken == \"def\" || state.lastToken == \"class\")\n        return \"def\";\n      return \"variable\";\n    }\n\n    // Handle non-detected items\n    stream.next();\n    return inFormat ? null :ERRORCLASS;\n  }\n\n  function formatStringFactory(delimiter, tokenOuter) {\n    while (\"rubf\".indexOf(delimiter.charAt(0).toLowerCase()) >= 0)\n      delimiter = delimiter.substr(1);\n\n    var singleline = delimiter.length == 1;\n    var OUTCLASS = \"string\";\n\n    function tokenNestedExpr(depth) {\n      return function(stream, state) {\n        var inner = tokenBaseInner(stream, state, true)\n        if (inner == \"punctuation\") {\n          if (stream.current() == \"{\") {\n            state.tokenize = tokenNestedExpr(depth + 1)\n          } else if (stream.current() == \"}\") {\n            if (depth > 1) state.tokenize = tokenNestedExpr(depth - 1)\n            else state.tokenize = tokenString\n          }\n        }\n        return inner\n      }\n    }\n\n    function tokenString(stream, state) {\n      while (!stream.eol()) {\n        stream.eatWhile(/[^'\"\\{\\}\\\\]/);\n        if (stream.eat(\"\\\\\")) {\n          stream.next();\n          if (singleline && stream.eol())\n            return OUTCLASS;\n        } else if (stream.match(delimiter)) {\n          state.tokenize = tokenOuter;\n          return OUTCLASS;\n        } else if (stream.match('{{')) {\n          // ignore {{ in f-str\n          return OUTCLASS;\n        } else if (stream.match('{', false)) {\n          // switch to nested mode\n          state.tokenize = tokenNestedExpr(0)\n          if (stream.current()) return OUTCLASS;\n          else return state.tokenize(stream, state)\n        } else if (stream.match('}}')) {\n          return OUTCLASS;\n        } else if (stream.match('}')) {\n          // single } in f-string is an error\n          return ERRORCLASS;\n        } else {\n          stream.eat(/['\"]/);\n        }\n      }\n      if (singleline) {\n        if (parserConf.singleLineStringErrors)\n          return ERRORCLASS;\n        else\n          state.tokenize = tokenOuter;\n      }\n      return OUTCLASS;\n    }\n    tokenString.isString = true;\n    return tokenString;\n  }\n\n  function tokenStringFactory(delimiter, tokenOuter) {\n    while (\"rubf\".indexOf(delimiter.charAt(0).toLowerCase()) >= 0)\n      delimiter = delimiter.substr(1);\n\n    var singleline = delimiter.length == 1;\n    var OUTCLASS = \"string\";\n\n    function tokenString(stream, state) {\n      while (!stream.eol()) {\n        stream.eatWhile(/[^'\"\\\\]/);\n        if (stream.eat(\"\\\\\")) {\n          stream.next();\n          if (singleline && stream.eol())\n            return OUTCLASS;\n        } else if (stream.match(delimiter)) {\n          state.tokenize = tokenOuter;\n          return OUTCLASS;\n        } else {\n          stream.eat(/['\"]/);\n        }\n      }\n      if (singleline) {\n        if (parserConf.singleLineStringErrors)\n          return ERRORCLASS;\n        else\n          state.tokenize = tokenOuter;\n      }\n      return OUTCLASS;\n    }\n    tokenString.isString = true;\n    return tokenString;\n  }\n\n  function pushPyScope(stream, state) {\n    while (top(state).type != \"py\") state.scopes.pop()\n    state.scopes.push({offset: top(state).offset + stream.indentUnit,\n                       type: \"py\",\n                       align: null})\n  }\n\n  function pushBracketScope(stream, state, type) {\n    var align = stream.match(/^[\\s\\[\\{\\(]*(?:#|$)/, false) ? null : stream.column() + 1\n    state.scopes.push({offset: state.indent + (hangingIndent || stream.indentUnit),\n                       type: type,\n                       align: align})\n  }\n\n  function dedent(stream, state) {\n    var indented = stream.indentation();\n    while (state.scopes.length > 1 && top(state).offset > indented) {\n      if (top(state).type != \"py\") return true;\n      state.scopes.pop();\n    }\n    return top(state).offset != indented;\n  }\n\n  function tokenLexer(stream, state) {\n    if (stream.sol()) {\n      state.beginningOfLine = true;\n      state.dedent = false;\n    }\n\n    var style = state.tokenize(stream, state);\n    var current = stream.current();\n\n    // Handle decorators\n    if (state.beginningOfLine && current == \"@\")\n      return stream.match(identifiers, false) ? \"meta\" : py3 ? \"operator\" : ERRORCLASS;\n\n    if (/\\S/.test(current)) state.beginningOfLine = false;\n\n    if ((style == \"variable\" || style == \"builtin\")\n        && state.lastToken == \"meta\")\n      style = \"meta\";\n\n    // Handle scope changes.\n    if (current == \"pass\" || current == \"return\")\n      state.dedent = true;\n\n    if (current == \"lambda\") state.lambda = true;\n    if (current == \":\" && !state.lambda && top(state).type == \"py\" && stream.match(/^\\s*(?:#|$)/, false))\n      pushPyScope(stream, state);\n\n    if (current.length == 1 && !/string|comment/.test(style)) {\n      var delimiter_index = \"[({\".indexOf(current);\n      if (delimiter_index != -1)\n        pushBracketScope(stream, state, \"])}\".slice(delimiter_index, delimiter_index+1));\n\n      delimiter_index = \"])}\".indexOf(current);\n      if (delimiter_index != -1) {\n        if (top(state).type == current) state.indent = state.scopes.pop().offset - (hangingIndent || stream.indentUnit)\n        else return ERRORCLASS;\n      }\n    }\n    if (state.dedent && stream.eol() && top(state).type == \"py\" && state.scopes.length > 1)\n      state.scopes.pop();\n\n    return style;\n  }\n\n  return {\n    name: \"python\",\n\n    startState: function() {\n      return {\n        tokenize: tokenBase,\n        scopes: [{offset: 0, type: \"py\", align: null}],\n        indent: 0,\n        lastToken: null,\n        lambda: false,\n        dedent: 0\n      };\n    },\n\n    token: function(stream, state) {\n      var addErr = state.errorToken;\n      if (addErr) state.errorToken = false;\n      var style = tokenLexer(stream, state);\n\n      if (style && style != \"comment\")\n        state.lastToken = (style == \"keyword\" || style == \"punctuation\") ? stream.current() : style;\n      if (style == \"punctuation\") style = null;\n\n      if (stream.eol() && state.lambda)\n        state.lambda = false;\n      return addErr ? ERRORCLASS : style;\n    },\n\n    indent: function(state, textAfter, cx) {\n      if (state.tokenize != tokenBase)\n        return state.tokenize.isString ? null : 0;\n\n      var scope = top(state)\n      var closing = scope.type == textAfter.charAt(0) ||\n          scope.type == \"py\" && !state.dedent && /^(else:|elif |except |finally:)/.test(textAfter)\n      if (scope.align != null)\n        return scope.align - (closing ? 1 : 0)\n      else\n        return scope.offset - (closing ? hangingIndent || cx.unit : 0)\n    },\n\n    languageData: {\n      autocomplete: commonKeywords.concat(commonBuiltins).concat([\"exec\", \"print\"]),\n      indentOnInput: /^\\s*([\\}\\]\\)]|else:|elif |except |finally:)$/,\n      commentTokens: {line: \"#\"},\n      closeBrackets: {brackets: [\"(\", \"[\", \"{\", \"'\", '\"', \"'''\", '\"\"\"']}\n    }\n  };\n};\n\nvar words = function(str) { return str.split(\" \"); };\n\nconst python = mkPython({})\n\nconst cython = mkPython({\n  extra_keywords: words(\"by cdef cimport cpdef ctypedef enum except \"+\n                        \"extern gil include nogil property public \"+\n                        \"readonly struct union DEF IF ELIF ELSE\")\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@codemirror/legacy-modes/mode/python.js\n"));

/***/ })

}]);