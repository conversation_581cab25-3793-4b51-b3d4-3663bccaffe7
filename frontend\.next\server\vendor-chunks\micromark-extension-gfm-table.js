"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark-extension-gfm-table";
exports.ids = ["vendor-chunks/micromark-extension-gfm-table"];
exports.modules = {

/***/ "(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/edit-map.js":
/*!************************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm-table/dev/lib/edit-map.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EditMap: () => (/* binding */ EditMap)\n/* harmony export */ });\n/**\n * @import {Event} from 'micromark-util-types'\n */\n\n// Port of `edit_map.rs` from `markdown-rs`.\n// This should move to `markdown-js` later.\n\n// Deal with several changes in events, batching them together.\n//\n// Preferably, changes should be kept to a minimum.\n// Sometimes, it’s needed to change the list of events, because parsing can be\n// messy, and it helps to expose a cleaner interface of events to the compiler\n// and other users.\n// It can also help to merge many adjacent similar events.\n// And, in other cases, it’s needed to parse subcontent: pass some events\n// through another tokenizer and inject the result.\n\n/**\n * @typedef {[number, number, Array<Event>]} Change\n * @typedef {[number, number, number]} Jump\n */\n\n/**\n * Tracks a bunch of edits.\n */\nclass EditMap {\n  /**\n   * Create a new edit map.\n   */\n  constructor() {\n    /**\n     * Record of changes.\n     *\n     * @type {Array<Change>}\n     */\n    this.map = []\n  }\n\n  /**\n   * Create an edit: a remove and/or add at a certain place.\n   *\n   * @param {number} index\n   * @param {number} remove\n   * @param {Array<Event>} add\n   * @returns {undefined}\n   */\n  add(index, remove, add) {\n    addImplementation(this, index, remove, add)\n  }\n\n  // To do: add this when moving to `micromark`.\n  // /**\n  //  * Create an edit: but insert `add` before existing additions.\n  //  *\n  //  * @param {number} index\n  //  * @param {number} remove\n  //  * @param {Array<Event>} add\n  //  * @returns {undefined}\n  //  */\n  // addBefore(index, remove, add) {\n  //   addImplementation(this, index, remove, add, true)\n  // }\n\n  /**\n   * Done, change the events.\n   *\n   * @param {Array<Event>} events\n   * @returns {undefined}\n   */\n  consume(events) {\n    this.map.sort(function (a, b) {\n      return a[0] - b[0]\n    })\n\n    /* c8 ignore next 3 -- `resolve` is never called without tables, so without edits. */\n    if (this.map.length === 0) {\n      return\n    }\n\n    // To do: if links are added in events, like they are in `markdown-rs`,\n    // this is needed.\n    // // Calculate jumps: where items in the current list move to.\n    // /** @type {Array<Jump>} */\n    // const jumps = []\n    // let index = 0\n    // let addAcc = 0\n    // let removeAcc = 0\n    // while (index < this.map.length) {\n    //   const [at, remove, add] = this.map[index]\n    //   removeAcc += remove\n    //   addAcc += add.length\n    //   jumps.push([at, removeAcc, addAcc])\n    //   index += 1\n    // }\n    //\n    // . shiftLinks(events, jumps)\n\n    let index = this.map.length\n    /** @type {Array<Array<Event>>} */\n    const vecs = []\n    while (index > 0) {\n      index -= 1\n      vecs.push(\n        events.slice(this.map[index][0] + this.map[index][1]),\n        this.map[index][2]\n      )\n\n      // Truncate rest.\n      events.length = this.map[index][0]\n    }\n\n    vecs.push(events.slice())\n    events.length = 0\n\n    let slice = vecs.pop()\n\n    while (slice) {\n      for (const element of slice) {\n        events.push(element)\n      }\n\n      slice = vecs.pop()\n    }\n\n    // Truncate everything.\n    this.map.length = 0\n  }\n}\n\n/**\n * Create an edit.\n *\n * @param {EditMap} editMap\n * @param {number} at\n * @param {number} remove\n * @param {Array<Event>} add\n * @returns {undefined}\n */\nfunction addImplementation(editMap, at, remove, add) {\n  let index = 0\n\n  /* c8 ignore next 3 -- `resolve` is never called without tables, so without edits. */\n  if (remove === 0 && add.length === 0) {\n    return\n  }\n\n  while (index < editMap.map.length) {\n    if (editMap.map[index][0] === at) {\n      editMap.map[index][1] += remove\n\n      // To do: before not used by tables, use when moving to micromark.\n      // if (before) {\n      //   add.push(...editMap.map[index][2])\n      //   editMap.map[index][2] = add\n      // } else {\n      editMap.map[index][2].push(...add)\n      // }\n\n      return\n    }\n\n    index += 1\n  }\n\n  editMap.map.push([at, remove, add])\n}\n\n// /**\n//  * Shift `previous` and `next` links according to `jumps`.\n//  *\n//  * This fixes links in case there are events removed or added between them.\n//  *\n//  * @param {Array<Event>} events\n//  * @param {Array<Jump>} jumps\n//  */\n// function shiftLinks(events, jumps) {\n//   let jumpIndex = 0\n//   let index = 0\n//   let add = 0\n//   let rm = 0\n\n//   while (index < events.length) {\n//     const rmCurr = rm\n\n//     while (jumpIndex < jumps.length && jumps[jumpIndex][0] <= index) {\n//       add = jumps[jumpIndex][2]\n//       rm = jumps[jumpIndex][1]\n//       jumpIndex += 1\n//     }\n\n//     // Ignore items that will be removed.\n//     if (rm > rmCurr) {\n//       index += rm - rmCurr\n//     } else {\n//       // ?\n//       // if let Some(link) = &events[index].link {\n//       //     if let Some(next) = link.next {\n//       //         events[next].link.as_mut().unwrap().previous = Some(index + add - rm);\n//       //         while jumpIndex < jumps.len() && jumps[jumpIndex].0 <= next {\n//       //             add = jumps[jumpIndex].2;\n//       //             rm = jumps[jumpIndex].1;\n//       //             jumpIndex += 1;\n//       //         }\n//       //         events[index].link.as_mut().unwrap().next = Some(next + add - rm);\n//       //         index = next;\n//       //         continue;\n//       //     }\n//       // }\n//       index += 1\n//     }\n//   }\n// }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/edit-map.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/html.js":
/*!********************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm-table/dev/lib/html.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmTableHtml: () => (/* binding */ gfmTableHtml)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/**\n * @import {HtmlExtension} from 'micromark-util-types'\n */\n\n\n\nconst alignment = {\n  none: '',\n  left: ' align=\"left\"',\n  right: ' align=\"right\"',\n  center: ' align=\"center\"'\n}\n\n// To do: micromark@5: use `infer` here, when all events are exposed.\n\n/**\n * Create an HTML extension for `micromark` to support GitHub tables when\n * serializing to HTML.\n *\n * @returns {HtmlExtension}\n *   Extension for `micromark` that can be passed in `htmlExtensions` to\n *   support GitHub tables when serializing to HTML.\n */\nfunction gfmTableHtml() {\n  return {\n    enter: {\n      table(token) {\n        const tableAlign = token._align\n        ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(tableAlign, 'expected `_align`')\n        this.lineEndingIfNeeded()\n        this.tag('<table>')\n        this.setData('tableAlign', tableAlign)\n      },\n      tableBody() {\n        this.tag('<tbody>')\n      },\n      tableData() {\n        const tableAlign = this.getData('tableAlign')\n        const tableColumn = this.getData('tableColumn')\n        ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(tableAlign, 'expected `tableAlign`')\n        ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(typeof tableColumn === 'number', 'expected `tableColumn`')\n        const align = alignment[tableAlign[tableColumn]]\n\n        if (align === undefined) {\n          // Capture results to ignore them.\n          this.buffer()\n        } else {\n          this.lineEndingIfNeeded()\n          this.tag('<td' + align + '>')\n        }\n      },\n      tableHead() {\n        this.lineEndingIfNeeded()\n        this.tag('<thead>')\n      },\n      tableHeader() {\n        const tableAlign = this.getData('tableAlign')\n        const tableColumn = this.getData('tableColumn')\n        ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(tableAlign, 'expected `tableAlign`')\n        ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(typeof tableColumn === 'number', 'expected `tableColumn`')\n        const align = alignment[tableAlign[tableColumn]]\n        this.lineEndingIfNeeded()\n        this.tag('<th' + align + '>')\n      },\n      tableRow() {\n        this.setData('tableColumn', 0)\n        this.lineEndingIfNeeded()\n        this.tag('<tr>')\n      }\n    },\n    exit: {\n      // Overwrite the default code text data handler to unescape escaped pipes when\n      // they are in tables.\n      codeTextData(token) {\n        let value = this.sliceSerialize(token)\n\n        if (this.getData('tableAlign')) {\n          value = value.replace(/\\\\([\\\\|])/g, replace)\n        }\n\n        this.raw(this.encode(value))\n      },\n      table() {\n        this.setData('tableAlign')\n        // Note: we don’t set `slurpAllLineEndings` anymore, in delimiter rows,\n        // but we do need to reset it to match a funky newline GH generates for\n        // list items combined with tables.\n        this.setData('slurpAllLineEndings')\n        this.lineEndingIfNeeded()\n        this.tag('</table>')\n      },\n      tableBody() {\n        this.lineEndingIfNeeded()\n        this.tag('</tbody>')\n      },\n      tableData() {\n        const tableAlign = this.getData('tableAlign')\n        const tableColumn = this.getData('tableColumn')\n        ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(tableAlign, 'expected `tableAlign`')\n        ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(typeof tableColumn === 'number', 'expected `tableColumn`')\n\n        if (tableColumn in tableAlign) {\n          this.tag('</td>')\n          this.setData('tableColumn', tableColumn + 1)\n        } else {\n          // Stop capturing.\n          this.resume()\n        }\n      },\n      tableHead() {\n        this.lineEndingIfNeeded()\n        this.tag('</thead>')\n      },\n      tableHeader() {\n        const tableColumn = this.getData('tableColumn')\n        ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(typeof tableColumn === 'number', 'expected `tableColumn`')\n        this.tag('</th>')\n        this.setData('tableColumn', tableColumn + 1)\n      },\n      tableRow() {\n        const tableAlign = this.getData('tableAlign')\n        let tableColumn = this.getData('tableColumn')\n        ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(tableAlign, 'expected `tableAlign`')\n        ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(typeof tableColumn === 'number', 'expected `tableColumn`')\n\n        while (tableColumn < tableAlign.length) {\n          this.lineEndingIfNeeded()\n          this.tag('<td' + alignment[tableAlign[tableColumn]] + '></td>')\n          tableColumn++\n        }\n\n        this.setData('tableColumn', tableColumn)\n        this.lineEndingIfNeeded()\n        this.tag('</tr>')\n      }\n    }\n  }\n}\n\n/**\n * @param {string} $0\n * @param {string} $1\n * @returns {string}\n */\nfunction replace($0, $1) {\n  // Pipes work, backslashes don’t (but can’t escape pipes).\n  return $1 === '|' ? $1 : $0\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/html.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/infer.js":
/*!*********************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm-table/dev/lib/infer.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmTableAlign: () => (/* binding */ gfmTableAlign)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/**\n * @import {Event} from 'micromark-util-types'\n */\n\n/**\n * @typedef {'center' | 'left' | 'none' | 'right'} Align\n */\n\n\n\n/**\n * Figure out the alignment of a GFM table.\n *\n * @param {Readonly<Array<Event>>} events\n *   List of events.\n * @param {number} index\n *   Table enter event.\n * @returns {Array<Align>}\n *   List of aligns.\n */\nfunction gfmTableAlign(events, index) {\n  (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(events[index][1].type === 'table', 'expected table')\n  let inDelimiterRow = false\n  /** @type {Array<Align>} */\n  const align = []\n\n  while (index < events.length) {\n    const event = events[index]\n\n    if (inDelimiterRow) {\n      if (event[0] === 'enter') {\n        // Start of alignment value: set a new column.\n        // To do: `markdown-rs` uses `tableDelimiterCellValue`.\n        if (event[1].type === 'tableContent') {\n          align.push(\n            events[index + 1][1].type === 'tableDelimiterMarker'\n              ? 'left'\n              : 'none'\n          )\n        }\n      }\n      // Exits:\n      // End of alignment value: change the column.\n      // To do: `markdown-rs` uses `tableDelimiterCellValue`.\n      else if (event[1].type === 'tableContent') {\n        if (events[index - 1][1].type === 'tableDelimiterMarker') {\n          const alignIndex = align.length - 1\n\n          align[alignIndex] = align[alignIndex] === 'left' ? 'center' : 'right'\n        }\n      }\n      // Done!\n      else if (event[1].type === 'tableDelimiterRow') {\n        break\n      }\n    } else if (event[0] === 'enter' && event[1].type === 'tableDelimiterRow') {\n      inDelimiterRow = true\n    }\n\n    index += 1\n  }\n\n  return align\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/infer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/syntax.js":
/*!**********************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm-table/dev/lib/syntax.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmTable: () => (/* binding */ gfmTable)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/constants.js\");\n/* harmony import */ var _edit_map_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./edit-map.js */ \"(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/edit-map.js\");\n/* harmony import */ var _infer_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./infer.js */ \"(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/infer.js\");\n/**\n * @import {Event, Extension, Point, Resolver, State, Token, TokenizeContext, Tokenizer} from 'micromark-util-types'\n */\n\n/**\n * @typedef {[number, number, number, number]} Range\n *   Cell info.\n *\n * @typedef {0 | 1 | 2 | 3} RowKind\n *   Where we are: `1` for head row, `2` for delimiter row, `3` for body row.\n */\n\n\n\n\n\n\n\n\n/**\n * Create an HTML extension for `micromark` to support GitHub tables syntax.\n *\n * @returns {Extension}\n *   Extension for `micromark` that can be passed in `extensions` to enable GFM\n *   table syntax.\n */\nfunction gfmTable() {\n  return {\n    flow: {\n      null: {name: 'table', tokenize: tokenizeTable, resolveAll: resolveTable}\n    }\n  }\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeTable(effects, ok, nok) {\n  const self = this\n  let size = 0\n  let sizeB = 0\n  /** @type {boolean | undefined} */\n  let seen\n\n  return start\n\n  /**\n   * Start of a GFM table.\n   *\n   * If there is a valid table row or table head before, then we try to parse\n   * another row.\n   * Otherwise, we try to parse a head.\n   *\n   * ```markdown\n   * > | | a |\n   *     ^\n   *   | | - |\n   * > | | b |\n   *     ^\n   * ```\n   * @type {State}\n   */\n  function start(code) {\n    let index = self.events.length - 1\n\n    while (index > -1) {\n      const type = self.events[index][1].type\n      if (\n        type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.lineEnding ||\n        // Note: markdown-rs uses `whitespace` instead of `linePrefix`\n        type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.linePrefix\n      )\n        index--\n      else break\n    }\n\n    const tail = index > -1 ? self.events[index][1].type : null\n\n    const next =\n      tail === 'tableHead' || tail === 'tableRow' ? bodyRowStart : headRowBefore\n\n    // Don’t allow lazy body rows.\n    if (next === bodyRowStart && self.parser.lazy[self.now().line]) {\n      return nok(code)\n    }\n\n    return next(code)\n  }\n\n  /**\n   * Before table head row.\n   *\n   * ```markdown\n   * > | | a |\n   *     ^\n   *   | | - |\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */\n  function headRowBefore(code) {\n    effects.enter('tableHead')\n    effects.enter('tableRow')\n    return headRowStart(code)\n  }\n\n  /**\n   * Before table head row, after whitespace.\n   *\n   * ```markdown\n   * > | | a |\n   *     ^\n   *   | | - |\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */\n  function headRowStart(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.verticalBar) {\n      return headRowBreak(code)\n    }\n\n    // To do: micromark-js should let us parse our own whitespace in extensions,\n    // like `markdown-rs`:\n    //\n    // ```js\n    // // 4+ spaces.\n    // if (markdownSpace(code)) {\n    //   return nok(code)\n    // }\n    // ```\n\n    seen = true\n    // Count the first character, that isn’t a pipe, double.\n    sizeB += 1\n    return headRowBreak(code)\n  }\n\n  /**\n   * At break in table head row.\n   *\n   * ```markdown\n   * > | | a |\n   *     ^\n   *       ^\n   *         ^\n   *   | | - |\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */\n  function headRowBreak(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof) {\n      // Note: in `markdown-rs`, we need to reset, in `micromark-js` we don‘t.\n      return nok(code)\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code)) {\n      // If anything other than one pipe (ignoring whitespace) was used, it’s fine.\n      if (sizeB > 1) {\n        sizeB = 0\n        // To do: check if this works.\n        // Feel free to interrupt:\n        self.interrupt = true\n        effects.exit('tableRow')\n        effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.lineEnding)\n        effects.consume(code)\n        effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.lineEnding)\n        return headDelimiterStart\n      }\n\n      // Note: in `markdown-rs`, we need to reset, in `micromark-js` we don‘t.\n      return nok(code)\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownSpace)(code)) {\n      // To do: check if this is fine.\n      // effects.attempt(State::Next(StateName::GfmTableHeadRowBreak), State::Nok)\n      // State::Retry(space_or_tab(tokenizer))\n      return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(effects, headRowBreak, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.whitespace)(code)\n    }\n\n    sizeB += 1\n\n    if (seen) {\n      seen = false\n      // Header cell count.\n      size += 1\n    }\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.verticalBar) {\n      effects.enter('tableCellDivider')\n      effects.consume(code)\n      effects.exit('tableCellDivider')\n      // Whether a delimiter was seen.\n      seen = true\n      return headRowBreak\n    }\n\n    // Anything else is cell data.\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.data)\n    return headRowData(code)\n  }\n\n  /**\n   * In table head row data.\n   *\n   * ```markdown\n   * > | | a |\n   *       ^\n   *   | | - |\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */\n  function headRowData(code) {\n    if (\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.verticalBar ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEndingOrSpace)(code)\n    ) {\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.data)\n      return headRowBreak(code)\n    }\n\n    effects.consume(code)\n    return code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.backslash ? headRowEscape : headRowData\n  }\n\n  /**\n   * In table head row escape.\n   *\n   * ```markdown\n   * > | | a\\-b |\n   *         ^\n   *   | | ---- |\n   *   | | c    |\n   * ```\n   *\n   * @type {State}\n   */\n  function headRowEscape(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.backslash || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.verticalBar) {\n      effects.consume(code)\n      return headRowData\n    }\n\n    return headRowData(code)\n  }\n\n  /**\n   * Before delimiter row.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | - |\n   *     ^\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterStart(code) {\n    // Reset `interrupt`.\n    self.interrupt = false\n\n    // Note: in `markdown-rs`, we need to handle piercing here too.\n    if (self.parser.lazy[self.now().line]) {\n      return nok(code)\n    }\n\n    effects.enter('tableDelimiterRow')\n    // Track if we’ve seen a `:` or `|`.\n    seen = false\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownSpace)(code)) {\n      (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(self.parser.constructs.disable.null, 'expected `disabled.null`')\n      return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(\n        effects,\n        headDelimiterBefore,\n        micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.linePrefix,\n        self.parser.constructs.disable.null.includes('codeIndented')\n          ? undefined\n          : micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.constants.tabSize\n      )(code)\n    }\n\n    return headDelimiterBefore(code)\n  }\n\n  /**\n   * Before delimiter row, after optional whitespace.\n   *\n   * Reused when a `|` is found later, to parse another cell.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | - |\n   *     ^\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterBefore(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dash || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.colon) {\n      return headDelimiterValueBefore(code)\n    }\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.verticalBar) {\n      seen = true\n      // If we start with a pipe, we open a cell marker.\n      effects.enter('tableCellDivider')\n      effects.consume(code)\n      effects.exit('tableCellDivider')\n      return headDelimiterCellBefore\n    }\n\n    // More whitespace / empty row not allowed at start.\n    return headDelimiterNok(code)\n  }\n\n  /**\n   * After `|`, before delimiter cell.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | - |\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterCellBefore(code) {\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownSpace)(code)) {\n      return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(\n        effects,\n        headDelimiterValueBefore,\n        micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.whitespace\n      )(code)\n    }\n\n    return headDelimiterValueBefore(code)\n  }\n\n  /**\n   * Before delimiter cell value.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | - |\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterValueBefore(code) {\n    // Align: left.\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.colon) {\n      sizeB += 1\n      seen = true\n\n      effects.enter('tableDelimiterMarker')\n      effects.consume(code)\n      effects.exit('tableDelimiterMarker')\n      return headDelimiterLeftAlignmentAfter\n    }\n\n    // Align: none.\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dash) {\n      sizeB += 1\n      // To do: seems weird that this *isn’t* left aligned, but that state is used?\n      return headDelimiterLeftAlignmentAfter(code)\n    }\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code)) {\n      return headDelimiterCellAfter(code)\n    }\n\n    return headDelimiterNok(code)\n  }\n\n  /**\n   * After delimiter cell left alignment marker.\n   *\n   * ```markdown\n   *   | | a  |\n   * > | | :- |\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterLeftAlignmentAfter(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dash) {\n      effects.enter('tableDelimiterFiller')\n      return headDelimiterFiller(code)\n    }\n\n    // Anything else is not ok after the left-align colon.\n    return headDelimiterNok(code)\n  }\n\n  /**\n   * In delimiter cell filler.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | - |\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterFiller(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dash) {\n      effects.consume(code)\n      return headDelimiterFiller\n    }\n\n    // Align is `center` if it was `left`, `right` otherwise.\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.colon) {\n      seen = true\n      effects.exit('tableDelimiterFiller')\n      effects.enter('tableDelimiterMarker')\n      effects.consume(code)\n      effects.exit('tableDelimiterMarker')\n      return headDelimiterRightAlignmentAfter\n    }\n\n    effects.exit('tableDelimiterFiller')\n    return headDelimiterRightAlignmentAfter(code)\n  }\n\n  /**\n   * After delimiter cell right alignment marker.\n   *\n   * ```markdown\n   *   | |  a |\n   * > | | -: |\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterRightAlignmentAfter(code) {\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownSpace)(code)) {\n      return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(\n        effects,\n        headDelimiterCellAfter,\n        micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.whitespace\n      )(code)\n    }\n\n    return headDelimiterCellAfter(code)\n  }\n\n  /**\n   * After delimiter cell.\n   *\n   * ```markdown\n   *   | |  a |\n   * > | | -: |\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterCellAfter(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.verticalBar) {\n      return headDelimiterBefore(code)\n    }\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code)) {\n      // Exit when:\n      // * there was no `:` or `|` at all (it’s a thematic break or setext\n      //   underline instead)\n      // * the header cell count is not the delimiter cell count\n      if (!seen || size !== sizeB) {\n        return headDelimiterNok(code)\n      }\n\n      // Note: in markdown-rs`, a reset is needed here.\n      effects.exit('tableDelimiterRow')\n      effects.exit('tableHead')\n      // To do: in `markdown-rs`, resolvers need to be registered manually.\n      // effects.register_resolver(ResolveName::GfmTable)\n      return ok(code)\n    }\n\n    return headDelimiterNok(code)\n  }\n\n  /**\n   * In delimiter row, at a disallowed byte.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | x |\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterNok(code) {\n    // Note: in `markdown-rs`, we need to reset, in `micromark-js` we don‘t.\n    return nok(code)\n  }\n\n  /**\n   * Before table body row.\n   *\n   * ```markdown\n   *   | | a |\n   *   | | - |\n   * > | | b |\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function bodyRowStart(code) {\n    // Note: in `markdown-rs` we need to manually take care of a prefix,\n    // but in `micromark-js` that is done for us, so if we’re here, we’re\n    // never at whitespace.\n    effects.enter('tableRow')\n    return bodyRowBreak(code)\n  }\n\n  /**\n   * At break in table body row.\n   *\n   * ```markdown\n   *   | | a |\n   *   | | - |\n   * > | | b |\n   *     ^\n   *       ^\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function bodyRowBreak(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.verticalBar) {\n      effects.enter('tableCellDivider')\n      effects.consume(code)\n      effects.exit('tableCellDivider')\n      return bodyRowBreak\n    }\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code)) {\n      effects.exit('tableRow')\n      return ok(code)\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownSpace)(code)) {\n      return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(effects, bodyRowBreak, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.whitespace)(code)\n    }\n\n    // Anything else is cell content.\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.data)\n    return bodyRowData(code)\n  }\n\n  /**\n   * In table body row data.\n   *\n   * ```markdown\n   *   | | a |\n   *   | | - |\n   * > | | b |\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function bodyRowData(code) {\n    if (\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.verticalBar ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEndingOrSpace)(code)\n    ) {\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.data)\n      return bodyRowBreak(code)\n    }\n\n    effects.consume(code)\n    return code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.backslash ? bodyRowEscape : bodyRowData\n  }\n\n  /**\n   * In table body row escape.\n   *\n   * ```markdown\n   *   | | a    |\n   *   | | ---- |\n   * > | | b\\-c |\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function bodyRowEscape(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.backslash || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.verticalBar) {\n      effects.consume(code)\n      return bodyRowData\n    }\n\n    return bodyRowData(code)\n  }\n}\n\n/** @type {Resolver} */\n\nfunction resolveTable(events, context) {\n  let index = -1\n  let inFirstCellAwaitingPipe = true\n  /** @type {RowKind} */\n  let rowKind = 0\n  /** @type {Range} */\n  let lastCell = [0, 0, 0, 0]\n  /** @type {Range} */\n  let cell = [0, 0, 0, 0]\n  let afterHeadAwaitingFirstBodyRow = false\n  let lastTableEnd = 0\n  /** @type {Token | undefined} */\n  let currentTable\n  /** @type {Token | undefined} */\n  let currentBody\n  /** @type {Token | undefined} */\n  let currentCell\n\n  const map = new _edit_map_js__WEBPACK_IMPORTED_MODULE_6__.EditMap()\n\n  while (++index < events.length) {\n    const event = events[index]\n    const token = event[1]\n\n    if (event[0] === 'enter') {\n      // Start of head.\n      if (token.type === 'tableHead') {\n        afterHeadAwaitingFirstBodyRow = false\n\n        // Inject previous (body end and) table end.\n        if (lastTableEnd !== 0) {\n          (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(currentTable, 'there should be a table opening')\n          flushTableEnd(map, context, lastTableEnd, currentTable, currentBody)\n          currentBody = undefined\n          lastTableEnd = 0\n        }\n\n        // Inject table start.\n        currentTable = {\n          type: 'table',\n          start: Object.assign({}, token.start),\n          // Note: correct end is set later.\n          end: Object.assign({}, token.end)\n        }\n        map.add(index, 0, [['enter', currentTable, context]])\n      } else if (\n        token.type === 'tableRow' ||\n        token.type === 'tableDelimiterRow'\n      ) {\n        inFirstCellAwaitingPipe = true\n        currentCell = undefined\n        lastCell = [0, 0, 0, 0]\n        cell = [0, index + 1, 0, 0]\n\n        // Inject table body start.\n        if (afterHeadAwaitingFirstBodyRow) {\n          afterHeadAwaitingFirstBodyRow = false\n          currentBody = {\n            type: 'tableBody',\n            start: Object.assign({}, token.start),\n            // Note: correct end is set later.\n            end: Object.assign({}, token.end)\n          }\n          map.add(index, 0, [['enter', currentBody, context]])\n        }\n\n        rowKind = token.type === 'tableDelimiterRow' ? 2 : currentBody ? 3 : 1\n      }\n      // Cell data.\n      else if (\n        rowKind &&\n        (token.type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.data ||\n          token.type === 'tableDelimiterMarker' ||\n          token.type === 'tableDelimiterFiller')\n      ) {\n        inFirstCellAwaitingPipe = false\n\n        // First value in cell.\n        if (cell[2] === 0) {\n          if (lastCell[1] !== 0) {\n            cell[0] = cell[1]\n            currentCell = flushCell(\n              map,\n              context,\n              lastCell,\n              rowKind,\n              undefined,\n              currentCell\n            )\n            lastCell = [0, 0, 0, 0]\n          }\n\n          cell[2] = index\n        }\n      } else if (token.type === 'tableCellDivider') {\n        if (inFirstCellAwaitingPipe) {\n          inFirstCellAwaitingPipe = false\n        } else {\n          if (lastCell[1] !== 0) {\n            cell[0] = cell[1]\n            currentCell = flushCell(\n              map,\n              context,\n              lastCell,\n              rowKind,\n              undefined,\n              currentCell\n            )\n          }\n\n          lastCell = cell\n          cell = [lastCell[1], index, 0, 0]\n        }\n      }\n    }\n    // Exit events.\n    else if (token.type === 'tableHead') {\n      afterHeadAwaitingFirstBodyRow = true\n      lastTableEnd = index\n    } else if (\n      token.type === 'tableRow' ||\n      token.type === 'tableDelimiterRow'\n    ) {\n      lastTableEnd = index\n\n      if (lastCell[1] !== 0) {\n        cell[0] = cell[1]\n        currentCell = flushCell(\n          map,\n          context,\n          lastCell,\n          rowKind,\n          index,\n          currentCell\n        )\n      } else if (cell[1] !== 0) {\n        currentCell = flushCell(map, context, cell, rowKind, index, currentCell)\n      }\n\n      rowKind = 0\n    } else if (\n      rowKind &&\n      (token.type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.data ||\n        token.type === 'tableDelimiterMarker' ||\n        token.type === 'tableDelimiterFiller')\n    ) {\n      cell[3] = index\n    }\n  }\n\n  if (lastTableEnd !== 0) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(currentTable, 'expected table opening')\n    flushTableEnd(map, context, lastTableEnd, currentTable, currentBody)\n  }\n\n  map.consume(context.events)\n\n  // To do: move this into `html`, when events are exposed there.\n  // That’s what `markdown-rs` does.\n  // That needs updates to `mdast-util-gfm-table`.\n  index = -1\n  while (++index < context.events.length) {\n    const event = context.events[index]\n    if (event[0] === 'enter' && event[1].type === 'table') {\n      event[1]._align = (0,_infer_js__WEBPACK_IMPORTED_MODULE_7__.gfmTableAlign)(context.events, index)\n    }\n  }\n\n  return events\n}\n\n/**\n * Generate a cell.\n *\n * @param {EditMap} map\n * @param {Readonly<TokenizeContext>} context\n * @param {Readonly<Range>} range\n * @param {RowKind} rowKind\n * @param {number | undefined} rowEnd\n * @param {Token | undefined} previousCell\n * @returns {Token | undefined}\n */\n// eslint-disable-next-line max-params\nfunction flushCell(map, context, range, rowKind, rowEnd, previousCell) {\n  // `markdown-rs` uses:\n  // rowKind === 2 ? 'tableDelimiterCell' : 'tableCell'\n  const groupName =\n    rowKind === 1\n      ? 'tableHeader'\n      : rowKind === 2\n        ? 'tableDelimiter'\n        : 'tableData'\n  // `markdown-rs` uses:\n  // rowKind === 2 ? 'tableDelimiterCellValue' : 'tableCellText'\n  const valueName = 'tableContent'\n\n  // Insert an exit for the previous cell, if there is one.\n  //\n  // ```markdown\n  // > | | aa | bb | cc |\n  //          ^-- exit\n  //           ^^^^-- this cell\n  // ```\n  if (range[0] !== 0) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(previousCell, 'expected previous cell enter')\n    previousCell.end = Object.assign({}, getPoint(context.events, range[0]))\n    map.add(range[0], 0, [['exit', previousCell, context]])\n  }\n\n  // Insert enter of this cell.\n  //\n  // ```markdown\n  // > | | aa | bb | cc |\n  //           ^-- enter\n  //           ^^^^-- this cell\n  // ```\n  const now = getPoint(context.events, range[1])\n  previousCell = {\n    type: groupName,\n    start: Object.assign({}, now),\n    // Note: correct end is set later.\n    end: Object.assign({}, now)\n  }\n  map.add(range[1], 0, [['enter', previousCell, context]])\n\n  // Insert text start at first data start and end at last data end, and\n  // remove events between.\n  //\n  // ```markdown\n  // > | | aa | bb | cc |\n  //            ^-- enter\n  //             ^-- exit\n  //           ^^^^-- this cell\n  // ```\n  if (range[2] !== 0) {\n    const relatedStart = getPoint(context.events, range[2])\n    const relatedEnd = getPoint(context.events, range[3])\n    /** @type {Token} */\n    const valueToken = {\n      type: valueName,\n      start: Object.assign({}, relatedStart),\n      end: Object.assign({}, relatedEnd)\n    }\n    map.add(range[2], 0, [['enter', valueToken, context]])\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(range[3] !== 0)\n\n    if (rowKind !== 2) {\n      // Fix positional info on remaining events\n      const start = context.events[range[2]]\n      const end = context.events[range[3]]\n      start[1].end = Object.assign({}, end[1].end)\n      start[1].type = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.chunkText\n      start[1].contentType = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.constants.contentTypeText\n\n      // Remove if needed.\n      if (range[3] > range[2] + 1) {\n        const a = range[2] + 1\n        const b = range[3] - range[2] - 1\n        map.add(a, b, [])\n      }\n    }\n\n    map.add(range[3] + 1, 0, [['exit', valueToken, context]])\n  }\n\n  // Insert an exit for the last cell, if at the row end.\n  //\n  // ```markdown\n  // > | | aa | bb | cc |\n  //                    ^-- exit\n  //               ^^^^^^-- this cell (the last one contains two “between” parts)\n  // ```\n  if (rowEnd !== undefined) {\n    previousCell.end = Object.assign({}, getPoint(context.events, rowEnd))\n    map.add(rowEnd, 0, [['exit', previousCell, context]])\n    previousCell = undefined\n  }\n\n  return previousCell\n}\n\n/**\n * Generate table end (and table body end).\n *\n * @param {Readonly<EditMap>} map\n * @param {Readonly<TokenizeContext>} context\n * @param {number} index\n * @param {Token} table\n * @param {Token | undefined} tableBody\n */\n// eslint-disable-next-line max-params\nfunction flushTableEnd(map, context, index, table, tableBody) {\n  /** @type {Array<Event>} */\n  const exits = []\n  const related = getPoint(context.events, index)\n\n  if (tableBody) {\n    tableBody.end = Object.assign({}, related)\n    exits.push(['exit', tableBody, context])\n  }\n\n  table.end = Object.assign({}, related)\n  exits.push(['exit', table, context])\n\n  map.add(index + 1, 0, exits)\n}\n\n/**\n * @param {Readonly<Array<Event>>} events\n * @param {number} index\n * @returns {Readonly<Point>}\n */\nfunction getPoint(events, index) {\n  const event = events[index]\n  const side = event[0] === 'enter' ? 'start' : 'end'\n  return event[1][side]\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/syntax.js\n");

/***/ })

};
;