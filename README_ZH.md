# Suna —— 开源通用 AI 助手

（代表你执行各类任务）

![Suna 截图](frontend/public/banner.png)

Suna 是一个完全开源的 AI 助手，能够通过自然对话轻松完成各种现实世界的任务。它是你进行研究、数据分析和日常挑战的数字伙伴——结合强大的功能与直观的界面，理解你的需求并交付结果。

Suna 强大的工具集包括：无缝的浏览器自动化以浏览网页和提取数据、文档管理以创建和编辑文件、网页爬虫与扩展搜索能力、命令行执行系统任务、网站部署以及与各类 API 和服务的集成。这些功能协同工作，只需简单对话即可解决复杂问题并自动化工作流！

[![License](https://img.shields.io/badge/License-Apache--2.0-blue)](./license)
[![Discord Follow](https://dcbadge.limes.pink/api/server/Py6pCBUUPw?style=flat)](https://discord.gg/Py6pCBUUPw)
[![Twitter Follow](https://img.shields.io/twitter/follow/kortixai)](https://x.com/kortixai)
[![GitHub Repo stars](https://img.shields.io/github/stars/kortix-ai/suna)](https://github.com/kortix-ai/suna)
[![Issues](https://img.shields.io/github/issues/kortix-ai/suna)](https://github.com/kortix-ai/suna/labels/bug)


## 目录

* [项目架构](#项目架构)

  * [后端 API](#后端-api)
  * [前端](#前端)
  * [Agent 容器](#agent-容器)
  * [Supabase 数据库](#supabase-数据库)
* [本地运行 / 自托管](#本地运行--自托管)

  * [需求](#需求)
  * [前置条件](#前置条件)
  * [安装步骤](#安装步骤)
* [致谢](#致谢)
* [许可证](#许可证)

## 项目架构

![架构图](docs/images/diagram.png)

Suna 由四个主要部分组成：

### 后端 API

基于 Python 和 FastAPI 的服务，负责 REST 接口、线程管理，以及通过 Anthropic 等（经由 LiteLLM）集成大型语言模型。

### 前端

基于 Next.js 和 React 的应用，提供响应式聊天界面、仪表盘等。

### Agent 容器

每个 Agent 的隔离执行环境，包含浏览器自动化、代码解释器、文件系统访问、工具集成和安全机制。

### Supabase 数据库

负责持久化存储，包括身份认证、用户管理、对话历史、文件存储、Agent 状态、分析数据和实时订阅。

## 使用案例

1. **竞品分析**
   *“帮我分析英国医疗行业市场，列出主要参与者、市场规模、优势与劣势，并附上其官网链接。完成后生成 PDF 报告。”*

2. **美国顶级风投资金名单**
   *“给我按管理资产规模排序的美国最重要的 VC 基金名单，附上官网链接，若可能请提供联系方式邮件。”*

3. **候选人搜索**
   *“去 LinkedIn 找 10 位目前可入职的慕尼黑初级软件工程师候选人，他们至少拥有计算机相关本科及 1 年经验。”*

4. **公司团建行程规划**
   *“为我们 8 人团队规划一次为期 7 天的加州行程（4 月 21 日从巴黎出发），检查天气预报并基于气温安排室内/室外活动。”*

5. **Excel 数据整理**
   *“公司让我制作一张包含意大利彩票（Lotto、10eLotto、Million Day）信息的表格，根据公开数据生成并发送给我。”*

6. **自动化活动演讲人搜寻**
   *“搜索欧洲过去一年内的 AI 伦理大会演讲人，输出 20 位联系人信息及演讲摘要。”*

7. **科学论文汇总与对比**
   *“研究过去 5 年有关酒精对人体影响的科学论文，生成重点论文报告。”*

8. **潜在客户研究与初次联系邮件**
   *“在 LinkedIn 上找出清洁科技行业的 B2B 客户，收集其官网与邮箱，并基于公司简介生成个性化初次联系邮件，介绍我的咨询服务。”*

9. **SEO 分析**
   *“基于我的网站 suna.so，生成 SEO 分析报告，找出关键词聚类的高排名页面，并指出我遗漏的话题。”*

10. **个人伦敦行程**
    *“规划 5 月 1 日从曼谷出发、10 天伦敦游，找到市中心评分 ≥4.5 的住宿，并推荐户外活动，生成详细行程。”*

11. **新近获投初创公司**
    *“在 Crunchbase、Dealroom 和 TechCrunch 上筛选 SaaS 金融领域 A 轮融资公司，整理公司信息、创始人和联系方式用于外呼销售。”*

12. **论坛讨论爬取**
    *“爬取讨论罗马美容中心的开放论坛，生成 5 个口碑最好的美容中心列表。”*

## 本地运行 / 自托管

你可以在自己的环境中部署 Suna，按以下步骤操作。

### 需求

* 一个 Supabase 项目（数据库与认证）
* Redis（缓存与会话管理）
* Daytona 沙箱（Agent 安全执行）
* Python 3.11（后端）
* 各 LLM 提供商 API Key（Anthropic、OpenRouter 等）
* Tavily API Key（增强搜索）
* Firecrawl API Key（网页爬取）

### 前置条件

1. **Supabase**

   * 注册并创建 [Supabase 项目](https://supabase.com/dashboard/projects)
   * 记录项目的 API URL、anon key 和 service role key
   * 安装 Supabase CLI

2. **Redis**

   ```bash
   cd backend
   docker compose up redis
   ```

3. **Daytona**

   * 注册 [Daytona](https://app.daytona.io/) 并获取 API Key
   * 在 “Images” 页面点击 “Add Image”
   * 填写镜像名称 `kortix/suna:0.1.2`
   * Entrypoint 设置为：

     ```
     /usr/bin/supervisord -n -c /etc/supervisor/conf.d/supervisord.conf
     ```

4. **LLM API Keys**

   * 获取 Anthropic API Key
   * 其他模型可通过 [LiteLLM](https://github.com/BerriAI/litellm) 接入，但需调整 Prompt 输出 XML 工具调用格式

5. **搜索 API Key（可选）**

   * 获取 [Tavily API Key](https://tavily.com/)
   * 获取 [Firecrawl API Key](https://firecrawl.dev/)

6. **RapidAPI API Key（可选）**

   * 注册 RapidAPI 并激活各服务
   * 在 `backend/agent/tools/data_providers/LinkedinProvider.py` 等文件中查看 `base_url`
   * 订阅并获取 RapidAPI Key

### 安装步骤

1. **克隆仓库**

   ```bash
   git clone https://github.com/kortix-ai/suna.git
   cd suna
   ```

2. **配置后端环境**

   ```bash
   cd backend
   cp .env.example .env
   ```

   编辑 `.env`，填入：

   ```env
   NEXT_PUBLIC_URL="http://localhost:3000"

   SUPABASE_URL=你的_supabase_url
   SUPABASE_ANON_KEY=你的_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=你的_supabase_service_role_key

   REDIS_HOST=你的_redis_host
   REDIS_PORT=6379
   REDIS_PASSWORD=你的_redis_password
   REDIS_SSL=True

   DAYTONA_API_KEY=你的_daytona_api_key
   DAYTONA_SERVER_URL="https://app.daytona.io/api"
   DAYTONA_TARGET="us"

   ANTHROPIC_API_KEY=
   OPENAI_API_KEY=你的_openai_api_key

   # 可选
   TAVILY_API_KEY=你的_tavily_api_key
   FIRECRAWL_API_KEY=你的_firecrawl_api_key
   RAPID_API_KEY=
   ```

3. **初始化 Supabase 数据库**

   ```bash
   supabase login
   supabase link --project-ref 你的项目引用ID
   supabase db push
   ```

   然后在 Supabase 控制台 → Project Settings → Data API，将暴露模式中添加 `basejump`。

4. **配置前端环境**

   ```bash
   cd ../frontend
   cp .env.example .env.local
   ```

   编辑 `.env.local`：

   ```env
   NEXT_PUBLIC_SUPABASE_URL=你的_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=你的_supabase_anon_key
   NEXT_PUBLIC_BACKEND_URL="http://localhost:8000/api"
   NEXT_PUBLIC_URL="http://localhost:3000"
   ```

   若使用 Docker Compose，将 `localhost` 改为容器名：

   ```env
   NEXT_PUBLIC_BACKEND_URL="http://backend:8000/api"
   ```

5. **安装依赖**

   ```bash
   # 前端
   cd frontend
   npm install

   # 后端
   cd ../backend
   poetry install
   ```

6. **启动应用**

   * 终端一（前端）：

     ```bash
     cd frontend
     npm run dev
     ```
   * 终端二（后端）：

     ```bash
     cd backend
     poetry run python3.11 api.py
     ```

7. **Docker Compose 方案**
   确保各环境变量已在 `backend/.env` 和 `frontend/.env.local` 中配置正确后：

   ```bash
   export GITHUB_REPOSITORY="你的GitHub用户名/仓库名"
   docker compose -f docker-compose.ghcr.yaml up
   ```

   若本地构建镜像：

   ```bash
   docker compose up
   ```

8. **访问 Suna**
   在浏览器打开 `http://localhost:3000`，注册登录，即可使用你的自托管 Suna 实例！

## 致谢

### 主要贡献者

* [Adam Cohen Hillel](https://x.com/adamcohenhillel)
* [Dat-lequoc](https://x.com/datlqqq)
* [Marko Kraemer](https://twitter.com/markokraemer)

### 技术栈

* [Daytona](https://daytona.io/) – 安全的 Agent 执行环境
* [Supabase](https://supabase.com/) – 后端即服务
* [Playwright](https://playwright.dev/) – 浏览器自动化
* [OpenAI](https://openai.com/) – LLM 提供商
* [Anthropic](https://www.anthropic.com/) – LLM 提供商
* [Tavily](https://tavily.com/) – 搜索能力
* [Firecrawl](https://firecrawl.dev/) – 网页爬取
* [RapidAPI](https://rapidapi.com/) – API 服务

## 许可证

Kortix Suna 遵循 Apache 2.0 开源许可证。详见 [LICENSE](./LICENSE)。
