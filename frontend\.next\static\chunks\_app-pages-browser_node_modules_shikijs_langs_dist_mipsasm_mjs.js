"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_mipsasm_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/mipsasm.mjs":
/*!******************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/mipsasm.mjs ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"MIPS Assembly\\\",\\\"fileTypes\\\":[\\\"s\\\",\\\"mips\\\",\\\"spim\\\",\\\"asm\\\"],\\\"name\\\":\\\"mipsasm\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(mul|abs|div|divu|mulo|mulou|neg|negu|not|rem|remu|rol|ror|li|seq|sge|sgeu|sgt|sgtu|sle|sleu|sne|b|beqz|bge|bgeu|bgt|bgtu|ble|bleu|blt|bltu|bnez|la|ld|ulh|ulhu|ulw|sd|ush|usw|move|mfc1\\\\\\\\.d|l\\\\\\\\.d|l\\\\\\\\.s|s\\\\\\\\.d|s\\\\\\\\.s)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.pseudo.mips\\\"},{\\\"match\\\":\\\"\\\\\\\\b(abs\\\\\\\\.d|abs\\\\\\\\.s|add|add\\\\\\\\.d|add\\\\\\\\.s|addi|addiu|addu|and|andi|bc1f|bc1t|beq|bgez|bgezal|bgtz|blez|bltz|bltzal|bne|break|c\\\\\\\\.eq\\\\\\\\.d|c\\\\\\\\.eq\\\\\\\\.s|c\\\\\\\\.le\\\\\\\\.d|c\\\\\\\\.le\\\\\\\\.s|c\\\\\\\\.lt\\\\\\\\.d|c\\\\\\\\.lt\\\\\\\\.s|ceil\\\\\\\\.w\\\\\\\\.d|ceil\\\\\\\\.w\\\\\\\\.s|clo|clz|cvt\\\\\\\\.d\\\\\\\\.s|cvt\\\\\\\\.d\\\\\\\\.w|cvt\\\\\\\\.s\\\\\\\\.d|cvt\\\\\\\\.s\\\\\\\\.w|cvt\\\\\\\\.w\\\\\\\\.d|cvt\\\\\\\\.w\\\\\\\\.s|div|div\\\\\\\\.d|div\\\\\\\\.s|divu|eret|floor\\\\\\\\.w\\\\\\\\.d|floor\\\\\\\\.w\\\\\\\\.s|j|jal|jalr|jr|lb|lbu|lh|lhu|ll|lui|lw|lwc1|lwl|lwr|madd|maddu|mfc0|mfc1|mfhi|mflo|mov\\\\\\\\.d|mov\\\\\\\\.s|movf|movf\\\\\\\\.d|movf\\\\\\\\.s|movn|movn\\\\\\\\.d|movn\\\\\\\\.s|movt|movt\\\\\\\\.d|movt\\\\\\\\.s|movz|movz\\\\\\\\.d|movz\\\\\\\\.s|msub|mtc0|mtc1|mthi|mtlo|mul|mul\\\\\\\\.d|mul\\\\\\\\.s|mult|multu|neg\\\\\\\\.d|neg\\\\\\\\.s|nop|nor|or|ori|round\\\\\\\\.w\\\\\\\\.d|round\\\\\\\\.w\\\\\\\\.s|sb|sc|sdc1|sh|sll|sllv|slt|slti|sltiu|sltu|sqrt\\\\\\\\.d|sqrt\\\\\\\\.s|sra|srav|srl|srlv|sub|sub\\\\\\\\.d|sub\\\\\\\\.s|subu|sw|swc1|swl|swr|syscall|teq|teqi|tge|tgei|tgeiu|tgeu|tlt|tlti|tltiu|tltu|trunc\\\\\\\\.w\\\\\\\\.d|trunc\\\\\\\\.w\\\\\\\\.s|xor|xori)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.mips\\\"},{\\\"match\\\":\\\"\\\\\\\\.(ascii|asciiz|byte|data|double|float|half|kdata|ktext|space|text|word|set\\\\\\\\s*(noat|at))\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.mips\\\"},{\\\"match\\\":\\\"\\\\\\\\.(align|extern||globl)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.modifier.mips\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.label.mips\\\"}},\\\"match\\\":\\\"\\\\\\\\b([A-Za-z0-9_]+):\\\",\\\"name\\\":\\\"meta.function.label.mips\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.variable.mips\\\"}},\\\"match\\\":\\\"(\\\\\\\\$)([02-9]|1[0-9]|2[0-5]|2[89]|3[0-1])\\\\\\\\b\\\",\\\"name\\\":\\\"variable.other.register.usable.by-number.mips\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.variable.mips\\\"}},\\\"match\\\":\\\"(\\\\\\\\$)(zero|v[01]|a[0-3]|t[0-9]|s[0-7]|gp|sp|fp|ra)\\\\\\\\b\\\",\\\"name\\\":\\\"variable.other.register.usable.by-name.mips\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.variable.mips\\\"}},\\\"match\\\":\\\"(\\\\\\\\$)(at|k[01]|1|2[67])\\\\\\\\b\\\",\\\"name\\\":\\\"variable.other.register.reserved.mips\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.variable.mips\\\"}},\\\"match\\\":\\\"(\\\\\\\\$)f([0-9]|1[0-9]|2[0-9]|3[0-1])\\\\\\\\b\\\",\\\"name\\\":\\\"variable.other.register.usable.floating-point.mips\\\"},{\\\"match\\\":\\\"\\\\\\\\b\\\\\\\\d+\\\\\\\\.\\\\\\\\d+\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.float.mips\\\"},{\\\"match\\\":\\\"\\\\\\\\b(\\\\\\\\d+|0([xX])\\\\\\\\h+)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.integer.mips\\\"},{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.mips\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.mips\\\"}},\\\"name\\\":\\\"string.quoted.double.mips\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\[rnt\\\\\\\\\\\\\\\\\\\\\\\"]\\\",\\\"name\\\":\\\"constant.character.escape.mips\\\"}]},{\\\"begin\\\":\\\"(^[ \\\\\\\\t]+)?(?=#)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.comment.leading.mips\\\"}},\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"#\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.mips\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.line.number-sign.mips\\\"}]}],\\\"scopeName\\\":\\\"source.mips\\\",\\\"aliases\\\":[\\\"mips\\\"]}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/mipsasm.mjs\n"));

/***/ })

}]);