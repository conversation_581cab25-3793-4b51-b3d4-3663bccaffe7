"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_git-commit_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/diff.mjs":
/*!***************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/diff.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Diff\\\",\\\"name\\\":\\\"diff\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.separator.diff\\\"}},\\\"match\\\":\\\"^((\\\\\\\\*{15})|(={67})|(-{3}))$\\\\\\\\n?\\\",\\\"name\\\":\\\"meta.separator.diff\\\"},{\\\"match\\\":\\\"^\\\\\\\\d+(,\\\\\\\\d+)*([adc])\\\\\\\\d+(,\\\\\\\\d+)*$\\\\\\\\n?\\\",\\\"name\\\":\\\"meta.diff.range.normal\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.range.diff\\\"},\\\"2\\\":{\\\"name\\\":\\\"meta.toc-list.line-number.diff\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.range.diff\\\"}},\\\"match\\\":\\\"^(@@)\\\\\\\\s*(.+?)\\\\\\\\s*(@@)($\\\\\\\\n?)?\\\",\\\"name\\\":\\\"meta.diff.range.unified\\\"},{\\\"captures\\\":{\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.range.diff\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.range.diff\\\"},\\\"6\\\":{\\\"name\\\":\\\"punctuation.definition.range.diff\\\"},\\\"7\\\":{\\\"name\\\":\\\"punctuation.definition.range.diff\\\"}},\\\"match\\\":\\\"^(((-{3}) .+ (-{4}))|((\\\\\\\\*{3}) .+ (\\\\\\\\*{4})))$\\\\\\\\n?\\\",\\\"name\\\":\\\"meta.diff.range.context\\\"},{\\\"match\\\":\\\"^diff --git a/.*$\\\\\\\\n?\\\",\\\"name\\\":\\\"meta.diff.header.git\\\"},{\\\"match\\\":\\\"^diff (-|\\\\\\\\S+\\\\\\\\s+\\\\\\\\S+).*$\\\\\\\\n?\\\",\\\"name\\\":\\\"meta.diff.header.command\\\"},{\\\"captures\\\":{\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.from-file.diff\\\"},\\\"6\\\":{\\\"name\\\":\\\"punctuation.definition.from-file.diff\\\"},\\\"7\\\":{\\\"name\\\":\\\"punctuation.definition.from-file.diff\\\"}},\\\"match\\\":\\\"(^(?:(((-{3}) .+)|((\\\\\\\\*{3}) .+))$\\\\\\\\n?|(={4}) .+(?= - )))\\\",\\\"name\\\":\\\"meta.diff.header.from-file\\\"},{\\\"captures\\\":{\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.to-file.diff\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.to-file.diff\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.to-file.diff\\\"}},\\\"match\\\":\\\"(^(\\\\\\\\+{3}) .+$\\\\\\\\n?| (-) .* (={4})$\\\\\\\\n?)\\\",\\\"name\\\":\\\"meta.diff.header.to-file\\\"},{\\\"captures\\\":{\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.inserted.diff\\\"},\\\"6\\\":{\\\"name\\\":\\\"punctuation.definition.inserted.diff\\\"}},\\\"match\\\":\\\"^(((>)( .*)?)|((\\\\\\\\+).*))$\\\\\\\\n?\\\",\\\"name\\\":\\\"markup.inserted.diff\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.changed.diff\\\"}},\\\"match\\\":\\\"^(!).*$\\\\\\\\n?\\\",\\\"name\\\":\\\"markup.changed.diff\\\"},{\\\"captures\\\":{\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.deleted.diff\\\"},\\\"6\\\":{\\\"name\\\":\\\"punctuation.definition.deleted.diff\\\"}},\\\"match\\\":\\\"^(((<)( .*)?)|((-).*))$\\\\\\\\n?\\\",\\\"name\\\":\\\"markup.deleted.diff\\\"},{\\\"begin\\\":\\\"^(#)\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.diff\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.line.number-sign.diff\\\"},{\\\"match\\\":\\\"^index [0-9a-f]{7,40}\\\\\\\\.\\\\\\\\.[0-9a-f]{7,40}.*$\\\\\\\\n?\\\",\\\"name\\\":\\\"meta.diff.index.git\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.key-value.diff\\\"},\\\"2\\\":{\\\"name\\\":\\\"meta.toc-list.file-name.diff\\\"}},\\\"match\\\":\\\"^Index(:) (.+)$\\\\\\\\n?\\\",\\\"name\\\":\\\"meta.diff.index\\\"},{\\\"match\\\":\\\"^Only in .*: .*$\\\\\\\\n?\\\",\\\"name\\\":\\\"meta.diff.only-in\\\"}],\\\"scopeName\\\":\\\"source.diff\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/diff.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/git-commit.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/git-commit.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _diff_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./diff.mjs */ \"(app-pages-browser)/./node_modules/@shikijs/langs/dist/diff.mjs\");\n\n\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Git Commit Message\\\",\\\"name\\\":\\\"git-commit\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?=^diff --git)\\\",\\\"contentName\\\":\\\"source.diff\\\",\\\"end\\\":\\\"\\\\\\\\z\\\",\\\"name\\\":\\\"meta.embedded.diff.git-commit\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.diff\\\"}]},{\\\"begin\\\":\\\"^(?!#)\\\",\\\"end\\\":\\\"^(?=#)\\\",\\\"name\\\":\\\"meta.scope.message.git-commit\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"invalid.deprecated.line-too-long.git-commit\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.line-too-long.git-commit\\\"}},\\\"match\\\":\\\"\\\\\\\\G.{0,50}(.{0,22}(.*))$\\\",\\\"name\\\":\\\"meta.scope.subject.git-commit\\\"}]},{\\\"begin\\\":\\\"^(?=#)\\\",\\\"contentName\\\":\\\"comment.line.number-sign.git-commit\\\",\\\"end\\\":\\\"^(?!#)\\\",\\\"name\\\":\\\"meta.scope.metadata.git-commit\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"markup.changed.git-commit\\\"}},\\\"match\\\":\\\"^#\\\\\\\\t((modified|renamed):.*)$\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"markup.inserted.git-commit\\\"}},\\\"match\\\":\\\"^#\\\\\\\\t(new file:.*)$\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"markup.deleted.git-commit\\\"}},\\\"match\\\":\\\"^#\\\\\\\\t(deleted.*)$\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.file-type.git-commit\\\"},\\\"2\\\":{\\\"name\\\":\\\"string.unquoted.filename.git-commit\\\"}},\\\"match\\\":\\\"^#\\\\\\\\t([^:]+): *(.*)$\\\"}]}],\\\"scopeName\\\":\\\"text.git-commit\\\",\\\"embeddedLangs\\\":[\\\"diff\\\"]}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\n..._diff_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/git-commit.mjs\n"));

/***/ })

}]);