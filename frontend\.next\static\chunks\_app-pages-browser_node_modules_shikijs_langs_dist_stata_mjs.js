"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_stata_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/sql.mjs":
/*!**************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/sql.mjs ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"SQL\\\",\\\"name\\\":\\\"sql\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"((?<!@)@)\\\\\\\\b(\\\\\\\\w+)\\\\\\\\b\\\",\\\"name\\\":\\\"text.variable\\\"},{\\\"match\\\":\\\"(\\\\\\\\[)[^\\\\\\\\]]*(])\\\",\\\"name\\\":\\\"text.bracketed\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.create.sql\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.sql\\\"},\\\"5\\\":{\\\"name\\\":\\\"entity.name.function.sql\\\"}},\\\"match\\\":\\\"(?i:^\\\\\\\\s*(create(?:\\\\\\\\s+or\\\\\\\\s+replace)?)\\\\\\\\s+(aggregate|conversion|database|domain|function|group|(unique\\\\\\\\s+)?index|language|operator class|operator|rule|schema|sequence|table|tablespace|trigger|type|user|view)\\\\\\\\s+)(['\\\\\\\"`]?)(\\\\\\\\w+)\\\\\\\\4\\\",\\\"name\\\":\\\"meta.create.sql\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.create.sql\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.sql\\\"}},\\\"match\\\":\\\"(?i:^\\\\\\\\s*(drop)\\\\\\\\s+(aggregate|conversion|database|domain|function|group|index|language|operator class|operator|rule|schema|sequence|table|tablespace|trigger|type|user|view))\\\",\\\"name\\\":\\\"meta.drop.sql\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.create.sql\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.table.sql\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.function.sql\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.other.cascade.sql\\\"}},\\\"match\\\":\\\"(?i:\\\\\\\\s*(drop)\\\\\\\\s+(table)\\\\\\\\s+(\\\\\\\\w+)(\\\\\\\\s+cascade)?\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.drop.sql\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.create.sql\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.table.sql\\\"}},\\\"match\\\":\\\"(?i:^\\\\\\\\s*(alter)\\\\\\\\s+(aggregate|conversion|database|domain|function|group|index|language|operator class|operator|proc(edure)?|rule|schema|sequence|table|tablespace|trigger|type|user|view)\\\\\\\\s+)\\\",\\\"name\\\":\\\"meta.alter.sql\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.sql\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.type.sql\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.numeric.sql\\\"},\\\"4\\\":{\\\"name\\\":\\\"storage.type.sql\\\"},\\\"5\\\":{\\\"name\\\":\\\"constant.numeric.sql\\\"},\\\"6\\\":{\\\"name\\\":\\\"storage.type.sql\\\"},\\\"7\\\":{\\\"name\\\":\\\"constant.numeric.sql\\\"},\\\"8\\\":{\\\"name\\\":\\\"constant.numeric.sql\\\"},\\\"9\\\":{\\\"name\\\":\\\"storage.type.sql\\\"},\\\"10\\\":{\\\"name\\\":\\\"constant.numeric.sql\\\"},\\\"11\\\":{\\\"name\\\":\\\"storage.type.sql\\\"},\\\"12\\\":{\\\"name\\\":\\\"storage.type.sql\\\"},\\\"13\\\":{\\\"name\\\":\\\"storage.type.sql\\\"},\\\"14\\\":{\\\"name\\\":\\\"constant.numeric.sql\\\"},\\\"15\\\":{\\\"name\\\":\\\"storage.type.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(bigint|bigserial|bit|boolean|box|bytea|cidr|circle|date|double\\\\\\\\sprecision|inet|int|integer|line|lseg|macaddr|money|oid|path|point|polygon|real|serial|smallint|sysdate|text)\\\\\\\\b|\\\\\\\\b(bit\\\\\\\\svarying|character\\\\\\\\s(?:varying)?|tinyint|var\\\\\\\\schar|float|interval)\\\\\\\\((\\\\\\\\d+)\\\\\\\\)|\\\\\\\\b(char|number|varchar\\\\\\\\d?)\\\\\\\\b(?:\\\\\\\\((\\\\\\\\d+)\\\\\\\\))?|\\\\\\\\b(numeric|decimal)\\\\\\\\b(?:\\\\\\\\((\\\\\\\\d+),(\\\\\\\\d+)\\\\\\\\))?|\\\\\\\\b(times?)\\\\\\\\b(?:\\\\\\\\((\\\\\\\\d+)\\\\\\\\))?(\\\\\\\\swith(?:out)?\\\\\\\\stime\\\\\\\\szone\\\\\\\\b)?|\\\\\\\\b(timestamp)(s|tz)?\\\\\\\\b(?:\\\\\\\\((\\\\\\\\d+)\\\\\\\\))?(\\\\\\\\s(with(?:|out))\\\\\\\\stime\\\\\\\\szone\\\\\\\\b)?\\\"},{\\\"match\\\":\\\"(?i:\\\\\\\\b((?:primary|foreign)\\\\\\\\s+key|references|on\\\\\\\\sdelete(\\\\\\\\s+cascade)?|nocheck|check|constraint|collate|default)\\\\\\\\b)\\\",\\\"name\\\":\\\"storage.modifier.sql\\\"},{\\\"match\\\":\\\"\\\\\\\\b\\\\\\\\d+\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.sql\\\"},{\\\"match\\\":\\\"(?i:\\\\\\\\b(select(\\\\\\\\s+(all|distinct))?|insert\\\\\\\\s+(ignore\\\\\\\\s+)?into|update|delete|from|set|where|group\\\\\\\\s+by|or|like|and|union(\\\\\\\\s+all)?|having|order\\\\\\\\s+by|limit|cross\\\\\\\\s+join|join|straight_join|(inner|(left|right|full)(\\\\\\\\s+outer)?)\\\\\\\\s+join|natural(\\\\\\\\s+(inner|(left|right|full)(\\\\\\\\s+outer)?))?\\\\\\\\s+join)\\\\\\\\b)\\\",\\\"name\\\":\\\"keyword.other.DML.sql\\\"},{\\\"match\\\":\\\"(?i:\\\\\\\\b(on|off|((is\\\\\\\\s+)?not\\\\\\\\s+)?null)\\\\\\\\b)\\\",\\\"name\\\":\\\"keyword.other.DDL.create.II.sql\\\"},{\\\"match\\\":\\\"(?i:\\\\\\\\bvalues\\\\\\\\b)\\\",\\\"name\\\":\\\"keyword.other.DML.II.sql\\\"},{\\\"match\\\":\\\"(?i:\\\\\\\\b(begin(\\\\\\\\s+work)?|start\\\\\\\\s+transaction|commit(\\\\\\\\s+work)?|rollback(\\\\\\\\s+work)?)\\\\\\\\b)\\\",\\\"name\\\":\\\"keyword.other.LUW.sql\\\"},{\\\"match\\\":\\\"(?i:\\\\\\\\b(grant(\\\\\\\\swith\\\\\\\\sgrant\\\\\\\\soption)?|revoke)\\\\\\\\b)\\\",\\\"name\\\":\\\"keyword.other.authorization.sql\\\"},{\\\"match\\\":\\\"(?i:\\\\\\\\bin\\\\\\\\b)\\\",\\\"name\\\":\\\"keyword.other.data-integrity.sql\\\"},{\\\"match\\\":\\\"(?i:^\\\\\\\\s*(comment\\\\\\\\s+on\\\\\\\\s+(table|column|aggregate|constraint|database|domain|function|index|operator|rule|schema|sequence|trigger|type|view))\\\\\\\\s+.*?\\\\\\\\s+(is)\\\\\\\\s+)\\\",\\\"name\\\":\\\"keyword.other.object-comments.sql\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\bAS\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.alias.sql\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\b(DESC|ASC)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.order.sql\\\"},{\\\"match\\\":\\\"\\\\\\\\*\\\",\\\"name\\\":\\\"keyword.operator.star.sql\\\"},{\\\"match\\\":\\\"[!<>]?=|<>|[<>]\\\",\\\"name\\\":\\\"keyword.operator.comparison.sql\\\"},{\\\"match\\\":\\\"[-+/]\\\",\\\"name\\\":\\\"keyword.operator.math.sql\\\"},{\\\"match\\\":\\\"\\\\\\\\|\\\\\\\\|\\\",\\\"name\\\":\\\"keyword.operator.concatenator.sql\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.aggregate.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(approx_count_distinct|approx_percentile_cont|approx_percentile_disc|avg|checksum_agg|count|count_big|group|grouping|grouping_id|max|min|sum|stdev|stdevp|var|varp)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.analytic.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(cume_dist|first_value|lag|last_value|lead|percent_rank|percentile_cont|percentile_disc)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.bitmanipulation.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(bit_count|get_bit|left_shift|right_shift|set_bit)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.conversion.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(cast|convert|parse|try_cast|try_convert|try_parse)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.collation.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(collationproperty|tertiary_weights)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.cryptographic.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(asymkey_id|asymkeyproperty|certproperty|cert_id|crypt_gen_random|decryptbyasymkey|decryptbycert|decryptbykey|decryptbykeyautoasymkey|decryptbykeyautocert|decryptbypassphrase|encryptbyasymkey|encryptbycert|encryptbykey|encryptbypassphrase|hashbytes|is_objectsigned|key_guid|key_id|key_name|signbyasymkey|signbycert|symkeyproperty|verifysignedbycert|verifysignedbyasymkey)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.cursor.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(cursor_status)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.datetime.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(sysdatetime|sysdatetimeoffset|sysutcdatetime|current_time(stamp)?|getdate|getutcdate|datename|datepart|day|month|year|datefromparts|datetime2fromparts|datetimefromparts|datetimeoffsetfromparts|smalldatetimefromparts|timefromparts|datediff|dateadd|datetrunc|eomonth|switchoffset|todatetimeoffset|isdate|date_bucket)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.datatype.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(datalength|ident_current|ident_incr|ident_seed|identity|sql_variant_property)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.expression.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(coalesce|nullif)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.globalvar.sql\\\"}},\\\"match\\\":\\\"(?<!@)@@(?i)\\\\\\\\b(cursor_rows|connections|cpu_busy|datefirst|dbts|error|fetch_status|identity|idle|io_busy|langid|language|lock_timeout|max_connections|max_precision|nestlevel|options|packet_errors|pack_received|pack_sent|procid|remserver|rowcount|servername|servicename|spid|textsize|timeticks|total_errors|total_read|total_write|trancount|version)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.json.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(json|isjson|json_object|json_array|json_value|json_query|json_modify|json_path_exists)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.logical.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(choose|iif|greatest|least)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.mathematical.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(abs|acos|asin|atan|atn2|ceiling|cos|cot|degrees|exp|floor|log|log10|pi|power|radians|rand|round|sign|sin|sqrt|square|tan)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.metadata.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(app_name|applock_mode|applock_test|assemblyproperty|col_length|col_name|columnproperty|database_principal_id|databasepropertyex|db_id|db_name|file_id|file_idex|file_name|filegroup_id|filegroup_name|filegroupproperty|fileproperty|fulltextcatalogproperty|fulltextserviceproperty|index_col|indexkey_property|indexproperty|object_definition|object_id|object_name|object_schema_name|objectproperty|objectpropertyex|original_db_name|parsename|schema_id|schema_name|scope_identity|serverproperty|stats_date|type_id|type_name|typeproperty)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.ranking.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(rank|dense_rank|ntile|row_number)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.rowset.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(generate_series|opendatasource|openjson|openrowset|openquery|openxml|predict|string_split)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.security.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(certencoded|certprivatekey|current_user|database_principal_id|has_perms_by_name|is_member|is_rolemember|is_srvrolemember|original_login|permissions|pwdcompare|pwdencrypt|schema_id|schema_name|session_user|suser_id|suser_sid|suser_sname|system_user|suser_name|user_id|user_name)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.string.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(ascii|char|charindex|concat|difference|format|left|len|lower|ltrim|nchar|nodes|patindex|quotename|replace|replicate|reverse|right|rtrim|soundex|space|str|string_agg|string_escape|string_split|stuff|substring|translate|trim|unicode|upper)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.system.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(binary_checksum|checksum|compress|connectionproperty|context_info|current_request_id|current_transaction_id|decompress|error_line|error_message|error_number|error_procedure|error_severity|error_state|formatmessage|get_filestream_transaction_context|getansinull|host_id|host_name|isnull|isnumeric|min_active_rowversion|newid|newsequentialid|rowcount_big|session_context|session_id|xact_state)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.textimage.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(patindex|textptr|textvalid)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.other.database-name.sql\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.other.table-name.sql\\\"}},\\\"match\\\":\\\"(\\\\\\\\w+?)\\\\\\\\.(\\\\\\\\w+)\\\"},{\\\"include\\\":\\\"#strings\\\"},{\\\"include\\\":\\\"#regexps\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i)(abort|abort_after_wait|absent|absolute|accent_sensitivity|acceptable_cursopt|acp|action|activation|add|address|admin|aes_128|aes_192|aes_256|affinity|after|aggregate|algorithm|all_constraints|all_errormsgs|all_indexes|all_levels|all_results|allow_connections|allow_dup_row|allow_encrypted_value_modifications|allow_page_locks|allow_row_locks|allow_snapshot_isolation|alter|altercolumn|always|anonymous|ansi_defaults|ansi_null_default|ansi_null_dflt_off|ansi_null_dflt_on|ansi_nulls|ansi_padding|ansi_warnings|appdomain|append|application|apply|arithabort|arithignore|array|assembly|asymmetric|asynchronous_commit|at|atan2|atomic|attach|attach_force_rebuild_log|attach_rebuild_log|audit|auth_realm|authentication|auto|auto_cleanup|auto_close|auto_create_statistics|auto_drop|auto_shrink|auto_update_statistics|auto_update_statistics_async|automated_backup_preference|automatic|autopilot|availability|availability_mode|backup|backup_priority|base64|basic|batches|batchsize|before|between|bigint|binary|binding|bit|block|blockers|blocksize|bmk|both|break|broker|broker_instance|bucket_count|buffer|buffercount|bulk_logged|by|call|caller|card|case|catalog|catch|cert|certificate|change_retention|change_tracking|change_tracking_context|changes|char|character|character_set|check_expiration|check_policy|checkconstraints|checkindex|checkpoint|checksum|cleanup_policy|clear|clear_port|close|clustered|codepage|collection|column_encryption_key|column_master_key|columnstore|columnstore_archive|colv_80_to_100|colv_100_to_80|commit_differential_base|committed|compatibility_level|compress_all_row_groups|compression|compression_delay|concat_null_yields_null|concatenate|configuration|connect|connection|containment|continue|continue_after_error|contract|contract_name|control|conversation|conversation_group_id|conversation_handle|copy|copy_only|count_rows|counter|create(\\\\\\\\\\\\\\\\s+or\\\\\\\\\\\\\\\\s+alter)?|credential|cross|cryptographic|cryptographic_provider|cube|cursor|cursor_close_on_commit|cursor_default|data|data_compression|data_flush_interval_seconds|data_mirroring|data_purity|data_source|database|database_name|database_snapshot|datafiletype|date_correlation_optimization|date|datefirst|dateformat|date_format|datetime|datetime2|datetimeoffset|day(s)?|db_chaining|dbid|dbidexec|dbo_only|deadlock_priority|deallocate|dec|decimal|declare|decrypt|decrypt_a|decryption|default_database|default_fulltext_language|default_language|default_logon_domain|default_schema|definition|delay|delayed_durability|delimitedtext|density_vector|dependent|des|description|desired_state|desx|differential|digest|disable|disable_broker|disable_def_cnst_chk|disabled|disk|distinct|distributed|distribution|drop|drop_existing|dts_buffers|dump|durability|dynamic|edition|elements|else|emergency|empty|enable|enable_broker|enabled|encoding|encrypted|encrypted_value|encryption|encryption_type|end|endpoint|endpoint_url|enhancedintegrity|entry|error_broker_conversations|errorfile|estimateonly|event|except|exec|executable|execute|exists|expand|expiredate|expiry_date|explicit|external|external_access|failover|failover_mode|failure_condition_level|fast|fast_forward|fastfirstrow|federated_service_account|fetch|field_terminator|fieldterminator|file|filelistonly|filegroup|filegrowth|filename|filestream|filestream_log|filestream_on|filetable|file_format|filter|first_row|fips_flagger|fire_triggers|first|firstrow|float|flush_interval_seconds|fmtonly|following|for|force|force_failover_allow_data_loss|force_service_allow_data_loss|forced|forceplan|formatfile|format_options|format_type|formsof|forward_only|free_cursors|free_exec_context|fullscan|fulltext|fulltextall|fulltextkey|function|generated|get|geography|geometry|global|go|goto|governor|guid|hadoop|hardening|hash|hashed|header_limit|headeronly|health_check_timeout|hidden|hierarchyid|histogram|histogram_steps|hits_cursors|hits_exec_context|hour(s)?|http|identity|identity_value|if|ifnull|ignore|ignore_constraints|ignore_dup_key|ignore_dup_row|ignore_triggers|image|immediate|implicit_transactions|include|include_null_values|incremental|index|inflectional|init|initiator|insensitive|insert|instead|int|integer|integrated|intersect|intermediate|interval_length_minutes|into|inuse_cursors|inuse_exec_context|io|is|isabout|iso_week|isolation|job_tracker_location|json|keep|keep_nulls|keep_replication|keepdefaults|keepfixed|keepidentity|keepnulls|kerberos|key|key_path|key_source|key_store_provider_name|keyset|kill|kilobytes_per_batch|labelonly|langid|language|last|lastrow|leading|legacy_cardinality_estimation|length|level|lifetime|lineage_80_to_100|lineage_100_to_80|listener_ip|listener_port|load|loadhistory|lob_compaction|local|local_service_name|locate|location|lock_escalation|lock_timeout|lockres|log|login|login_type|loop|manual|mark_in_use_for_removal|masked|master|match|matched|max_queue_readers|max_duration|max_outstanding_io_per_volume|maxdop|maxerrors|maxlength|maxtransfersize|max_plans_per_query|max_storage_size_mb|mediadescription|medianame|mediapassword|memogroup|memory_optimized|merge|message|message_forward_size|message_forwarding|microsecond|millisecond|minute(s)?|mirror_address|misses_cursors|misses_exec_context|mixed|modify|money|month|move|multi_user|must_change|name|namespace|nanosecond|native|native_compilation|nchar|ncharacter|nested_triggers|never|new_account|new_broker|newname|next|no|no_browsetable|no_checksum|no_compression|no_infomsgs|no_triggers|no_truncate|nocount|noexec|noexpand|noformat|noinit|nolock|nonatomic|nonclustered|nondurable|none|norecompute|norecovery|noreset|norewind|noskip|not|notification|nounload|now|nowait|ntext|ntlm|nulls|numeric|numeric_roundabort|nvarchar|object|objid|oem|offline|old_account|online|operation_mode|open|openjson|optimistic|option|orc|out|outer|output|over|override|owner|ownership|pad_index|page|page_checksum|page_verify|pagecount|paglock|param|parameter_sniffing|parameter_type_expansion|parameterization|parquet|parseonly|partial|partition|partner|password|path|pause|percentage|permission_set|persisted|period|physical_only|plan_forcing_mode|policy|pool|population|ports|preceding|precision|predicate|presume_abort|primary|primary_role|print|prior|priority |priority_level|private|proc(edure)?|procedure_name|profile|provider|quarter|query_capture_mode|query_governor_cost_limit|query_optimizer_hotfixes|query_store|queue|quoted_identifier|raiserror|range|raw|rcfile|rc2|rc4|rc4_128|rdbms|read_committed_snapshot|read|read_only|read_write|readcommitted|readcommittedlock|readonly|readpast|readuncommitted|readwrite|real|rebuild|receive|recmodel_70backcomp|recompile|reconfigure|recovery|recursive|recursive_triggers|redo_queue|reject_sample_value|reject_type|reject_value|relative|remote|remote_data_archive|remote_proc_transactions|remote_service_name|remove|removed_cursors|removed_exec_context|reorganize|repeat|repeatable|repeatableread|replace|replica|replicated|replnick_100_to_80|replnickarray_80_to_100|replnickarray_100_to_80|required|required_cursopt|resample|reset|resource|resource_manager_location|respect|restart|restore|restricted_user|resume|retaindays|retention|return|revert|rewind|rewindonly|returns|robust|role|rollup|root|round_robin|route|row|rowdump|rowguidcol|rowlock|row_terminator|rows|rows_per_batch|rowsets_only|rowterminator|rowversion|rsa_1024|rsa_2048|rsa_3072|rsa_4096|rsa_512|safe|safety|sample|save|scalar|schema|schemabinding|scoped|scroll|scroll_locks|sddl|second|secexpr|seconds|secondary|secondary_only|secondary_role|secret|security|securityaudit|selective|self|send|sent|sequence|serde_method|serializable|server|service|service_broker|service_name|service_objective|session_timeout|session|sessions|seterror|setopts|sets|shard_map_manager|shard_map_name|sharded|shared_memory|shortest_path|show_statistics|showplan_all|showplan_text|showplan_xml|showplan_xml_with_recompile|shrinkdb|shutdown|sid|signature|simple|single_blob|single_clob|single_nclob|single_user|singleton|site|size|size_based_cleanup_mode|skip|smalldatetime|smallint|smallmoney|snapshot|snapshot_import|snapshotrestorephase|soap|softnuma|sort_in_tempdb|sorted_data|sorted_data_reorg|spatial|sql|sql_bigint|sql_binary|sql_bit|sql_char|sql_date|sql_decimal|sql_double|sql_float|sql_guid|sql_handle|sql_longvarbinary|sql_longvarchar|sql_numeric|sql_real|sql_smallint|sql_time|sql_timestamp|sql_tinyint|sql_tsi_day|sql_tsi_frac_second|sql_tsi_hour|sql_tsi_minute|sql_tsi_month|sql_tsi_quarter|sql_tsi_second|sql_tsi_week|sql_tsi_year|sql_type_date|sql_type_time|sql_type_timestamp|sql_varbinary|sql_varchar|sql_variant|sql_wchar|sql_wlongvarchar|ssl|ssl_port|standard|standby|start|start_date|started|stat_header|state|statement|static|statistics|statistics_incremental|statistics_norecompute|statistics_only|statman|stats|stats_stream|status|stop|stop_on_error|stopat|stopatmark|stopbeforemark|stoplist|stopped|string_delimiter|subject|supplemental_logging|supported|suspend|symmetric|synchronous_commit|synonym|sysname|system|system_time|system_versioning|table|tableresults|tablock|tablockx|take|tape|target|target_index|target_partition|target_recovery_time|tcp|temporal_history_retention|text|textimage_on|then|thesaurus|throw|time|timeout|timestamp|tinyint|to|top|torn_page_detection|track_columns_updated|trailing|tran|transaction|transfer|transform_noise_words|triple_des|triple_des_3key|truncate|trustworthy|try|tsql|two_digit_year_cutoff|type|type_desc|type_warning|tzoffset|uid|unbounded|uncommitted|unique|uniqueidentifier|unlimited|unload|unlock|unsafe|updlock|url|use|useplan|useroptions|use_type_default|using|utcdatetime|valid_xml|validation|value|values|varbinary|varchar|vector|verbose|verifyonly|version|view_metadata|virtual_device|visiblity|wait_at_low_priority|waitfor|webmethod|week|weekday|weight|well_formed_xml|when|while|widechar|widechar_ansi|widenative|window|windows|with|within|within group|witness|without|without_array_wrapper|workload|wsdl|xact_abort|xlock|xml|xmlschema|xquery|xsinil|year|zone)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.sql\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.scope.begin.sql\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.section.scope.end.sql\\\"}},\\\"match\\\":\\\"(\\\\\\\\()(\\\\\\\\))\\\",\\\"name\\\":\\\"meta.block.sql\\\"}],\\\"repository\\\":{\\\"comment-block\\\":{\\\"begin\\\":\\\"/\\\\\\\\*\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.sql\\\"}},\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"name\\\":\\\"comment.block\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment-block\\\"}]},\\\"comments\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(^[ \\\\\\\\t]+)?(?=--)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.comment.leading.sql\\\"}},\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"--\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.sql\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.line.double-dash.sql\\\"}]},{\\\"begin\\\":\\\"(^[ \\\\\\\\t]+)?(?=#)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.comment.leading.sql\\\"}},\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"patterns\\\":[]},{\\\"include\\\":\\\"#comment-block\\\"}]},\\\"regexps\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"/(?=\\\\\\\\S.*/)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.sql\\\"}},\\\"end\\\":\\\"/\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.sql\\\"}},\\\"name\\\":\\\"string.regexp.sql\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_interpolation\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\/\\\",\\\"name\\\":\\\"constant.character.escape.slash.sql\\\"}]},{\\\"begin\\\":\\\"%r\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.sql\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.sql\\\"}},\\\"name\\\":\\\"string.regexp.modr.sql\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_interpolation\\\"}]}]},\\\"string_escape\\\":{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"constant.character.escape.sql\\\"},\\\"string_interpolation\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.sql\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.sql\\\"}},\\\"match\\\":\\\"(#\\\\\\\\{)([^}]*)(})\\\",\\\"name\\\":\\\"string.interpolated.sql\\\"},\\\"strings\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.sql\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.sql\\\"}},\\\"match\\\":\\\"(N)?(')[^']*(')\\\",\\\"name\\\":\\\"string.quoted.single.sql\\\"},{\\\"begin\\\":\\\"'\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.sql\\\"}},\\\"end\\\":\\\"'\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.sql\\\"}},\\\"name\\\":\\\"string.quoted.single.sql\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escape\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.sql\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.sql\\\"}},\\\"match\\\":\\\"(`)[^`\\\\\\\\\\\\\\\\]*(`)\\\",\\\"name\\\":\\\"string.quoted.other.backtick.sql\\\"},{\\\"begin\\\":\\\"`\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.sql\\\"}},\\\"end\\\":\\\"`\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.sql\\\"}},\\\"name\\\":\\\"string.quoted.other.backtick.sql\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escape\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.sql\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.sql\\\"}},\\\"match\\\":\\\"(\\\\\\\")[^\\\\\\\"#]*(\\\\\\\")\\\",\\\"name\\\":\\\"string.quoted.double.sql\\\"},{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.sql\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.sql\\\"}},\\\"name\\\":\\\"string.quoted.double.sql\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_interpolation\\\"}]},{\\\"begin\\\":\\\"%\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.sql\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.sql\\\"}},\\\"name\\\":\\\"string.other.quoted.brackets.sql\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_interpolation\\\"}]}]}},\\\"scopeName\\\":\\\"source.sql\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/sql.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/stata.mjs":
/*!****************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/stata.mjs ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _sql_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./sql.mjs */ \"(app-pages-browser)/./node_modules/@shikijs/langs/dist/sql.mjs\");\n\n\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Stata\\\",\\\"fileTypes\\\":[\\\"do\\\",\\\"ado\\\",\\\"mata\\\"],\\\"foldingStartMarker\\\":\\\"\\\\\\\\{\\\\\\\\s*$\\\",\\\"foldingStopMarker\\\":\\\"^\\\\\\\\s*}\\\",\\\"name\\\":\\\"stata\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#ascii-regex-functions\\\"},{\\\"include\\\":\\\"#unicode-regex-functions\\\"},{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#functions\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#subscripts\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"include\\\":\\\"#string-compound\\\"},{\\\"include\\\":\\\"#string-regular\\\"},{\\\"include\\\":\\\"#builtin_variables\\\"},{\\\"include\\\":\\\"#macro-commands\\\"},{\\\"match\\\":\\\"\\\\\\\\b(if|else if|else)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.conditional.stata\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.scalar.stata\\\"}},\\\"match\\\":\\\"^\\\\\\\\s*(sca(l(?:ar|a|))?(\\\\\\\\s+de(f(?:ine|in|i|))?)?)\\\\\\\\s+(?!(drop|dir?|l(i(?:st|s|))?)\\\\\\\\s+)\\\"},{\\\"begin\\\":\\\"\\\\\\\\b(mer(g(?:e|))?)\\\\\\\\s+([1mn])(:)([1mn])\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.flow.stata\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#constants\\\"},{\\\"match\\\":\\\"[mn]\\\",\\\"name\\\":\\\"\\\"}]},\\\"4\\\":{\\\"name\\\":\\\"punctuation.separator.key-value\\\"},\\\"5\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#constants\\\"},{\\\"match\\\":\\\"[mn]\\\",\\\"name\\\":\\\"\\\"}]}},\\\"end\\\":\\\"using\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#builtin_variables\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"include\\\":\\\"#comments\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.flow.stata\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#macro-local-identifiers\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"keyword.control.flow.stata\\\"}},\\\"match\\\":\\\"\\\\\\\\b(foreach)\\\\\\\\s+((?!in|of).+)\\\\\\\\s+(in|of var(l(?:ist|is|i|))?|of new(l(?:ist|is|i|))?|of num(l(?:ist|is|i|))?)\\\\\\\\b\\\"},{\\\"begin\\\":\\\"\\\\\\\\b(foreach)\\\\\\\\s+((?!in|of).+)\\\\\\\\s+(of (?:loc(a(?:l|))?|glo(b(?:al|a|))?))\\\\\\\\b\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.flow.stata\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#macro-local-identifiers\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"keyword.control.flow.stata\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\s*\\\\\\\\{)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#macro-local-identifiers\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\b(forv(?:alues|alue|alu|al|a|))\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.flow.stata\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(=)\\\\\\\\s*([^{]+)\\\\\\\\s*|(?=\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.assignment.stata\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"}]}},\\\"patterns\\\":[{\\\"include\\\":\\\"#macro-local-identifiers\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"}]},{\\\"match\\\":\\\"\\\\\\\\b(while|continue)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.flow.stata\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.stata\\\"}},\\\"match\\\":\\\"\\\\\\\\b(as(?:|s|se|ser|sert))\\\\\\\\b\\\"},{\\\"match\\\":\\\"\\\\\\\\b(by(s(?:ort|or|o|))?|statsby|rolling|bootstrap|jackknife|permute|simulate|svy|mi est(i(?:mate|mat|ma|m|))?|nestreg|stepwise|xi|fp|mfp|vers(i(?:on|o|))?)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.function.stata\\\"},{\\\"match\\\":\\\"\\\\\\\\b(qui(e(?:tly|tl|t|))?|n(o(?:isily|isil|isi|is|i|))?|cap(t(?:ure|ur|u|))?)\\\\\\\\b:?\\\",\\\"name\\\":\\\"keyword.control.flow.stata\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.function.stata\\\"},\\\"3\\\":{\\\"name\\\":\\\"storage.type.function.stata\\\"},\\\"7\\\":{\\\"name\\\":\\\"entity.name.function.stata\\\"}},\\\"match\\\":\\\"\\\\\\\\s*(pr(o(?:gram|gra|gr|g|))?)\\\\\\\\s+((di(r)?|drop|l(i(?:st|s|))?)\\\\\\\\s+)([\\\\\\\\w&&[^0-9]]\\\\\\\\w{0,31})\\\"},{\\\"begin\\\":\\\"^\\\\\\\\s*(pr(o(?:gram|gra|gr|g|))?)\\\\\\\\s+(de(f(?:ine|in|i|))?\\\\\\\\s+)?\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.function.stata\\\"},\\\"3\\\":{\\\"name\\\":\\\"storage.type.function.stata\\\"}},\\\"end\\\":\\\"(?=[,\\\\\\\\n/])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"match\\\":\\\"[\\\\\\\\w&&[^0-9]]\\\\\\\\w{0,31}\\\",\\\"name\\\":\\\"entity.name.function.stata\\\"},{\\\"match\\\":\\\"[^A-za-z_0-9,\\\\\\\\n/ ]+\\\",\\\"name\\\":\\\"invalid.illegal.name.stata\\\"}]},{\\\"captures\\\":{\\\"1\\\":\\\"keyword.functions.data.stata.test\\\"},\\\"match\\\":\\\"\\\\\\\\b(form(a(?:t|))?)\\\\\\\\s*([\\\\\\\\w&&[^0-9]]\\\\\\\\w{0,31})*\\\\\\\\s*(%)(-)?(0)?([0-9]+)(.)([0-9]+)([efg])(c)?\\\"},{\\\"include\\\":\\\"#braces-with-error\\\"},{\\\"begin\\\":\\\"(?=syntax)\\\",\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"syntax\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.functions.program.stata\\\"}},\\\"end\\\":\\\"(?=[,\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"///\\\",\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.block.stata\\\"},{\\\"match\\\":\\\"\\\\\\\\[\\\",\\\"name\\\":\\\"punctuation.definition.parameters.begin.stata\\\"},{\\\"match\\\":\\\"]\\\",\\\"name\\\":\\\"punctuation.definition.parameters.end.stata\\\"},{\\\"match\\\":\\\"\\\\\\\\b(varlist|varname|newvarlist|newvarname|namelist|name|anything)\\\\\\\\b\\\",\\\"name\\\":\\\"entity.name.type.class.stata\\\"},{\\\"captures\\\":{\\\"2\\\":{\\\"name\\\":\\\"entity.name.type.class.stata\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.operator.arithmetic.stata\\\"}},\\\"match\\\":\\\"\\\\\\\\b((if|in|using|fweight|aweight|pweight|iweight))\\\\\\\\b(/)?\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.arithmetic.stata\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.type.class.stata\\\"}},\\\"match\\\":\\\"(/)?(exp)\\\"},{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"include\\\":\\\"#string-compound\\\"},{\\\"include\\\":\\\"#string-regular\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"include\\\":\\\"#builtin_variables\\\"}]},{\\\"begin\\\":\\\",\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.variable.begin.stata\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\n)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"///\\\",\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.block.stata\\\"},{\\\"begin\\\":\\\"([^\\\\\\\\s\\\\\\\\[\\\\\\\\]]+)(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#macro-local-identifiers\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"}]},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.parentheses.stata\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.parentheses.stata\\\"}},\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.type.stata\\\"}},\\\"match\\\":\\\"\\\\\\\\b(integer|intege|integ|inte|int|real|string|strin|stri|str)\\\\\\\\b\\\"},{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"include\\\":\\\"#string-compound\\\"},{\\\"include\\\":\\\"#string-regular\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"include\\\":\\\"#builtin_variables\\\"}]},{\\\"include\\\":\\\"#macro-local-identifiers\\\"},{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"include\\\":\\\"#string-compound\\\"},{\\\"include\\\":\\\"#string-regular\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"include\\\":\\\"#builtin_variables\\\"}]}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.functions.data.stata\\\"}},\\\"match\\\":\\\"\\\\\\\\b(sa(v(?:|e))|saveold|destring|tostring|u(s(?:e|))?|note(s)?|form(a(?:t|))?)\\\\\\\\b\\\"},{\\\"match\\\":\\\"\\\\\\\\b(e(?:xit|nd))\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.functions.data.stata\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.functions.data.stata\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#macro-local\\\"}]},\\\"4\\\":{\\\"name\\\":\\\"invalid.illegal.name.stata\\\"},\\\"5\\\":{\\\"name\\\":\\\"keyword.operator.assignment.stata\\\"}},\\\"match\\\":\\\"\\\\\\\\b(replace)\\\\\\\\s+([^=]+)\\\\\\\\s*((==)|(=))\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.functions.data.stata\\\"},\\\"3\\\":{\\\"name\\\":\\\"support.type.stata\\\"},\\\"5\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#reserved-names\\\"},{\\\"include\\\":\\\"#macro-local\\\"}]},\\\"7\\\":{\\\"name\\\":\\\"invalid.illegal.name.stata\\\"},\\\"8\\\":{\\\"name\\\":\\\"keyword.operator.assignment.stata\\\"}},\\\"match\\\":\\\"\\\\\\\\b(g(e(?:nerate|nerat|nera|ner|ne|n|))?|egen)\\\\\\\\s+((byte|int|long|float|double|str[1-9]?[0-9]?[0-9]?[0-9]?|strL)\\\\\\\\s+)?([^=\\\\\\\\s]+)\\\\\\\\s*((==)|(=))\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.functions.data.stata\\\"},\\\"3\\\":{\\\"name\\\":\\\"support.type.stata\\\"}},\\\"match\\\":\\\"\\\\\\\\b(set ty(p(?:e|))?)\\\\\\\\s+((byte|int|long|float|double|str[1-9]?[0-9]?[0-9]?[0-9]?|strL)?\\\\\\\\s+)\\\\\\\\b\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.functions.data.stata\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.functions.data.stata\\\"},\\\"6\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.stata\\\"},\\\"7\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#string-compound\\\"},{\\\"include\\\":\\\"#macro-local-escaped\\\"},{\\\"include\\\":\\\"#macro-global-escaped\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"match\\\":\\\"[^`$]{81,}\\\",\\\"name\\\":\\\"invalid.illegal.name.stata\\\"},{\\\"match\\\":\\\".\\\",\\\"name\\\":\\\"string.quoted.double.compound.stata\\\"}]},\\\"8\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.stata\\\"}},\\\"match\\\":\\\"\\\\\\\\b(la(b(?:el|e|))?)\\\\\\\\s+(var(i(?:able|abl|ab|a|))?)\\\\\\\\s+([\\\\\\\\w&&[^0-9]]\\\\\\\\w{0,31})\\\\\\\\s+(`\\\\\\\")(.+)(\\\\\\\"')\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.functions.data.stata\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.functions.data.stata\\\"},\\\"6\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.stata\\\"},\\\"7\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#macro-local-escaped\\\"},{\\\"include\\\":\\\"#macro-global-escaped\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"match\\\":\\\"[^`$]{81,}\\\",\\\"name\\\":\\\"invalid.illegal.name.stata\\\"},{\\\"match\\\":\\\".\\\",\\\"name\\\":\\\"string.quoted.double.stata\\\"}]},\\\"8\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.stata\\\"}},\\\"match\\\":\\\"\\\\\\\\b(la(b(?:el|e|))?)\\\\\\\\s+(var(i(?:able|abl|ab|a|))?)\\\\\\\\s+([\\\\\\\\w&&[^0-9]]\\\\\\\\w{0,31})\\\\\\\\s+(\\\\\\\")(.+)(\\\\\\\")\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.functions.data.stata\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.functions.data.stata\\\"}},\\\"match\\\":\\\"\\\\\\\\b(la(b(?:el|e|))?)\\\\\\\\s+(da(t(?:a|))?|var(i(?:able|abl|ab|a|))?|de(f(?:|i|in|ine))?|val(u(?:es|e|))?|di(r)?|l(i(?:st|s|))?|copy|drop|save|lang(u(?:age|ag|a|))?)\\\\\\\\b\\\"},{\\\"begin\\\":\\\"\\\\\\\\b(drop|keep)\\\\\\\\b(?!\\\\\\\\s+(i[fn])\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.functions.data.stata\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(i[fn])\\\\\\\\b\\\",\\\"name\\\":\\\"invalid.illegal.name.stata\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"include\\\":\\\"#operators\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.functions.data.stata\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.functions.data.stata\\\"}},\\\"match\\\":\\\"\\\\\\\\b(drop|keep)\\\\\\\\s+(i[fn])\\\\\\\\b\\\"},{\\\"begin\\\":\\\"^\\\\\\\\s*mata:?\\\\\\\\s*$\\\",\\\"end\\\":\\\"^\\\\\\\\s*end\\\\\\\\s*$\\\\\\\\n?\\\",\\\"name\\\":\\\"meta.embedded.block.mata\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<![^$\\\\\\\\s])(version|pragma|if|else|for|while|do|break|continue|goto|return)(?=\\\\\\\\s)\\\",\\\"name\\\":\\\"keyword.control.mata\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.eltype.mata\\\"},\\\"4\\\":{\\\"name\\\":\\\"storage.type.orgtype.mata\\\"}},\\\"match\\\":\\\"\\\\\\\\b(transmorphic|string|numeric|real|complex|(pointer(\\\\\\\\([^)]+\\\\\\\\))?))\\\\\\\\s+(matrix|vector|rowvector|colvector|scalar)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.mata\\\"},{\\\"match\\\":\\\"\\\\\\\\b(transmorphic|string|numeric|real|complex|(pointer(\\\\\\\\([^)]+\\\\\\\\))?))\\\\\\\\s\\\",\\\"name\\\":\\\"storage.type.eltype.mata\\\"},{\\\"match\\\":\\\"\\\\\\\\b(matrix|vector|rowvector|colvector|scalar)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.orgtype.mata\\\"},{\\\"match\\\":\\\"!|\\\\\\\\+\\\\\\\\+|--|[\\\\\\\\&'?\\\\\\\\\\\\\\\\]|::|,|\\\\\\\\.\\\\\\\\.|[|=]|==|>=|<=|[<>]|!=|[#+\\\\\\\\-*^/]\\\",\\\"name\\\":\\\"keyword.operator.mata\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\b(odbc)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.control.flow.stata\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"///\\\",\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.block.stata\\\"},{\\\"begin\\\":\\\"(exec?)(\\\\\\\\(\\\\\\\")\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.builtin.stata\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.begin.stata\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.end.stata\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"source.sql\\\"}]},{\\\"include\\\":\\\"$self\\\"}]},{\\\"include\\\":\\\"#commands-other\\\"}],\\\"repository\\\":{\\\"ascii-regex-character-class\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\[*+?\\\\\\\\-.^$|\\\\\\\\[\\\\\\\\]()\\\\\\\\\\\\\\\\]\\\",\\\"name\\\":\\\"constant.character.escape.backslash.stata\\\"},{\\\"match\\\":\\\"\\\\\\\\.\\\",\\\"name\\\":\\\"constant.character.character-class.stata\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"illegal.invalid.character-class.stata\\\"},{\\\"begin\\\":\\\"(\\\\\\\\[)(\\\\\\\\^)?\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.character-class.stata\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.negation.stata\\\"}},\\\"end\\\":\\\"(])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.character-class.stata\\\"}},\\\"name\\\":\\\"constant.other.character-class.set.stata\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#ascii-regex-character-class\\\"},{\\\"captures\\\":{\\\"2\\\":{\\\"name\\\":\\\"constant.character.escape.backslash.stata\\\"},\\\"4\\\":{\\\"name\\\":\\\"constant.character.escape.backslash.stata\\\"}},\\\"match\\\":\\\"((\\\\\\\\\\\\\\\\.)|.)-((\\\\\\\\\\\\\\\\.)|[^\\\\\\\\]])\\\",\\\"name\\\":\\\"constant.other.character-class.range.stata\\\"}]}]},\\\"ascii-regex-functions\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.builtin.stata\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.begin.stata\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#string-compound\\\"},{\\\"include\\\":\\\"#string-regular\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"include\\\":\\\"#functions\\\"},{\\\"match\\\":\\\"[\\\\\\\\w&&[^0-9]]\\\\\\\\w{0,31}\\\",\\\"name\\\":\\\"variable.parameter.function.stata\\\"},{\\\"include\\\":\\\"#comments-triple-slash\\\"}]},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.variable.begin.stata\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.stata\\\"},\\\"6\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#ascii-regex-internals\\\"}]},\\\"7\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.stata\\\"},\\\"8\\\":{\\\"name\\\":\\\"invalid.illegal.punctuation.stata\\\"},\\\"9\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.end.stata\\\"}},\\\"match\\\":\\\"\\\\\\\\b(regexm)(\\\\\\\\()([^,]+)(,)\\\\\\\\s*(\\\\\\\")([^\\\\\\\"]+)(\\\\\\\"(')?)\\\\\\\\s*(\\\\\\\\))\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.builtin.stata\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.begin.stata\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#string-compound\\\"},{\\\"include\\\":\\\"#string-regular\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"include\\\":\\\"#functions\\\"},{\\\"match\\\":\\\"[\\\\\\\\w&&[^0-9]]\\\\\\\\w{0,31}\\\",\\\"name\\\":\\\"variable.parameter.function.stata\\\"},{\\\"include\\\":\\\"#comments-triple-slash\\\"}]},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.variable.begin.stata\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.stata\\\"},\\\"6\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#ascii-regex-internals\\\"}]},\\\"7\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.stata\\\"},\\\"8\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.end.stata\\\"}},\\\"match\\\":\\\"\\\\\\\\b(regexm)(\\\\\\\\()([^,]+)(,)\\\\\\\\s*(`\\\\\\\")([^\\\\\\\"]+)(\\\\\\\"')\\\\\\\\s*(\\\\\\\\))\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.builtin.stata\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.begin.stata\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#string-compound\\\"},{\\\"include\\\":\\\"#string-regular\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"include\\\":\\\"#functions\\\"},{\\\"match\\\":\\\"[\\\\\\\\w&&[^0-9]]\\\\\\\\w{0,31}\\\",\\\"name\\\":\\\"variable.parameter.function.stata\\\"},{\\\"include\\\":\\\"#comments\\\"}]},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.variable.begin.stata\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.stata\\\"},\\\"6\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#ascii-regex-internals\\\"}]},\\\"7\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.stata\\\"},\\\"8\\\":{\\\"name\\\":\\\"invalid.illegal.punctuation.stata\\\"},\\\"9\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.definition.variable.begin.stata\\\"},{\\\"include\\\":\\\"#string-compound\\\"},{\\\"include\\\":\\\"#string-regular\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"include\\\":\\\"#functions\\\"},{\\\"match\\\":\\\"[\\\\\\\\w&&[^0-9]]\\\\\\\\w{0,31}\\\",\\\"name\\\":\\\"variable.parameter.function.stata\\\"},{\\\"include\\\":\\\"#comments-triple-slash\\\"}]},\\\"10\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.end.stata\\\"}},\\\"match\\\":\\\"\\\\\\\\b(regexr)(\\\\\\\\()([^,]+)(,)\\\\\\\\s*(\\\\\\\")([^\\\\\\\"]+)(\\\\\\\"(')?)\\\\\\\\s*([^)]*)(\\\\\\\\))\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.builtin.stata\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.begin.stata\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#string-compound\\\"},{\\\"include\\\":\\\"#string-regular\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"include\\\":\\\"#functions\\\"},{\\\"match\\\":\\\"[\\\\\\\\w&&[^0-9]]\\\\\\\\w{0,31}\\\",\\\"name\\\":\\\"variable.parameter.function.stata\\\"},{\\\"include\\\":\\\"#comments\\\"}]},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.variable.begin.stata\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.stata\\\"},\\\"6\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#ascii-regex-internals\\\"}]},\\\"7\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.stata\\\"},\\\"8\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.definition.variable.begin.stata\\\"},{\\\"include\\\":\\\"#string-compound\\\"},{\\\"include\\\":\\\"#string-regular\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"include\\\":\\\"#functions\\\"},{\\\"match\\\":\\\"[\\\\\\\\w&&[^0-9]]\\\\\\\\w{0,31}\\\",\\\"name\\\":\\\"variable.parameter.function.stata\\\"},{\\\"include\\\":\\\"#comments-triple-slash\\\"}]},\\\"9\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.end.stata\\\"}},\\\"match\\\":\\\"\\\\\\\\b(regexr)(\\\\\\\\()([^,]+)(,)\\\\\\\\s*(`\\\\\\\")([^\\\\\\\"]+)(\\\\\\\"')\\\\\\\\s*([^)]*)(\\\\\\\\))\\\"}]},\\\"ascii-regex-internals\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\^\\\",\\\"name\\\":\\\"keyword.control.anchor.stata\\\"},{\\\"match\\\":\\\"\\\\\\\\$(?![a-zA-Z_{])\\\",\\\"name\\\":\\\"keyword.control.anchor.stata\\\"},{\\\"match\\\":\\\"[?+*]\\\",\\\"name\\\":\\\"keyword.control.quantifier.stata\\\"},{\\\"match\\\":\\\"\\\\\\\\|\\\",\\\"name\\\":\\\"keyword.control.or.stata\\\"},{\\\"begin\\\":\\\"(\\\\\\\\()(?=[?*+])\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.group.stata\\\"}},\\\"contentName\\\":\\\"invalid.illegal.regexm.stata\\\",\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.group.stata\\\"}}},{\\\"begin\\\":\\\"(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.group.stata\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.group.stata\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#ascii-regex-internals\\\"}]},{\\\"include\\\":\\\"#ascii-regex-character-class\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"match\\\":\\\".\\\",\\\"name\\\":\\\"string.quoted.stata\\\"}]},\\\"braces-with-error\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(\\\\\\\\{)\\\\\\\\s*([^\\\\\\\\n]*)(?=\\\\\\\\n)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.block.begin.stata\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"match\\\":\\\"[^\\\\\\\\n]+\\\",\\\"name\\\":\\\"illegal.invalid.name.stata\\\"}]}},\\\"end\\\":\\\"^\\\\\\\\s*(})\\\\\\\\s*$|^\\\\\\\\s*([^*\\\\\\\"}]+)\\\\\\\\s+(})\\\\\\\\s*([^*\\\\\\\"}/\\\\\\\\n]+)|^\\\\\\\\s*([^\\\\\\\"*}]+)\\\\\\\\s+(})|\\\\\\\\s*(})\\\\\\\\s*([^\\\\\\\"*}/\\\\\\\\n]+)|(})$\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.block.end.stata\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.name.stata\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.control.block.end.stata\\\"},\\\"4\\\":{\\\"name\\\":\\\"invalid.illegal.name.stata\\\"},\\\"5\\\":{\\\"name\\\":\\\"invalid.illegal.name.stata\\\"},\\\"6\\\":{\\\"name\\\":\\\"keyword.control.block.end.stata\\\"},\\\"7\\\":{\\\"name\\\":\\\"keyword.control.block.end.stata\\\"},\\\"8\\\":{\\\"name\\\":\\\"invalid.illegal.name.stata\\\"},\\\"9\\\":{\\\"name\\\":\\\"keyword.control.block.end.stata\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]}]},\\\"braces-without-error\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.control.block.begin.stata\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.control.block.end.stata\\\"}}}]},\\\"builtin_types\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(byte|int|long|float|double|str[1-9]?[0-9]?[0-9]?[0-9]?|strL)\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.stata\\\"}]},\\\"builtin_variables\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(_(?:b|coef|cons|[nN]|rc|se))\\\\\\\\b\\\",\\\"name\\\":\\\"variable.object.stata\\\"}]},\\\"commands-other\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(reghdfe|ivreghdfe|ivreg2|outreg|gcollapse|gcontract|gegen|gisid|glevelsof|gquantiles)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.flow.stata\\\"},{\\\"match\\\":\\\"\\\\\\\\b(about|ac|acprplot|ado|adopath|adoupdate|alpha|ameans|an|ano|anov|anova|anova_terms|anovadef|aorder|ap|app|appe|appen|append|arch|arch_dr|arch_estat|arch_p|archlm|areg|areg_p|args|arima|arima_dr|arima_estat|arima_p|asmprobit|asmprobit_estat|asmprobit_lf|asmprobit_mfx__dlg|asmprobit_p|avplot|avplots|bcskew0|bgodfrey|binreg|bip0_lf|biplot|bipp_lf|bipr_lf|bipr_p|biprobit|bitest|bitesti|bitowt|blogit|bmemsize|boot|bootsamp|boxco_l|boxco_p|boxcox|boxcox_p|bprobit|br|break|brier|bro|brow|brows|browse|brr|brrstat|bs|bsampl_w|bsample|bsqreg|bstat|bstrap|ca|ca_estat|ca_p|cabiplot|camat|canon|canon_estat|canon_p|caprojection|cat|cc|cchart|cci|cd|censobs_table|centile|cf|char|chdir|checkdlgfiles|checkestimationsample|checkhlpfiles|checksum|chelp|ci|cii|cl|class|classutil|clear|cli|clis|clist|clog|clog_lf|clog_p|clogi|clogi_sw|clogit|clogit_lf|clogit_p|clogitp|clogl_sw|cloglog|clonevar|clslistarray|cluster|cluster_measures|cluster_stop|cluster_tree|cluster_tree_8|clustermat|cmdlog|cnr|cnre|cnreg|cnreg_p|cnreg_sw|cnsreg|codebook|collaps4|collapse|colormult_nb|colormult_nw|compare|compress|conf|confi|confir|confirm|conren|cons|const|constr|constra|constrai|constrain|constraint|contract|copy|copyright|copysource|cor|corc|corr|corr2data|corr_anti|corr_kmo|corr_smc|corre|correl|correla|correlat|correlate|corrgram|cou|coun|count|cprplot|crc|cret|cretu|cretur|creturn|cross|cs|cscript|cscript_log|csi|ct|ct_is|ctset|ctst_st|cttost|cumsp|cumul|cusum|cutil|d|datasig|datasign|datasigna|datasignat|datasignatu|datasignatur|datasignature|datetof|db|dbeta|de|dec|deco|decod|decode|deff|des|desc|descr|descri|describ|describe|dfbeta|dfgls|dfuller|di|di_g|dir|dirstats|dis|discard|disp|disp_res|disp_s|displ|displa|display|do|doe|doed|doedi|doedit|dotplot|dprobit|drawnorm|ds|ds_util|dstdize|duplicates|durbina|dwstat|dydx|ed|edi|edit|eivreg|emdef|en|enc|enco|encod|encode|eq|erase|ereg|ereg_lf|ereg_p|ereg_sw|ereghet|ereghet_glf|ereghet_glf_sh|ereghet_gp|ereghet_ilf|ereghet_ilf_sh|ereghet_ip|eret|eretu|eretur|ereturn|err|erro|error|est|est_cfexist|est_cfname|est_clickable|est_expand|est_hold|est_table|est_unhold|est_unholdok|estat|estat_default|estat_summ|estat_vce_only|esti|estimates|etodow|etof|etomdy|expand|expandcl|fac|fact|facto|factor|factor_estat|factor_p|factor_pca_rotated|factor_rotate|factormat|fcast|fcast_compute|fcast_graph|fdades|fdadesc|fdadescr|fdadescri|fdadescrib|fdadescribe|fdasav|fdasave|fdause|fh_st|file|filefilter|fillin|find_hlp_file|findfile|findit|fit|fl|fli|flis|flist|fpredict|frac_adj|frac_chk|frac_cox|frac_ddp|frac_dis|frac_dv|frac_in|frac_mun|frac_pp|frac_pq|frac_pv|frac_wgt|frac_xo|fracgen|fracplot|fracpoly|fracpred|fron_ex|fron_hn|fron_p|fron_tn|fron_tn2|frontier|ftodate|ftoe|ftomdy|ftowdate|gamhet_glf|gamhet_gp|gamhet_ilf|gamhet_ip|gamma|gamma_d2|gamma_p|gamma_sw|gammahet|gdi_hexagon|gdi_spokes|genrank|genstd|genvmean|gettoken|gladder|glim_l01|glim_l02|glim_l03|glim_l04|glim_l05|glim_l06|glim_l07|glim_l08|glim_l09|glim_l10|glim_l11|glim_l12|glim_lf|glim_mu|glim_nw1|glim_nw2|glim_nw3|glim_p|glim_v1|glim_v2|glim_v3|glim_v4|glim_v5|glim_v6|glim_v7|glm|glm_p|glm_sw|glmpred|glogit|glogit_p|gmeans|gnbre_lf|gnbreg|gnbreg_p|gomp_lf|gompe_sw|gomper_p|gompertz|gompertzhet|gomphet_glf|gomphet_glf_sh|gomphet_gp|gomphet_ilf|gomphet_ilf_sh|gomphet_ip|gphdot|gphpen|gphprint|gprefs|gprobi_p|gprobit|gr|gr7|gr_copy|gr_current|gr_db|gr_describe|gr_dir|gr_draw|gr_draw_replay|gr_drop|gr_edit|gr_editviewopts|gr_example|gr_example2|gr_export|gr_print|gr_qscheme|gr_query|gr_read|gr_rename|gr_replay|gr_save|gr_set|gr_setscheme|gr_table|gr_undo|gr_use|graph|grebar|greigen|grmeanby|gs_fileinfo|gs_filetype|gs_graphinfo|gs_stat|gsort|gwood|h|hareg|hausman|haver|he|heck_d2|heckma_p|heckman|heckp_lf|heckpr_p|heckprob|hel|help|hereg|hetpr_lf|hetpr_p|hetprob|hettest|hexdump|hilite|hist|histogram|hlogit|hlu|hmeans|hotel|hotelling|hprobit|hreg|hsearch|icd9|icd9_ff|icd9p|iis|impute|imtest|inbase|include|inf|infi|infil|infile|infix|inp|inpu|input|ins|insheet|insp|inspe|inspec|inspect|integ|inten|intreg|intreg_p|intrg2_ll|intrg_ll|intrg_ll2|ipolate|iqreg|ir|irf|irf_create|irfm|iri|is_svy|is_svysum|isid|istdize|ivprobit|ivprobit_p|ivreg|ivreg_footnote|ivtob_lf|ivtobit|ivtobit_p|jacknife|jknife|jkstat|joinby|kalarma1|kap|kapmeier|kappa|kapwgt|kdensity|ksm|ksmirnov|ktau|kwallis|labelbook|ladder|levelsof|leverage|lfit|lfit_p|li|lincom|line|linktest|lis|list|lloghet_glf|lloghet_glf_sh|lloghet_gp|lloghet_ilf|lloghet_ilf_sh|lloghet_ip|llogi_sw|llogis_p|llogist|llogistic|llogistichet|lnorm_lf|lnorm_sw|lnorma_p|lnormal|lnormalhet|lnormhet_glf|lnormhet_glf_sh|lnormhet_gp|lnormhet_ilf|lnormhet_ilf_sh|lnormhet_ip|lnskew0|loadingplot|(?<!\\\\\\\\.)log|logi|logis_lf|logistic|logistic_p|logit|logit_estat|logit_p|loglogs|logrank|loneway|lookfor|lookup|lowess|lpredict|lrecomp|lroc|lrtest|ls|lsens|lsens_x|lstat|ltable|ltriang|lv|lvr2plot|m|ma|mac|macr|macro|makecns|man|manova|manovatest|mantel|mark|markin|markout|marksample|mat|mat_capp|mat_order|mat_put_rr|mat_rapp|mata|mata_clear|mata_describe|mata_drop|mata_matdescribe|mata_matsave|mata_matuse|mata_memory|mata_mlib|mata_mosave|mata_rename|mata_which|matalabel|matcproc|matlist|matname|matr|matri|matrix|matrix_input__dlg|matstrik|mcc|mcci|md0_|md1_|md1debug_|md2_|md2debug_|mds|mds_estat|mds_p|mdsconfig|mdslong|mdsmat|mdsshepard|mdytoe|mdytof|me_derd|mean|means|median|memory|memsize|mfp|mfx|mhelp|mhodds|minbound|mixed_ll|mixed_ll_reparm|mkassert|mkdir|mkmat|mkspline|ml|ml_adjs|ml_bhhhs|ml_c_d|ml_check|ml_clear|ml_cnt|ml_debug|ml_defd|ml_e0|ml_e0_bfgs|ml_e0_cycle|ml_e0_dfp|ml_e0i|ml_e1|ml_e1_bfgs|ml_e1_bhhh|ml_e1_cycle|ml_e1_dfp|ml_e2|ml_e2_cycle|ml_ebfg0|ml_ebfr0|ml_ebfr1|ml_ebh0q|ml_ebhh0|ml_ebhr0|ml_ebr0i|ml_ecr0i|ml_edfp0|ml_edfr0|ml_edfr1|ml_edr0i|ml_eds|ml_eer0i|ml_egr0i|ml_elf|ml_elf_bfgs|ml_elf_bhhh|ml_elf_cycle|ml_elf_dfp|ml_elfi|ml_elfs|ml_enr0i|ml_enrr0|ml_erdu0|ml_erdu0_bfgs|ml_erdu0_bhhh|ml_erdu0_bhhhq|ml_erdu0_cycle|ml_erdu0_dfp|ml_erdu0_nrbfgs|ml_exde|ml_footnote|ml_geqnr|ml_grad0|ml_graph|ml_hbhhh|ml_hd0|ml_hold|ml_init|ml_inv|ml_log|ml_max|ml_mlout|ml_mlout_8|ml_model|ml_nb0|ml_opt|ml_p|ml_plot|ml_query|ml_rdgrd|ml_repor|ml_s_e|ml_score|ml_searc|ml_technique|ml_unhold|mleval|mlf_|mlmatbysum|mlmatsum|mlog|mlogi|mlogit|mlogit_footnote|mlogit_p|mlopts|mlsum|mlvecsum|mnl0_|mor|more|mov|move|mprobit|mprobit_lf|mprobit_p|mrdu0_|mrdu1_|mvdecode|mvencode|mvreg|mvreg_estat|nbreg|nbreg_al|nbreg_lf|nbreg_p|nbreg_sw|nestreg|net|newey|newey_p|news|nl|nlcom|nlcom_p|nlexp2|nlexp2a|nlexp3|nlgom3|nlgom4|nlinit|nllog3|nllog4|nlog_rd|nlogit|nlogit_p|nlogitgen|nlogittree|nlpred|nobreak|notes_dlg|nptrend|numlabel|numlist|old_ver|olo|olog|ologi|ologi_sw|ologit|ologit_p|ologitp|on|one|onew|onewa|oneway|op_colnm|op_comp|op_diff|op_inv|op_str|opr|opro|oprob|oprob_sw|oprobi|oprobi_p|oprobit|oprobitp|opts_exclusive|order|orthog|orthpoly|ou|out|outf|outfi|outfil|outfile|outs|outsh|outshe|outshee|outsheet|ovtest|pac|palette|parse_dissim|pause|pca|pca_display|pca_estat|pca_p|pca_rotate|pcamat|pchart|pchi|pcorr|pctile|pentium|pergram|personal|peto_st|pkcollapse|pkcross|pkequiv|pkexamine|pkshape|pksumm|plugin|pnorm|poisgof|poiss_lf|poiss_sw|poisso_p|poisson|poisson_estat|post|postclose|postfile|postutil|pperron|prais|prais_e|prais_e2|prais_p|predict|predictnl|preserve|print|prob|probi|probit|probit_estat|probit_p|proc_time|procoverlay|procrustes|procrustes_estat|procrustes_p|profiler|prop|proportion|prtest|prtesti|pwcorr|pwd|qs|qby|qbys|qchi|qladder|qnorm|qqplot|qreg|qreg_c|qreg_p|qreg_sw|qu|quadchk|quantile|que|quer|query|range|ranksum|ratio|rchart|rcof|recast|recode|reg|reg3|reg3_p|regdw|regr|regre|regre_p2|regres|regres_p|regress|regress_estat|regriv_p|remap|ren|rena|renam|rename|renpfix|repeat|reshape|restore|ret|retu|retur|return|rmdir|robvar|roccomp|rocf_lf|rocfit|rocgold|rocplot|roctab|rologit|rologit_p|rot|rota|rotat|rotate|rotatemat|rreg|rreg_p|ru|run|runtest|rvfplot|rvpplot|safesum|sample|sampsi|savedresults|sc|scatter|scm_mine|sco|scob_lf|scob_p|scobi_sw|scobit|scor|score|scoreplot|scoreplot_help|scree|screeplot|screeplot_help|sdtest|sdtesti|se|search|separate|seperate|serrbar|serset|set|set_defaults|sfrancia|sh|she|shel|shell|shewhart|signestimationsample|signrank|signtest|simul|sktest|sleep|slogit|slogit_d2|slogit_p|smooth|snapspan|so|sor|sort|spearman|spikeplot|spikeplt|spline_x|split|sqreg|sqreg_p|sret|sretu|sretur|sreturn|ssc|st|st_ct|st_hc|st_hcd|st_hcd_sh|st_is|st_issys|st_note|st_promo|st_set|st_show|st_smpl|st_subid|stack|stbase|stci|stcox|stcox_estat|stcox_fr|stcox_fr_ll|stcox_p|stcox_sw|stcoxkm|stcstat|stcurv|stcurve|stdes|stem|stepwise|stfill|stgen|stir|stjoin|stmc|stmh|stphplot|stphtest|stptime|strate|streg|streg_sw|streset|sts|stset|stsplit|stsum|sttocc|sttoct|stvary|su|suest|sum|summ|summa|summar|summari|summariz|summarize|sunflower|sureg|survcurv|survsum|svar|svar_p|svmat|svy_disp|svy_dreg|svy_est|svy_est_7|svy_estat|svy_get|svy_gnbreg_p|svy_head|svy_header|svy_heckman_p|svy_heckprob_p|svy_intreg_p|svy_ivreg_p|svy_logistic_p|svy_logit_p|svy_mlogit_p|svy_nbreg_p|svy_ologit_p|svy_oprobit_p|svy_poisson_p|svy_probit_p|svy_regress_p|svy_sub|svy_sub_7|svy_x|svy_x_7|svy_x_p|svydes|svygen|svygnbreg|svyheckman|svyheckprob|svyintreg|svyintrg|svyivreg|svylc|svylog_p|svylogit|svymarkout|svymean|svymlog|svymlogit|svynbreg|svyolog|svyologit|svyoprob|svyoprobit|svyopts|svypois|svypoisson|svyprobit|svyprobt|svyprop|svyratio|svyreg|svyreg_p|svyregress|svyset|svytab|svytest|svytotal|sw|swilk|symmetry|symmi|symplot|sysdescribe|sysdir|sysuse|szroeter|ta|tab|tab1|tab2|tab_or|tabd|tabdi|tabdis|tabdisp|tabi|table|tabodds|tabstat|tabu|tabul|tabula|tabulat|tabulate|te|tes|test|testnl|testparm|teststd|tetrachoric|time_it|timer|tis|tob|tobi|tobit|tobit_p|tobit_sw|token|tokeni|tokeniz|tokenize|total|translate|translator|transmap|treat_ll|treatr_p|treatreg|trim|trnb_cons|trnb_mean|trpoiss_d2|trunc_ll|truncr_p|truncreg|tsappend|tset|tsfill|tsline|tsline_ex|tsreport|tsrevar|tsrline|tsset|tssmooth|tsunab|ttest|ttesti|tut_chk|tut_wait|tutorial|tw|tware_st|two|twoway|twoway__fpfit_serset|twoway__function_gen|twoway__histogram_gen|twoway__ipoint_serset|twoway__ipoints_serset|twoway__kdensity_gen|twoway__lfit_serset|twoway__normgen_gen|twoway__pci_serset|twoway__qfit_serset|twoway__scatteri_serset|twoway__sunflower_gen|twoway_ksm_serset|ty|typ|type|typeof|unab|unabbrev|unabcmd|update|uselabel|var|var_mkcompanion|var_p|varbasic|varfcast|vargranger|varirf|varirf_add|varirf_cgraph|varirf_create|varirf_ctable|varirf_describe|varirf_dir|varirf_drop|varirf_erase|varirf_graph|varirf_ograph|varirf_rename|varirf_set|varirf_table|varlmar|varnorm|varsoc|varstable|varstable_w|varstable_w2|varwle|vec|vec_fevd|vec_mkphi|vec_p|vec_p_w|vecirf_create|veclmar|veclmar_w|vecnorm|vecnorm_w|vecrank|vecstable|verinst|vers|versi|versio|version|view|viewsource|vif|vwls|wdatetof|webdescribe|webseek|webuse|wh|whelp|whi|which|wilc_st|wilcoxon|win|wind|windo|window|winexec|wntestb|wntestq|xchart|xcorr|xi|xmlsav|xmlsave|xmluse|xpose|xsh|xshe|xshel|xshell|xt_iis|xt_tis|xtab_p|xtabond|xtbin_p|xtclog|xtcloglog|xtcloglog_d2|xtcloglog_pa_p|xtcloglog_re_p|xtcnt_p|xtcorr|xtdata|xtdes|xtfront_p|xtfrontier|xtgee|xtgee_elink|xtgee_estat|xtgee_makeivar|xtgee_p|xtgee_plink|xtgls|xtgls_p|xthaus|xthausman|xtht_p|xthtaylor|xtile|xtint_p|xtintreg|xtintreg_d2|xtintreg_p|xtivreg|xtline|xtline_ex|xtlogit|xtlogit_d2|xtlogit_fe_p|xtlogit_pa_p|xtlogit_re_p|xtmixed|xtmixed_estat|xtmixed_p|xtnb_fe|xtnb_lf|xtnbreg|xtnbreg_pa_p|xtnbreg_refe_p|xtpcse|xtpcse_p|xtpois|xtpoisson|xtpoisson_d2|xtpoisson_pa_p|xtpoisson_refe_p|xtpred|xtprobit|xtprobit_d2|xtprobit_re_p|xtps_fe|xtps_lf|xtps_ren|xtps_ren_8|xtrar_p|xtrc|xtrc_p|xtrchh|xtrefe_p|yx|yxview__barlike_draw|yxview_area_draw|yxview_bar_draw|yxview_dot_draw|yxview_dropline_draw|yxview_function_draw|yxview_iarrow_draw|yxview_ilabels_draw|yxview_normal_draw|yxview_pcarrow_draw|yxview_pcbarrow_draw|yxview_pccapsym_draw|yxview_pcscatter_draw|yxview_pcspike_draw|yxview_rarea_draw|yxview_rbar_draw|yxview_rbarm_draw|yxview_rcap_draw|yxview_rcapsym_draw|yxview_rconnected_draw|yxview_rline_draw|yxview_rscatter_draw|yxview_rspike_draw|yxview_spike_draw|yxview_sunflower_draw|zap_s|zinb|zinb_llf|zinb_plf|zip|zip_llf|zip_p|zip_plf|zt_ct_5|zt_hc_5|zt_hcd_5|zt_is_5|zt_iss_5|zt_sho_5|zt_smp_5|ztnb|ztnb_p|ztp|ztp_p|prtab|prchange|eststo|estout|esttab|estadd|estpost|ivregress|xtreg|xtreg_be|xtreg_fe|xtreg_ml|xtreg_pa_p|xtreg_re|xtregar|xtrere_p|xtset|xtsf_ll|xtsf_llti|xtsum|xttab|xttest0|xttobit|xttobit_p|xttrans)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.flow.stata\\\"}]},\\\"comments\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comments-double-slash\\\"},{\\\"include\\\":\\\"#comments-star\\\"},{\\\"include\\\":\\\"#comments-block\\\"},{\\\"include\\\":\\\"#comments-triple-slash\\\"}]},\\\"comments-block\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"/\\\\\\\\*\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.begin.stata\\\"}},\\\"end\\\":\\\"(\\\\\\\\*/\\\\\\\\s+\\\\\\\\*[^\\\\\\\\n]*)|(\\\\\\\\*/(?!\\\\\\\\*))\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.end.stata\\\"}},\\\"name\\\":\\\"comment.block.stata\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\*/\\\\\\\\*\\\"},{\\\"include\\\":\\\"#docblockr-comment\\\"},{\\\"include\\\":\\\"#comments-block\\\"},{\\\"include\\\":\\\"#docstring\\\"}]}]},\\\"comments-double-slash\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(^//|(?<=\\\\\\\\s)//)(?!/)\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.stata\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"comment.line.double-slash.stata\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#docblockr-comment\\\"}]}]},\\\"comments-star\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*(\\\\\\\\*)\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.stata\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"comment.line.star.stata\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#docblockr-comment\\\"},{\\\"begin\\\":\\\"///\\\",\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.line-continuation.stata\\\"},{\\\"include\\\":\\\"#comments\\\"}]}]},\\\"comments-triple-slash\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(^///|(?<=\\\\\\\\s)///)\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.stata\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"comment.line.triple-slash.stata\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#docblockr-comment\\\"}]}]},\\\"constants\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#factorvariables\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i:(\\\\\\\\d+\\\\\\\\.\\\\\\\\d*(e[-+]?\\\\\\\\d+)?))(?=[^a-zA-Z_])\\\",\\\"name\\\":\\\"constant.numeric.float.stata\\\"},{\\\"match\\\":\\\"(?<=[^0-9a-zA-Z_])(?i:(\\\\\\\\.\\\\\\\\d+(e[-+]?\\\\\\\\d+)?))\\\",\\\"name\\\":\\\"constant.numeric.float.stata\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i:(\\\\\\\\d+e[-+]?\\\\\\\\d+))\\\",\\\"name\\\":\\\"constant.numeric.float.stata\\\"},{\\\"match\\\":\\\"\\\\\\\\b(\\\\\\\\d+)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.integer.decimal.stata\\\"},{\\\"match\\\":\\\"(?<!\\\\\\\\w)(\\\\\\\\.(?![./]))(?!\\\\\\\\w)\\\",\\\"name\\\":\\\"constant.language.missing.stata\\\"},{\\\"match\\\":\\\"\\\\\\\\b_all\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.allvars.stata\\\"}]},\\\"docblockr-comment\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"invalid.illegal.name.stata\\\"}},\\\"match\\\":\\\"(?<!\\\\\\\\w)(@(error|ERROR|Error))\\\\\\\\b\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.docblockr.stata\\\"}},\\\"match\\\":\\\"(?<!\\\\\\\\w)(@\\\\\\\\w+)\\\\\\\\b\\\"}]},\\\"docstring\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"'''\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.stata\\\"}},\\\"end\\\":\\\"'''\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.stata\\\"}},\\\"name\\\":\\\"string.quoted.docstring.stata\\\"},{\\\"begin\\\":\\\"\\\\\\\"\\\\\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.stata\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\\\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.stata\\\"}},\\\"name\\\":\\\"string.quoted.docstring.stata\\\"}]},\\\"factorvariables\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b([ico])\\\\\\\\.(?=[\\\\\\\\w&&[^0-9]]|\\\\\\\\([\\\\\\\\w&&[^0-9]])\\\",\\\"name\\\":\\\"constant.language.factorvars.stata\\\"},{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"constant.language.factorvars.stata\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#constants\\\"}]}},\\\"match\\\":\\\"\\\\\\\\b(i?b)((\\\\\\\\d+)|n)\\\\\\\\.(?=[\\\\\\\\w&&[^0-9]]|\\\\\\\\([\\\\\\\\w&&[^0-9]])\\\"},{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"constant.language.factorvars.stata\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.parentheses.stata\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#operators\\\"}]},\\\"4\\\":{\\\"name\\\":\\\"keyword.operator.parentheses.stata\\\"}},\\\"match\\\":\\\"\\\\\\\\b(i?b)(\\\\\\\\()(#\\\\\\\\d+|first|last|freq)(\\\\\\\\))\\\\\\\\.(?=[\\\\\\\\w&&[^0-9]]|\\\\\\\\([\\\\\\\\w&&[^0-9]])\\\"},{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"constant.language.factorvars.stata\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#constants\\\"}]}},\\\"match\\\":\\\"\\\\\\\\b(i?o?)(\\\\\\\\d+)\\\\\\\\.(?=[\\\\\\\\w&&[^0-9]]|\\\\\\\\([\\\\\\\\w&&[^0-9]])\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.language.factorvars.stata\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.parentheses.stata\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},\\\"4\\\":{\\\"name\\\":\\\"keyword.operator.parentheses.stata\\\"},\\\"5\\\":{\\\"name\\\":\\\"constant.language.factorvars.stata\\\"}},\\\"match\\\":\\\"\\\\\\\\b(i?o?)(\\\\\\\\()(.*?)(\\\\\\\\))(\\\\\\\\.)(?=[\\\\\\\\w&&[^0-9]]|\\\\\\\\([\\\\\\\\w&&[^0-9]])\\\"}]},\\\"functions\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\b((abbrev|abs|acos|acosh|asin|asinh|atan|atan2|atanh|autocode|betaden|binomial|binomialp|binomialtail|binormalbofd|byteorder|c|cauchy|cauchyden|cauchytail|Cdhms|ceil|char|chi2|chi2den|chi2tail|Chms|cholesky|chop|clip|clock|Clock|cloglog|Cmdyhms|cofC|Cofc|cofd|Cofd|coleqnumb|collatorlocale|collatorversion|colnfreeparms|colnumb|colsof|comb|cond|corr|cos|cosh|daily|date|day|det|dgammapda|dgammapdada|dgammapdadx|dgammapdx|dgammapdxdx|dhms|diag|diag0cnt|digamma|dofb|dofc|dofC|dofh|dofm|dofq|dofw|dofy|dow|doy|dunnettprob|e|el|epsdouble|epsfloat|exp|exponential|exponentialden|exponentialtail|F|Fden|fileexists|fileread|filereaderror|filewrite|float|floor|fmtwidth|Ftail|gammaden|gammap|gammaptail|get|hadamard|halfyear|halfyearly|hh|hhC|hms|hofd|hours|hypergeometric|hypergeometricp|I|ibeta|ibetatail|igaussian|igaussianden|igaussiantail|indexnot|inlist|inrange|int|inv|invbinomial|invbinomialtail|invcauchy|invcauchytail|invchi2|invchi2tail|invcloglog|invdunnettprob|invexponential|invexponentialtail|invF|invFtail|invgammap|invgammaptail|invibeta|invibetatail|invigaussian|invigaussiantail|invlaplace|invlaplacetail|invlogistic|invlogistictail|invlogit|invnbinomial|invnbinomialtail|invnchi2|invnchi2tail|invnF|invnFtail|invnibeta|invnormal|invnt|invnttail|invpoisson|invpoissontail|invsym|invt|invttail|invtukeyprob|invweibull|invweibullph|invweibullphtail|invweibulltail|irecode|issymmetric|itrim|J|laplace|laplaceden|laplacetail|length|ln|lncauchyden|lnfactorial|lngamma|lnigammaden|lnigaussianden|lniwishartden|lnlaplaceden|lnmvnormalden|lnnormal|lnnormalden|lnwishartden|log|log10|logistic|logisticden|logistictail|logit|lower|ltrim|matmissing|matrix|matuniform|max|maxbyte|maxdouble|maxfloat|maxint|maxlong|mdy|mdyhms|mi|min|minbyte|mindouble|minfloat|minint|minlong|minutes|missing|mm|mmC|mod|mofd|month|monthly|mreldif|msofhours|msofminutes|msofseconds|nbetaden|nbinomial|nbinomialp|nbinomialtail|nchi2|nchi2den|nchi2tail|nF|nFden|nFtail|nibeta|normal|normalden|npnchi2|npnF|npnt|nt|ntden|nttail|nullmat|plural|poisson|poissonp|poissontail|proper|qofd|quarter|quarterly|r|rbeta|rbinomial|rcauchy|rchi2|real|recode|regexs|reldif|replay|return|reverse|rexponential|rgamma|rhypergeometric|rigaussian|rlaplace|rlogistic|rnbinomial|rnormal|round|roweqnumb|rownfreeparms|rownumb|rowsof|rpoisson|rt|rtrim|runiform|runiformint|rweibull|rweibullph|s|scalar|seconds|sign|sin|sinh|smallestdouble|soundex|sqrt|ss|ssC|string|stritrim|strlen|strlower|strltrim|strmatch|strofreal|strpos|strproper|strreverse|strrpos|strrtrim|strtoname|strtrim|strupper|subinstr|subinword|substr|sum|sweep|t|tan|tanh|tc|tC|td|tden|th|tin|tm|tobytes|tq|trace|trigamma|trim|trunc|ttail|tukeyprob|tw|twithin|uchar|udstrlen|udsubstr|uisdigit|uisletter|upper|ustrcompare|ustrcompareex|ustrfix|ustrfrom|ustrinvalidcnt|ustrleft|ustrlen|ustrlower|ustrltrim|ustrnormalize|ustrpos|ustrregexs|ustrreverse|ustrright|ustrrpos|ustrrtrim|ustrsortkey|ustrsortkeyex|ustrtitle|ustrto|ustrtohex|ustrtoname|ustrtrim|ustrunescape|ustrupper|ustrword|ustrwordcount|usubinstr|usubstr|vec|vecdiag|week|weekly|weibull|weibullden|weibullph|weibullphden|weibullphtail|weibulltail|wofd|word|wordbreaklocale|wordcount|year|yearly|yh|ym|yofd|yq|yw)|([\\\\\\\\w&&[^0-9]]\\\\\\\\w{0,31}))(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"support.function.builtin.stata\\\"},\\\"3\\\":{\\\"name\\\":\\\"support.function.custom.stata\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.begin.stata\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.end.stata\\\"}},\\\"patterns\\\":[{\\\"match\\\":\\\"[\\\\\\\\w&&[^0-9]]\\\\\\\\w{0,31}\\\",\\\"name\\\":\\\"variable.parameter.function.stata\\\"},{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.parentheses.stata\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.parentheses.stata\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#ascii-regex-functions\\\"},{\\\"include\\\":\\\"#unicode-regex-functions\\\"},{\\\"include\\\":\\\"#functions\\\"},{\\\"include\\\":\\\"#subscripts\\\"},{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"include\\\":\\\"#string-compound\\\"},{\\\"include\\\":\\\"#string-regular\\\"},{\\\"include\\\":\\\"#builtin_variables\\\"},{\\\"include\\\":\\\"#macro-commands\\\"},{\\\"include\\\":\\\"#braces-without-error\\\"},{\\\"match\\\":\\\"[\\\\\\\\w&&[^0-9]]\\\\\\\\w{0,31}\\\",\\\"name\\\":\\\"variable.parameter.function.stata\\\"}]},{\\\"include\\\":\\\"#ascii-regex-functions\\\"},{\\\"include\\\":\\\"#unicode-regex-functions\\\"},{\\\"include\\\":\\\"#functions\\\"},{\\\"include\\\":\\\"#subscripts\\\"},{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"include\\\":\\\"#string-compound\\\"},{\\\"include\\\":\\\"#string-regular\\\"},{\\\"include\\\":\\\"#builtin_variables\\\"},{\\\"include\\\":\\\"#macro-commands\\\"},{\\\"include\\\":\\\"#braces-without-error\\\"}]}]},\\\"macro-commands\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\b(loc(a(?:l|))?)\\\\\\\\s+([\\\\\\\\w'`$(){}]+)\\\\\\\\s*(?=[:=])\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.macro.stata\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#macro-local-identifiers\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"}]}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"=\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.arithmetic.stata\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\n)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\":\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.arithmetic.stata\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\n)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#macro-extended-functions\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(gl(o(?:bal|ba|b|))?)\\\\\\\\s+(?=[\\\\\\\\w`$])\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.macro.stata\\\"}},\\\"end\\\":\\\"(})|(?=[\\\\\\\"\\\\\\\\s\\\\\\\\n/,=])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#reserved-names\\\"},{\\\"match\\\":\\\"[\\\\\\\\w&&[^0-9_]]\\\\\\\\w{0,31}\\\",\\\"name\\\":\\\"entity.name.type.class.stata\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\b(loc(a(?:l|))?)\\\\\\\\s+(\\\\\\\\+\\\\\\\\+|--)?(?=[\\\\\\\\w`$])\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.macro.stata\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.operator.arithmetic.stata\\\"}},\\\"end\\\":\\\"(?=[\\\\\\\"\\\\\\\\s\\\\\\\\n/,=])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#macro-local-identifiers\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\b(temp(?:var|name|file))\\\\\\\\s*(?=\\\\\\\\s)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.macro.stata\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"///\\\",\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.block.stata\\\"},{\\\"include\\\":\\\"#macro-local-identifiers\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\b(ma(c(?:ro|r|))?)\\\\\\\\s+(drop|l(i(?:st|s|))?)\\\\\\\\s*(?=\\\\\\\\s)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.macro.stata\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"///\\\",\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.block.stata\\\"},{\\\"match\\\":\\\"\\\\\\\\*\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.stata\\\"},{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"match\\\":\\\"\\\\\\\\w{1,31}\\\",\\\"name\\\":\\\"entity.name.type.class.stata\\\"}]}]},\\\"macro-extended-functions\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(properties)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.macro.extendedfcn.stata\\\"},{\\\"match\\\":\\\"\\\\\\\\b(t(y(?:pe|p|))?|f(o(?:rmat|rma|rm|r|))?|val(u(?:e|))?\\\\\\\\s+l(a(?:ble|bl|b|))?|var(i(?:able|abl|ab|a|))?\\\\\\\\s+l(a(?:bel|be|b|))?|data\\\\\\\\s+l(a(?:ble|bl|b|))?|sort(e(?:dby|db|d|))?|lab(e(?:l|))?|maxlength|constraint|char)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.macro.extendedfcn.stata\\\"},{\\\"match\\\":\\\"\\\\\\\\b(permname)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.macro.extendedfcn.stata\\\"},{\\\"match\\\":\\\"\\\\\\\\b(adosubdir|dir|files?|dirs?|other|sysdir)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.macro.extendedfcn.stata\\\"},{\\\"match\\\":\\\"\\\\\\\\b(env(i(?:ronment|ronmen|ronme|ronm|ron|ro|r|))?)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.macro.extendedfcn.stata\\\"},{\\\"match\\\":\\\"\\\\\\\\b(all\\\\\\\\s+(globals|scalars|matrices)|((numeric|string)\\\\\\\\s+scalars))\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.macro.extendedfcn.stata\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.macro.extendedfcn.stata\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.macro.extendedfcn.stata\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.type.class.stata\\\"}},\\\"match\\\":\\\"\\\\\\\\b(list)\\\\\\\\s+(uniq|dups|sort|clean|retok(e(?:nize|niz|ni|n|))?|sizeof)\\\\\\\\s+(\\\\\\\\w{1,32})\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.macro.extendedfcn.stata\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.type.class.stata\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.operator.list.stata\\\"},\\\"4\\\":{\\\"name\\\":\\\"entity.name.type.class.stata\\\"}},\\\"match\\\":\\\"\\\\\\\\b(list)\\\\\\\\s+(\\\\\\\\w{1,32})\\\\\\\\s+([|\\\\\\\\&-]|===|==|in)\\\\\\\\s+(\\\\\\\\w{1,32})\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.macro.extendedfcn.stata\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.stata\\\"},\\\"3\\\":{\\\"name\\\":\\\"string.quoted.double.stata\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.stata\\\"},\\\"5\\\":{\\\"name\\\":\\\"keyword.macro.extendedfcn.stata\\\"},\\\"6\\\":{\\\"name\\\":\\\"entity.name.type.class.stata\\\"}},\\\"match\\\":\\\"\\\\\\\\b(list\\\\\\\\s+posof)\\\\\\\\s+(\\\\\\\")(\\\\\\\\w+)(\\\\\\\")\\\\\\\\s+(in)\\\\\\\\s+(\\\\\\\\w{1,32})\\\"},{\\\"match\\\":\\\"\\\\\\\\b(rown(a(?:mes|me|m|))?|coln(a(?:mes|me|m|))?|rowf(u(?:llnames|llname|llnam|llna|lln|ll|l|))?|colf(u(?:llnames|llname|llnam|llna|lln|ll|l|))?|roweq?|coleq?|rownumb|colnumb|roweqnumb|coleqnumb|rownfreeparms|colnfreeparms|rownlfs|colnlfs|rowsof|colsof|rowvarlist|colvarlist|rowlfnames|collfnames)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.macro.extendedfcn.stata\\\"},{\\\"match\\\":\\\"\\\\\\\\b(tsnorm)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.macro.extendedfcn.stata\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.macro.extendedfcn.stata\\\"},\\\"7\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"}]}},\\\"match\\\":\\\"\\\\\\\\b((copy|(u(?:d|))?strlen)\\\\\\\\s+(loc(a(?:l|))?|gl(o(?:bal|ba|b|))?))\\\\\\\\s+([^']+)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.macro.extendedfcn.stata\\\"}},\\\"match\\\":\\\"\\\\\\\\b(word\\\\\\\\s+count)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.macro.extendedfcn.stata\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#constants\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"keyword.macro.extendedfcn.stata\\\"}},\\\"match\\\":\\\"(word|piece)\\\\\\\\s+([\\\\\\\\s`'\\\\\\\\w]+)\\\\\\\\s+(of)\\\"},{\\\"begin\\\":\\\"\\\\\\\\b(subinstr\\\\\\\\s+(loc(a(?:l|))?|gl(o(?:bal|ba|b|))?))\\\\\\\\s+(\\\\\\\\w{1,32})\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.macro.extendedfcn.stata\\\"},\\\"5\\\":{\\\"name\\\":\\\"entity.name.type.class.stata\\\"}},\\\"end\\\":\\\"(?=//|\\\\\\\\n)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"include\\\":\\\"#string-compound\\\"},{\\\"include\\\":\\\"#string-regular\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.builtin.stata\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.begin.stata\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.macro.extendedfcn.stata\\\"},\\\"4\\\":{\\\"name\\\":\\\"entity.name.type.class.stata\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.end.stata\\\"}},\\\"match\\\":\\\"(c(?:ount|oun|ou|o|))(\\\\\\\\()(local|loca|loc|global|globa|glob|glo|gl)\\\\\\\\s+(\\\\\\\\w{1,32})(\\\\\\\\))\\\"}]},{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"include\\\":\\\"$self\\\"}]},\\\"macro-global\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(\\\\\\\\$)(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.stata\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.stata\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"include\\\":\\\"#comments-block\\\"},{\\\"begin\\\":\\\"\\\\\\\\W\\\",\\\"end\\\":\\\"\\\\\\\\n|(?=})\\\",\\\"name\\\":\\\"comment.line.stata\\\"},{\\\"match\\\":\\\"\\\\\\\\w{1,32}\\\",\\\"name\\\":\\\"entity.name.type.class.stata\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\$\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.stata\\\"}},\\\"end\\\":\\\"(?!\\\\\\\\w)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.stata\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"match\\\":\\\"[\\\\\\\\w&&[^0-9_]]\\\\\\\\w{0,31}|_\\\\\\\\w{1,31}\\\",\\\"name\\\":\\\"entity.name.type.class.stata\\\"}]}]},\\\"macro-global-escaped\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(\\\\\\\\\\\\\\\\\\\\\\\\$)(\\\\\\\\\\\\\\\\\\\\\\\\{)?\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.stata\\\"}},\\\"end\\\":\\\"(\\\\\\\\\\\\\\\\})|(?=[\\\\\\\"\\\\\\\\s\\\\\\\\n/,])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.stata\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"match\\\":\\\"[\\\\\\\\w&&[^0-9_]]\\\\\\\\w{0,31}|_\\\\\\\\w{1,31}\\\",\\\"name\\\":\\\"entity.name.type.class.stata\\\"}]}]},\\\"macro-local\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(`)(=)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.stata\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.comparison.stata\\\"}},\\\"end\\\":\\\"'\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.stata\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"(`)(:)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.stata\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.comparison.stata\\\"}},\\\"contentName\\\":\\\"meta.macro-extended-function.stata\\\",\\\"end\\\":\\\"'\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.stata\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-extended-functions\\\"},{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#string-compound\\\"},{\\\"include\\\":\\\"#string-regular\\\"}]},{\\\"begin\\\":\\\"(`)(macval)(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.stata\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.function.builtin.stata\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.begin.stata\\\"}},\\\"contentName\\\":\\\"meta.macro-extended-function.stata\\\",\\\"end\\\":\\\"(\\\\\\\\))(')\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.begin.stata\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.stata\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"match\\\":\\\"\\\\\\\\w{1,31}\\\",\\\"name\\\":\\\"entity.name.type.class.stata\\\"}]},{\\\"begin\\\":\\\"`(?!\\\\\\\")\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.stata\\\"}},\\\"end\\\":\\\"'\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.stata\\\"}},\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\+\\\\\\\\+|--\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.stata\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"include\\\":\\\"#comments-block\\\"},{\\\"begin\\\":\\\"\\\\\\\\W\\\",\\\"end\\\":\\\"\\\\\\\\n|(?=')\\\",\\\"name\\\":\\\"comment.line.stata\\\"},{\\\"match\\\":\\\"\\\\\\\\w{1,31}\\\",\\\"name\\\":\\\"entity.name.type.class.stata\\\"}]}]},\\\"macro-local-escaped\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\\\\\\\\\`(?!\\\\\\\")\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.stata\\\"}},\\\"end\\\":\\\"\\\\\\\\\\\\\\\\'|'\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.stata\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"match\\\":\\\"\\\\\\\\w{1,31}\\\",\\\"name\\\":\\\"entity.name.type.class.stata\\\"}]}]},\\\"macro-local-identifiers\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"[^\\\\\\\\w'`$()\\\\\\\\s]\\\",\\\"name\\\":\\\"invalid.illegal.name.stata\\\"},{\\\"match\\\":\\\"\\\\\\\\w{32,}\\\",\\\"name\\\":\\\"invalid.illegal.name.stata\\\"},{\\\"match\\\":\\\"\\\\\\\\w{1,31}\\\",\\\"name\\\":\\\"entity.name.type.class.stata\\\"}]},\\\"operators\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\+\\\\\\\\+|--|[+\\\\\\\\-*^]\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.stata\\\"},{\\\"match\\\":\\\"(?<![[\\\\\\\\w.]&&[^0-9]])/(?![[\\\\\\\\w.]&&[^0-9]]|$)\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.stata\\\"},{\\\"match\\\":\\\"(?<![[\\\\\\\\w.]&&[^0-9]])\\\\\\\\\\\\\\\\(?![[\\\\\\\\w.]&&[^0-9]]|$)\\\",\\\"name\\\":\\\"keyword.operator.matrix.addrow.stata\\\"},{\\\"match\\\":\\\"\\\\\\\\|\\\\\\\\|\\\",\\\"name\\\":\\\"keyword.operator.graphcombine.stata\\\"},{\\\"match\\\":\\\"[\\\\\\\\&|]\\\",\\\"name\\\":\\\"keyword.operator.logical.stata\\\"},{\\\"match\\\":\\\"(?:<=|>=|:=|==|!=|~=|[<>=]|!!|!)\\\",\\\"name\\\":\\\"keyword.operator.comparison.stata\\\"},{\\\"match\\\":\\\"[()]\\\",\\\"name\\\":\\\"keyword.operator.parentheses.stata\\\"},{\\\"match\\\":\\\"(#(?:#|))\\\",\\\"name\\\":\\\"keyword.operator.factor-variables.stata\\\"},{\\\"match\\\":\\\"%\\\",\\\"name\\\":\\\"keyword.operator.format.stata\\\"},{\\\"match\\\":\\\":\\\",\\\"name\\\":\\\"punctuation.separator.key-value\\\"},{\\\"match\\\":\\\"\\\\\\\\[\\\",\\\"name\\\":\\\"punctuation.definition.parameters.begin.stata\\\"},{\\\"match\\\":\\\"]\\\",\\\"name\\\":\\\"punctuation.definition.parameters.end.stata\\\"},{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.definition.variable.begin.stata\\\"},{\\\"match\\\":\\\";\\\",\\\"name\\\":\\\"keyword.operator.delimiter.stata\\\"}]},\\\"reserved-names\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(_all|_b|byte|_coef|_cons|double|float|if|in|int|long|_n|_N|_pi|_pred|_rc|_skip|str[0-9]+|strL|using|with)\\\\\\\\b\\\",\\\"name\\\":\\\"invalid.illegal.name.stata\\\"},{\\\"match\\\":\\\"[^\\\\\\\\w'`$()\\\\\\\\s]\\\",\\\"name\\\":\\\"invalid.illegal.name.stata\\\"},{\\\"match\\\":\\\"[0-9]\\\\\\\\w{31,}\\\",\\\"name\\\":\\\"invalid.illegal.name.stata\\\"},{\\\"match\\\":\\\"\\\\\\\\w{33,}\\\",\\\"name\\\":\\\"invalid.illegal.name.stata\\\"}]},\\\"string-compound\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"`\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.stata\\\"}},\\\"end\\\":\\\"\\\\\\\"'|(?=\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.stata\\\"}},\\\"name\\\":\\\"string.quoted.double.compound.stata\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\"\\\",\\\"name\\\":\\\"string.quoted.double.compound.stata\\\"},{\\\"match\\\":\\\"```(?=[^']*\\\\\\\")\\\",\\\"name\\\":\\\"meta.markdown.code.block.stata\\\"},{\\\"include\\\":\\\"#string-regular\\\"},{\\\"include\\\":\\\"#string-compound\\\"},{\\\"include\\\":\\\"#macro-local-escaped\\\"},{\\\"include\\\":\\\"#macro-global-escaped\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"}]}]},\\\"string-regular\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<!`)\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.stata\\\"}},\\\"end\\\":\\\"(\\\\\\\")(')?|(?=\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.stata\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.punctuation.stata\\\"}},\\\"name\\\":\\\"string.quoted.double.stata\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"```(?=[^']*\\\\\\\")\\\",\\\"name\\\":\\\"meta.markdown.code.block.stata\\\"},{\\\"include\\\":\\\"#macro-local-escaped\\\"},{\\\"include\\\":\\\"#macro-global-escaped\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"}]}]},\\\"subscripts\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=[\\\\\\\\w'])(\\\\\\\\[)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.begin.stata\\\"}},\\\"end\\\":\\\"(])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.end.stata\\\"}},\\\"name\\\":\\\"meta.subscripts.stata\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"include\\\":\\\"#builtin_variables\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#functions\\\"}]}]},\\\"unicode-regex-character-class\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\[wWsSdD]|\\\\\\\\.\\\",\\\"name\\\":\\\"constant.character.character-class.stata\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"constant.character.escape.backslash.stata\\\"},{\\\"begin\\\":\\\"(\\\\\\\\[)(\\\\\\\\^)?\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.character-class.stata\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.negation.stata\\\"}},\\\"end\\\":\\\"(])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.character-class.stata\\\"}},\\\"name\\\":\\\"constant.other.character-class.set.stata\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#unicode-regex-character-class\\\"},{\\\"captures\\\":{\\\"2\\\":{\\\"name\\\":\\\"constant.character.escape.backslash.stata\\\"},\\\"4\\\":{\\\"name\\\":\\\"constant.character.escape.backslash.stata\\\"}},\\\"match\\\":\\\"((\\\\\\\\\\\\\\\\.)|.)-((\\\\\\\\\\\\\\\\.)|[^\\\\\\\\]])\\\",\\\"name\\\":\\\"constant.other.character-class.range.stata\\\"}]}]},\\\"unicode-regex-functions\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.builtin.stata\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.begin.stata\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#string-compound\\\"},{\\\"include\\\":\\\"#string-regular\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"include\\\":\\\"#functions\\\"},{\\\"match\\\":\\\"[\\\\\\\\w&&[^0-9]]\\\\\\\\w{0,31}\\\",\\\"name\\\":\\\"variable.parameter.function.stata\\\"},{\\\"include\\\":\\\"#comments-triple-slash\\\"}]},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.variable.begin.stata\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.stata\\\"},\\\"6\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#unicode-regex-internals\\\"}]},\\\"7\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.stata\\\"},\\\"8\\\":{\\\"name\\\":\\\"invalid.illegal.punctuation.stata\\\"},\\\"9\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#constants\\\"},{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.definition.variable.begin.stata\\\"}]},\\\"10\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.end.stata\\\"}},\\\"match\\\":\\\"\\\\\\\\b(ustrregexm)(\\\\\\\\()([^,]+)(,)\\\\\\\\s*(\\\\\\\")([^\\\\\\\"]+)(\\\\\\\"(')?)([,0-9\\\\\\\\s]*)?\\\\\\\\s*(\\\\\\\\))\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.builtin.stata\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.begin.stata\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#string-compound\\\"},{\\\"include\\\":\\\"#string-regular\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"include\\\":\\\"#functions\\\"},{\\\"match\\\":\\\"[\\\\\\\\w&&[^0-9]]\\\\\\\\w{0,31}\\\",\\\"name\\\":\\\"variable.parameter.function.stata\\\"},{\\\"include\\\":\\\"#comments-triple-slash\\\"}]},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.variable.begin.stata\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.stata\\\"},\\\"6\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#unicode-regex-internals\\\"}]},\\\"7\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.stata\\\"},\\\"8\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#constants\\\"},{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.definition.variable.begin.stata\\\"}]},\\\"9\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.end.stata\\\"}},\\\"match\\\":\\\"\\\\\\\\b(ustrregexm)(\\\\\\\\()([^,]+)(,)\\\\\\\\s*(`\\\\\\\")([^\\\\\\\"]+)(\\\\\\\"')([,0-9\\\\\\\\s]*)?\\\\\\\\s*(\\\\\\\\))\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.builtin.stata\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.begin.stata\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#string-compound\\\"},{\\\"include\\\":\\\"#string-regular\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"include\\\":\\\"#functions\\\"},{\\\"match\\\":\\\"[\\\\\\\\w&&[^0-9]]\\\\\\\\w{0,31}\\\",\\\"name\\\":\\\"variable.parameter.function.stata\\\"},{\\\"include\\\":\\\"#comments\\\"}]},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.variable.begin.stata\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.stata\\\"},\\\"6\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#unicode-regex-internals\\\"}]},\\\"7\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.stata\\\"},\\\"8\\\":{\\\"name\\\":\\\"invalid.illegal.punctuation.stata\\\"},\\\"9\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.definition.variable.begin.stata\\\"},{\\\"include\\\":\\\"#string-compound\\\"},{\\\"include\\\":\\\"#string-regular\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"include\\\":\\\"#functions\\\"},{\\\"match\\\":\\\"[\\\\\\\\w&&[^0-9]]\\\\\\\\w{0,31}\\\",\\\"name\\\":\\\"variable.parameter.function.stata\\\"},{\\\"include\\\":\\\"#comments-triple-slash\\\"},{\\\"include\\\":\\\"#constants\\\"}]},\\\"10\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.end.stata\\\"}},\\\"match\\\":\\\"\\\\\\\\b(ustrregexr[fa])(\\\\\\\\()([^,]+)(,)\\\\\\\\s*(\\\\\\\")([^\\\\\\\"]+)(\\\\\\\"(')?)\\\\\\\\s*([^)]*)(\\\\\\\\))\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.builtin.stata\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.begin.stata\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#string-compound\\\"},{\\\"include\\\":\\\"#string-regular\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"include\\\":\\\"#functions\\\"},{\\\"match\\\":\\\"[\\\\\\\\w&&[^0-9]]\\\\\\\\w{0,31}\\\",\\\"name\\\":\\\"variable.parameter.function.stata\\\"},{\\\"include\\\":\\\"#comments\\\"}]},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.variable.begin.stata\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.stata\\\"},\\\"6\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#unicode-regex-internals\\\"}]},\\\"7\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.stata\\\"},\\\"8\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.definition.variable.begin.stata\\\"},{\\\"include\\\":\\\"#string-compound\\\"},{\\\"include\\\":\\\"#string-regular\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"include\\\":\\\"#functions\\\"},{\\\"match\\\":\\\"[\\\\\\\\w&&[^0-9]]\\\\\\\\w{0,31}\\\",\\\"name\\\":\\\"variable.parameter.function.stata\\\"},{\\\"include\\\":\\\"#comments-triple-slash\\\"},{\\\"include\\\":\\\"#constants\\\"}]},\\\"9\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.end.stata\\\"}},\\\"match\\\":\\\"\\\\\\\\b(ustrregexr[fa])(\\\\\\\\()([^,]+)(,)\\\\\\\\s*(`\\\\\\\")([^\\\\\\\"]+)(\\\\\\\"')\\\\\\\\s*([^)]*)(\\\\\\\\))\\\"}]},\\\"unicode-regex-internals\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\[bBAZzG]|\\\\\\\\^\\\",\\\"name\\\":\\\"keyword.control.anchor.stata\\\"},{\\\"match\\\":\\\"\\\\\\\\$(?![[\\\\\\\\w&&[^0-9_]]\\\\\\\\w{0,31}|_])\\\",\\\"name\\\":\\\"keyword.control.anchor.stata\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\[1-9][0-9]?\\\",\\\"name\\\":\\\"keyword.other.back-reference.stata\\\"},{\\\"match\\\":\\\"[?+*][?+]?|\\\\\\\\{(\\\\\\\\d+,\\\\\\\\d+|\\\\\\\\d+,|,\\\\\\\\d+|\\\\\\\\d+)}\\\\\\\\??\\\",\\\"name\\\":\\\"keyword.operator.quantifier.stata\\\"},{\\\"match\\\":\\\"\\\\\\\\|\\\",\\\"name\\\":\\\"keyword.operator.or.stata\\\"},{\\\"begin\\\":\\\"\\\\\\\\((?!\\\\\\\\?(?:[#=!]|<=|<!))\\\",\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"name\\\":\\\"keyword.operator.group.stata\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#unicode-regex-internals\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\(\\\\\\\\?#\\\",\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"name\\\":\\\"comment.block.stata\\\"},{\\\"match\\\":\\\"(?<=^|\\\\\\\\s)#\\\\\\\\s[a-zA-Z0-9,. \\\\\\\\t?!-:[^\\\\\\\\x00-\\\\\\\\x7F]]*$\\\",\\\"name\\\":\\\"comment.line.number-sign.stata\\\"},{\\\"match\\\":\\\"\\\\\\\\(\\\\\\\\?[iLmsux]+\\\\\\\\)\\\",\\\"name\\\":\\\"keyword.other.option-toggle.stata\\\"},{\\\"begin\\\":\\\"(\\\\\\\\()((\\\\\\\\?=)|(\\\\\\\\?!)|(\\\\\\\\?<=)|(\\\\\\\\?<!))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.group.stata\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.group.assertion.stata\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.assertion.look-ahead.stata\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.assertion.negative-look-ahead.stata\\\"},\\\"5\\\":{\\\"name\\\":\\\"keyword.assertion.look-behind.stata\\\"},\\\"6\\\":{\\\"name\\\":\\\"keyword.assertion.negative-look-behind.stata\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.group.stata\\\"}},\\\"name\\\":\\\"meta.group.assertion.stata\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#unicode-regex-internals\\\"}]},{\\\"begin\\\":\\\"(\\\\\\\\()(\\\\\\\\?\\\\\\\\(([1-9][0-9]?|[a-zA-Z_][a-zA-Z_0-9]*)\\\\\\\\))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.group.stata\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.group.assertion.conditional.stata\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.section.back-reference.stata\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"name\\\":\\\"meta.group.assertion.conditional.stata\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#unicode-regex-internals\\\"}]},{\\\"include\\\":\\\"#unicode-regex-character-class\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"match\\\":\\\".\\\",\\\"name\\\":\\\"string.quoted.stata\\\"}]}},\\\"scopeName\\\":\\\"source.stata\\\",\\\"embeddedLangs\\\":[\\\"sql\\\"]}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\n..._sql_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/stata.mjs\n"));

/***/ })

}]);