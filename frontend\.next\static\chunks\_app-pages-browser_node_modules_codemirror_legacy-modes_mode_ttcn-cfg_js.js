"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_codemirror_legacy-modes_mode_ttcn-cfg_js"],{

/***/ "(app-pages-browser)/./node_modules/@codemirror/legacy-modes/mode/ttcn-cfg.js":
/*!****************************************************************!*\
  !*** ./node_modules/@codemirror/legacy-modes/mode/ttcn-cfg.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ttcnCfg: () => (/* binding */ ttcnCfg)\n/* harmony export */ });\nfunction words(str) {\n  var obj = {}, words = str.split(\" \");\n  for (var i = 0; i < words.length; ++i)\n    obj[words[i]] = true;\n  return obj;\n}\n\nconst parserConfig = {\n  name: \"ttcn-cfg\",\n  keywords: words(\"Yes No LogFile FileMask ConsoleMask AppendFile\" +\n                  \" TimeStampFormat LogEventTypes SourceInfoFormat\" +\n                  \" LogEntityName LogSourceInfo DiskFullAction\" +\n                  \" LogFileNumber LogFileSize MatchingHints Detailed\" +\n                  \" Compact SubCategories Stack Single None Seconds\" +\n                  \" DateTime Time Stop Error Retry Delete TCPPort KillTimer\" +\n                  \" NumHCs UnixSocketsEnabled LocalAddress\"),\n  fileNCtrlMaskOptions: words(\"TTCN_EXECUTOR TTCN_ERROR TTCN_WARNING\" +\n                              \" TTCN_PORTEVENT TTCN_TIMEROP TTCN_VERDICTOP\" +\n                              \" TTCN_DEFAULTOP TTCN_TESTCASE TTCN_ACTION\" +\n                              \" TTCN_USER TTCN_FUNCTION TTCN_STATISTICS\" +\n                              \" TTCN_PARALLEL TTCN_MATCHING TTCN_DEBUG\" +\n                              \" EXECUTOR ERROR WARNING PORTEVENT TIMEROP\" +\n                              \" VERDICTOP DEFAULTOP TESTCASE ACTION USER\" +\n                              \" FUNCTION STATISTICS PARALLEL MATCHING DEBUG\" +\n                              \" LOG_ALL LOG_NOTHING ACTION_UNQUALIFIED\" +\n                              \" DEBUG_ENCDEC DEBUG_TESTPORT\" +\n                              \" DEBUG_UNQUALIFIED DEFAULTOP_ACTIVATE\" +\n                              \" DEFAULTOP_DEACTIVATE DEFAULTOP_EXIT\" +\n                              \" DEFAULTOP_UNQUALIFIED ERROR_UNQUALIFIED\" +\n                              \" EXECUTOR_COMPONENT EXECUTOR_CONFIGDATA\" +\n                              \" EXECUTOR_EXTCOMMAND EXECUTOR_LOGOPTIONS\" +\n                              \" EXECUTOR_RUNTIME EXECUTOR_UNQUALIFIED\" +\n                              \" FUNCTION_RND FUNCTION_UNQUALIFIED\" +\n                              \" MATCHING_DONE MATCHING_MCSUCCESS\" +\n                              \" MATCHING_MCUNSUCC MATCHING_MMSUCCESS\" +\n                              \" MATCHING_MMUNSUCC MATCHING_PCSUCCESS\" +\n                              \" MATCHING_PCUNSUCC MATCHING_PMSUCCESS\" +\n                              \" MATCHING_PMUNSUCC MATCHING_PROBLEM\" +\n                              \" MATCHING_TIMEOUT MATCHING_UNQUALIFIED\" +\n                              \" PARALLEL_PORTCONN PARALLEL_PORTMAP\" +\n                              \" PARALLEL_PTC PARALLEL_UNQUALIFIED\" +\n                              \" PORTEVENT_DUALRECV PORTEVENT_DUALSEND\" +\n                              \" PORTEVENT_MCRECV PORTEVENT_MCSEND\" +\n                              \" PORTEVENT_MMRECV PORTEVENT_MMSEND\" +\n                              \" PORTEVENT_MQUEUE PORTEVENT_PCIN\" +\n                              \" PORTEVENT_PCOUT PORTEVENT_PMIN\" +\n                              \" PORTEVENT_PMOUT PORTEVENT_PQUEUE\" +\n                              \" PORTEVENT_STATE PORTEVENT_UNQUALIFIED\" +\n                              \" STATISTICS_UNQUALIFIED STATISTICS_VERDICT\" +\n                              \" TESTCASE_FINISH TESTCASE_START\" +\n                              \" TESTCASE_UNQUALIFIED TIMEROP_GUARD\" +\n                              \" TIMEROP_READ TIMEROP_START TIMEROP_STOP\" +\n                              \" TIMEROP_TIMEOUT TIMEROP_UNQUALIFIED\" +\n                              \" USER_UNQUALIFIED VERDICTOP_FINAL\" +\n                              \" VERDICTOP_GETVERDICT VERDICTOP_SETVERDICT\" +\n                              \" VERDICTOP_UNQUALIFIED WARNING_UNQUALIFIED\"),\n  externalCommands: words(\"BeginControlPart EndControlPart BeginTestCase\" +\n                          \" EndTestCase\"),\n  multiLineStrings: true\n}\n\nvar keywords = parserConfig.keywords,\n    fileNCtrlMaskOptions = parserConfig.fileNCtrlMaskOptions,\n    externalCommands = parserConfig.externalCommands,\n    multiLineStrings = parserConfig.multiLineStrings,\n    indentStatements = parserConfig.indentStatements !== false;\nvar isOperatorChar = /[\\|]/;\nvar curPunc;\n\nfunction tokenBase(stream, state) {\n  var ch = stream.next();\n  if (ch == '\"' || ch == \"'\") {\n    state.tokenize = tokenString(ch);\n    return state.tokenize(stream, state);\n  }\n  if (/[:=]/.test(ch)) {\n    curPunc = ch;\n    return \"punctuation\";\n  }\n  if (ch == \"#\"){\n    stream.skipToEnd();\n    return \"comment\";\n  }\n  if (/\\d/.test(ch)) {\n    stream.eatWhile(/[\\w\\.]/);\n    return \"number\";\n  }\n  if (isOperatorChar.test(ch)) {\n    stream.eatWhile(isOperatorChar);\n    return \"operator\";\n  }\n  if (ch == \"[\"){\n    stream.eatWhile(/[\\w_\\]]/);\n    return \"number\";\n  }\n\n  stream.eatWhile(/[\\w\\$_]/);\n  var cur = stream.current();\n  if (keywords.propertyIsEnumerable(cur)) return \"keyword\";\n  if (fileNCtrlMaskOptions.propertyIsEnumerable(cur))\n    return \"atom\";\n  if (externalCommands.propertyIsEnumerable(cur)) return \"deleted\";\n\n  return \"variable\";\n}\n\nfunction tokenString(quote) {\n  return function(stream, state) {\n    var escaped = false, next, end = false;\n    while ((next = stream.next()) != null) {\n      if (next == quote && !escaped){\n        var afterNext = stream.peek();\n        //look if the character if the quote is like the B in '10100010'B\n        if (afterNext){\n          afterNext = afterNext.toLowerCase();\n          if(afterNext == \"b\" || afterNext == \"h\" || afterNext == \"o\")\n            stream.next();\n        }\n        end = true; break;\n      }\n      escaped = !escaped && next == \"\\\\\";\n    }\n    if (end || !(escaped || multiLineStrings))\n      state.tokenize = null;\n    return \"string\";\n  };\n}\n\nfunction Context(indented, column, type, align, prev) {\n  this.indented = indented;\n  this.column = column;\n  this.type = type;\n  this.align = align;\n  this.prev = prev;\n}\nfunction pushContext(state, col, type) {\n  var indent = state.indented;\n  if (state.context && state.context.type == \"statement\")\n    indent = state.context.indented;\n  return state.context = new Context(indent, col, type, null, state.context);\n}\nfunction popContext(state) {\n  var t = state.context.type;\n  if (t == \")\" || t == \"]\" || t == \"}\")\n    state.indented = state.context.indented;\n  return state.context = state.context.prev;\n}\n\n//Interface\nconst ttcnCfg = {\n  name: \"ttcn\",\n  startState: function() {\n    return {\n      tokenize: null,\n      context: new Context(0, 0, \"top\", false),\n      indented: 0,\n      startOfLine: true\n    };\n  },\n\n  token: function(stream, state) {\n    var ctx = state.context;\n    if (stream.sol()) {\n      if (ctx.align == null) ctx.align = false;\n      state.indented = stream.indentation();\n      state.startOfLine = true;\n    }\n    if (stream.eatSpace()) return null;\n    curPunc = null;\n    var style = (state.tokenize || tokenBase)(stream, state);\n    if (style == \"comment\") return style;\n    if (ctx.align == null) ctx.align = true;\n\n    if ((curPunc == \";\" || curPunc == \":\" || curPunc == \",\")\n        && ctx.type == \"statement\"){\n      popContext(state);\n    }\n    else if (curPunc == \"{\") pushContext(state, stream.column(), \"}\");\n    else if (curPunc == \"[\") pushContext(state, stream.column(), \"]\");\n    else if (curPunc == \"(\") pushContext(state, stream.column(), \")\");\n    else if (curPunc == \"}\") {\n      while (ctx.type == \"statement\") ctx = popContext(state);\n      if (ctx.type == \"}\") ctx = popContext(state);\n      while (ctx.type == \"statement\") ctx = popContext(state);\n    }\n    else if (curPunc == ctx.type) popContext(state);\n    else if (indentStatements && (((ctx.type == \"}\" || ctx.type == \"top\")\n                                   && curPunc != ';') || (ctx.type == \"statement\"\n                                                          && curPunc == \"newstatement\")))\n      pushContext(state, stream.column(), \"statement\");\n    state.startOfLine = false;\n    return style;\n  },\n\n  languageData: {\n    indentOnInput: /^\\s*[{}]$/,\n    commentTokens: {line: \"#\"}\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@codemirror/legacy-modes/mode/ttcn-cfg.js\n"));

/***/ })

}]);