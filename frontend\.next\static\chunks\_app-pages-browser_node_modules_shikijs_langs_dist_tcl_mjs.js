"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_tcl_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/tcl.mjs":
/*!**************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/tcl.mjs ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Tcl\\\",\\\"fileTypes\\\":[\\\"tcl\\\"],\\\"foldingStartMarker\\\":\\\"\\\\\\\\{\\\\\\\\s*$\\\",\\\"foldingStopMarker\\\":\\\"^\\\\\\\\s*}\\\",\\\"name\\\":\\\"tcl\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=^|;)\\\\\\\\s*((#))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"comment.line.number-sign.tcl\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.comment.tcl\\\"}},\\\"contentName\\\":\\\"comment.line.number-sign.tcl\\\",\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(\\\\\\\\\\\\\\\\[\\\\\\\\\\\\\\\\\\\\\\\\n])\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.tcl\\\"}},\\\"match\\\":\\\"(?<=^|[\\\\\\\\[{;])\\\\\\\\s*(if|while|for|catch|default|return|break|continue|switch|exit|foreach|try|throw)\\\\\\\\b\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.tcl\\\"}},\\\"match\\\":\\\"(?<=^|})\\\\\\\\s*(then|elseif|else)\\\\\\\\b\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.tcl\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.tcl\\\"}},\\\"match\\\":\\\"(?<=^|\\\\\\\\{)\\\\\\\\s*(proc)\\\\\\\\s+(\\\\\\\\S+)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.tcl\\\"}},\\\"match\\\":\\\"(?<=^|[\\\\\\\\[{;])\\\\\\\\s*(after|append|array|auto_execok|auto_import|auto_load|auto_mkindex|auto_mkindex_old|auto_qualify|auto_reset|bgerror|binary|cd|clock|close|concat|dde|encoding|eof|error|eval|exec|expr|fblocked|fconfigure|fcopy|file|fileevent|filename|flush|format|gets|glob|global|history|http|incr|info|interp|join|lappend|library|lindex|linsert|list|llength|load|lrange|lreplace|lsearch|lset|lsort|memory|msgcat|namespace|open|package|parray|pid|pkg::create|pkg_mkIndex|proc|puts|pwd|re_syntax|read|registry|rename|resource|scan|seek|set|socket|SafeBase|source|split|string|subst|Tcl|tcl_endOfWord|tcl_findLibrary|tcl_startOfNextWord|tcl_startOfPreviousWord|tcl_wordBreakAfter|tcl_wordBreakBefore|tcltest|tclvars|tell|time|trace|unknown|unset|update|uplevel|upvar|variable|vwait)\\\\\\\\b\\\"},{\\\"begin\\\":\\\"(?<=^|[\\\\\\\\[{;])\\\\\\\\s*(reg(?:exp|sub))\\\\\\\\b\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.tcl\\\"}},\\\"end\\\":\\\"[\\\\\\\\n;\\\\\\\\]]\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\(?:.|\\\\\\\\n)\\\",\\\"name\\\":\\\"constant.character.escape.tcl\\\"},{\\\"match\\\":\\\"-\\\\\\\\w+\\\\\\\\s*\\\"},{\\\"applyEndPatternLast\\\":1,\\\"begin\\\":\\\"--\\\\\\\\s*\\\",\\\"end\\\":\\\"\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#regexp\\\"}]},{\\\"include\\\":\\\"#regexp\\\"}]},{\\\"include\\\":\\\"#escape\\\"},{\\\"include\\\":\\\"#variable\\\"},{\\\"include\\\":\\\"#operator\\\"},{\\\"include\\\":\\\"#numeric\\\"},{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.tcl\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.tcl\\\"}},\\\"name\\\":\\\"string.quoted.double.tcl\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#escape\\\"},{\\\"include\\\":\\\"#variable\\\"},{\\\"include\\\":\\\"#embedded\\\"}]}],\\\"repository\\\":{\\\"bare-string\\\":{\\\"begin\\\":\\\"(?:^|(?<=\\\\\\\\s))\\\\\\\"\\\",\\\"end\\\":\\\"\\\\\\\"([^\\\\\\\\s\\\\\\\\]]*)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"invalid.illegal.tcl\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#escape\\\"},{\\\"include\\\":\\\"#variable\\\"}]},\\\"braces\\\":{\\\"begin\\\":\\\"(?:^|(?<=\\\\\\\\s))\\\\\\\\{\\\",\\\"end\\\":\\\"}([^\\\\\\\\s\\\\\\\\]]*)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"invalid.illegal.tcl\\\"}},\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\[{}\\\\\\\\n]\\\",\\\"name\\\":\\\"constant.character.escape.tcl\\\"},{\\\"include\\\":\\\"#inner-braces\\\"}]},\\\"embedded\\\":{\\\"begin\\\":\\\"\\\\\\\\[\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.embedded.begin.tcl\\\"}},\\\"end\\\":\\\"]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.embedded.end.tcl\\\"}},\\\"name\\\":\\\"source.tcl.embedded\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.tcl\\\"}]},\\\"escape\\\":{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\(\\\\\\\\d{1,3}|x\\\\\\\\h+|u\\\\\\\\h{1,4}|.|\\\\\\\\n)\\\",\\\"name\\\":\\\"constant.character.escape.tcl\\\"},\\\"inner-braces\\\":{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"end\\\":\\\"}\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\[{}\\\\\\\\n]\\\",\\\"name\\\":\\\"constant.character.escape.tcl\\\"},{\\\"include\\\":\\\"#inner-braces\\\"}]},\\\"numeric\\\":{\\\"match\\\":\\\"(?<![a-zA-Z])([+-]?([0-9]*\\\\\\\\.)?[0-9]+f?)(?![.a-zA-Z])\\\",\\\"name\\\":\\\"constant.numeric.tcl\\\"},\\\"operator\\\":{\\\"match\\\":\\\"(?<=[ \\\\\\\\d])([-+~]|&{1,2}|\\\\\\\\|{1,2}|<{1,2}|>{1,2}|\\\\\\\\*{1,2}|[!%/]|<=|>=|={1,2}|!=|\\\\\\\\^)(?=[ \\\\\\\\d])\\\",\\\"name\\\":\\\"keyword.operator.tcl\\\"},\\\"regexp\\\":{\\\"begin\\\":\\\"(?=\\\\\\\\S)(?![\\\\\\\\n;\\\\\\\\]])\\\",\\\"end\\\":\\\"(?=[\\\\\\\\n;\\\\\\\\]])\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?=[^ \\\\\\\\t\\\\\\\\n;])\\\",\\\"end\\\":\\\"(?=[ \\\\\\\\t\\\\\\\\n;])\\\",\\\"name\\\":\\\"string.regexp.tcl\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#braces\\\"},{\\\"include\\\":\\\"#bare-string\\\"},{\\\"include\\\":\\\"#escape\\\"},{\\\"include\\\":\\\"#variable\\\"}]},{\\\"begin\\\":\\\"[ \\\\\\\\t]\\\",\\\"end\\\":\\\"(?=[\\\\\\\\n;\\\\\\\\]])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#variable\\\"},{\\\"include\\\":\\\"#embedded\\\"},{\\\"include\\\":\\\"#escape\\\"},{\\\"include\\\":\\\"#braces\\\"},{\\\"include\\\":\\\"#string\\\"}]}]},\\\"string\\\":{\\\"applyEndPatternLast\\\":1,\\\"begin\\\":\\\"(?:^|(?<=\\\\\\\\s))(?=\\\\\\\")\\\",\\\"end\\\":\\\"\\\",\\\"name\\\":\\\"string.quoted.double.tcl\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#bare-string\\\"}]},\\\"variable\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.variable.tcl\\\"}},\\\"match\\\":\\\"(\\\\\\\\$)((?:[a-zA-Z0-9_]|::)+(\\\\\\\\([^)]+\\\\\\\\))?|\\\\\\\\{[^}]*})\\\",\\\"name\\\":\\\"support.function.tcl\\\"}},\\\"scopeName\\\":\\\"source.tcl\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/tcl.mjs\n"));

/***/ })

}]);