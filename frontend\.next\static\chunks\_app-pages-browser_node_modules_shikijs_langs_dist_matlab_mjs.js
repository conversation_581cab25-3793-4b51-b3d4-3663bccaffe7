"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_matlab_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/matlab.mjs":
/*!*****************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/matlab.mjs ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"MATLAB\\\",\\\"fileTypes\\\":[\\\"m\\\"],\\\"name\\\":\\\"matlab\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#all_before_command_dual\\\"},{\\\"include\\\":\\\"#command_dual\\\"},{\\\"include\\\":\\\"#all_after_command_dual\\\"}],\\\"repository\\\":{\\\"all_after_command_dual\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#string\\\"},{\\\"include\\\":\\\"#line_continuation\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#conjugate_transpose\\\"},{\\\"include\\\":\\\"#transpose\\\"},{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#variables\\\"},{\\\"include\\\":\\\"#numbers\\\"},{\\\"include\\\":\\\"#operators\\\"}]},\\\"all_before_command_dual\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#classdef\\\"},{\\\"include\\\":\\\"#function\\\"},{\\\"include\\\":\\\"#blocks\\\"},{\\\"include\\\":\\\"#control_statements\\\"},{\\\"include\\\":\\\"#global_persistent\\\"},{\\\"include\\\":\\\"#parens\\\"},{\\\"include\\\":\\\"#square_brackets\\\"},{\\\"include\\\":\\\"#indexing_curly_brackets\\\"},{\\\"include\\\":\\\"#curly_brackets\\\"}]},\\\"blocks\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\s*(?:^|[\\\\\\\\s,;])(for)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.for.matlab\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(?:^|[\\\\\\\\s,;])(end)\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.end.for.matlab\\\"}},\\\"name\\\":\\\"meta.for.matlab\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\s*(?:^|[\\\\\\\\s,;])(if)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.if.matlab\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(?:^|[\\\\\\\\s,;])(end)\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.end.if.matlab\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]}},\\\"name\\\":\\\"meta.if.matlab\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"2\\\":{\\\"name\\\":\\\"keyword.control.elseif.matlab\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]}},\\\"end\\\":\\\"^\\\",\\\"match\\\":\\\"(\\\\\\\\s*)(?:^|[\\\\\\\\s,;])(elseif)\\\\\\\\b(.*)$\\\\\\\\n?\\\",\\\"name\\\":\\\"meta.elseif.matlab\\\"},{\\\"captures\\\":{\\\"2\\\":{\\\"name\\\":\\\"keyword.control.else.matlab\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]}},\\\"end\\\":\\\"^\\\",\\\"match\\\":\\\"(\\\\\\\\s*)(?:^|[\\\\\\\\s,;])(else)\\\\\\\\b(.*)?$\\\\\\\\n?\\\",\\\"name\\\":\\\"meta.else.matlab\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\s*(?:^|[\\\\\\\\s,;])(parfor)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.for.matlab\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(?:^|[\\\\\\\\s,;])(end)\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.end.for.matlab\\\"}},\\\"name\\\":\\\"meta.parfor.matlab\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(?!$)\\\",\\\"end\\\":\\\"$\\\\\\\\n?\\\",\\\"name\\\":\\\"meta.parfor-quantity.matlab\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\s*(?:^|[\\\\\\\\s,;])(spmd)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.spmd.matlab\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(?:^|[\\\\\\\\s,;])(end)\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.end.spmd.matlab\\\"}},\\\"name\\\":\\\"meta.spmd.matlab\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(?!$)\\\",\\\"end\\\":\\\"$\\\\\\\\n?\\\",\\\"name\\\":\\\"meta.spmd-statement.matlab\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\s*(?:^|[\\\\\\\\s,;])(switch)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.switch.matlab\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(?:^|[\\\\\\\\s,;])(end)\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.end.switch.matlab\\\"}},\\\"name\\\":\\\"meta.switch.matlab\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"2\\\":{\\\"name\\\":\\\"keyword.control.case.matlab\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]}},\\\"end\\\":\\\"^\\\",\\\"match\\\":\\\"(\\\\\\\\s*)(?:^|[\\\\\\\\s,;])(case)\\\\\\\\b(.*)$\\\\\\\\n?\\\",\\\"name\\\":\\\"meta.case.matlab\\\"},{\\\"captures\\\":{\\\"2\\\":{\\\"name\\\":\\\"keyword.control.otherwise.matlab\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]}},\\\"end\\\":\\\"^\\\",\\\"match\\\":\\\"(\\\\\\\\s*)(?:^|[\\\\\\\\s,;])(otherwise)\\\\\\\\b(.*)?$\\\\\\\\n?\\\",\\\"name\\\":\\\"meta.otherwise.matlab\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\s*(?:^|[\\\\\\\\s,;])(try)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.try.matlab\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(?:^|[\\\\\\\\s,;])(end)\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.end.try.matlab\\\"}},\\\"name\\\":\\\"meta.try.matlab\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"2\\\":{\\\"name\\\":\\\"keyword.control.catch.matlab\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]}},\\\"end\\\":\\\"^\\\",\\\"match\\\":\\\"(\\\\\\\\s*)(?:^|[\\\\\\\\s,;])(catch)\\\\\\\\b(.*)?$\\\\\\\\n?\\\",\\\"name\\\":\\\"meta.catch.matlab\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\s*(?:^|[\\\\\\\\s,;])(while)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.while.matlab\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(?:^|[\\\\\\\\s,;])(end)\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.end.while.matlab\\\"}},\\\"name\\\":\\\"meta.while.matlab\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]}]},\\\"braced_validator_list\\\":{\\\"begin\\\":\\\"\\\\\\\\s*(\\\\\\\\{)\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.matlab\\\"}},\\\"end\\\":\\\"(})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.matlab\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#braced_validator_list\\\"},{\\\"include\\\":\\\"#validator_strings\\\"},{\\\"include\\\":\\\"#line_continuation\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.matlab\\\"}},\\\"match\\\":\\\"([^{}'\\\\\\\".]+)\\\"},{\\\"match\\\":\\\"\\\\\\\\.\\\",\\\"name\\\":\\\"storage.type.matlab\\\"}]},\\\"classdef\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(^\\\\\\\\s*)(classdef)\\\\\\\\b\\\\\\\\s*(.*)\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"storage.type.class.matlab\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"[a-zA-Z][a-zA-Z0-9_]*\\\",\\\"name\\\":\\\"variable.parameter.class.matlab\\\"},{\\\"begin\\\":\\\"=\\\\\\\\s*\\\",\\\"end\\\":\\\",|(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"true|false\\\",\\\"name\\\":\\\"constant.language.boolean.matlab\\\"},{\\\"include\\\":\\\"#string\\\"}]}]},\\\"2\\\":{\\\"name\\\":\\\"meta.class-declaration.matlab\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.section.class.matlab\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.operator.other.matlab\\\"},\\\"5\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"[a-zA-Z][a-zA-Z0-9_]*(\\\\\\\\.[a-zA-Z][a-zA-Z0-9_]*)*\\\",\\\"name\\\":\\\"entity.other.inherited-class.matlab\\\"},{\\\"match\\\":\\\"&\\\",\\\"name\\\":\\\"keyword.operator.other.matlab\\\"}]},\\\"6\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]}},\\\"match\\\":\\\"(\\\\\\\\([^)]*\\\\\\\\))?\\\\\\\\s*(([a-zA-Z][a-zA-Z0-9_]*)(?:\\\\\\\\s*(<)\\\\\\\\s*([^%]*))?)\\\\\\\\s*($|(?=(%|...)).*)\\\"}]}},\\\"end\\\":\\\"\\\\\\\\s*(?:^|[\\\\\\\\s,;])(end)\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.end.class.matlab\\\"}},\\\"name\\\":\\\"meta.class.matlab\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(^\\\\\\\\s*)(properties)\\\\\\\\b([^%]*)\\\\\\\\s*(\\\\\\\\([^)]*\\\\\\\\))?\\\\\\\\s*($|(?=%))\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"keyword.control.properties.matlab\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"[a-zA-Z][a-zA-Z0-9_]*\\\",\\\"name\\\":\\\"variable.parameter.properties.matlab\\\"},{\\\"begin\\\":\\\"=\\\\\\\\s*\\\",\\\"end\\\":\\\",|(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"true|false\\\",\\\"name\\\":\\\"constant.language.boolean.matlab\\\"},{\\\"match\\\":\\\"p(?:ublic|rotected|rivate)\\\",\\\"name\\\":\\\"constant.language.access.matlab\\\"}]}]}},\\\"end\\\":\\\"\\\\\\\\s*(?:^|[\\\\\\\\s,;])(end)\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.end.properties.matlab\\\"}},\\\"name\\\":\\\"meta.properties.matlab\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#validators\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"(^\\\\\\\\s*)(methods)\\\\\\\\b([^%]*)\\\\\\\\s*(\\\\\\\\([^)]*\\\\\\\\))?\\\\\\\\s*($|(?=%))\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"keyword.control.methods.matlab\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"[a-zA-Z][a-zA-Z0-9_]*\\\",\\\"name\\\":\\\"variable.parameter.methods.matlab\\\"},{\\\"begin\\\":\\\"=\\\\\\\\s*\\\",\\\"end\\\":\\\",|(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"true|false\\\",\\\"name\\\":\\\"constant.language.boolean.matlab\\\"},{\\\"match\\\":\\\"p(?:ublic|rotected|rivate)\\\",\\\"name\\\":\\\"constant.language.access.matlab\\\"}]}]}},\\\"end\\\":\\\"\\\\\\\\s*(?:^|[\\\\\\\\s,;])(end)\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.end.methods.matlab\\\"}},\\\"name\\\":\\\"meta.methods.matlab\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"(^\\\\\\\\s*)(events)\\\\\\\\b([^%]*)\\\\\\\\s*(\\\\\\\\([^)]*\\\\\\\\))?\\\\\\\\s*($|(?=%))\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"keyword.control.events.matlab\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"[a-zA-Z][a-zA-Z0-9_]*\\\",\\\"name\\\":\\\"variable.parameter.events.matlab\\\"},{\\\"begin\\\":\\\"=\\\\\\\\s*\\\",\\\"end\\\":\\\",|(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"true|false\\\",\\\"name\\\":\\\"constant.language.boolean.matlab\\\"},{\\\"match\\\":\\\"p(?:ublic|rotected|rivate)\\\",\\\"name\\\":\\\"constant.language.access.matlab\\\"}]}]}},\\\"end\\\":\\\"\\\\\\\\s*(?:^|[\\\\\\\\s,;])(end)\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.end.events.matlab\\\"}},\\\"name\\\":\\\"meta.events.matlab\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"(^\\\\\\\\s*)(enumeration)\\\\\\\\b([^%]*)\\\\\\\\s*($|(?=%))\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"keyword.control.enumeration.matlab\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(?:^|[\\\\\\\\s,;])(end)\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.end.enumeration.matlab\\\"}},\\\"name\\\":\\\"meta.enumeration.matlab\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"include\\\":\\\"$self\\\"}]}]},\\\"command_dual\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"string.interpolated.matlab\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.other.command.matlab\\\"},\\\"28\\\":{\\\"name\\\":\\\"comment.line.percentage.matlab\\\"}},\\\"match\\\":\\\"^\\\\\\\\s*(([b-df-hk-moq-zA-HJ-MO-Z]\\\\\\\\w*|a|an|a([A-Za-mo-z0-9_]\\\\\\\\w*|n[A-Za-rt-z0-9_]\\\\\\\\w*|ns\\\\\\\\w+)|e|ep|e([A-Za-oq-z0-9_]\\\\\\\\w*|p[A-Za-rt-z0-9_]\\\\\\\\w*|ps\\\\\\\\w+)|in|i([A-Za-mo-z0-9_]\\\\\\\\w*|n[A-Za-eg-z0-9_]\\\\\\\\w*|nf\\\\\\\\w+)|I|In|I([A-Za-mo-z0-9_]\\\\\\\\w*|n[A-Za-eg-z0-9_]\\\\\\\\w*|nf\\\\\\\\w+)|j\\\\\\\\w+|N|Na|N([A-Zb-z0-9_]\\\\\\\\w*|a[A-MO-Za-z0-9_]\\\\\\\\w*|aN\\\\\\\\w+)|n|na|nar|narg|nargi|nargo|nargou|n([A-Zb-z0-9_]\\\\\\\\w*|a([A-Za-mo-qs-z0-9_]\\\\\\\\w*|n\\\\\\\\w+|r([A-Za-fh-z0-9_]\\\\\\\\w*|g([A-Za-hj-nq-z0-9_]\\\\\\\\w*|i([A-Za-mo-z0-9_]\\\\\\\\w*|n\\\\\\\\w+)|o([A-Za-tv-z0-9_]\\\\\\\\w*|u([A-Za-su-z]\\\\\\\\w*|t\\\\\\\\w+))))))|p|p[A-Za-hj-z0-9_]\\\\\\\\w*|pi\\\\\\\\w+)\\\\\\\\s+((([^\\\\\\\\s;,%()=.{\\\\\\\\&|~<>:+\\\\\\\\-*/\\\\\\\\\\\\\\\\@^'\\\\\\\"]|(?=')|(?=\\\\\\\"))|(\\\\\\\\.\\\\\\\\^|\\\\\\\\.\\\\\\\\*|\\\\\\\\./|\\\\\\\\.\\\\\\\\\\\\\\\\|\\\\\\\\.'|\\\\\\\\.\\\\\\\\(|&&|==|\\\\\\\\|\\\\\\\\||&(?=[^\\\\\\\\&])|\\\\\\\\|(?=[^|])|~=|<=|>=|~(?!=)|<(?!=)|>(?!=)|[:+\\\\\\\\-*/\\\\\\\\\\\\\\\\@^])(\\\\\\\\S|\\\\\\\\s*(?=%)|\\\\\\\\s+$|\\\\\\\\s+([,;)}\\\\\\\\]\\\\\\\\&|<>=:*/\\\\\\\\\\\\\\\\^@]|(\\\\\\\\.(?:[^\\\\\\\\d.]|\\\\\\\\.[^.]))))|(\\\\\\\\.[^^*/\\\\\\\\\\\\\\\\'(\\\\\\\\sA-Za-z]))([^%]|'[^']*'|\\\\\\\"[^\\\\\\\"]*\\\\\\\")*|(\\\\\\\\.(?=\\\\\\\\s)|\\\\\\\\.[A-Za-z]|(?=\\\\\\\\{))([^(='\\\\\\\"%]|==|'[^']*'|\\\\\\\"[^\\\\\\\"]*\\\\\\\"|\\\\\\\\(|\\\\\\\\([^)%]*\\\\\\\\)|\\\\\\\\[|\\\\\\\\[[^\\\\\\\\]%]*]|\\\\\\\\{|\\\\\\\\{[^}%]*})*(\\\\\\\\.\\\\\\\\.\\\\\\\\.[^%]*)?((?=%)|$)))(%.*)?$\\\"},\\\"comment_block\\\":{\\\"begin\\\":\\\"(^\\\\\\\\s*)%\\\\\\\\{[^\\\\\\\\n\\\\\\\\S]*+\\\\\\\\n\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.matlab\\\"}},\\\"end\\\":\\\"^\\\\\\\\s*%}[^\\\\\\\\n\\\\\\\\S]*+(?:\\\\\\\\n|$)\\\",\\\"name\\\":\\\"comment.block.percentage.matlab\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_block\\\"},{\\\"match\\\":\\\"^[^\\\\\\\\n]*\\\\\\\\n\\\"}]},\\\"comments\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(^[ \\\\\\\\t]+)?(?=%%\\\\\\\\s)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.comment.leading.matlab\\\"}},\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"%%\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.matlab\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.line.double-percentage.matlab\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G[^\\\\\\\\S\\\\\\\\n]*(?![\\\\\\\\n\\\\\\\\s])\\\",\\\"contentName\\\":\\\"meta.cell.matlab\\\",\\\"end\\\":\\\"(?=\\\\\\\\n)\\\"}]}]},{\\\"include\\\":\\\"#comment_block\\\"},{\\\"begin\\\":\\\"(^[ \\\\\\\\t]+)?(?=%)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.comment.leading.matlab\\\"}},\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"%\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.matlab\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.line.percentage.matlab\\\"}]}]},\\\"conjugate_transpose\\\":{\\\"match\\\":\\\"((?<=\\\\\\\\S)|(?<=])|(?<=\\\\\\\\))|(?<=}))'\\\",\\\"name\\\":\\\"keyword.operator.transpose.matlab\\\"},\\\"constants\\\":{\\\"match\\\":\\\"(?<!\\\\\\\\.)\\\\\\\\b(eps|false|Inf|inf|intmax|intmin|namelengthmax|NaN|nan|on|off|realmax|realmin|true|pi)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.matlab\\\"},\\\"control_statements\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.matlab\\\"}},\\\"match\\\":\\\"\\\\\\\\s*(?:^|[\\\\\\\\s,;])(break|continue|return)\\\\\\\\b\\\",\\\"name\\\":\\\"meta.control.matlab\\\"},\\\"curly_brackets\\\":{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"end\\\":\\\"}\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#end_in_parens\\\"},{\\\"include\\\":\\\"#all_before_command_dual\\\"},{\\\"include\\\":\\\"#all_after_command_dual\\\"},{\\\"include\\\":\\\"#end_in_parens\\\"},{\\\"include\\\":\\\"#block_keywords\\\"}]},\\\"end_in_parens\\\":{\\\"match\\\":\\\"\\\\\\\\bend\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.operator.symbols.matlab\\\"},\\\"function\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(^\\\\\\\\s*)(function)\\\\\\\\s+(?:(?:(\\\\\\\\[)([^\\\\\\\\]]*)(])|([a-zA-Z][a-zA-Z0-9_]*))\\\\\\\\s*=\\\\\\\\s*)?([a-zA-Z][a-zA-Z0-9_]*(\\\\\\\\.[a-zA-Z][a-zA-Z0-9_]*)*)\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"storage.type.function.matlab\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.matlab\\\"},\\\"4\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\w+\\\",\\\"name\\\":\\\"variable.parameter.output.matlab\\\"}]},\\\"5\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.matlab\\\"},\\\"6\\\":{\\\"name\\\":\\\"variable.parameter.output.function.matlab\\\"},\\\"7\\\":{\\\"name\\\":\\\"entity.name.function.matlab\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(?:^|[\\\\\\\\s,;])(end)\\\\\\\\b(\\\\\\\\s*\\\\\\\\n)?\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.end.function.matlab\\\"}},\\\"name\\\":\\\"meta.function.matlab\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G\\\\\\\\(\\\",\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"name\\\":\\\"meta.arguments.function.matlab\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#line_continuation\\\"},{\\\"match\\\":\\\"\\\\\\\\w+\\\",\\\"name\\\":\\\"variable.parameter.input.matlab\\\"}]},{\\\"begin\\\":\\\"(^\\\\\\\\s*)(arguments)\\\\\\\\b([^%]*)\\\\\\\\s*(\\\\\\\\([^)]*\\\\\\\\))?\\\\\\\\s*($|(?=%))\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"keyword.control.arguments.matlab\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"[a-zA-Z][a-zA-Z0-9_]*\\\",\\\"name\\\":\\\"variable.parameter.arguments.matlab\\\"}]}},\\\"end\\\":\\\"\\\\\\\\s*(?:^|[\\\\\\\\s,;])(end)\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.end.arguments.matlab\\\"}},\\\"name\\\":\\\"meta.arguments.matlab\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#validators\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"include\\\":\\\"$self\\\"}]}]},\\\"global_persistent\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.globalpersistent.matlab\\\"}},\\\"match\\\":\\\"^\\\\\\\\s*(global|persistent)\\\\\\\\b\\\",\\\"name\\\":\\\"meta.globalpersistent.matlab\\\"},\\\"indexing_curly_brackets\\\":{\\\"Comment\\\":\\\"Match identifier{idx, idx, } and stop at newline without ... This helps with partially written code like x{idx \\\",\\\"begin\\\":\\\"([a-zA-Z][a-zA-Z0-9_.]*\\\\\\\\s*)\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]}},\\\"end\\\":\\\"(}|(?<!\\\\\\\\.\\\\\\\\.\\\\\\\\.).\\\\\\\\n)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#end_in_parens\\\"},{\\\"include\\\":\\\"#all_before_command_dual\\\"},{\\\"include\\\":\\\"#all_after_command_dual\\\"},{\\\"include\\\":\\\"#end_in_parens\\\"},{\\\"include\\\":\\\"#block_keywords\\\"}]},\\\"line_continuation\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.symbols.matlab\\\"},\\\"2\\\":{\\\"name\\\":\\\"comment.line.continuation.matlab\\\"}},\\\"match\\\":\\\"(\\\\\\\\.\\\\\\\\.\\\\\\\\.)(.*)$\\\",\\\"name\\\":\\\"meta.linecontinuation.matlab\\\"},\\\"numbers\\\":{\\\"match\\\":\\\"(?<=[\\\\\\\\s\\\\\\\\-+*/\\\\\\\\\\\\\\\\=:\\\\\\\\[({,]|^)\\\\\\\\d*\\\\\\\\.?\\\\\\\\d+([eE][+-]?\\\\\\\\d)?([0-9&&[^.]])*([ij])?\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.matlab\\\"},\\\"operators\\\":{\\\"match\\\":\\\"(?<=\\\\\\\\s)(==|~=|>|>=|<|<=|&|&&|[:|]|\\\\\\\\|\\\\\\\\||[+\\\\\\\\-*]|\\\\\\\\.\\\\\\\\*|/|\\\\\\\\./|\\\\\\\\\\\\\\\\|\\\\\\\\.\\\\\\\\\\\\\\\\|\\\\\\\\^|\\\\\\\\.\\\\\\\\^)(?=\\\\\\\\s)\\\",\\\"name\\\":\\\"keyword.operator.symbols.matlab\\\"},\\\"parens\\\":{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"end\\\":\\\"(\\\\\\\\)|(?<!\\\\\\\\.\\\\\\\\.\\\\\\\\.).\\\\\\\\n)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#end_in_parens\\\"},{\\\"include\\\":\\\"#all_before_command_dual\\\"},{\\\"include\\\":\\\"#all_after_command_dual\\\"},{\\\"include\\\":\\\"#block_keywords\\\"}]},\\\"square_brackets\\\":{\\\"begin\\\":\\\"\\\\\\\\[\\\",\\\"end\\\":\\\"]\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#all_before_command_dual\\\"},{\\\"include\\\":\\\"#all_after_command_dual\\\"},{\\\"include\\\":\\\"#block_keywords\\\"}]},\\\"string\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"string.interpolated.matlab\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.matlab\\\"}},\\\"match\\\":\\\"^\\\\\\\\s*((!).*$\\\\\\\\n?)\\\"},{\\\"begin\\\":\\\"((?<=([\\\\\\\\[({=\\\\\\\\s;:,~<>\\\\\\\\&|\\\\\\\\-+*/\\\\\\\\\\\\\\\\.^]))|^)'\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.matlab\\\"}},\\\"end\\\":\\\"'(?=([\\\\\\\\[({\\\\\\\\])}=~<>\\\\\\\\&|\\\\\\\\-+*/\\\\\\\\\\\\\\\\.^\\\\\\\\s;:,]))\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.matlab\\\"}},\\\"name\\\":\\\"string.quoted.single.matlab\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"''\\\",\\\"name\\\":\\\"constant.character.escape.matlab\\\"},{\\\"match\\\":\\\"'(?=.)\\\",\\\"name\\\":\\\"invalid.illegal.unescaped-quote.matlab\\\"},{\\\"match\\\":\\\"((%([+\\\\\\\\-0]?\\\\\\\\d{0,3}(\\\\\\\\.\\\\\\\\d{1,3})?)([cdeEfgGs]|(([bt])?([ouxX]))))|%%|\\\\\\\\\\\\\\\\([bfnrt\\\\\\\\\\\\\\\\]))\\\",\\\"name\\\":\\\"constant.character.escape.matlab\\\"}]},{\\\"begin\\\":\\\"((?<=([\\\\\\\\[({=\\\\\\\\s;:,~<>\\\\\\\\&|\\\\\\\\-+*/\\\\\\\\\\\\\\\\.^]))|^)\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.matlab\\\"}},\\\"end\\\":\\\"\\\\\\\"(?=([\\\\\\\\[({\\\\\\\\])}=~<>\\\\\\\\&|\\\\\\\\-+*/\\\\\\\\\\\\\\\\.^\\\\\\\\s;:,]))\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.matlab\\\"}},\\\"name\\\":\\\"string.quoted.double.matlab\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\"\\\\\\\"\\\",\\\"name\\\":\\\"constant.character.escape.matlab\\\"},{\\\"match\\\":\\\"\\\\\\\"(?=.)\\\",\\\"name\\\":\\\"invalid.illegal.unescaped-quote.matlab\\\"}]}]},\\\"transpose\\\":{\\\"match\\\":\\\"\\\\\\\\.'\\\",\\\"name\\\":\\\"keyword.operator.transpose.matlab\\\"},\\\"validator_strings\\\":{\\\"patterns\\\":[{\\\"patterns\\\":[{\\\"begin\\\":\\\"((?<=([\\\\\\\\[({=\\\\\\\\s;:,~<>\\\\\\\\&|\\\\\\\\-+*/\\\\\\\\\\\\\\\\.^]))|^)'\\\",\\\"end\\\":\\\"'(?=([\\\\\\\\[({\\\\\\\\])}=~<>\\\\\\\\&|\\\\\\\\-+*/\\\\\\\\\\\\\\\\.^\\\\\\\\s;:,]))\\\",\\\"name\\\":\\\"storage.type.matlab\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"''\\\"},{\\\"match\\\":\\\"'(?=.)\\\"},{\\\"match\\\":\\\"([^']+)\\\"}]},{\\\"begin\\\":\\\"((?<=([\\\\\\\\[({=\\\\\\\\s;:,~<>\\\\\\\\&|\\\\\\\\-+*/\\\\\\\\\\\\\\\\.^]))|^)\\\\\\\"\\\",\\\"end\\\":\\\"\\\\\\\"(?=([\\\\\\\\[({\\\\\\\\])}=~<>\\\\\\\\&|\\\\\\\\-+*/\\\\\\\\\\\\\\\\.^\\\\\\\\s;:,]))\\\",\\\"name\\\":\\\"storage.type.matlab\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\"\\\\\\\"\\\"},{\\\"match\\\":\\\"\\\\\\\"(?=.)\\\"},{\\\"match\\\":\\\"[^\\\\\\\"]+\\\"}]}]}]},\\\"validators\\\":{\\\"begin\\\":\\\"\\\\\\\\s*;?\\\\\\\\s*([a-zA-Z][a-zA-Z0-9_.?]*)\\\",\\\"end\\\":\\\"([;\\\\\\\\n%=].*)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]}},\\\"match\\\":\\\"(%.*)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]}},\\\"match\\\":\\\"(=[^;]*)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#validators\\\"}]}},\\\"match\\\":\\\"([\\\\\\\\n;]\\\\\\\\s*[a-zA-Z].*)\\\"},{\\\"include\\\":\\\"$self\\\"}]}},\\\"patterns\\\":[{\\\"include\\\":\\\"#line_continuation\\\"},{\\\"match\\\":\\\"\\\\\\\\s*(\\\\\\\\([^)]*\\\\\\\\))\\\",\\\"name\\\":\\\"storage.type.matlab\\\"},{\\\"match\\\":\\\"([a-zA-Z][a-zA-Z0-9_.]*)\\\",\\\"name\\\":\\\"storage.type.matlab\\\"},{\\\"include\\\":\\\"#braced_validator_list\\\"}]},\\\"variables\\\":{\\\"match\\\":\\\"(?<!\\\\\\\\.)\\\\\\\\b(nargin|nargout|varargin|varargout)\\\\\\\\b\\\",\\\"name\\\":\\\"variable.other.function.matlab\\\"}},\\\"scopeName\\\":\\\"source.matlab\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ac2hpa2lqcy9sYW5ncy9kaXN0L21hdGxhYi5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLHdDQUF3QyxvRkFBb0YseUNBQXlDLEVBQUUsOEJBQThCLEVBQUUsd0NBQXdDLGtCQUFrQiw0QkFBNEIsZUFBZSx3QkFBd0IsRUFBRSxtQ0FBbUMsRUFBRSwwQkFBMEIsRUFBRSxxQ0FBcUMsRUFBRSwyQkFBMkIsRUFBRSwyQkFBMkIsRUFBRSwyQkFBMkIsRUFBRSx5QkFBeUIsRUFBRSwyQkFBMkIsRUFBRSw4QkFBOEIsZUFBZSwwQkFBMEIsRUFBRSwwQkFBMEIsRUFBRSx3QkFBd0IsRUFBRSxvQ0FBb0MsRUFBRSxtQ0FBbUMsRUFBRSx3QkFBd0IsRUFBRSxpQ0FBaUMsRUFBRSx5Q0FBeUMsRUFBRSxnQ0FBZ0MsRUFBRSxhQUFhLGVBQWUsK0JBQStCLGtDQUFrQyxPQUFPLHlDQUF5Qyw4QkFBOEIsZ0NBQWdDLE9BQU8sNkNBQTZDLDZDQUE2QyxzQkFBc0IsRUFBRSxFQUFFLCtCQUErQixpQ0FBaUMsT0FBTyx3Q0FBd0MsOEJBQThCLGdDQUFnQyxPQUFPLDJDQUEyQyxRQUFRLGVBQWUsc0JBQXNCLEdBQUcsNENBQTRDLGNBQWMsT0FBTywyQ0FBMkMsUUFBUSxlQUFlLHNCQUFzQixHQUFHLGdEQUFnRCw2REFBNkQsRUFBRSxjQUFjLE9BQU8seUNBQXlDLFFBQVEsZUFBZSxzQkFBc0IsR0FBRyxnREFBZ0QsMERBQTBELEVBQUUsc0JBQXNCLEVBQUUsRUFBRSwrQkFBK0IscUNBQXFDLE9BQU8seUNBQXlDLDhCQUE4QixnQ0FBZ0MsT0FBTyw2Q0FBNkMsZ0RBQWdELHFHQUFxRyxzQkFBc0IsRUFBRSxFQUFFLHNCQUFzQixFQUFFLEVBQUUsK0JBQStCLG1DQUFtQyxPQUFPLDBDQUEwQyw4QkFBOEIsZ0NBQWdDLE9BQU8sOENBQThDLDhDQUE4QyxvR0FBb0csc0JBQXNCLEVBQUUsRUFBRSxzQkFBc0IsRUFBRSxFQUFFLCtCQUErQixxQ0FBcUMsT0FBTyw0Q0FBNEMsOEJBQThCLGdDQUFnQyxPQUFPLGdEQUFnRCxnREFBZ0QsY0FBYyxPQUFPLHlDQUF5QyxRQUFRLGVBQWUsc0JBQXNCLEdBQUcsZ0RBQWdELHlEQUF5RCxFQUFFLGNBQWMsT0FBTyw4Q0FBOEMsUUFBUSxlQUFlLHNCQUFzQixHQUFHLGdEQUFnRCxvRUFBb0UsRUFBRSxzQkFBc0IsRUFBRSxFQUFFLCtCQUErQixrQ0FBa0MsT0FBTyx5Q0FBeUMsOEJBQThCLGdDQUFnQyxPQUFPLDZDQUE2Qyw2Q0FBNkMsY0FBYyxPQUFPLDBDQUEwQyxRQUFRLGVBQWUsc0JBQXNCLEdBQUcsZ0RBQWdELDREQUE0RCxFQUFFLHNCQUFzQixFQUFFLEVBQUUsK0JBQStCLG9DQUFvQyxPQUFPLDJDQUEyQyw4QkFBOEIsZ0NBQWdDLE9BQU8sK0NBQStDLCtDQUErQyxzQkFBc0IsRUFBRSxFQUFFLDRCQUE0Qix3QkFBd0IsNkJBQTZCLE9BQU8sa0NBQWtDLGFBQWEscUJBQXFCLE9BQU8sa0NBQWtDLGdCQUFnQix1Q0FBdUMsRUFBRSxtQ0FBbUMsRUFBRSxtQ0FBbUMsRUFBRSxjQUFjLE9BQU8sa0NBQWtDLGtCQUFrQixZQUFZLEVBQUUscURBQXFELEVBQUUsZUFBZSxlQUFlLG9FQUFvRSxPQUFPLHVDQUF1QyxRQUFRLGVBQWUsY0FBYyxPQUFPLGVBQWUsaUZBQWlGLEVBQUUsNkRBQTZELHVFQUF1RSxFQUFFLHdCQUF3QixFQUFFLEVBQUUsUUFBUSwyQ0FBMkMsUUFBUSw4Q0FBOEMsUUFBUSwyQ0FBMkMsUUFBUSxlQUFlLGtIQUFrSCxFQUFFLDJEQUEyRCxFQUFFLFFBQVEsZUFBZSxzQkFBc0IsR0FBRyxtSEFBbUgsR0FBRyw4QkFBOEIsZ0NBQWdDLE9BQU8sK0NBQStDLCtDQUErQywwR0FBMEcsT0FBTywrQ0FBK0MsUUFBUSxlQUFlLHNGQUFzRixFQUFFLDZEQUE2RCx1RUFBdUUsRUFBRSxzRkFBc0YsRUFBRSxHQUFHLDhCQUE4QixnQ0FBZ0MsT0FBTyxvREFBb0Qsb0RBQW9ELDRCQUE0QixFQUFFLHNCQUFzQixFQUFFLEVBQUUsdUdBQXVHLE9BQU8sNENBQTRDLFFBQVEsZUFBZSxtRkFBbUYsRUFBRSw2REFBNkQsdUVBQXVFLEVBQUUsc0ZBQXNGLEVBQUUsR0FBRyw4QkFBOEIsZ0NBQWdDLE9BQU8saURBQWlELGlEQUFpRCxzQkFBc0IsRUFBRSxFQUFFLHNHQUFzRyxPQUFPLDJDQUEyQyxRQUFRLGVBQWUsa0ZBQWtGLEVBQUUsNkRBQTZELHVFQUF1RSxFQUFFLHNGQUFzRixFQUFFLEdBQUcsOEJBQThCLGdDQUFnQyxPQUFPLGdEQUFnRCxnREFBZ0Qsc0JBQXNCLEVBQUUsRUFBRSxtRkFBbUYsT0FBTyxpREFBaUQsOEJBQThCLGdDQUFnQyxPQUFPLHFEQUFxRCxxREFBcUQsc0JBQXNCLEVBQUUsRUFBRSxzQkFBc0IsRUFBRSxFQUFFLG1CQUFtQixjQUFjLE9BQU8sd0NBQXdDLFFBQVEsMkNBQTJDLFNBQVMsNkNBQTZDLHdvQkFBd29CLE9BQU8sdVBBQXVQLEVBQUUsd0tBQXdLLDhGQUE4RixNQUFNLEdBQUcsSUFBSSw4Q0FBOEMsb0JBQW9CLDJCQUEyQiwwQ0FBMEMsT0FBTyxvREFBb0Qsb0JBQW9CLHlGQUF5RiwrQkFBK0IsRUFBRSw4QkFBOEIsRUFBRSxlQUFlLGVBQWUsMERBQTBELE9BQU8sNERBQTRELHNDQUFzQyxvQ0FBb0MsT0FBTyxvREFBb0QscUZBQXFGLDZHQUE2RyxFQUFFLEVBQUUsRUFBRSwrQkFBK0IsRUFBRSxvREFBb0QsT0FBTyw0REFBNEQsc0NBQXNDLG1DQUFtQyxPQUFPLG9EQUFvRCwrREFBK0QsRUFBRSxFQUFFLDBCQUEwQiwrQ0FBK0MscURBQXFELGdCQUFnQiwrSkFBK0oseUJBQXlCLGNBQWMsT0FBTyxxQ0FBcUMsZ0NBQWdDLGtFQUFrRSxxQkFBcUIsaUJBQWlCLGNBQWMsa0JBQWtCLCtCQUErQixFQUFFLHlDQUF5QyxFQUFFLHdDQUF3QyxFQUFFLCtCQUErQixFQUFFLGdDQUFnQyxFQUFFLG9CQUFvQix5RUFBeUUsZUFBZSxlQUFlLHdMQUF3TCxPQUFPLDBDQUEwQyxRQUFRLDJEQUEyRCxRQUFRLGVBQWUsbUVBQW1FLEVBQUUsUUFBUSx5REFBeUQsUUFBUSx1REFBdUQsUUFBUSwwQ0FBMEMsOEJBQThCLDhDQUE4QyxPQUFPLGtEQUFrRCxrREFBa0Qsc0dBQXNHLG1DQUFtQyxFQUFFLGtFQUFrRSxFQUFFLEVBQUUseUdBQXlHLE9BQU8sOENBQThDLFFBQVEsZUFBZSxxRkFBcUYsR0FBRyw4QkFBOEIsZ0NBQWdDLE9BQU8sbURBQW1ELG1EQUFtRCw0QkFBNEIsRUFBRSxzQkFBc0IsRUFBRSxFQUFFLHNCQUFzQixFQUFFLEVBQUUsd0JBQXdCLGNBQWMsT0FBTyxzREFBc0QseUZBQXlGLDhCQUE4QiwrQkFBK0IsWUFBWSw4RUFBOEUsc0RBQXNELHNCQUFzQixPQUFPLGVBQWUsc0JBQXNCLEdBQUcsYUFBYSw4Q0FBOEMsK0JBQStCLEVBQUUseUNBQXlDLEVBQUUsd0NBQXdDLEVBQUUsK0JBQStCLEVBQUUsZ0NBQWdDLEVBQUUsd0JBQXdCLGNBQWMsT0FBTyw2Q0FBNkMsUUFBUSwrQ0FBK0MsZ0ZBQWdGLGNBQWMsK0NBQStDLDBHQUEwRyxnQkFBZ0IsMkxBQTJMLGFBQWEsa0ZBQWtGLCtCQUErQixFQUFFLHlDQUF5QyxFQUFFLHdDQUF3QyxFQUFFLGdDQUFnQyxFQUFFLHNCQUFzQixpREFBaUQseUNBQXlDLEVBQUUsd0NBQXdDLEVBQUUsZ0NBQWdDLEVBQUUsYUFBYSxlQUFlLGNBQWMsT0FBTyx3Q0FBd0MsUUFBUSx5REFBeUQscUNBQXFDLEVBQUUsMEJBQTBCLE9BQU8sMERBQTBELE9BQU8seURBQXlELHdCQUF3QixPQUFPLGtDQUFrQyx5QkFBeUIsT0FBTyx1REFBdUQseURBQXlELCtEQUErRCxFQUFFLHlFQUF5RSxFQUFFLGdDQUFnQyxJQUFJLFlBQVksSUFBSSxpSEFBaUgsRUFBRSxFQUFFLDBCQUEwQixPQUFPLDZEQUE2RCxPQUFPLHlEQUF5RCwyQkFBMkIsT0FBTyxrQ0FBa0MseUJBQXlCLE9BQU8sdURBQXVELHlEQUF5RCxxRUFBcUUsRUFBRSw0RUFBNEUsRUFBRSxFQUFFLGdCQUFnQixvRUFBb0Usd0JBQXdCLGVBQWUsZUFBZSwwQkFBMEIsT0FBTyw4REFBOEQsT0FBTyxrQ0FBa0Msd0RBQXdELGlCQUFpQixFQUFFLHFCQUFxQixFQUFFLHNCQUFzQixFQUFFLEVBQUUsMEJBQTBCLE9BQU8sb0VBQW9FLE9BQU8sa0NBQWtDLHdEQUF3RCx1QkFBdUIsRUFBRSx3QkFBd0IsRUFBRSx1QkFBdUIsRUFBRSxFQUFFLEVBQUUsaUJBQWlCLG1CQUFtQixnREFBZ0QsK0JBQStCLE9BQU8sZUFBZSxjQUFjLE9BQU8sZUFBZSxzQkFBc0IsR0FBRyxxQkFBcUIsRUFBRSxjQUFjLE9BQU8sZUFBZSxzQkFBc0IsR0FBRyxrQkFBa0IsTUFBTSxFQUFFLGNBQWMsT0FBTyxlQUFlLDRCQUE0QixHQUFHLHFCQUFxQixxQkFBcUIsRUFBRSxzQkFBc0IsR0FBRyxnQkFBZ0IsbUNBQW1DLEVBQUUsdUVBQXVFLEVBQUUsd0VBQXdFLEVBQUUsdUNBQXVDLEVBQUUsZ0JBQWdCLG1IQUFtSCxpQ0FBaUM7O0FBRS9wakIsaUVBQWU7QUFDZjtBQUNBLENBQUMiLCJzb3VyY2VzIjpbIkQ6XFxzb2Z0XFxTZWFQcm9qZWN0XFxnaXRodWJfb3Blbl9wcm9qZWN0X3N1Y2Nlc3NcXHN1bmFcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXEBzaGlraWpzXFxsYW5nc1xcZGlzdFxcbWF0bGFiLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBsYW5nID0gT2JqZWN0LmZyZWV6ZShKU09OLnBhcnNlKFwie1xcXCJkaXNwbGF5TmFtZVxcXCI6XFxcIk1BVExBQlxcXCIsXFxcImZpbGVUeXBlc1xcXCI6W1xcXCJtXFxcIl0sXFxcIm5hbWVcXFwiOlxcXCJtYXRsYWJcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNhbGxfYmVmb3JlX2NvbW1hbmRfZHVhbFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNjb21tYW5kX2R1YWxcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjYWxsX2FmdGVyX2NvbW1hbmRfZHVhbFxcXCJ9XSxcXFwicmVwb3NpdG9yeVxcXCI6e1xcXCJhbGxfYWZ0ZXJfY29tbWFuZF9kdWFsXFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3N0cmluZ1xcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNsaW5lX2NvbnRpbnVhdGlvblxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNjb21tZW50c1xcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNjb25qdWdhdGVfdHJhbnNwb3NlXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3RyYW5zcG9zZVxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNjb25zdGFudHNcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjdmFyaWFibGVzXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI251bWJlcnNcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjb3BlcmF0b3JzXFxcIn1dfSxcXFwiYWxsX2JlZm9yZV9jb21tYW5kX2R1YWxcXFwiOntcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjY2xhc3NkZWZcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjZnVuY3Rpb25cXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjYmxvY2tzXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2NvbnRyb2xfc3RhdGVtZW50c1xcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNnbG9iYWxfcGVyc2lzdGVudFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNwYXJlbnNcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjc3F1YXJlX2JyYWNrZXRzXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2luZGV4aW5nX2N1cmx5X2JyYWNrZXRzXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2N1cmx5X2JyYWNrZXRzXFxcIn1dfSxcXFwiYmxvY2tzXFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxccyooPzpefFtcXFxcXFxcXHMsO10pKGZvcilcXFxcXFxcXGJcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQuY29udHJvbC5mb3IubWF0bGFiXFxcIn19LFxcXCJlbmRcXFwiOlxcXCJcXFxcXFxcXHMqKD86XnxbXFxcXFxcXFxzLDtdKShlbmQpXFxcXFxcXFxiXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5jb250cm9sLmVuZC5mb3IubWF0bGFiXFxcIn19LFxcXCJuYW1lXFxcIjpcXFwibWV0YS5mb3IubWF0bGFiXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIkc2VsZlxcXCJ9XX0se1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxccyooPzpefFtcXFxcXFxcXHMsO10pKGlmKVxcXFxcXFxcYlxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5jb250cm9sLmlmLm1hdGxhYlxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiXFxcXFxcXFxzKig/Ol58W1xcXFxcXFxccyw7XSkoZW5kKVxcXFxcXFxcYlxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQuY29udHJvbC5lbmQuaWYubWF0bGFiXFxcIn0sXFxcIjJcXFwiOntcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIkc2VsZlxcXCJ9XX19LFxcXCJuYW1lXFxcIjpcXFwibWV0YS5pZi5tYXRsYWJcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiY2FwdHVyZXNcXFwiOntcXFwiMlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5jb250cm9sLmVsc2VpZi5tYXRsYWJcXFwifSxcXFwiM1xcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiRzZWxmXFxcIn1dfX0sXFxcImVuZFxcXCI6XFxcIl5cXFwiLFxcXCJtYXRjaFxcXCI6XFxcIihcXFxcXFxcXHMqKSg/Ol58W1xcXFxcXFxccyw7XSkoZWxzZWlmKVxcXFxcXFxcYiguKikkXFxcXFxcXFxuP1xcXCIsXFxcIm5hbWVcXFwiOlxcXCJtZXRhLmVsc2VpZi5tYXRsYWJcXFwifSx7XFxcImNhcHR1cmVzXFxcIjp7XFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQuY29udHJvbC5lbHNlLm1hdGxhYlxcXCJ9LFxcXCIzXFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiJHNlbGZcXFwifV19fSxcXFwiZW5kXFxcIjpcXFwiXlxcXCIsXFxcIm1hdGNoXFxcIjpcXFwiKFxcXFxcXFxccyopKD86XnxbXFxcXFxcXFxzLDtdKShlbHNlKVxcXFxcXFxcYiguKik/JFxcXFxcXFxcbj9cXFwiLFxcXCJuYW1lXFxcIjpcXFwibWV0YS5lbHNlLm1hdGxhYlxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiRzZWxmXFxcIn1dfSx7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXFxzKig/Ol58W1xcXFxcXFxccyw7XSkocGFyZm9yKVxcXFxcXFxcYlxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5jb250cm9sLmZvci5tYXRsYWJcXFwifX0sXFxcImVuZFxcXCI6XFxcIlxcXFxcXFxccyooPzpefFtcXFxcXFxcXHMsO10pKGVuZClcXFxcXFxcXGJcXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLmNvbnRyb2wuZW5kLmZvci5tYXRsYWJcXFwifX0sXFxcIm5hbWVcXFwiOlxcXCJtZXRhLnBhcmZvci5tYXRsYWJcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiYmVnaW5cXFwiOlxcXCJcXFxcXFxcXEcoPyEkKVxcXCIsXFxcImVuZFxcXCI6XFxcIiRcXFxcXFxcXG4/XFxcIixcXFwibmFtZVxcXCI6XFxcIm1ldGEucGFyZm9yLXF1YW50aXR5Lm1hdGxhYlxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiJHNlbGZcXFwifV19LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiRzZWxmXFxcIn1dfSx7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXFxzKig/Ol58W1xcXFxcXFxccyw7XSkoc3BtZClcXFxcXFxcXGJcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQuY29udHJvbC5zcG1kLm1hdGxhYlxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiXFxcXFxcXFxzKig/Ol58W1xcXFxcXFxccyw7XSkoZW5kKVxcXFxcXFxcYlxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQuY29udHJvbC5lbmQuc3BtZC5tYXRsYWJcXFwifX0sXFxcIm5hbWVcXFwiOlxcXCJtZXRhLnNwbWQubWF0bGFiXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXFxHKD8hJClcXFwiLFxcXCJlbmRcXFwiOlxcXCIkXFxcXFxcXFxuP1xcXCIsXFxcIm5hbWVcXFwiOlxcXCJtZXRhLnNwbWQtc3RhdGVtZW50Lm1hdGxhYlxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiJHNlbGZcXFwifV19LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiRzZWxmXFxcIn1dfSx7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXFxzKig/Ol58W1xcXFxcXFxccyw7XSkoc3dpdGNoKVxcXFxcXFxcYlxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5jb250cm9sLnN3aXRjaC5tYXRsYWJcXFwifX0sXFxcImVuZFxcXCI6XFxcIlxcXFxcXFxccyooPzpefFtcXFxcXFxcXHMsO10pKGVuZClcXFxcXFxcXGJcXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLmNvbnRyb2wuZW5kLnN3aXRjaC5tYXRsYWJcXFwifX0sXFxcIm5hbWVcXFwiOlxcXCJtZXRhLnN3aXRjaC5tYXRsYWJcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiY2FwdHVyZXNcXFwiOntcXFwiMlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5jb250cm9sLmNhc2UubWF0bGFiXFxcIn0sXFxcIjNcXFwiOntcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIkc2VsZlxcXCJ9XX19LFxcXCJlbmRcXFwiOlxcXCJeXFxcIixcXFwibWF0Y2hcXFwiOlxcXCIoXFxcXFxcXFxzKikoPzpefFtcXFxcXFxcXHMsO10pKGNhc2UpXFxcXFxcXFxiKC4qKSRcXFxcXFxcXG4/XFxcIixcXFwibmFtZVxcXCI6XFxcIm1ldGEuY2FzZS5tYXRsYWJcXFwifSx7XFxcImNhcHR1cmVzXFxcIjp7XFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQuY29udHJvbC5vdGhlcndpc2UubWF0bGFiXFxcIn0sXFxcIjNcXFwiOntcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIkc2VsZlxcXCJ9XX19LFxcXCJlbmRcXFwiOlxcXCJeXFxcIixcXFwibWF0Y2hcXFwiOlxcXCIoXFxcXFxcXFxzKikoPzpefFtcXFxcXFxcXHMsO10pKG90aGVyd2lzZSlcXFxcXFxcXGIoLiopPyRcXFxcXFxcXG4/XFxcIixcXFwibmFtZVxcXCI6XFxcIm1ldGEub3RoZXJ3aXNlLm1hdGxhYlxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiRzZWxmXFxcIn1dfSx7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXFxzKig/Ol58W1xcXFxcXFxccyw7XSkodHJ5KVxcXFxcXFxcYlxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5jb250cm9sLnRyeS5tYXRsYWJcXFwifX0sXFxcImVuZFxcXCI6XFxcIlxcXFxcXFxccyooPzpefFtcXFxcXFxcXHMsO10pKGVuZClcXFxcXFxcXGJcXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLmNvbnRyb2wuZW5kLnRyeS5tYXRsYWJcXFwifX0sXFxcIm5hbWVcXFwiOlxcXCJtZXRhLnRyeS5tYXRsYWJcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiY2FwdHVyZXNcXFwiOntcXFwiMlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5jb250cm9sLmNhdGNoLm1hdGxhYlxcXCJ9LFxcXCIzXFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiJHNlbGZcXFwifV19fSxcXFwiZW5kXFxcIjpcXFwiXlxcXCIsXFxcIm1hdGNoXFxcIjpcXFwiKFxcXFxcXFxccyopKD86XnxbXFxcXFxcXFxzLDtdKShjYXRjaClcXFxcXFxcXGIoLiopPyRcXFxcXFxcXG4/XFxcIixcXFwibmFtZVxcXCI6XFxcIm1ldGEuY2F0Y2gubWF0bGFiXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiJHNlbGZcXFwifV19LHtcXFwiYmVnaW5cXFwiOlxcXCJcXFxcXFxcXHMqKD86XnxbXFxcXFxcXFxzLDtdKSh3aGlsZSlcXFxcXFxcXGJcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQuY29udHJvbC53aGlsZS5tYXRsYWJcXFwifX0sXFxcImVuZFxcXCI6XFxcIlxcXFxcXFxccyooPzpefFtcXFxcXFxcXHMsO10pKGVuZClcXFxcXFxcXGJcXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLmNvbnRyb2wuZW5kLndoaWxlLm1hdGxhYlxcXCJ9fSxcXFwibmFtZVxcXCI6XFxcIm1ldGEud2hpbGUubWF0bGFiXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIkc2VsZlxcXCJ9XX1dfSxcXFwiYnJhY2VkX3ZhbGlkYXRvcl9saXN0XFxcIjp7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXFxzKihcXFxcXFxcXHspXFxcXFxcXFxzKlxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwic3RvcmFnZS50eXBlLm1hdGxhYlxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiKH0pXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwic3RvcmFnZS50eXBlLm1hdGxhYlxcXCJ9fSxcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjYnJhY2VkX3ZhbGlkYXRvcl9saXN0XFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3ZhbGlkYXRvcl9zdHJpbmdzXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2xpbmVfY29udGludWF0aW9uXFxcIn0se1xcXCJjYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJzdG9yYWdlLnR5cGUubWF0bGFiXFxcIn19LFxcXCJtYXRjaFxcXCI6XFxcIihbXnt9J1xcXFxcXFwiLl0rKVxcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCJcXFxcXFxcXC5cXFwiLFxcXCJuYW1lXFxcIjpcXFwic3RvcmFnZS50eXBlLm1hdGxhYlxcXCJ9XX0sXFxcImNsYXNzZGVmXFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJiZWdpblxcXCI6XFxcIiheXFxcXFxcXFxzKikoY2xhc3NkZWYpXFxcXFxcXFxiXFxcXFxcXFxzKiguKilcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcInN0b3JhZ2UudHlwZS5jbGFzcy5tYXRsYWJcXFwifSxcXFwiM1xcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiY2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwibWF0Y2hcXFwiOlxcXCJbYS16QS1aXVthLXpBLVowLTlfXSpcXFwiLFxcXCJuYW1lXFxcIjpcXFwidmFyaWFibGUucGFyYW1ldGVyLmNsYXNzLm1hdGxhYlxcXCJ9LHtcXFwiYmVnaW5cXFwiOlxcXCI9XFxcXFxcXFxzKlxcXCIsXFxcImVuZFxcXCI6XFxcIix8KD89XFxcXFxcXFwpKVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJtYXRjaFxcXCI6XFxcInRydWV8ZmFsc2VcXFwiLFxcXCJuYW1lXFxcIjpcXFwiY29uc3RhbnQubGFuZ3VhZ2UuYm9vbGVhbi5tYXRsYWJcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjc3RyaW5nXFxcIn1dfV19LFxcXCIyXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJtZXRhLmNsYXNzLWRlY2xhcmF0aW9uLm1hdGxhYlxcXCJ9LFxcXCIzXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJlbnRpdHkubmFtZS5zZWN0aW9uLmNsYXNzLm1hdGxhYlxcXCJ9LFxcXCI0XFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLm9wZXJhdG9yLm90aGVyLm1hdGxhYlxcXCJ9LFxcXCI1XFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJtYXRjaFxcXCI6XFxcIlthLXpBLVpdW2EtekEtWjAtOV9dKihcXFxcXFxcXC5bYS16QS1aXVthLXpBLVowLTlfXSopKlxcXCIsXFxcIm5hbWVcXFwiOlxcXCJlbnRpdHkub3RoZXIuaW5oZXJpdGVkLWNsYXNzLm1hdGxhYlxcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCImXFxcIixcXFwibmFtZVxcXCI6XFxcImtleXdvcmQub3BlcmF0b3Iub3RoZXIubWF0bGFiXFxcIn1dfSxcXFwiNlxcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiRzZWxmXFxcIn1dfX0sXFxcIm1hdGNoXFxcIjpcXFwiKFxcXFxcXFxcKFteKV0qXFxcXFxcXFwpKT9cXFxcXFxcXHMqKChbYS16QS1aXVthLXpBLVowLTlfXSopKD86XFxcXFxcXFxzKig8KVxcXFxcXFxccyooW14lXSopKT8pXFxcXFxcXFxzKigkfCg/PSglfC4uLikpLiopXFxcIn1dfX0sXFxcImVuZFxcXCI6XFxcIlxcXFxcXFxccyooPzpefFtcXFxcXFxcXHMsO10pKGVuZClcXFxcXFxcXGJcXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLmNvbnRyb2wuZW5kLmNsYXNzLm1hdGxhYlxcXCJ9fSxcXFwibmFtZVxcXCI6XFxcIm1ldGEuY2xhc3MubWF0bGFiXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImJlZ2luXFxcIjpcXFwiKF5cXFxcXFxcXHMqKShwcm9wZXJ0aWVzKVxcXFxcXFxcYihbXiVdKilcXFxcXFxcXHMqKFxcXFxcXFxcKFteKV0qXFxcXFxcXFwpKT9cXFxcXFxcXHMqKCR8KD89JSkpXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIyXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLmNvbnRyb2wucHJvcGVydGllcy5tYXRsYWJcXFwifSxcXFwiM1xcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwibWF0Y2hcXFwiOlxcXCJbYS16QS1aXVthLXpBLVowLTlfXSpcXFwiLFxcXCJuYW1lXFxcIjpcXFwidmFyaWFibGUucGFyYW1ldGVyLnByb3BlcnRpZXMubWF0bGFiXFxcIn0se1xcXCJiZWdpblxcXCI6XFxcIj1cXFxcXFxcXHMqXFxcIixcXFwiZW5kXFxcIjpcXFwiLHwoPz1cXFxcXFxcXCkpXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcIm1hdGNoXFxcIjpcXFwidHJ1ZXxmYWxzZVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJjb25zdGFudC5sYW5ndWFnZS5ib29sZWFuLm1hdGxhYlxcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCJwKD86dWJsaWN8cm90ZWN0ZWR8cml2YXRlKVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJjb25zdGFudC5sYW5ndWFnZS5hY2Nlc3MubWF0bGFiXFxcIn1dfV19fSxcXFwiZW5kXFxcIjpcXFwiXFxcXFxcXFxzKig/Ol58W1xcXFxcXFxccyw7XSkoZW5kKVxcXFxcXFxcYlxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQuY29udHJvbC5lbmQucHJvcGVydGllcy5tYXRsYWJcXFwifX0sXFxcIm5hbWVcXFwiOlxcXCJtZXRhLnByb3BlcnRpZXMubWF0bGFiXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjdmFsaWRhdG9yc1xcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiRzZWxmXFxcIn1dfSx7XFxcImJlZ2luXFxcIjpcXFwiKF5cXFxcXFxcXHMqKShtZXRob2RzKVxcXFxcXFxcYihbXiVdKilcXFxcXFxcXHMqKFxcXFxcXFxcKFteKV0qXFxcXFxcXFwpKT9cXFxcXFxcXHMqKCR8KD89JSkpXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIyXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLmNvbnRyb2wubWV0aG9kcy5tYXRsYWJcXFwifSxcXFwiM1xcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwibWF0Y2hcXFwiOlxcXCJbYS16QS1aXVthLXpBLVowLTlfXSpcXFwiLFxcXCJuYW1lXFxcIjpcXFwidmFyaWFibGUucGFyYW1ldGVyLm1ldGhvZHMubWF0bGFiXFxcIn0se1xcXCJiZWdpblxcXCI6XFxcIj1cXFxcXFxcXHMqXFxcIixcXFwiZW5kXFxcIjpcXFwiLHwoPz1cXFxcXFxcXCkpXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcIm1hdGNoXFxcIjpcXFwidHJ1ZXxmYWxzZVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJjb25zdGFudC5sYW5ndWFnZS5ib29sZWFuLm1hdGxhYlxcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCJwKD86dWJsaWN8cm90ZWN0ZWR8cml2YXRlKVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJjb25zdGFudC5sYW5ndWFnZS5hY2Nlc3MubWF0bGFiXFxcIn1dfV19fSxcXFwiZW5kXFxcIjpcXFwiXFxcXFxcXFxzKig/Ol58W1xcXFxcXFxccyw7XSkoZW5kKVxcXFxcXFxcYlxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQuY29udHJvbC5lbmQubWV0aG9kcy5tYXRsYWJcXFwifX0sXFxcIm5hbWVcXFwiOlxcXCJtZXRhLm1ldGhvZHMubWF0bGFiXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIkc2VsZlxcXCJ9XX0se1xcXCJiZWdpblxcXCI6XFxcIiheXFxcXFxcXFxzKikoZXZlbnRzKVxcXFxcXFxcYihbXiVdKilcXFxcXFxcXHMqKFxcXFxcXFxcKFteKV0qXFxcXFxcXFwpKT9cXFxcXFxcXHMqKCR8KD89JSkpXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIyXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLmNvbnRyb2wuZXZlbnRzLm1hdGxhYlxcXCJ9LFxcXCIzXFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJtYXRjaFxcXCI6XFxcIlthLXpBLVpdW2EtekEtWjAtOV9dKlxcXCIsXFxcIm5hbWVcXFwiOlxcXCJ2YXJpYWJsZS5wYXJhbWV0ZXIuZXZlbnRzLm1hdGxhYlxcXCJ9LHtcXFwiYmVnaW5cXFwiOlxcXCI9XFxcXFxcXFxzKlxcXCIsXFxcImVuZFxcXCI6XFxcIix8KD89XFxcXFxcXFwpKVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJtYXRjaFxcXCI6XFxcInRydWV8ZmFsc2VcXFwiLFxcXCJuYW1lXFxcIjpcXFwiY29uc3RhbnQubGFuZ3VhZ2UuYm9vbGVhbi5tYXRsYWJcXFwifSx7XFxcIm1hdGNoXFxcIjpcXFwicCg/OnVibGljfHJvdGVjdGVkfHJpdmF0ZSlcXFwiLFxcXCJuYW1lXFxcIjpcXFwiY29uc3RhbnQubGFuZ3VhZ2UuYWNjZXNzLm1hdGxhYlxcXCJ9XX1dfX0sXFxcImVuZFxcXCI6XFxcIlxcXFxcXFxccyooPzpefFtcXFxcXFxcXHMsO10pKGVuZClcXFxcXFxcXGJcXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLmNvbnRyb2wuZW5kLmV2ZW50cy5tYXRsYWJcXFwifX0sXFxcIm5hbWVcXFwiOlxcXCJtZXRhLmV2ZW50cy5tYXRsYWJcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiRzZWxmXFxcIn1dfSx7XFxcImJlZ2luXFxcIjpcXFwiKF5cXFxcXFxcXHMqKShlbnVtZXJhdGlvbilcXFxcXFxcXGIoW14lXSopXFxcXFxcXFxzKigkfCg/PSUpKVxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5jb250cm9sLmVudW1lcmF0aW9uLm1hdGxhYlxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiXFxcXFxcXFxzKig/Ol58W1xcXFxcXFxccyw7XSkoZW5kKVxcXFxcXFxcYlxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQuY29udHJvbC5lbmQuZW51bWVyYXRpb24ubWF0bGFiXFxcIn19LFxcXCJuYW1lXFxcIjpcXFwibWV0YS5lbnVtZXJhdGlvbi5tYXRsYWJcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiRzZWxmXFxcIn1dfSx7XFxcImluY2x1ZGVcXFwiOlxcXCIkc2VsZlxcXCJ9XX1dfSxcXFwiY29tbWFuZF9kdWFsXFxcIjp7XFxcImNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcInN0cmluZy5pbnRlcnBvbGF0ZWQubWF0bGFiXFxcIn0sXFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcInZhcmlhYmxlLm90aGVyLmNvbW1hbmQubWF0bGFiXFxcIn0sXFxcIjI4XFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJjb21tZW50LmxpbmUucGVyY2VudGFnZS5tYXRsYWJcXFwifX0sXFxcIm1hdGNoXFxcIjpcXFwiXlxcXFxcXFxccyooKFtiLWRmLWhrLW1vcS16QS1ISi1NTy1aXVxcXFxcXFxcdyp8YXxhbnxhKFtBLVphLW1vLXowLTlfXVxcXFxcXFxcdyp8bltBLVphLXJ0LXowLTlfXVxcXFxcXFxcdyp8bnNcXFxcXFxcXHcrKXxlfGVwfGUoW0EtWmEtb3EtejAtOV9dXFxcXFxcXFx3KnxwW0EtWmEtcnQtejAtOV9dXFxcXFxcXFx3Knxwc1xcXFxcXFxcdyspfGlufGkoW0EtWmEtbW8tejAtOV9dXFxcXFxcXFx3KnxuW0EtWmEtZWctejAtOV9dXFxcXFxcXFx3KnxuZlxcXFxcXFxcdyspfEl8SW58SShbQS1aYS1tby16MC05X11cXFxcXFxcXHcqfG5bQS1aYS1lZy16MC05X11cXFxcXFxcXHcqfG5mXFxcXFxcXFx3Kyl8alxcXFxcXFxcdyt8TnxOYXxOKFtBLVpiLXowLTlfXVxcXFxcXFxcdyp8YVtBLU1PLVphLXowLTlfXVxcXFxcXFxcdyp8YU5cXFxcXFxcXHcrKXxufG5hfG5hcnxuYXJnfG5hcmdpfG5hcmdvfG5hcmdvdXxuKFtBLVpiLXowLTlfXVxcXFxcXFxcdyp8YShbQS1aYS1tby1xcy16MC05X11cXFxcXFxcXHcqfG5cXFxcXFxcXHcrfHIoW0EtWmEtZmgtejAtOV9dXFxcXFxcXFx3KnxnKFtBLVphLWhqLW5xLXowLTlfXVxcXFxcXFxcdyp8aShbQS1aYS1tby16MC05X11cXFxcXFxcXHcqfG5cXFxcXFxcXHcrKXxvKFtBLVphLXR2LXowLTlfXVxcXFxcXFxcdyp8dShbQS1aYS1zdS16XVxcXFxcXFxcdyp8dFxcXFxcXFxcdyspKSkpKSl8cHxwW0EtWmEtaGotejAtOV9dXFxcXFxcXFx3KnxwaVxcXFxcXFxcdyspXFxcXFxcXFxzKygoKFteXFxcXFxcXFxzOywlKCk9LntcXFxcXFxcXCZ8fjw+OitcXFxcXFxcXC0qL1xcXFxcXFxcXFxcXFxcXFxAXidcXFxcXFxcIl18KD89Jyl8KD89XFxcXFxcXCIpKXwoXFxcXFxcXFwuXFxcXFxcXFxefFxcXFxcXFxcLlxcXFxcXFxcKnxcXFxcXFxcXC4vfFxcXFxcXFxcLlxcXFxcXFxcXFxcXFxcXFx8XFxcXFxcXFwuJ3xcXFxcXFxcXC5cXFxcXFxcXCh8JiZ8PT18XFxcXFxcXFx8XFxcXFxcXFx8fCYoPz1bXlxcXFxcXFxcJl0pfFxcXFxcXFxcfCg/PVtefF0pfH49fDw9fD49fH4oPyE9KXw8KD8hPSl8Pig/IT0pfFs6K1xcXFxcXFxcLSovXFxcXFxcXFxcXFxcXFxcXEBeXSkoXFxcXFxcXFxTfFxcXFxcXFxccyooPz0lKXxcXFxcXFxcXHMrJHxcXFxcXFxcXHMrKFssOyl9XFxcXFxcXFxdXFxcXFxcXFwmfDw+PToqL1xcXFxcXFxcXFxcXFxcXFxeQF18KFxcXFxcXFxcLig/OlteXFxcXFxcXFxkLl18XFxcXFxcXFwuW14uXSkpKSl8KFxcXFxcXFxcLlteXiovXFxcXFxcXFxcXFxcXFxcXCcoXFxcXFxcXFxzQS1aYS16XSkpKFteJV18J1teJ10qJ3xcXFxcXFxcIlteXFxcXFxcXCJdKlxcXFxcXFwiKSp8KFxcXFxcXFxcLig/PVxcXFxcXFxccyl8XFxcXFxcXFwuW0EtWmEtel18KD89XFxcXFxcXFx7KSkoW14oPSdcXFxcXFxcIiVdfD09fCdbXiddKid8XFxcXFxcXCJbXlxcXFxcXFwiXSpcXFxcXFxcInxcXFxcXFxcXCh8XFxcXFxcXFwoW14pJV0qXFxcXFxcXFwpfFxcXFxcXFxcW3xcXFxcXFxcXFtbXlxcXFxcXFxcXSVdKl18XFxcXFxcXFx7fFxcXFxcXFxce1tefSVdKn0pKihcXFxcXFxcXC5cXFxcXFxcXC5cXFxcXFxcXC5bXiVdKik/KCg/PSUpfCQpKSkoJS4qKT8kXFxcIn0sXFxcImNvbW1lbnRfYmxvY2tcXFwiOntcXFwiYmVnaW5cXFwiOlxcXCIoXlxcXFxcXFxccyopJVxcXFxcXFxce1teXFxcXFxcXFxuXFxcXFxcXFxTXSorXFxcXFxcXFxuXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLmNvbW1lbnQubWF0bGFiXFxcIn19LFxcXCJlbmRcXFwiOlxcXCJeXFxcXFxcXFxzKiV9W15cXFxcXFxcXG5cXFxcXFxcXFNdKisoPzpcXFxcXFxcXG58JClcXFwiLFxcXCJuYW1lXFxcIjpcXFwiY29tbWVudC5ibG9jay5wZXJjZW50YWdlLm1hdGxhYlxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2NvbW1lbnRfYmxvY2tcXFwifSx7XFxcIm1hdGNoXFxcIjpcXFwiXlteXFxcXFxcXFxuXSpcXFxcXFxcXG5cXFwifV19LFxcXCJjb21tZW50c1xcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiYmVnaW5cXFwiOlxcXCIoXlsgXFxcXFxcXFx0XSspPyg/PSUlXFxcXFxcXFxzKVxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24ud2hpdGVzcGFjZS5jb21tZW50LmxlYWRpbmcubWF0bGFiXFxcIn19LFxcXCJlbmRcXFwiOlxcXCIoPyFcXFxcXFxcXEcpXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImJlZ2luXFxcIjpcXFwiJSVcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24uY29tbWVudC5tYXRsYWJcXFwifX0sXFxcImVuZFxcXCI6XFxcIlxcXFxcXFxcblxcXCIsXFxcIm5hbWVcXFwiOlxcXCJjb21tZW50LmxpbmUuZG91YmxlLXBlcmNlbnRhZ2UubWF0bGFiXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXFxHW15cXFxcXFxcXFNcXFxcXFxcXG5dKig/IVtcXFxcXFxcXG5cXFxcXFxcXHNdKVxcXCIsXFxcImNvbnRlbnROYW1lXFxcIjpcXFwibWV0YS5jZWxsLm1hdGxhYlxcXCIsXFxcImVuZFxcXCI6XFxcIig/PVxcXFxcXFxcbilcXFwifV19XX0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2NvbW1lbnRfYmxvY2tcXFwifSx7XFxcImJlZ2luXFxcIjpcXFwiKF5bIFxcXFxcXFxcdF0rKT8oPz0lKVxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24ud2hpdGVzcGFjZS5jb21tZW50LmxlYWRpbmcubWF0bGFiXFxcIn19LFxcXCJlbmRcXFwiOlxcXCIoPyFcXFxcXFxcXEcpXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImJlZ2luXFxcIjpcXFwiJVxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uZGVmaW5pdGlvbi5jb21tZW50Lm1hdGxhYlxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiXFxcXFxcXFxuXFxcIixcXFwibmFtZVxcXCI6XFxcImNvbW1lbnQubGluZS5wZXJjZW50YWdlLm1hdGxhYlxcXCJ9XX1dfSxcXFwiY29uanVnYXRlX3RyYW5zcG9zZVxcXCI6e1xcXCJtYXRjaFxcXCI6XFxcIigoPzw9XFxcXFxcXFxTKXwoPzw9XSl8KD88PVxcXFxcXFxcKSl8KD88PX0pKSdcXFwiLFxcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5vcGVyYXRvci50cmFuc3Bvc2UubWF0bGFiXFxcIn0sXFxcImNvbnN0YW50c1xcXCI6e1xcXCJtYXRjaFxcXCI6XFxcIig/PCFcXFxcXFxcXC4pXFxcXFxcXFxiKGVwc3xmYWxzZXxJbmZ8aW5mfGludG1heHxpbnRtaW58bmFtZWxlbmd0aG1heHxOYU58bmFufG9ufG9mZnxyZWFsbWF4fHJlYWxtaW58dHJ1ZXxwaSlcXFxcXFxcXGJcXFwiLFxcXCJuYW1lXFxcIjpcXFwiY29uc3RhbnQubGFuZ3VhZ2UubWF0bGFiXFxcIn0sXFxcImNvbnRyb2xfc3RhdGVtZW50c1xcXCI6e1xcXCJjYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLmNvbnRyb2wubWF0bGFiXFxcIn19LFxcXCJtYXRjaFxcXCI6XFxcIlxcXFxcXFxccyooPzpefFtcXFxcXFxcXHMsO10pKGJyZWFrfGNvbnRpbnVlfHJldHVybilcXFxcXFxcXGJcXFwiLFxcXCJuYW1lXFxcIjpcXFwibWV0YS5jb250cm9sLm1hdGxhYlxcXCJ9LFxcXCJjdXJseV9icmFja2V0c1xcXCI6e1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxce1xcXCIsXFxcImVuZFxcXCI6XFxcIn1cXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNlbmRfaW5fcGFyZW5zXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2FsbF9iZWZvcmVfY29tbWFuZF9kdWFsXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2FsbF9hZnRlcl9jb21tYW5kX2R1YWxcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjZW5kX2luX3BhcmVuc1xcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNibG9ja19rZXl3b3Jkc1xcXCJ9XX0sXFxcImVuZF9pbl9wYXJlbnNcXFwiOntcXFwibWF0Y2hcXFwiOlxcXCJcXFxcXFxcXGJlbmRcXFxcXFxcXGJcXFwiLFxcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5vcGVyYXRvci5zeW1ib2xzLm1hdGxhYlxcXCJ9LFxcXCJmdW5jdGlvblxcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiYmVnaW5cXFwiOlxcXCIoXlxcXFxcXFxccyopKGZ1bmN0aW9uKVxcXFxcXFxccysoPzooPzooXFxcXFxcXFxbKShbXlxcXFxcXFxcXV0qKShdKXwoW2EtekEtWl1bYS16QS1aMC05X10qKSlcXFxcXFxcXHMqPVxcXFxcXFxccyopPyhbYS16QS1aXVthLXpBLVowLTlfXSooXFxcXFxcXFwuW2EtekEtWl1bYS16QS1aMC05X10qKSopXFxcXFxcXFxzKlxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwic3RvcmFnZS50eXBlLmZ1bmN0aW9uLm1hdGxhYlxcXCJ9LFxcXCIzXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLmFyZ3VtZW50cy5iZWdpbi5tYXRsYWJcXFwifSxcXFwiNFxcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwibWF0Y2hcXFwiOlxcXCJcXFxcXFxcXHcrXFxcIixcXFwibmFtZVxcXCI6XFxcInZhcmlhYmxlLnBhcmFtZXRlci5vdXRwdXQubWF0bGFiXFxcIn1dfSxcXFwiNVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uZGVmaW5pdGlvbi5hcmd1bWVudHMuZW5kLm1hdGxhYlxcXCJ9LFxcXCI2XFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJ2YXJpYWJsZS5wYXJhbWV0ZXIub3V0cHV0LmZ1bmN0aW9uLm1hdGxhYlxcXCJ9LFxcXCI3XFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJlbnRpdHkubmFtZS5mdW5jdGlvbi5tYXRsYWJcXFwifX0sXFxcImVuZFxcXCI6XFxcIlxcXFxcXFxccyooPzpefFtcXFxcXFxcXHMsO10pKGVuZClcXFxcXFxcXGIoXFxcXFxcXFxzKlxcXFxcXFxcbik/XFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5jb250cm9sLmVuZC5mdW5jdGlvbi5tYXRsYWJcXFwifX0sXFxcIm5hbWVcXFwiOlxcXCJtZXRhLmZ1bmN0aW9uLm1hdGxhYlxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxcR1xcXFxcXFxcKFxcXCIsXFxcImVuZFxcXCI6XFxcIlxcXFxcXFxcKVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJtZXRhLmFyZ3VtZW50cy5mdW5jdGlvbi5tYXRsYWJcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNsaW5lX2NvbnRpbnVhdGlvblxcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCJcXFxcXFxcXHcrXFxcIixcXFwibmFtZVxcXCI6XFxcInZhcmlhYmxlLnBhcmFtZXRlci5pbnB1dC5tYXRsYWJcXFwifV19LHtcXFwiYmVnaW5cXFwiOlxcXCIoXlxcXFxcXFxccyopKGFyZ3VtZW50cylcXFxcXFxcXGIoW14lXSopXFxcXFxcXFxzKihcXFxcXFxcXChbXildKlxcXFxcXFxcKSk/XFxcXFxcXFxzKigkfCg/PSUpKVxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5jb250cm9sLmFyZ3VtZW50cy5tYXRsYWJcXFwifSxcXFwiM1xcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwibWF0Y2hcXFwiOlxcXCJbYS16QS1aXVthLXpBLVowLTlfXSpcXFwiLFxcXCJuYW1lXFxcIjpcXFwidmFyaWFibGUucGFyYW1ldGVyLmFyZ3VtZW50cy5tYXRsYWJcXFwifV19fSxcXFwiZW5kXFxcIjpcXFwiXFxcXFxcXFxzKig/Ol58W1xcXFxcXFxccyw7XSkoZW5kKVxcXFxcXFxcYlxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQuY29udHJvbC5lbmQuYXJndW1lbnRzLm1hdGxhYlxcXCJ9fSxcXFwibmFtZVxcXCI6XFxcIm1ldGEuYXJndW1lbnRzLm1hdGxhYlxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3ZhbGlkYXRvcnNcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIkc2VsZlxcXCJ9XX0se1xcXCJpbmNsdWRlXFxcIjpcXFwiJHNlbGZcXFwifV19XX0sXFxcImdsb2JhbF9wZXJzaXN0ZW50XFxcIjp7XFxcImNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQuY29udHJvbC5nbG9iYWxwZXJzaXN0ZW50Lm1hdGxhYlxcXCJ9fSxcXFwibWF0Y2hcXFwiOlxcXCJeXFxcXFxcXFxzKihnbG9iYWx8cGVyc2lzdGVudClcXFxcXFxcXGJcXFwiLFxcXCJuYW1lXFxcIjpcXFwibWV0YS5nbG9iYWxwZXJzaXN0ZW50Lm1hdGxhYlxcXCJ9LFxcXCJpbmRleGluZ19jdXJseV9icmFja2V0c1xcXCI6e1xcXCJDb21tZW50XFxcIjpcXFwiTWF0Y2ggaWRlbnRpZmllcntpZHgsIGlkeCwgfSBhbmQgc3RvcCBhdCBuZXdsaW5lIHdpdGhvdXQgLi4uIFRoaXMgaGVscHMgd2l0aCBwYXJ0aWFsbHkgd3JpdHRlbiBjb2RlIGxpa2UgeHtpZHggXFxcIixcXFwiYmVnaW5cXFwiOlxcXCIoW2EtekEtWl1bYS16QS1aMC05Xy5dKlxcXFxcXFxccyopXFxcXFxcXFx7XFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiJHNlbGZcXFwifV19fSxcXFwiZW5kXFxcIjpcXFwiKH18KD88IVxcXFxcXFxcLlxcXFxcXFxcLlxcXFxcXFxcLikuXFxcXFxcXFxuKVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2VuZF9pbl9wYXJlbnNcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjYWxsX2JlZm9yZV9jb21tYW5kX2R1YWxcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjYWxsX2FmdGVyX2NvbW1hbmRfZHVhbFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNlbmRfaW5fcGFyZW5zXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2Jsb2NrX2tleXdvcmRzXFxcIn1dfSxcXFwibGluZV9jb250aW51YXRpb25cXFwiOntcXFwiY2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5vcGVyYXRvci5zeW1ib2xzLm1hdGxhYlxcXCJ9LFxcXCIyXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJjb21tZW50LmxpbmUuY29udGludWF0aW9uLm1hdGxhYlxcXCJ9fSxcXFwibWF0Y2hcXFwiOlxcXCIoXFxcXFxcXFwuXFxcXFxcXFwuXFxcXFxcXFwuKSguKikkXFxcIixcXFwibmFtZVxcXCI6XFxcIm1ldGEubGluZWNvbnRpbnVhdGlvbi5tYXRsYWJcXFwifSxcXFwibnVtYmVyc1xcXCI6e1xcXCJtYXRjaFxcXCI6XFxcIig/PD1bXFxcXFxcXFxzXFxcXFxcXFwtKyovXFxcXFxcXFxcXFxcXFxcXD06XFxcXFxcXFxbKHssXXxeKVxcXFxcXFxcZCpcXFxcXFxcXC4/XFxcXFxcXFxkKyhbZUVdWystXT9cXFxcXFxcXGQpPyhbMC05JiZbXi5dXSkqKFtpal0pP1xcXFxcXFxcYlxcXCIsXFxcIm5hbWVcXFwiOlxcXCJjb25zdGFudC5udW1lcmljLm1hdGxhYlxcXCJ9LFxcXCJvcGVyYXRvcnNcXFwiOntcXFwibWF0Y2hcXFwiOlxcXCIoPzw9XFxcXFxcXFxzKSg9PXx+PXw+fD49fDx8PD18JnwmJnxbOnxdfFxcXFxcXFxcfFxcXFxcXFxcfHxbK1xcXFxcXFxcLSpdfFxcXFxcXFxcLlxcXFxcXFxcKnwvfFxcXFxcXFxcLi98XFxcXFxcXFxcXFxcXFxcXHxcXFxcXFxcXC5cXFxcXFxcXFxcXFxcXFxcfFxcXFxcXFxcXnxcXFxcXFxcXC5cXFxcXFxcXF4pKD89XFxcXFxcXFxzKVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLm9wZXJhdG9yLnN5bWJvbHMubWF0bGFiXFxcIn0sXFxcInBhcmVuc1xcXCI6e1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxcKFxcXCIsXFxcImVuZFxcXCI6XFxcIihcXFxcXFxcXCl8KD88IVxcXFxcXFxcLlxcXFxcXFxcLlxcXFxcXFxcLikuXFxcXFxcXFxuKVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2VuZF9pbl9wYXJlbnNcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjYWxsX2JlZm9yZV9jb21tYW5kX2R1YWxcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjYWxsX2FmdGVyX2NvbW1hbmRfZHVhbFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNibG9ja19rZXl3b3Jkc1xcXCJ9XX0sXFxcInNxdWFyZV9icmFja2V0c1xcXCI6e1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxcW1xcXCIsXFxcImVuZFxcXCI6XFxcIl1cXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNhbGxfYmVmb3JlX2NvbW1hbmRfZHVhbFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNhbGxfYWZ0ZXJfY29tbWFuZF9kdWFsXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2Jsb2NrX2tleXdvcmRzXFxcIn1dfSxcXFwic3RyaW5nXFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJjYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJzdHJpbmcuaW50ZXJwb2xhdGVkLm1hdGxhYlxcXCJ9LFxcXCIyXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLnN0cmluZy5iZWdpbi5tYXRsYWJcXFwifX0sXFxcIm1hdGNoXFxcIjpcXFwiXlxcXFxcXFxccyooKCEpLiokXFxcXFxcXFxuPylcXFwifSx7XFxcImJlZ2luXFxcIjpcXFwiKCg/PD0oW1xcXFxcXFxcWyh7PVxcXFxcXFxcczs6LH48PlxcXFxcXFxcJnxcXFxcXFxcXC0rKi9cXFxcXFxcXFxcXFxcXFxcLl5dKSl8XiknXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLnN0cmluZy5iZWdpbi5tYXRsYWJcXFwifX0sXFxcImVuZFxcXCI6XFxcIicoPz0oW1xcXFxcXFxcWyh7XFxcXFxcXFxdKX09fjw+XFxcXFxcXFwmfFxcXFxcXFxcLSsqL1xcXFxcXFxcXFxcXFxcXFwuXlxcXFxcXFxcczs6LF0pKVxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24uc3RyaW5nLmVuZC5tYXRsYWJcXFwifX0sXFxcIm5hbWVcXFwiOlxcXCJzdHJpbmcucXVvdGVkLnNpbmdsZS5tYXRsYWJcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwibWF0Y2hcXFwiOlxcXCInJ1xcXCIsXFxcIm5hbWVcXFwiOlxcXCJjb25zdGFudC5jaGFyYWN0ZXIuZXNjYXBlLm1hdGxhYlxcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCInKD89LilcXFwiLFxcXCJuYW1lXFxcIjpcXFwiaW52YWxpZC5pbGxlZ2FsLnVuZXNjYXBlZC1xdW90ZS5tYXRsYWJcXFwifSx7XFxcIm1hdGNoXFxcIjpcXFwiKCglKFsrXFxcXFxcXFwtMF0/XFxcXFxcXFxkezAsM30oXFxcXFxcXFwuXFxcXFxcXFxkezEsM30pPykoW2NkZUVmZ0dzXXwoKFtidF0pPyhbb3V4WF0pKSkpfCUlfFxcXFxcXFxcXFxcXFxcXFwoW2JmbnJ0XFxcXFxcXFxcXFxcXFxcXF0pKVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJjb25zdGFudC5jaGFyYWN0ZXIuZXNjYXBlLm1hdGxhYlxcXCJ9XX0se1xcXCJiZWdpblxcXCI6XFxcIigoPzw9KFtcXFxcXFxcXFsoez1cXFxcXFxcXHM7Oix+PD5cXFxcXFxcXCZ8XFxcXFxcXFwtKyovXFxcXFxcXFxcXFxcXFxcXC5eXSkpfF4pXFxcXFxcXCJcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24uc3RyaW5nLmJlZ2luLm1hdGxhYlxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiXFxcXFxcXCIoPz0oW1xcXFxcXFxcWyh7XFxcXFxcXFxdKX09fjw+XFxcXFxcXFwmfFxcXFxcXFxcLSsqL1xcXFxcXFxcXFxcXFxcXFwuXlxcXFxcXFxcczs6LF0pKVxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24uc3RyaW5nLmVuZC5tYXRsYWJcXFwifX0sXFxcIm5hbWVcXFwiOlxcXCJzdHJpbmcucXVvdGVkLmRvdWJsZS5tYXRsYWJcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwibWF0Y2hcXFwiOlxcXCJcXFxcXFxcIlxcXFxcXFwiXFxcIixcXFwibmFtZVxcXCI6XFxcImNvbnN0YW50LmNoYXJhY3Rlci5lc2NhcGUubWF0bGFiXFxcIn0se1xcXCJtYXRjaFxcXCI6XFxcIlxcXFxcXFwiKD89LilcXFwiLFxcXCJuYW1lXFxcIjpcXFwiaW52YWxpZC5pbGxlZ2FsLnVuZXNjYXBlZC1xdW90ZS5tYXRsYWJcXFwifV19XX0sXFxcInRyYW5zcG9zZVxcXCI6e1xcXCJtYXRjaFxcXCI6XFxcIlxcXFxcXFxcLidcXFwiLFxcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5vcGVyYXRvci50cmFuc3Bvc2UubWF0bGFiXFxcIn0sXFxcInZhbGlkYXRvcl9zdHJpbmdzXFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiYmVnaW5cXFwiOlxcXCIoKD88PShbXFxcXFxcXFxbKHs9XFxcXFxcXFxzOzosfjw+XFxcXFxcXFwmfFxcXFxcXFxcLSsqL1xcXFxcXFxcXFxcXFxcXFwuXl0pKXxeKSdcXFwiLFxcXCJlbmRcXFwiOlxcXCInKD89KFtcXFxcXFxcXFsoe1xcXFxcXFxcXSl9PX48PlxcXFxcXFxcJnxcXFxcXFxcXC0rKi9cXFxcXFxcXFxcXFxcXFxcLl5cXFxcXFxcXHM7OixdKSlcXFwiLFxcXCJuYW1lXFxcIjpcXFwic3RvcmFnZS50eXBlLm1hdGxhYlxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJtYXRjaFxcXCI6XFxcIicnXFxcIn0se1xcXCJtYXRjaFxcXCI6XFxcIicoPz0uKVxcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCIoW14nXSspXFxcIn1dfSx7XFxcImJlZ2luXFxcIjpcXFwiKCg/PD0oW1xcXFxcXFxcWyh7PVxcXFxcXFxcczs6LH48PlxcXFxcXFxcJnxcXFxcXFxcXC0rKi9cXFxcXFxcXFxcXFxcXFxcLl5dKSl8XilcXFxcXFxcIlxcXCIsXFxcImVuZFxcXCI6XFxcIlxcXFxcXFwiKD89KFtcXFxcXFxcXFsoe1xcXFxcXFxcXSl9PX48PlxcXFxcXFxcJnxcXFxcXFxcXC0rKi9cXFxcXFxcXFxcXFxcXFxcLl5cXFxcXFxcXHM7OixdKSlcXFwiLFxcXCJuYW1lXFxcIjpcXFwic3RvcmFnZS50eXBlLm1hdGxhYlxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJtYXRjaFxcXCI6XFxcIlxcXFxcXFwiXFxcXFxcXCJcXFwifSx7XFxcIm1hdGNoXFxcIjpcXFwiXFxcXFxcXCIoPz0uKVxcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCJbXlxcXFxcXFwiXStcXFwifV19XX1dfSxcXFwidmFsaWRhdG9yc1xcXCI6e1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxccyo7P1xcXFxcXFxccyooW2EtekEtWl1bYS16QS1aMC05Xy4/XSopXFxcIixcXFwiZW5kXFxcIjpcXFwiKFs7XFxcXFxcXFxuJT1dLiopXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiY2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiRzZWxmXFxcIn1dfX0sXFxcIm1hdGNoXFxcIjpcXFwiKCUuKilcXFwifSx7XFxcImNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIkc2VsZlxcXCJ9XX19LFxcXCJtYXRjaFxcXCI6XFxcIig9W147XSopXFxcIn0se1xcXCJjYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3ZhbGlkYXRvcnNcXFwifV19fSxcXFwibWF0Y2hcXFwiOlxcXCIoW1xcXFxcXFxcbjtdXFxcXFxcXFxzKlthLXpBLVpdLiopXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiJHNlbGZcXFwifV19fSxcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjbGluZV9jb250aW51YXRpb25cXFwifSx7XFxcIm1hdGNoXFxcIjpcXFwiXFxcXFxcXFxzKihcXFxcXFxcXChbXildKlxcXFxcXFxcKSlcXFwiLFxcXCJuYW1lXFxcIjpcXFwic3RvcmFnZS50eXBlLm1hdGxhYlxcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCIoW2EtekEtWl1bYS16QS1aMC05Xy5dKilcXFwiLFxcXCJuYW1lXFxcIjpcXFwic3RvcmFnZS50eXBlLm1hdGxhYlxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNicmFjZWRfdmFsaWRhdG9yX2xpc3RcXFwifV19LFxcXCJ2YXJpYWJsZXNcXFwiOntcXFwibWF0Y2hcXFwiOlxcXCIoPzwhXFxcXFxcXFwuKVxcXFxcXFxcYihuYXJnaW58bmFyZ291dHx2YXJhcmdpbnx2YXJhcmdvdXQpXFxcXFxcXFxiXFxcIixcXFwibmFtZVxcXCI6XFxcInZhcmlhYmxlLm90aGVyLmZ1bmN0aW9uLm1hdGxhYlxcXCJ9fSxcXFwic2NvcGVOYW1lXFxcIjpcXFwic291cmNlLm1hdGxhYlxcXCJ9XCIpKVxuXG5leHBvcnQgZGVmYXVsdCBbXG5sYW5nXG5dXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/matlab.mjs\n"));

/***/ })

}]);