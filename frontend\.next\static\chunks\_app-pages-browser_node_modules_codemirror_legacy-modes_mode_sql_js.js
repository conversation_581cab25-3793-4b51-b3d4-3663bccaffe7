"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_codemirror_legacy-modes_mode_sql_js"],{

/***/ "(app-pages-browser)/./node_modules/@codemirror/legacy-modes/mode/sql.js":
/*!***********************************************************!*\
  !*** ./node_modules/@codemirror/legacy-modes/mode/sql.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cassandra: () => (/* binding */ cassandra),\n/* harmony export */   esper: () => (/* binding */ esper),\n/* harmony export */   gpSQL: () => (/* binding */ gpSQL),\n/* harmony export */   gql: () => (/* binding */ gql),\n/* harmony export */   hive: () => (/* binding */ hive),\n/* harmony export */   mariaDB: () => (/* binding */ mariaDB),\n/* harmony export */   msSQL: () => (/* binding */ msSQL),\n/* harmony export */   mySQL: () => (/* binding */ mySQL),\n/* harmony export */   pgSQL: () => (/* binding */ pgSQL),\n/* harmony export */   plSQL: () => (/* binding */ plSQL),\n/* harmony export */   sparkSQL: () => (/* binding */ sparkSQL),\n/* harmony export */   sql: () => (/* binding */ sql),\n/* harmony export */   sqlite: () => (/* binding */ sqlite),\n/* harmony export */   standardSQL: () => (/* binding */ standardSQL)\n/* harmony export */ });\nfunction sql(parserConfig) {\n  var client         = parserConfig.client || {},\n      atoms          = parserConfig.atoms || {\"false\": true, \"true\": true, \"null\": true},\n      builtin        = parserConfig.builtin || set(defaultBuiltin),\n      keywords       = parserConfig.keywords || set(sqlKeywords),\n      operatorChars  = parserConfig.operatorChars || /^[*+\\-%<>!=&|~^\\/]/,\n      support        = parserConfig.support || {},\n      hooks          = parserConfig.hooks || {},\n      dateSQL        = parserConfig.dateSQL || {\"date\" : true, \"time\" : true, \"timestamp\" : true},\n      backslashStringEscapes = parserConfig.backslashStringEscapes !== false,\n      brackets       = parserConfig.brackets || /^[\\{}\\(\\)\\[\\]]/,\n      punctuation    = parserConfig.punctuation || /^[;.,:]/\n\n  function tokenBase(stream, state) {\n    var ch = stream.next();\n\n    // call hooks from the mime type\n    if (hooks[ch]) {\n      var result = hooks[ch](stream, state);\n      if (result !== false) return result;\n    }\n\n    if (support.hexNumber &&\n      ((ch == \"0\" && stream.match(/^[xX][0-9a-fA-F]+/))\n      || (ch == \"x\" || ch == \"X\") && stream.match(/^'[0-9a-fA-F]*'/))) {\n      // hex\n      // ref: http://dev.mysql.com/doc/refman/5.5/en/hexadecimal-literals.html\n      return \"number\";\n    } else if (support.binaryNumber &&\n      (((ch == \"b\" || ch == \"B\") && stream.match(/^'[01]+'/))\n      || (ch == \"0\" && stream.match(/^b[01]*/)))) {\n      // bitstring\n      // ref: http://dev.mysql.com/doc/refman/5.5/en/bit-field-literals.html\n      return \"number\";\n    } else if (ch.charCodeAt(0) > 47 && ch.charCodeAt(0) < 58) {\n      // numbers\n      // ref: http://dev.mysql.com/doc/refman/5.5/en/number-literals.html\n      stream.match(/^[0-9]*(\\.[0-9]+)?([eE][-+]?[0-9]+)?/);\n      support.decimallessFloat && stream.match(/^\\.(?!\\.)/);\n      return \"number\";\n    } else if (ch == \"?\" && (stream.eatSpace() || stream.eol() || stream.eat(\";\"))) {\n      // placeholders\n      return \"macroName\";\n    } else if (ch == \"'\" || (ch == '\"' && support.doubleQuote)) {\n      // strings\n      // ref: http://dev.mysql.com/doc/refman/5.5/en/string-literals.html\n      state.tokenize = tokenLiteral(ch);\n      return state.tokenize(stream, state);\n    } else if ((((support.nCharCast && (ch == \"n\" || ch == \"N\"))\n        || (support.charsetCast && ch == \"_\" && stream.match(/[a-z][a-z0-9]*/i)))\n        && (stream.peek() == \"'\" || stream.peek() == '\"'))) {\n      // charset casting: _utf8'str', N'str', n'str'\n      // ref: http://dev.mysql.com/doc/refman/5.5/en/string-literals.html\n      return \"keyword\";\n    } else if (support.escapeConstant && (ch == \"e\" || ch == \"E\")\n        && (stream.peek() == \"'\" || (stream.peek() == '\"' && support.doubleQuote))) {\n      // escape constant: E'str', e'str'\n      // ref: https://www.postgresql.org/docs/current/sql-syntax-lexical.html#SQL-SYNTAX-STRINGS-ESCAPE\n      state.tokenize = function(stream, state) {\n        return (state.tokenize = tokenLiteral(stream.next(), true))(stream, state);\n      }\n      return \"keyword\";\n    } else if (support.commentSlashSlash && ch == \"/\" && stream.eat(\"/\")) {\n      // 1-line comment\n      stream.skipToEnd();\n      return \"comment\";\n    } else if ((support.commentHash && ch == \"#\")\n        || (ch == \"-\" && stream.eat(\"-\") && (!support.commentSpaceRequired || stream.eat(\" \")))) {\n      // 1-line comments\n      // ref: https://kb.askmonty.org/en/comment-syntax/\n      stream.skipToEnd();\n      return \"comment\";\n    } else if (ch == \"/\" && stream.eat(\"*\")) {\n      // multi-line comments\n      // ref: https://kb.askmonty.org/en/comment-syntax/\n      state.tokenize = tokenComment(1);\n      return state.tokenize(stream, state);\n    } else if (ch == \".\") {\n      // .1 for 0.1\n      if (support.zerolessFloat && stream.match(/^(?:\\d+(?:e[+-]?\\d+)?)/i))\n        return \"number\";\n      if (stream.match(/^\\.+/))\n        return null\n      // .table_name (ODBC)\n      // // ref: http://dev.mysql.com/doc/refman/5.6/en/identifier-qualifiers.html\n      if (support.ODBCdotTable && stream.match(/^[\\w\\d_$#]+/))\n        return \"type\";\n    } else if (operatorChars.test(ch)) {\n      // operators\n      stream.eatWhile(operatorChars);\n      return \"operator\";\n    } else if (brackets.test(ch)) {\n      // brackets\n      return \"bracket\";\n    } else if (punctuation.test(ch)) {\n      // punctuation\n      stream.eatWhile(punctuation);\n      return \"punctuation\";\n    } else if (ch == '{' &&\n        (stream.match(/^( )*(d|D|t|T|ts|TS)( )*'[^']*'( )*}/) || stream.match(/^( )*(d|D|t|T|ts|TS)( )*\"[^\"]*\"( )*}/))) {\n      // dates (weird ODBC syntax)\n      // ref: http://dev.mysql.com/doc/refman/5.5/en/date-and-time-literals.html\n      return \"number\";\n    } else {\n      stream.eatWhile(/^[_\\w\\d]/);\n      var word = stream.current().toLowerCase();\n      // dates (standard SQL syntax)\n      // ref: http://dev.mysql.com/doc/refman/5.5/en/date-and-time-literals.html\n      if (dateSQL.hasOwnProperty(word) && (stream.match(/^( )+'[^']*'/) || stream.match(/^( )+\"[^\"]*\"/)))\n        return \"number\";\n      if (atoms.hasOwnProperty(word)) return \"atom\";\n      if (builtin.hasOwnProperty(word)) return \"type\";\n      if (keywords.hasOwnProperty(word)) return \"keyword\";\n      if (client.hasOwnProperty(word)) return \"builtin\";\n      return null;\n    }\n  }\n\n  // 'string', with char specified in quote escaped by '\\'\n  function tokenLiteral(quote, backslashEscapes) {\n    return function(stream, state) {\n      var escaped = false, ch;\n      while ((ch = stream.next()) != null) {\n        if (ch == quote && !escaped) {\n          state.tokenize = tokenBase;\n          break;\n        }\n        escaped = (backslashStringEscapes || backslashEscapes) && !escaped && ch == \"\\\\\";\n      }\n      return \"string\";\n    };\n  }\n  function tokenComment(depth) {\n    return function(stream, state) {\n      var m = stream.match(/^.*?(\\/\\*|\\*\\/)/)\n      if (!m) stream.skipToEnd()\n      else if (m[1] == \"/*\") state.tokenize = tokenComment(depth + 1)\n      else if (depth > 1) state.tokenize = tokenComment(depth - 1)\n      else state.tokenize = tokenBase\n      return \"comment\"\n    }\n  }\n\n  function pushContext(stream, state, type) {\n    state.context = {\n      prev: state.context,\n      indent: stream.indentation(),\n      col: stream.column(),\n      type: type\n    };\n  }\n\n  function popContext(state) {\n    state.indent = state.context.indent;\n    state.context = state.context.prev;\n  }\n\n  return {\n    name: \"sql\",\n\n    startState: function() {\n      return {tokenize: tokenBase, context: null};\n    },\n\n    token: function(stream, state) {\n      if (stream.sol()) {\n        if (state.context && state.context.align == null)\n          state.context.align = false;\n      }\n      if (state.tokenize == tokenBase && stream.eatSpace()) return null;\n\n      var style = state.tokenize(stream, state);\n      if (style == \"comment\") return style;\n\n      if (state.context && state.context.align == null)\n        state.context.align = true;\n\n      var tok = stream.current();\n      if (tok == \"(\")\n        pushContext(stream, state, \")\");\n      else if (tok == \"[\")\n        pushContext(stream, state, \"]\");\n      else if (state.context && state.context.type == tok)\n        popContext(state);\n      return style;\n    },\n\n    indent: function(state, textAfter, iCx) {\n      var cx = state.context;\n      if (!cx) return null;\n      var closing = textAfter.charAt(0) == cx.type;\n      if (cx.align) return cx.col + (closing ? 0 : 1);\n      else return cx.indent + (closing ? 0 : iCx.unit);\n    },\n\n    languageData: {\n      commentTokens: {\n        line: support.commentSlashSlash ? \"//\" : support.commentHash ? \"#\" : \"--\",\n        block: {open: \"/*\", close: \"*/\"}\n      },\n      closeBrackets: {brackets: [\"(\", \"[\", \"{\", \"'\", '\"', \"`\"]}\n    }\n  };\n};\n\n// `identifier`\nfunction hookIdentifier(stream) {\n  // MySQL/MariaDB identifiers\n  // ref: http://dev.mysql.com/doc/refman/5.6/en/identifier-qualifiers.html\n  var ch;\n  while ((ch = stream.next()) != null) {\n    if (ch == \"`\" && !stream.eat(\"`\")) return \"string.special\";\n  }\n  stream.backUp(stream.current().length - 1);\n  return stream.eatWhile(/\\w/) ? \"string.special\" : null;\n}\n\n// \"identifier\"\nfunction hookIdentifierDoublequote(stream) {\n  // Standard SQL /SQLite identifiers\n  // ref: http://web.archive.org/web/20160813185132/http://savage.net.au/SQL/sql-99.bnf.html#delimited%20identifier\n  // ref: http://sqlite.org/lang_keywords.html\n  var ch;\n  while ((ch = stream.next()) != null) {\n    if (ch == \"\\\"\" && !stream.eat(\"\\\"\")) return \"string.special\";\n  }\n  stream.backUp(stream.current().length - 1);\n  return stream.eatWhile(/\\w/) ? \"string.special\" : null;\n}\n\n// variable token\nfunction hookVar(stream) {\n  // variables\n  // @@prefix.varName @varName\n  // varName can be quoted with ` or ' or \"\n  // ref: http://dev.mysql.com/doc/refman/5.5/en/user-variables.html\n  if (stream.eat(\"@\")) {\n    stream.match('session.');\n    stream.match('local.');\n    stream.match('global.');\n  }\n\n  if (stream.eat(\"'\")) {\n    stream.match(/^.*'/);\n    return \"string.special\";\n  } else if (stream.eat('\"')) {\n    stream.match(/^.*\"/);\n    return \"string.special\";\n  } else if (stream.eat(\"`\")) {\n    stream.match(/^.*`/);\n    return \"string.special\";\n  } else if (stream.match(/^[0-9a-zA-Z$\\.\\_]+/)) {\n    return \"string.special\";\n  }\n  return null;\n};\n\n// short client keyword token\nfunction hookClient(stream) {\n  // \\N means NULL\n  // ref: http://dev.mysql.com/doc/refman/5.5/en/null-values.html\n  if (stream.eat(\"N\")) {\n    return \"atom\";\n  }\n  // \\g, etc\n  // ref: http://dev.mysql.com/doc/refman/5.5/en/mysql-commands.html\n  return stream.match(/^[a-zA-Z.#!?]/) ? \"string.special\" : null;\n}\n\n// these keywords are used by all SQL dialects (however, a mode can still overwrite it)\nvar sqlKeywords = \"alter and as asc between by count create delete desc distinct drop from group having in insert into is join like not on or order select set table union update values where limit \";\n\n// turn a space-separated list into an array\nfunction set(str) {\n  var obj = {}, words = str.split(\" \");\n  for (var i = 0; i < words.length; ++i) obj[words[i]] = true;\n  return obj;\n}\n\nvar defaultBuiltin = \"bool boolean bit blob enum long longblob longtext medium mediumblob mediumint mediumtext time timestamp tinyblob tinyint tinytext text bigint int int1 int2 int3 int4 int8 integer float float4 float8 double char varbinary varchar varcharacter precision real date datetime year unsigned signed decimal numeric\"\n\n// A generic SQL Mode. It's not a standard, it just try to support what is generally supported\nconst standardSQL = sql({\n  keywords: set(sqlKeywords + \"begin\"),\n  builtin: set(defaultBuiltin),\n  atoms: set(\"false true null unknown\"),\n  dateSQL: set(\"date time timestamp\"),\n  support: set(\"ODBCdotTable doubleQuote binaryNumber hexNumber\")\n});\n\nconst msSQL = sql({\n  client: set(\"$partition binary_checksum checksum connectionproperty context_info current_request_id error_line error_message error_number error_procedure error_severity error_state formatmessage get_filestream_transaction_context getansinull host_id host_name isnull isnumeric min_active_rowversion newid newsequentialid rowcount_big xact_state object_id\"),\n  keywords: set(sqlKeywords + \"begin trigger proc view index for add constraint key primary foreign collate clustered nonclustered declare exec go if use index holdlock nolock nowait paglock readcommitted readcommittedlock readpast readuncommitted repeatableread rowlock serializable snapshot tablock tablockx updlock with\"),\n  builtin: set(\"bigint numeric bit smallint decimal smallmoney int tinyint money float real char varchar text nchar nvarchar ntext binary varbinary image cursor timestamp hierarchyid uniqueidentifier sql_variant xml table \"),\n  atoms: set(\"is not null like and or in left right between inner outer join all any some cross unpivot pivot exists\"),\n  operatorChars: /^[*+\\-%<>!=^\\&|\\/]/,\n  brackets: /^[\\{}\\(\\)]/,\n  punctuation: /^[;.,:/]/,\n  backslashStringEscapes: false,\n  dateSQL: set(\"date datetimeoffset datetime2 smalldatetime datetime time\"),\n  hooks: {\n    \"@\":   hookVar\n  }\n});\n\nconst mySQL = sql({\n  client: set(\"charset clear connect edit ego exit go help nopager notee nowarning pager print prompt quit rehash source status system tee\"),\n  keywords: set(sqlKeywords + \"accessible action add after algorithm all analyze asensitive at authors auto_increment autocommit avg avg_row_length before binary binlog both btree cache call cascade cascaded case catalog_name chain change changed character check checkpoint checksum class_origin client_statistics close coalesce code collate collation collations column columns comment commit committed completion concurrent condition connection consistent constraint contains continue contributors convert cross current current_date current_time current_timestamp current_user cursor data database databases day_hour day_microsecond day_minute day_second deallocate dec declare default delay_key_write delayed delimiter des_key_file describe deterministic dev_pop dev_samp deviance diagnostics directory disable discard distinctrow div dual dumpfile each elseif enable enclosed end ends engine engines enum errors escape escaped even event events every execute exists exit explain extended fast fetch field fields first flush for force foreign found_rows full fulltext function general get global grant grants group group_concat handler hash help high_priority hosts hour_microsecond hour_minute hour_second if ignore ignore_server_ids import index index_statistics infile inner innodb inout insensitive insert_method install interval invoker isolation iterate key keys kill language last leading leave left level limit linear lines list load local localtime localtimestamp lock logs low_priority master master_heartbeat_period master_ssl_verify_server_cert masters match max max_rows maxvalue message_text middleint migrate min min_rows minute_microsecond minute_second mod mode modifies modify mutex mysql_errno natural next no no_write_to_binlog offline offset one online open optimize option optionally out outer outfile pack_keys parser partition partitions password phase plugin plugins prepare preserve prev primary privileges procedure processlist profile profiles purge query quick range read read_write reads real rebuild recover references regexp relaylog release remove rename reorganize repair repeatable replace require resignal restrict resume return returns revoke right rlike rollback rollup row row_format rtree savepoint schedule schema schema_name schemas second_microsecond security sensitive separator serializable server session share show signal slave slow smallint snapshot soname spatial specific sql sql_big_result sql_buffer_result sql_cache sql_calc_found_rows sql_no_cache sql_small_result sqlexception sqlstate sqlwarning ssl start starting starts status std stddev stddev_pop stddev_samp storage straight_join subclass_origin sum suspend table_name table_statistics tables tablespace temporary terminated to trailing transaction trigger triggers truncate uncommitted undo uninstall unique unlock upgrade usage use use_frm user user_resources user_statistics using utc_date utc_time utc_timestamp value variables varying view views warnings when while with work write xa xor year_month zerofill begin do then else loop repeat\"),\n  builtin: set(\"bool boolean bit blob decimal double float long longblob longtext medium mediumblob mediumint mediumtext time timestamp tinyblob tinyint tinytext text bigint int int1 int2 int3 int4 int8 integer float float4 float8 double char varbinary varchar varcharacter precision date datetime year unsigned signed numeric\"),\n  atoms: set(\"false true null unknown\"),\n  operatorChars: /^[*+\\-%<>!=&|^]/,\n  dateSQL: set(\"date time timestamp\"),\n  support: set(\"ODBCdotTable decimallessFloat zerolessFloat binaryNumber hexNumber doubleQuote nCharCast charsetCast commentHash commentSpaceRequired\"),\n  hooks: {\n    \"@\":   hookVar,\n    \"`\":   hookIdentifier,\n    \"\\\\\":  hookClient\n  }\n});\n\nconst mariaDB = sql({\n  client: set(\"charset clear connect edit ego exit go help nopager notee nowarning pager print prompt quit rehash source status system tee\"),\n  keywords: set(sqlKeywords + \"accessible action add after algorithm all always analyze asensitive at authors auto_increment autocommit avg avg_row_length before binary binlog both btree cache call cascade cascaded case catalog_name chain change changed character check checkpoint checksum class_origin client_statistics close coalesce code collate collation collations column columns comment commit committed completion concurrent condition connection consistent constraint contains continue contributors convert cross current current_date current_time current_timestamp current_user cursor data database databases day_hour day_microsecond day_minute day_second deallocate dec declare default delay_key_write delayed delimiter des_key_file describe deterministic dev_pop dev_samp deviance diagnostics directory disable discard distinctrow div dual dumpfile each elseif enable enclosed end ends engine engines enum errors escape escaped even event events every execute exists exit explain extended fast fetch field fields first flush for force foreign found_rows full fulltext function general generated get global grant grants group group_concat handler hard hash help high_priority hosts hour_microsecond hour_minute hour_second if ignore ignore_server_ids import index index_statistics infile inner innodb inout insensitive insert_method install interval invoker isolation iterate key keys kill language last leading leave left level limit linear lines list load local localtime localtimestamp lock logs low_priority master master_heartbeat_period master_ssl_verify_server_cert masters match max max_rows maxvalue message_text middleint migrate min min_rows minute_microsecond minute_second mod mode modifies modify mutex mysql_errno natural next no no_write_to_binlog offline offset one online open optimize option optionally out outer outfile pack_keys parser partition partitions password persistent phase plugin plugins prepare preserve prev primary privileges procedure processlist profile profiles purge query quick range read read_write reads real rebuild recover references regexp relaylog release remove rename reorganize repair repeatable replace require resignal restrict resume return returns revoke right rlike rollback rollup row row_format rtree savepoint schedule schema schema_name schemas second_microsecond security sensitive separator serializable server session share show shutdown signal slave slow smallint snapshot soft soname spatial specific sql sql_big_result sql_buffer_result sql_cache sql_calc_found_rows sql_no_cache sql_small_result sqlexception sqlstate sqlwarning ssl start starting starts status std stddev stddev_pop stddev_samp storage straight_join subclass_origin sum suspend table_name table_statistics tables tablespace temporary terminated to trailing transaction trigger triggers truncate uncommitted undo uninstall unique unlock upgrade usage use use_frm user user_resources user_statistics using utc_date utc_time utc_timestamp value variables varying view views virtual warnings when while with work write xa xor year_month zerofill begin do then else loop repeat\"),\n  builtin: set(\"bool boolean bit blob decimal double float long longblob longtext medium mediumblob mediumint mediumtext time timestamp tinyblob tinyint tinytext text bigint int int1 int2 int3 int4 int8 integer float float4 float8 double char varbinary varchar varcharacter precision date datetime year unsigned signed numeric\"),\n  atoms: set(\"false true null unknown\"),\n  operatorChars: /^[*+\\-%<>!=&|^]/,\n  dateSQL: set(\"date time timestamp\"),\n  support: set(\"ODBCdotTable decimallessFloat zerolessFloat binaryNumber hexNumber doubleQuote nCharCast charsetCast commentHash commentSpaceRequired\"),\n  hooks: {\n    \"@\":   hookVar,\n    \"`\":   hookIdentifier,\n    \"\\\\\":  hookClient\n  }\n});\n\n// provided by the phpLiteAdmin project - phpliteadmin.org\nconst sqlite = sql({\n  // commands of the official SQLite client, ref: https://www.sqlite.org/cli.html#dotcmd\n  client: set(\"auth backup bail binary changes check clone databases dbinfo dump echo eqp exit explain fullschema headers help import imposter indexes iotrace limit lint load log mode nullvalue once open output print prompt quit read restore save scanstats schema separator session shell show stats system tables testcase timeout timer trace vfsinfo vfslist vfsname width\"),\n  // ref: http://sqlite.org/lang_keywords.html\n  keywords: set(sqlKeywords + \"abort action add after all analyze attach autoincrement before begin cascade case cast check collate column commit conflict constraint cross current_date current_time current_timestamp database default deferrable deferred detach each else end escape except exclusive exists explain fail for foreign full glob if ignore immediate index indexed initially inner instead intersect isnull key left limit match natural no notnull null of offset outer plan pragma primary query raise recursive references regexp reindex release rename replace restrict right rollback row savepoint temp temporary then to transaction trigger unique using vacuum view virtual when with without\"),\n  // SQLite is weakly typed, ref: http://sqlite.org/datatype3.html. This is just a list of some common types.\n  builtin: set(\"bool boolean bit blob decimal double float long longblob longtext medium mediumblob mediumint mediumtext time timestamp tinyblob tinyint tinytext text clob bigint int int2 int8 integer float double char varchar date datetime year unsigned signed numeric real\"),\n  // ref: http://sqlite.org/syntax/literal-value.html\n  atoms: set(\"null current_date current_time current_timestamp\"),\n  // ref: http://sqlite.org/lang_expr.html#binaryops\n  operatorChars: /^[*+\\-%<>!=&|/~]/,\n  // SQLite is weakly typed, ref: http://sqlite.org/datatype3.html. This is just a list of some common types.\n  dateSQL: set(\"date time timestamp datetime\"),\n  support: set(\"decimallessFloat zerolessFloat\"),\n  identifierQuote: \"\\\"\",  //ref: http://sqlite.org/lang_keywords.html\n  hooks: {\n    // bind-parameters ref:http://sqlite.org/lang_expr.html#varparam\n    \"@\":   hookVar,\n    \":\":   hookVar,\n    \"?\":   hookVar,\n    \"$\":   hookVar,\n    // The preferred way to escape Identifiers is using double quotes, ref: http://sqlite.org/lang_keywords.html\n    \"\\\"\":   hookIdentifierDoublequote,\n    // there is also support for backticks, ref: http://sqlite.org/lang_keywords.html\n    \"`\":   hookIdentifier\n  }\n});\n\n// the query language used by Apache Cassandra is called CQL, but this mime type\n// is called Cassandra to avoid confusion with Contextual Query Language\nconst cassandra = sql({\n  client: { },\n  keywords: set(\"add all allow alter and any apply as asc authorize batch begin by clustering columnfamily compact consistency count create custom delete desc distinct drop each_quorum exists filtering from grant if in index insert into key keyspace keyspaces level limit local_one local_quorum modify nan norecursive nosuperuser not of on one order password permission permissions primary quorum rename revoke schema select set storage superuser table three to token truncate ttl two type unlogged update use user users using values where with writetime\"),\n  builtin: set(\"ascii bigint blob boolean counter decimal double float frozen inet int list map static text timestamp timeuuid tuple uuid varchar varint\"),\n  atoms: set(\"false true infinity NaN\"),\n  operatorChars: /^[<>=]/,\n  dateSQL: { },\n  support: set(\"commentSlashSlash decimallessFloat\"),\n  hooks: { }\n});\n\n// this is based on Peter Raganitsch's 'plsql' mode\nconst plSQL = sql({\n  client:     set(\"appinfo arraysize autocommit autoprint autorecovery autotrace blockterminator break btitle cmdsep colsep compatibility compute concat copycommit copytypecheck define describe echo editfile embedded escape exec execute feedback flagger flush heading headsep instance linesize lno loboffset logsource long longchunksize markup native newpage numformat numwidth pagesize pause pno recsep recsepchar release repfooter repheader serveroutput shiftinout show showmode size spool sqlblanklines sqlcase sqlcode sqlcontinue sqlnumber sqlpluscompatibility sqlprefix sqlprompt sqlterminator suffix tab term termout time timing trimout trimspool ttitle underline verify version wrap\"),\n  keywords:   set(\"abort accept access add all alter and any array arraylen as asc assert assign at attributes audit authorization avg base_table begin between binary_integer body boolean by case cast char char_base check close cluster clusters colauth column comment commit compress connect connected constant constraint crash create current currval cursor data_base database date dba deallocate debugoff debugon decimal declare default definition delay delete desc digits dispose distinct do drop else elseif elsif enable end entry escape exception exception_init exchange exclusive exists exit external fast fetch file for force form from function generic goto grant group having identified if immediate in increment index indexes indicator initial initrans insert interface intersect into is key level library like limited local lock log logging long loop master maxextents maxtrans member minextents minus mislabel mode modify multiset new next no noaudit nocompress nologging noparallel not nowait number_base object of off offline on online only open option or order out package parallel partition pctfree pctincrease pctused pls_integer positive positiven pragma primary prior private privileges procedure public raise range raw read rebuild record ref references refresh release rename replace resource restrict return returning returns reverse revoke rollback row rowid rowlabel rownum rows run savepoint schema segment select separate session set share snapshot some space split sql start statement storage subtype successful synonym tabauth table tables tablespace task terminate then to trigger truncate type union unique unlimited unrecoverable unusable update use using validate value values variable view views when whenever where while with work\"),\n  builtin:    set(\"abs acos add_months ascii asin atan atan2 average bfile bfilename bigserial bit blob ceil character chartorowid chr clob concat convert cos cosh count dec decode deref dual dump dup_val_on_index empty error exp false float floor found glb greatest hextoraw initcap instr instrb int integer isopen last_day least length lengthb ln lower lpad ltrim lub make_ref max min mlslabel mod months_between natural naturaln nchar nclob new_time next_day nextval nls_charset_decl_len nls_charset_id nls_charset_name nls_initcap nls_lower nls_sort nls_upper nlssort no_data_found notfound null number numeric nvarchar2 nvl others power rawtohex real reftohex round rowcount rowidtochar rowtype rpad rtrim serial sign signtype sin sinh smallint soundex sqlcode sqlerrm sqrt stddev string substr substrb sum sysdate tan tanh to_char text to_date to_label to_multi_byte to_number to_single_byte translate true trunc uid unlogged upper user userenv varchar varchar2 variance varying vsize xml\"),\n  operatorChars: /^[*\\/+\\-%<>!=~]/,\n  dateSQL:    set(\"date time timestamp\"),\n  support:    set(\"doubleQuote nCharCast zerolessFloat binaryNumber hexNumber\")\n});\n\n// Created to support specific hive keywords\nconst hive = sql({\n  keywords: set(\"select alter $elem$ $key$ $value$ add after all analyze and archive as asc before between binary both bucket buckets by cascade case cast change cluster clustered clusterstatus collection column columns comment compute concatenate continue create cross cursor data database databases dbproperties deferred delete delimited desc describe directory disable distinct distribute drop else enable end escaped exclusive exists explain export extended external fetch fields fileformat first format formatted from full function functions grant group having hold_ddltime idxproperties if import in index indexes inpath inputdriver inputformat insert intersect into is items join keys lateral left like limit lines load local location lock locks mapjoin materialized minus msck no_drop nocompress not of offline on option or order out outer outputdriver outputformat overwrite partition partitioned partitions percent plus preserve procedure purge range rcfile read readonly reads rebuild recordreader recordwriter recover reduce regexp rename repair replace restrict revoke right rlike row schema schemas semi sequencefile serde serdeproperties set shared show show_database sort sorted ssl statistics stored streamtable table tables tablesample tblproperties temporary terminated textfile then tmp to touch transform trigger unarchive undo union uniquejoin unlock update use using utc utc_tmestamp view when where while with admin authorization char compact compactions conf cube current current_date current_timestamp day decimal defined dependency directories elem_type exchange file following for grouping hour ignore inner interval jar less logical macro minute month more none noscan over owner partialscan preceding pretty principals protection reload rewrite role roles rollup rows second server sets skewed transactions truncate unbounded unset uri user values window year\"),\n  builtin: set(\"bool boolean long timestamp tinyint smallint bigint int float double date datetime unsigned string array struct map uniontype key_type utctimestamp value_type varchar\"),\n  atoms: set(\"false true null unknown\"),\n  operatorChars: /^[*+\\-%<>!=]/,\n  dateSQL: set(\"date timestamp\"),\n  support: set(\"ODBCdotTable doubleQuote binaryNumber hexNumber\")\n});\n\nconst pgSQL = sql({\n  client: set(\"source\"),\n  // For PostgreSQL - https://www.postgresql.org/docs/11/sql-keywords-appendix.html\n  // For pl/pgsql lang - https://github.com/postgres/postgres/blob/REL_11_2/src/pl/plpgsql/src/pl_scanner.c\n  keywords: set(sqlKeywords + \"a abort abs absent absolute access according action ada add admin after aggregate alias all allocate also alter always analyse analyze and any are array array_agg array_max_cardinality as asc asensitive assert assertion assignment asymmetric at atomic attach attribute attributes authorization avg backward base64 before begin begin_frame begin_partition bernoulli between bigint binary bit bit_length blob blocked bom boolean both breadth by c cache call called cardinality cascade cascaded case cast catalog catalog_name ceil ceiling chain char char_length character character_length character_set_catalog character_set_name character_set_schema characteristics characters check checkpoint class class_origin clob close cluster coalesce cobol collate collation collation_catalog collation_name collation_schema collect column column_name columns command_function command_function_code comment comments commit committed concurrently condition condition_number configuration conflict connect connection connection_name constant constraint constraint_catalog constraint_name constraint_schema constraints constructor contains content continue control conversion convert copy corr corresponding cost count covar_pop covar_samp create cross csv cube cume_dist current current_catalog current_date current_default_transform_group current_path current_role current_row current_schema current_time current_timestamp current_transform_group_for_type current_user cursor cursor_name cycle data database datalink datatype date datetime_interval_code datetime_interval_precision day db deallocate debug dec decimal declare default defaults deferrable deferred defined definer degree delete delimiter delimiters dense_rank depends depth deref derived desc describe descriptor detach detail deterministic diagnostics dictionary disable discard disconnect dispatch distinct dlnewcopy dlpreviouscopy dlurlcomplete dlurlcompleteonly dlurlcompletewrite dlurlpath dlurlpathonly dlurlpathwrite dlurlscheme dlurlserver dlvalue do document domain double drop dump dynamic dynamic_function dynamic_function_code each element else elseif elsif empty enable encoding encrypted end end_frame end_partition endexec enforced enum equals errcode error escape event every except exception exclude excluding exclusive exec execute exists exit exp explain expression extension external extract false family fetch file filter final first first_value flag float floor following for force foreach foreign fortran forward found frame_row free freeze from fs full function functions fusion g general generated get global go goto grant granted greatest group grouping groups handler having header hex hierarchy hint hold hour id identity if ignore ilike immediate immediately immutable implementation implicit import in include including increment indent index indexes indicator info inherit inherits initially inline inner inout input insensitive insert instance instantiable instead int integer integrity intersect intersection interval into invoker is isnull isolation join k key key_member key_type label lag language large last last_value lateral lead leading leakproof least left length level library like like_regex limit link listen ln load local localtime localtimestamp location locator lock locked log logged loop lower m map mapping match matched materialized max max_cardinality maxvalue member merge message message_length message_octet_length message_text method min minute minvalue mod mode modifies module month more move multiset mumps name names namespace national natural nchar nclob nesting new next nfc nfd nfkc nfkd nil no none normalize normalized not nothing notice notify notnull nowait nth_value ntile null nullable nullif nulls number numeric object occurrences_regex octet_length octets of off offset oids old on only open operator option options or order ordering ordinality others out outer output over overlaps overlay overriding owned owner p pad parallel parameter parameter_mode parameter_name parameter_ordinal_position parameter_specific_catalog parameter_specific_name parameter_specific_schema parser partial partition pascal passing passthrough password path percent percent_rank percentile_cont percentile_disc perform period permission pg_context pg_datatype_name pg_exception_context pg_exception_detail pg_exception_hint placing plans pli policy portion position position_regex power precedes preceding precision prepare prepared preserve primary print_strict_params prior privileges procedural procedure procedures program public publication query quote raise range rank read reads real reassign recheck recovery recursive ref references referencing refresh regr_avgx regr_avgy regr_count regr_intercept regr_r2 regr_slope regr_sxx regr_sxy regr_syy reindex relative release rename repeatable replace replica requiring reset respect restart restore restrict result result_oid return returned_cardinality returned_length returned_octet_length returned_sqlstate returning returns reverse revoke right role rollback rollup routine routine_catalog routine_name routine_schema routines row row_count row_number rows rowtype rule savepoint scale schema schema_name schemas scope scope_catalog scope_name scope_schema scroll search second section security select selective self sensitive sequence sequences serializable server server_name session session_user set setof sets share show similar simple size skip slice smallint snapshot some source space specific specific_name specifictype sql sqlcode sqlerror sqlexception sqlstate sqlwarning sqrt stable stacked standalone start state statement static statistics stddev_pop stddev_samp stdin stdout storage strict strip structure style subclass_origin submultiset subscription substring substring_regex succeeds sum symmetric sysid system system_time system_user t table table_name tables tablesample tablespace temp template temporary text then ties time timestamp timezone_hour timezone_minute to token top_level_count trailing transaction transaction_active transactions_committed transactions_rolled_back transform transforms translate translate_regex translation treat trigger trigger_catalog trigger_name trigger_schema trim trim_array true truncate trusted type types uescape unbounded uncommitted under unencrypted union unique unknown unlink unlisten unlogged unnamed unnest until untyped update upper uri usage use_column use_variable user user_defined_type_catalog user_defined_type_code user_defined_type_name user_defined_type_schema using vacuum valid validate validator value value_of values var_pop var_samp varbinary varchar variable_conflict variadic varying verbose version versioning view views volatile warning when whenever where while whitespace width_bucket window with within without work wrapper write xml xmlagg xmlattributes xmlbinary xmlcast xmlcomment xmlconcat xmldeclaration xmldocument xmlelement xmlexists xmlforest xmliterate xmlnamespaces xmlparse xmlpi xmlquery xmlroot xmlschema xmlserialize xmltable xmltext xmlvalidate year yes zone\"),\n  // https://www.postgresql.org/docs/11/datatype.html\n  builtin: set(\"bigint int8 bigserial serial8 bit varying varbit boolean bool box bytea character char varchar cidr circle date double precision float8 inet integer int int4 interval json jsonb line lseg macaddr macaddr8 money numeric decimal path pg_lsn point polygon real float4 smallint int2 smallserial serial2 serial serial4 text time without zone with timetz timestamp timestamptz tsquery tsvector txid_snapshot uuid xml\"),\n  atoms: set(\"false true null unknown\"),\n  operatorChars: /^[*\\/+\\-%<>!=&|^\\/#@?~]/,\n  backslashStringEscapes: false,\n  dateSQL: set(\"date time timestamp\"),\n  support: set(\"ODBCdotTable decimallessFloat zerolessFloat binaryNumber hexNumber nCharCast charsetCast escapeConstant\")\n});\n\n// Google's SQL-like query language, GQL\nconst gql = sql({\n  keywords: set(\"ancestor and asc by contains desc descendant distinct from group has in is limit offset on order select superset where\"),\n  atoms: set(\"false true\"),\n  builtin: set(\"blob datetime first key __key__ string integer double boolean null\"),\n  operatorChars: /^[*+\\-%<>!=]/\n});\n\n// Greenplum\nconst gpSQL = sql({\n  client: set(\"source\"),\n  //https://github.com/greenplum-db/gpdb/blob/master/src/include/parser/kwlist.h\n  keywords: set(\"abort absolute access action active add admin after aggregate all also alter always analyse analyze and any array as asc assertion assignment asymmetric at authorization backward before begin between bigint binary bit boolean both by cache called cascade cascaded case cast chain char character characteristics check checkpoint class close cluster coalesce codegen collate column comment commit committed concurrency concurrently configuration connection constraint constraints contains content continue conversion copy cost cpu_rate_limit create createdb createexttable createrole createuser cross csv cube current current_catalog current_date current_role current_schema current_time current_timestamp current_user cursor cycle data database day deallocate dec decimal declare decode default defaults deferrable deferred definer delete delimiter delimiters deny desc dictionary disable discard distinct distributed do document domain double drop dxl each else enable encoding encrypted end enum errors escape every except exchange exclude excluding exclusive execute exists explain extension external extract false family fetch fields filespace fill filter first float following for force foreign format forward freeze from full function global grant granted greatest group group_id grouping handler hash having header hold host hour identity if ignore ilike immediate immutable implicit in including inclusive increment index indexes inherit inherits initially inline inner inout input insensitive insert instead int integer intersect interval into invoker is isnull isolation join key language large last leading least left level like limit list listen load local localtime localtimestamp location lock log login mapping master match maxvalue median merge minute minvalue missing mode modifies modify month move name names national natural nchar new newline next no nocreatedb nocreateexttable nocreaterole nocreateuser noinherit nologin none noovercommit nosuperuser not nothing notify notnull nowait null nullif nulls numeric object of off offset oids old on only operator option options or order ordered others out outer over overcommit overlaps overlay owned owner parser partial partition partitions passing password percent percentile_cont percentile_disc placing plans position preceding precision prepare prepared preserve primary prior privileges procedural procedure protocol queue quote randomly range read readable reads real reassign recheck recursive ref references reindex reject relative release rename repeatable replace replica reset resource restart restrict returning returns revoke right role rollback rollup rootpartition row rows rule savepoint scatter schema scroll search second security segment select sequence serializable session session_user set setof sets share show similar simple smallint some split sql stable standalone start statement statistics stdin stdout storage strict strip subpartition subpartitions substring superuser symmetric sysid system table tablespace temp template temporary text then threshold ties time timestamp to trailing transaction treat trigger trim true truncate trusted type unbounded uncommitted unencrypted union unique unknown unlisten until update user using vacuum valid validation validator value values varchar variadic varying verbose version view volatile web when where whitespace window with within without work writable write xml xmlattributes xmlconcat xmlelement xmlexists xmlforest xmlparse xmlpi xmlroot xmlserialize year yes zone\"),\n  builtin: set(\"bigint int8 bigserial serial8 bit varying varbit boolean bool box bytea character char varchar cidr circle date double precision float float8 inet integer int int4 interval json jsonb line lseg macaddr macaddr8 money numeric decimal path pg_lsn point polygon real float4 smallint int2 smallserial serial2 serial serial4 text time without zone with timetz timestamp timestamptz tsquery tsvector txid_snapshot uuid xml\"),\n  atoms: set(\"false true null unknown\"),\n  operatorChars: /^[*+\\-%<>!=&|^\\/#@?~]/,\n  dateSQL: set(\"date time timestamp\"),\n  support: set(\"ODBCdotTable decimallessFloat zerolessFloat binaryNumber hexNumber nCharCast charsetCast\")\n});\n\n// Spark SQL\nconst sparkSQL = sql({\n  keywords: set(\"add after all alter analyze and anti archive array as asc at between bucket buckets by cache cascade case cast change clear cluster clustered codegen collection column columns comment commit compact compactions compute concatenate cost create cross cube current current_date current_timestamp database databases data dbproperties defined delete delimited deny desc describe dfs directories distinct distribute drop else end escaped except exchange exists explain export extended external false fields fileformat first following for format formatted from full function functions global grant group grouping having if ignore import in index indexes inner inpath inputformat insert intersect interval into is items join keys last lateral lazy left like limit lines list load local location lock locks logical macro map minus msck natural no not null nulls of on optimize option options or order out outer outputformat over overwrite partition partitioned partitions percent preceding principals purge range recordreader recordwriter recover reduce refresh regexp rename repair replace reset restrict revoke right rlike role roles rollback rollup row rows schema schemas select semi separated serde serdeproperties set sets show skewed sort sorted start statistics stored stratify struct table tables tablesample tblproperties temp temporary terminated then to touch transaction transactions transform true truncate unarchive unbounded uncache union unlock unset use using values view when where window with\"),\n  builtin: set(\"tinyint smallint int bigint boolean float double string binary timestamp decimal array map struct uniontype delimited serde sequencefile textfile rcfile inputformat outputformat\"),\n  atoms: set(\"false true null\"),\n  operatorChars: /^[*\\/+\\-%<>!=~&|^]/,\n  dateSQL: set(\"date time timestamp\"),\n  support: set(\"ODBCdotTable doubleQuote zerolessFloat\")\n});\n\n// Esper\nconst esper = sql({\n  client: set(\"source\"),\n  // http://www.espertech.com/esper/release-5.5.0/esper-reference/html/appendix_keywords.html\n  keywords: set(\"alter and as asc between by count create delete desc distinct drop from group having in insert into is join like not on or order select set table union update values where limit after all and as at asc avedev avg between by case cast coalesce count create current_timestamp day days delete define desc distinct else end escape events every exists false first from full group having hour hours in inner insert instanceof into irstream is istream join last lastweekday left limit like max match_recognize matches median measures metadatasql min minute minutes msec millisecond milliseconds not null offset on or order outer output partition pattern prev prior regexp retain-union retain-intersection right rstream sec second seconds select set some snapshot sql stddev sum then true unidirectional until update variable weekday when where window\"),\n  builtin: {},\n  atoms: set(\"false true null\"),\n  operatorChars: /^[*+\\-%<>!=&|^\\/#@?~]/,\n  dateSQL: set(\"time\"),\n  support: set(\"decimallessFloat zerolessFloat binaryNumber hexNumber\")\n});\n\n/*\n  How options are used by SQL Mode\n  =================================================\n\n  keywords:\n    A list of keywords you want to be highlighted.\n  builtin:\n    A list of builtin types you want to be highlighted (if you want types to be of class \"builtin\" instead of \"keyword\").\n  operatorChars:\n    All characters that must be handled as operators.\n  client:\n    Commands parsed and executed by the client (not the server).\n  support:\n    A list of supported syntaxes which are not common, but are supported by more than 1 DBMS.\n    * ODBCdotTable: .tableName\n    * zerolessFloat: .1\n    * doubleQuote\n    * nCharCast: N'string'\n    * charsetCast: _utf8'string'\n    * commentHash: use # char for comments\n    * commentSlashSlash: use // for comments\n    * commentSpaceRequired: require a space after -- for comments\n  atoms:\n    Keywords that must be highlighted as atoms,. Some DBMS's support more atoms than others:\n    UNKNOWN, INFINITY, UNDERFLOW, NaN...\n  dateSQL:\n    Used for date/time SQL standard syntax, because not all DBMS's support same temporal types.\n*/\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@codemirror/legacy-modes/mode/sql.js\n"));

/***/ })

}]);