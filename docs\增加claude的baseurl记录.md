# 增加 Claude API Base URL 自定义支持

## 修改概述

为了支持使用自定义的 Anthropic API 端点（而不是官方的 API 端点），我们对代码进行了以下修改。这对于需要使用代理服务或第三方 Anthropic API 兼容服务的用户非常有用。

## 修改详情

### 1. 配置文件修改

#### `backend/utils/config.py`

在 LLM API keys 部分添加了 `ANTHROPIC_API_BASE` 配置项：

```python
# LLM API keys
ANTHROPIC_API_KEY: str = None
ANTHROPIC_API_BASE: Optional[str] = None  #使用DMXapi的claude模型替换掉官网api
OPENAI_API_KEY: Optional[str] = None
GROQ_API_KEY: Optional[str] = None
OPENROUTER_API_KEY: Optional[str] = None
OPENROUTER_API_BASE: Optional[str] = "https://openrouter.ai/api/v1"
OR_SITE_URL: Optional[str] = "https://kortix.ai"
OR_APP_NAME: Optional[str] = "Kortix AI"
```

#### `backend/.env.example`

在 LLM Providers 部分添加了 `ANTHROPIC_API_BASE` 示例配置：

```env
# LLM Providers:
ANTHROPIC_API_KEY=
ANTHROPIC_API_BASE=  #使用DMXapi的claude模型替换掉官网api
OPENAI_API_KEY=
MODEL_TO_USE=
```

### 2. LLM 服务修改

#### `backend/services/llm.py`

在 `setup_api_keys()` 函数中添加了设置 Anthropic base URL 的逻辑：

```python
def setup_api_keys() -> None:
    """Set up API keys from environment variables."""
    providers = ['OPENAI', 'ANTHROPIC', 'GROQ', 'OPENROUTER']
    for provider in providers:
        key = getattr(config, f'{provider}_API_KEY')
        if key:
            logger.debug(f"API key set for provider: {provider}")
        else:
            logger.warning(f"No API key found for provider: {provider}")

    # Set up Anthropic API base if not already set (新增)
    if config.ANTHROPIC_API_KEY and config.ANTHROPIC_API_BASE:
        os.environ['ANTHROPIC_API_BASE'] = config.ANTHROPIC_API_BASE
        logger.debug(f"Set ANTHROPIC_API_BASE to {config.ANTHROPIC_API_BASE}")

    # Set up OpenRouter API base if not already set
    if config.OPENROUTER_API_KEY and config.OPENROUTER_API_BASE:
        os.environ['OPENROUTER_API_BASE'] = config.OPENROUTER_API_BASE
        logger.debug(f"Set OPENROUTER_API_BASE to {config.OPENROUTER_API_BASE}")
```

## 使用方法

### 1. 环境变量配置

在您的 `.env` 文件中设置自定义的 Anthropic API base URL：

```env
ANTHROPIC_API_KEY=your_api_key_here
ANTHROPIC_API_BASE=https://your-custom-anthropic-endpoint.com/v1
```

### 2. 常见使用场景

- **使用代理服务**：在中国大陆等地区需要通过代理访问 Anthropic API
- **使用第三方兼容服务**：使用提供 Anthropic API 兼容接口的第三方服务
- **企业内部部署**：使用企业内部部署的 Anthropic API 兼容服务

### 3. 示例配置

```env
# 使用代理服务的示例
ANTHROPIC_API_KEY=sk-ant-api03-xxxxx
ANTHROPIC_API_BASE=https://api.anthropic-proxy.com/v1

# 使用第三方兼容服务的示例
ANTHROPIC_API_KEY=your_third_party_key
ANTHROPIC_API_BASE=https://api.third-party-service.com/anthropic/v1
```

## 工作原理

1. **模块初始化**：当 `backend/services/llm.py` 模块加载时，会自动调用 `setup_api_keys()` 函数
2. **环境变量设置**：如果同时设置了 `ANTHROPIC_API_KEY` 和 `ANTHROPIC_API_BASE`，代码会将自定义的 base URL 设置到环境变量 `ANTHROPIC_API_BASE` 中
3. **LiteLLM 自动使用**：LiteLLM 库会自动读取 `ANTHROPIC_API_BASE` 环境变量，并使用自定义的 base URL 来调用 Anthropic 模型，而不是默认的官方端点

## 注意事项

1. **API 兼容性**：确保您使用的自定义端点与 Anthropic API 完全兼容
2. **安全性**：使用第三方服务时，请确保服务提供商的安全性和可靠性
3. **日志记录**：系统会在日志中记录使用的 base URL，便于调试和监控
4. **向后兼容**：如果不设置 `ANTHROPIC_API_BASE`，系统会继续使用官方的 Anthropic API 端点

## 测试验证

修改完成后，您可以通过以下方式验证配置是否生效：

1. 查看应用启动日志，确认看到类似以下的日志信息：
   ```
   Set ANTHROPIC_API_BASE to https://your-custom-endpoint.com/v1
   ```

2. 发起一个使用 Anthropic 模型的请求，确认请求被发送到了正确的端点

## 修改日期

- **修改时间**：2025年1月27日
- **修改人**：Augment Agent
- **修改原因**：支持用户使用自定义的 Anthropic API 端点，提高系统的灵活性和可用性

---

# 修改默认模型配置

## 修改概述

将系统默认使用的模型从 `anthropic/claude-3-7-sonnet-latest` 更改为 `claude-sonnet-4-20250514-thinking`，同时将最大迭代次数从100次修改为20次。

## 修改详情

### 1. 主要配置文件修改

#### `backend/utils/config.py`

修改第127行的默认模型配置：

```python
# Model configuration
MODEL_TO_USE: Optional[str] = "claude-sonnet-4-20250514-thinking"  # 从 "anthropic/claude-3-7-sonnet-latest" 改为新模型
```

### 2. Agent 运行函数修改

#### `backend/agent/run.py`

修改第35-36行，同时更新了最大迭代次数和默认模型：

```python
async def run_agent(
    thread_id: str,
    project_id: str,
    stream: bool,
    thread_manager: Optional[ThreadManager] = None,
    native_max_auto_continues: int = 25,
    max_iterations: int = 20,  # 从 100 改为 20
    model_name: str = "claude-sonnet-4-20250514-thinking",  # 从 "anthropic/claude-3-7-sonnet-latest" 改为新模型
    enable_thinking: Optional[bool] = False,
    reasoning_effort: Optional[str] = 'low',
    enable_context_manager: bool = True
):
```

### 3. 模型别名映射修改

#### `backend/agent/api.py`

在第34-59行的 `MODEL_NAME_ALIASES` 中添加了新模型的映射：

```python
MODEL_NAME_ALIASES = {
    # Short names to full names
    "sonnet-3.7": "anthropic/claude-3-7-sonnet-latest",
    "sonnet-4": "claude-sonnet-4-20250514-thinking",  # 新增短名称映射
    "gpt-4.1": "openai/gpt-4.1-2025-04-14",
    # ...其他模型...

    # Also include full names as keys to ensure they map to themselves
    "anthropic/claude-3-7-sonnet-latest": "anthropic/claude-3-7-sonnet-latest",
    "claude-sonnet-4-20250514-thinking": "claude-sonnet-4-20250514-thinking",  # 新增完整名称自映射
    # ...其他模型...
}
```

## 修改效果

### 1. 模型使用变化

修改后，系统日志将显示：

```
🚀 Starting agent with model: claude-sonnet-4-20250514-thinking
Using model: claude-sonnet-4-20250514-thinking
📡 API Call: Using model claude-sonnet-4-20250514-thinking
```

### 2. 迭代次数变化

修改后，系统日志将显示：

```
🔄 Running iteration 1 of 20...
🔄 Running iteration 2 of 20...
...
🔄 Running iteration 20 of 20...
```

而不是之前的：

```
🔄 Running iteration 1 of 100...
```

## 使用方法

### 1. 通过配置文件使用

系统会自动使用新的默认模型，无需额外配置。

### 2. 通过API指定模型

可以使用短名称：
```json
{
  "model_name": "sonnet-4"
}
```

或完整名称：
```json
{
  "model_name": "claude-sonnet-4-20250514-thinking"
}
```

### 3. 通过环境变量覆盖

在 `.env` 文件中设置：
```env
MODEL_TO_USE=claude-sonnet-4-20250514-thinking
```

## 注意事项

1. **模型兼容性**：确保新模型与现有的工具调用和功能兼容
2. **API 端点**：新模型需要配合正确的 API 端点使用
3. **迭代次数限制**：20次迭代限制有助于控制资源使用和成本
4. **向后兼容**：旧的模型名称仍然可以通过API参数指定使用

## 修改日期

- **修改时间**：2025年1月27日
- **修改人**：Augment Agent
- **修改原因**：
  - 使用更新的Claude模型以获得更好的性能
  - 降低最大迭代次数以控制资源使用
  - 根据用户偏好调整系统配置