self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"7f4dff9eb83a22d35d3acb6f82d7b420b98f32e57e\": {\n      \"workers\": {\n        \"app/(home)/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22D%3A%5C%5Csoft%5C%5CSeaProject%5C%5Cgithub_open_project_success%5C%5Csuna%5C%5Cfrontend%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cthreads.ts%22%2C%5B%7B%22id%22%3A%227f4dff9eb83a22d35d3acb6f82d7b420b98f32e57e%22%2C%22exportedName%22%3A%22generateThreadName%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": false\n        },\n        \"app/(dashboard)/dashboard/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22D%3A%5C%5Csoft%5C%5CSeaProject%5C%5Cgithub_open_project_success%5C%5Csuna%5C%5Cfrontend%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cteams.ts%22%2C%5B%7B%22id%22%3A%22608b7967e2831d8c5568299992a639f94e11b15120%22%2C%22exportedName%22%3A%22createTeam%22%7D%2C%7B%22id%22%3A%2260c9e4ae5ba61c10ed410caf36fe992bce27657f86%22%2C%22exportedName%22%3A%22editTeamSlug%22%7D%2C%7B%22id%22%3A%2260cbf9dc0d607876a1ece630fcdca5f5220c3d4a43%22%2C%22exportedName%22%3A%22editTeamName%22%7D%5D%5D%2C%5B%22D%3A%5C%5Csoft%5C%5CSeaProject%5C%5Cgithub_open_project_success%5C%5Csuna%5C%5Cfrontend%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cthreads.ts%22%2C%5B%7B%22id%22%3A%227f4dff9eb83a22d35d3acb6f82d7b420b98f32e57e%22%2C%22exportedName%22%3A%22generateThreadName%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(home)/page\": \"action-browser\",\n        \"app/(dashboard)/dashboard/page\": \"action-browser\"\n      }\n    },\n    \"608b7967e2831d8c5568299992a639f94e11b15120\": {\n      \"workers\": {\n        \"app/(dashboard)/dashboard/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22D%3A%5C%5Csoft%5C%5CSeaProject%5C%5Cgithub_open_project_success%5C%5Csuna%5C%5Cfrontend%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cteams.ts%22%2C%5B%7B%22id%22%3A%22608b7967e2831d8c5568299992a639f94e11b15120%22%2C%22exportedName%22%3A%22createTeam%22%7D%2C%7B%22id%22%3A%2260c9e4ae5ba61c10ed410caf36fe992bce27657f86%22%2C%22exportedName%22%3A%22editTeamSlug%22%7D%2C%7B%22id%22%3A%2260cbf9dc0d607876a1ece630fcdca5f5220c3d4a43%22%2C%22exportedName%22%3A%22editTeamName%22%7D%5D%5D%2C%5B%22D%3A%5C%5Csoft%5C%5CSeaProject%5C%5Cgithub_open_project_success%5C%5Csuna%5C%5Cfrontend%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cthreads.ts%22%2C%5B%7B%22id%22%3A%227f4dff9eb83a22d35d3acb6f82d7b420b98f32e57e%22%2C%22exportedName%22%3A%22generateThreadName%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": false\n        },\n        \"app/(dashboard)/agents/[threadId]/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22D%3A%5C%5Csoft%5C%5CSeaProject%5C%5Cgithub_open_project_success%5C%5Csuna%5C%5Cfrontend%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cteams.ts%22%2C%5B%7B%22id%22%3A%22608b7967e2831d8c5568299992a639f94e11b15120%22%2C%22exportedName%22%3A%22createTeam%22%7D%2C%7B%22id%22%3A%2260c9e4ae5ba61c10ed410caf36fe992bce27657f86%22%2C%22exportedName%22%3A%22editTeamSlug%22%7D%2C%7B%22id%22%3A%2260cbf9dc0d607876a1ece630fcdca5f5220c3d4a43%22%2C%22exportedName%22%3A%22editTeamName%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(dashboard)/dashboard/page\": \"action-browser\",\n        \"app/(dashboard)/agents/[threadId]/page\": \"action-browser\"\n      }\n    },\n    \"60c9e4ae5ba61c10ed410caf36fe992bce27657f86\": {\n      \"workers\": {\n        \"app/(dashboard)/dashboard/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22D%3A%5C%5Csoft%5C%5CSeaProject%5C%5Cgithub_open_project_success%5C%5Csuna%5C%5Cfrontend%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cteams.ts%22%2C%5B%7B%22id%22%3A%22608b7967e2831d8c5568299992a639f94e11b15120%22%2C%22exportedName%22%3A%22createTeam%22%7D%2C%7B%22id%22%3A%2260c9e4ae5ba61c10ed410caf36fe992bce27657f86%22%2C%22exportedName%22%3A%22editTeamSlug%22%7D%2C%7B%22id%22%3A%2260cbf9dc0d607876a1ece630fcdca5f5220c3d4a43%22%2C%22exportedName%22%3A%22editTeamName%22%7D%5D%5D%2C%5B%22D%3A%5C%5Csoft%5C%5CSeaProject%5C%5Cgithub_open_project_success%5C%5Csuna%5C%5Cfrontend%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cthreads.ts%22%2C%5B%7B%22id%22%3A%227f4dff9eb83a22d35d3acb6f82d7b420b98f32e57e%22%2C%22exportedName%22%3A%22generateThreadName%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": false\n        },\n        \"app/(dashboard)/agents/[threadId]/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22D%3A%5C%5Csoft%5C%5CSeaProject%5C%5Cgithub_open_project_success%5C%5Csuna%5C%5Cfrontend%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cteams.ts%22%2C%5B%7B%22id%22%3A%22608b7967e2831d8c5568299992a639f94e11b15120%22%2C%22exportedName%22%3A%22createTeam%22%7D%2C%7B%22id%22%3A%2260c9e4ae5ba61c10ed410caf36fe992bce27657f86%22%2C%22exportedName%22%3A%22editTeamSlug%22%7D%2C%7B%22id%22%3A%2260cbf9dc0d607876a1ece630fcdca5f5220c3d4a43%22%2C%22exportedName%22%3A%22editTeamName%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(dashboard)/dashboard/page\": \"action-browser\",\n        \"app/(dashboard)/agents/[threadId]/page\": \"action-browser\"\n      }\n    },\n    \"60cbf9dc0d607876a1ece630fcdca5f5220c3d4a43\": {\n      \"workers\": {\n        \"app/(dashboard)/dashboard/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22D%3A%5C%5Csoft%5C%5CSeaProject%5C%5Cgithub_open_project_success%5C%5Csuna%5C%5Cfrontend%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cteams.ts%22%2C%5B%7B%22id%22%3A%22608b7967e2831d8c5568299992a639f94e11b15120%22%2C%22exportedName%22%3A%22createTeam%22%7D%2C%7B%22id%22%3A%2260c9e4ae5ba61c10ed410caf36fe992bce27657f86%22%2C%22exportedName%22%3A%22editTeamSlug%22%7D%2C%7B%22id%22%3A%2260cbf9dc0d607876a1ece630fcdca5f5220c3d4a43%22%2C%22exportedName%22%3A%22editTeamName%22%7D%5D%5D%2C%5B%22D%3A%5C%5Csoft%5C%5CSeaProject%5C%5Cgithub_open_project_success%5C%5Csuna%5C%5Cfrontend%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cthreads.ts%22%2C%5B%7B%22id%22%3A%227f4dff9eb83a22d35d3acb6f82d7b420b98f32e57e%22%2C%22exportedName%22%3A%22generateThreadName%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": false\n        },\n        \"app/(dashboard)/agents/[threadId]/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22D%3A%5C%5Csoft%5C%5CSeaProject%5C%5Cgithub_open_project_success%5C%5Csuna%5C%5Cfrontend%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cteams.ts%22%2C%5B%7B%22id%22%3A%22608b7967e2831d8c5568299992a639f94e11b15120%22%2C%22exportedName%22%3A%22createTeam%22%7D%2C%7B%22id%22%3A%2260c9e4ae5ba61c10ed410caf36fe992bce27657f86%22%2C%22exportedName%22%3A%22editTeamSlug%22%7D%2C%7B%22id%22%3A%2260cbf9dc0d607876a1ece630fcdca5f5220c3d4a43%22%2C%22exportedName%22%3A%22editTeamName%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(dashboard)/dashboard/page\": \"action-browser\",\n        \"app/(dashboard)/agents/[threadId]/page\": \"action-browser\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"process.env.NEXT_SERVER_ACTIONS_ENCRYPTION_KEY\"\n}"