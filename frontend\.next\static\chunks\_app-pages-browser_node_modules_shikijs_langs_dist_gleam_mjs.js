"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_gleam_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/gleam.mjs":
/*!****************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/gleam.mjs ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Gleam\\\",\\\"fileTypes\\\":[\\\"gleam\\\"],\\\"name\\\":\\\"gleam\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#keywords\\\"},{\\\"include\\\":\\\"#strings\\\"},{\\\"include\\\":\\\"#constant\\\"},{\\\"include\\\":\\\"#entity\\\"},{\\\"include\\\":\\\"#discards\\\"}],\\\"repository\\\":{\\\"binary_number\\\":{\\\"match\\\":\\\"\\\\\\\\b0[bB]0*1[01_]*\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.binary.gleam\\\",\\\"patterns\\\":[]},\\\"comments\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"//.*\\\",\\\"name\\\":\\\"comment.line.gleam\\\"}]},\\\"constant\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#binary_number\\\"},{\\\"include\\\":\\\"#octal_number\\\"},{\\\"include\\\":\\\"#hexadecimal_number\\\"},{\\\"include\\\":\\\"#decimal_number\\\"},{\\\"match\\\":\\\"\\\\\\\\p{upper}\\\\\\\\p{alnum}*\\\",\\\"name\\\":\\\"entity.name.type.gleam\\\"}]},\\\"decimal_number\\\":{\\\"match\\\":\\\"\\\\\\\\b(0*[1-9][0-9_]*|0)(\\\\\\\\.(0*[1-9][0-9_]*|0)?(e-?0*[1-9][0-9]*)?)?\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.decimal.gleam\\\",\\\"patterns\\\":[]},\\\"discards\\\":{\\\"match\\\":\\\"\\\\\\\\b_\\\\\\\\p{word}+{0,1}\\\\\\\\b\\\",\\\"name\\\":\\\"comment.unused.gleam\\\"},\\\"entity\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\b(\\\\\\\\p{lower}\\\\\\\\p{word}*)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.gleam\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"match\\\":\\\"\\\\\\\\b(\\\\\\\\p{lower}\\\\\\\\p{word}*):\\\\\\\\s\\\",\\\"name\\\":\\\"variable.parameter.gleam\\\"},{\\\"match\\\":\\\"\\\\\\\\b(\\\\\\\\p{lower}\\\\\\\\p{word}*):\\\",\\\"name\\\":\\\"entity.name.namespace.gleam\\\"}]},\\\"hexadecimal_number\\\":{\\\"match\\\":\\\"\\\\\\\\b0[xX]0*[1-9a-zA-Z][0-9a-zA-Z]*\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.hexadecimal.gleam\\\",\\\"patterns\\\":[]},\\\"keywords\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(as|use|case|if|fn|import|let|assert|pub|type|opaque|const|todo|panic|else|try|echo)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.gleam\\\"},{\\\"match\\\":\\\"(<-|->)\\\",\\\"name\\\":\\\"keyword.operator.arrow.gleam\\\"},{\\\"match\\\":\\\"\\\\\\\\|>\\\",\\\"name\\\":\\\"keyword.operator.pipe.gleam\\\"},{\\\"match\\\":\\\"\\\\\\\\.\\\\\\\\.\\\",\\\"name\\\":\\\"keyword.operator.splat.gleam\\\"},{\\\"match\\\":\\\"(==|!=)\\\",\\\"name\\\":\\\"keyword.operator.comparison.gleam\\\"},{\\\"match\\\":\\\"([<>](?:=\\\\\\\\.|\\\\\\\\.))\\\",\\\"name\\\":\\\"keyword.operator.comparison.float.gleam\\\"},{\\\"match\\\":\\\"(<=|>=|[<>])\\\",\\\"name\\\":\\\"keyword.operator.comparison.int.gleam\\\"},{\\\"match\\\":\\\"(&&|\\\\\\\\|\\\\\\\\|)\\\",\\\"name\\\":\\\"keyword.operator.logical.gleam\\\"},{\\\"match\\\":\\\"<>\\\",\\\"name\\\":\\\"keyword.operator.string.gleam\\\"},{\\\"match\\\":\\\"\\\\\\\\|\\\",\\\"name\\\":\\\"keyword.operator.other.gleam\\\"},{\\\"match\\\":\\\"(\\\\\\\\+\\\\\\\\.|-\\\\\\\\.|/\\\\\\\\.|\\\\\\\\*\\\\\\\\.)\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.float.gleam\\\"},{\\\"match\\\":\\\"([+\\\\\\\\-/*%])\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.int.gleam\\\"},{\\\"match\\\":\\\"=\\\",\\\"name\\\":\\\"keyword.operator.assignment.gleam\\\"}]},\\\"octal_number\\\":{\\\"match\\\":\\\"\\\\\\\\b0[oO]0*[1-7][0-7]*\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.octal.gleam\\\",\\\"patterns\\\":[]},\\\"strings\\\":{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"end\\\":\\\"\\\\\\\"\\\",\\\"name\\\":\\\"string.quoted.double.gleam\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"constant.character.escape.gleam\\\"}]}},\\\"scopeName\\\":\\\"source.gleam\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/gleam.mjs\n"));

/***/ })

}]);