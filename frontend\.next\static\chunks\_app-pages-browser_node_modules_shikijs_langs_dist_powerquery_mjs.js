"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_powerquery_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/powerquery.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/powerquery.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"PowerQuery\\\",\\\"fileTypes\\\":[\\\"pq\\\",\\\"pqm\\\"],\\\"name\\\":\\\"powerquery\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#Noise\\\"},{\\\"include\\\":\\\"#LiteralExpression\\\"},{\\\"include\\\":\\\"#Keywords\\\"},{\\\"include\\\":\\\"#ImplicitVariable\\\"},{\\\"include\\\":\\\"#IntrinsicVariable\\\"},{\\\"include\\\":\\\"#Operators\\\"},{\\\"include\\\":\\\"#DotOperators\\\"},{\\\"include\\\":\\\"#TypeName\\\"},{\\\"include\\\":\\\"#RecordExpression\\\"},{\\\"include\\\":\\\"#Punctuation\\\"},{\\\"include\\\":\\\"#QuotedIdentifier\\\"},{\\\"include\\\":\\\"#Identifier\\\"}],\\\"repository\\\":{\\\"BlockComment\\\":{\\\"begin\\\":\\\"/\\\\\\\\*\\\",\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"name\\\":\\\"comment.block.powerquery\\\"},\\\"DecimalNumber\\\":{\\\"match\\\":\\\"(?<![\\\\\\\\d\\\\\\\\w])(\\\\\\\\d*\\\\\\\\.\\\\\\\\d+)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.decimal.powerquery\\\"},\\\"DotOperators\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.ellipsis.powerquery\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.list.powerquery\\\"}},\\\"match\\\":\\\"(?<!\\\\\\\\.)(?:(\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(\\\\\\\\.\\\\\\\\.))(?!\\\\\\\\.)\\\"},\\\"EscapeSequence\\\":{\\\"begin\\\":\\\"#\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.escapesequence.begin.powerquery\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.escapesequence.end.powerquery\\\"}},\\\"name\\\":\\\"constant.character.escapesequence.powerquery\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(#|\\\\\\\\h{4}|\\\\\\\\h{8}|cr|lf|tab)(?:,(#|\\\\\\\\h{4}|\\\\\\\\h{8}|cr|lf|tab))*\\\"},{\\\"match\\\":\\\"[^)]\\\",\\\"name\\\":\\\"invalid.illegal.escapesequence.powerquery\\\"}]},\\\"FloatNumber\\\":{\\\"match\\\":\\\"(\\\\\\\\d*\\\\\\\\.)?\\\\\\\\d+([eE])([+-])?\\\\\\\\d+\\\",\\\"name\\\":\\\"constant.numeric.float.powerquery\\\"},\\\"HexNumber\\\":{\\\"match\\\":\\\"0([xX])\\\\\\\\h+\\\",\\\"name\\\":\\\"constant.numeric.integer.hexadecimal.powerquery\\\"},\\\"Identifier\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.inclusiveidentifier.powerquery\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.powerquery\\\"}},\\\"match\\\":\\\"(?<![._\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\d\\\\\\\\p{Pc}\\\\\\\\p{Mn}\\\\\\\\p{Mc}\\\\\\\\p{Cf}])(@?)([_\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}][_\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\d\\\\\\\\p{Pc}\\\\\\\\p{Mn}\\\\\\\\p{Mc}\\\\\\\\p{Cf}]*(?:\\\\\\\\.[_\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}][_\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\d\\\\\\\\p{Pc}\\\\\\\\p{Mn}\\\\\\\\p{Mc}\\\\\\\\p{Cf}])*)\\\\\\\\b\\\"},\\\"ImplicitVariable\\\":{\\\"match\\\":\\\"\\\\\\\\b_\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.operator.implicitvariable.powerquery\\\"},\\\"InclusiveIdentifier\\\":{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"inclusiveidentifier.powerquery\\\"}},\\\"match\\\":\\\"@\\\"},\\\"IntNumber\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.numeric.integer.powerquery\\\"}},\\\"match\\\":\\\"\\\\\\\\b(\\\\\\\\d+)\\\\\\\\b\\\"},\\\"IntrinsicVariable\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.language.intrinsicvariable.powerquery\\\"}},\\\"match\\\":\\\"(?<![\\\\\\\\d\\\\\\\\w])(#s(?:ections|hared))\\\\\\\\b\\\"},\\\"Keywords\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.word.logical.powerquery\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.conditional.powerquery\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.control.exception.powerquery\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.other.powerquery\\\"},\\\"5\\\":{\\\"name\\\":\\\"keyword.powerquery\\\"}},\\\"match\\\":\\\"\\\\\\\\b(?:(and|or|not)|(if|then|else)|(try|otherwise)|(as|each|in|is|let|meta|type|error)|(s(?:ection|hared)))\\\\\\\\b\\\"},\\\"LineComment\\\":{\\\"match\\\":\\\"//.*\\\",\\\"name\\\":\\\"comment.line.double-slash.powerquery\\\"},\\\"LiteralExpression\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#String\\\"},{\\\"include\\\":\\\"#NumericConstant\\\"},{\\\"include\\\":\\\"#LogicalConstant\\\"},{\\\"include\\\":\\\"#NullConstant\\\"},{\\\"include\\\":\\\"#FloatNumber\\\"},{\\\"include\\\":\\\"#DecimalNumber\\\"},{\\\"include\\\":\\\"#HexNumber\\\"},{\\\"include\\\":\\\"#IntNumber\\\"}]},\\\"LogicalConstant\\\":{\\\"match\\\":\\\"\\\\\\\\b(true|false)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.logical.powerquery\\\"},\\\"Noise\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#BlockComment\\\"},{\\\"include\\\":\\\"#LineComment\\\"},{\\\"include\\\":\\\"#Whitespace\\\"}]},\\\"NullConstant\\\":{\\\"match\\\":\\\"\\\\\\\\b(null)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.null.powerquery\\\"},\\\"NumericConstant\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.language.numeric.float.powerquery\\\"}},\\\"match\\\":\\\"(?<![\\\\\\\\d\\\\\\\\w])(#(?:infinity|nan))\\\\\\\\b\\\"},\\\"Operators\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.function.powerquery\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.assignment-or-comparison.powerquery\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.operator.comparison.powerquery\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.operator.combination.powerquery\\\"},\\\"5\\\":{\\\"name\\\":\\\"keyword.operator.arithmetic.powerquery\\\"},\\\"6\\\":{\\\"name\\\":\\\"keyword.operator.sectionaccess.powerquery\\\"},\\\"7\\\":{\\\"name\\\":\\\"keyword.operator.optional.powerquery\\\"}},\\\"match\\\":\\\"(=>)|(=)|(<>|[<>]|<=|>=)|(&)|([+\\\\\\\\-*/])|(!)|(\\\\\\\\?)\\\"},\\\"Punctuation\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.powerquery\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.section.parens.begin.powerquery\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.section.parens.end.powerquery\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.section.braces.begin.powerquery\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.section.braces.end.powerquery\\\"}},\\\"match\\\":\\\"(,)|(\\\\\\\\()|(\\\\\\\\))|(\\\\\\\\{)|(})\\\"},\\\"QuotedIdentifier\\\":{\\\"begin\\\":\\\"#\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.quotedidentifier.begin.powerquery\\\"}},\\\"end\\\":\\\"\\\\\\\"(?!\\\\\\\")\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.quotedidentifier.end.powerquery\\\"}},\\\"name\\\":\\\"entity.name.powerquery\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\"\\\\\\\"\\\",\\\"name\\\":\\\"constant.character.escape.quote.powerquery\\\"},{\\\"include\\\":\\\"#EscapeSequence\\\"}]},\\\"RecordExpression\\\":{\\\"begin\\\":\\\"\\\\\\\\[\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.brackets.begin.powerquery\\\"}},\\\"contentName\\\":\\\"meta.recordexpression.powerquery\\\",\\\"end\\\":\\\"]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.brackets.end.powerquery\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},\\\"String\\\":{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.powerquery\\\"}},\\\"end\\\":\\\"\\\\\\\"(?!\\\\\\\")\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.powerquery\\\"}},\\\"name\\\":\\\"string.quoted.double.powerquery\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\"\\\\\\\"\\\",\\\"name\\\":\\\"constant.character.escape.quote.powerquery\\\"},{\\\"include\\\":\\\"#EscapeSequence\\\"}]},\\\"TypeName\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.powerquery\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.type.powerquery\\\"}},\\\"match\\\":\\\"\\\\\\\\b(?:(optional|nullable)|(action|any|anynonnull|binary|date|datetime|datetimezone|duration|function|list|logical|none|null|number|record|table|text|type))\\\\\\\\b\\\"},\\\"Whitespace\\\":{\\\"match\\\":\\\"\\\\\\\\s+\\\"}},\\\"scopeName\\\":\\\"source.powerquery\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/powerquery.mjs\n"));

/***/ })

}]);