"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_bsl_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/bsl.mjs":
/*!**************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/bsl.mjs ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _sdbl_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./sdbl.mjs */ \"(app-pages-browser)/./node_modules/@shikijs/langs/dist/sdbl.mjs\");\n\n\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"1C (Enterprise)\\\",\\\"fileTypes\\\":[\\\"bsl\\\",\\\"os\\\"],\\\"name\\\":\\\"bsl\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#basic\\\"},{\\\"include\\\":\\\"#miscellaneous\\\"},{\\\"begin\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё.]|^)(Процедура|Procedure|Функция|Function)\\\\\\\\s+([a-zа-яё0-9_]+)\\\\\\\\s*(\\\\\\\\())\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.bsl\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.bsl\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.bracket.begin.bsl\\\"}},\\\"end\\\":\\\"(?i:(\\\\\\\\))\\\\\\\\s*((Экспорт|Export)(?=[^\\\\\\\\wа-яё.]|$))?)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.bracket.end.bsl\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.modifier.bsl\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#annotations\\\"},{\\\"include\\\":\\\"#basic\\\"},{\\\"match\\\":\\\"(=)\\\",\\\"name\\\":\\\"keyword.operator.assignment.bsl\\\"},{\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё.]|^)(Знач|Val)(?=[^\\\\\\\\wа-яё.]|$))\\\",\\\"name\\\":\\\"storage.modifier.bsl\\\"},{\\\"match\\\":\\\"(?<=[^\\\\\\\\wа-яё.]|^)((?<==)(?i)[a-zа-яё0-9_]+)(?=[^\\\\\\\\wа-яё.]|$)\\\",\\\"name\\\":\\\"invalid.illegal.bsl\\\"},{\\\"match\\\":\\\"(?<=[^\\\\\\\\wа-яё.]|^)((?<==\\\\\\\\s)\\\\\\\\s*(?i)[a-zа-яё0-9_]+)(?=[^\\\\\\\\wа-яё.]|$)\\\",\\\"name\\\":\\\"invalid.illegal.bsl\\\"},{\\\"match\\\":\\\"(?i:[a-zа-яё0-9_]+)\\\",\\\"name\\\":\\\"variable.parameter.bsl\\\"}]},{\\\"begin\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё.]|^)(Перем|Var)\\\\\\\\s+([a-zа-яё0-9_]+)\\\\\\\\s*)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.var.bsl\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.bsl\\\"}},\\\"end\\\":\\\"(;)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.bsl\\\"}},\\\"patterns\\\":[{\\\"match\\\":\\\"(,)\\\",\\\"name\\\":\\\"keyword.operator.bsl\\\"},{\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё.]|^)(Экспорт|Export)(?=[^\\\\\\\\wа-яё.]|$))\\\",\\\"name\\\":\\\"storage.modifier.bsl\\\"},{\\\"match\\\":\\\"(?i:[a-zа-яё0-9_]+)\\\",\\\"name\\\":\\\"variable.bsl\\\"}]},{\\\"begin\\\":\\\"(?i:(?<=;|^)\\\\\\\\s*(Если|If))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.conditional.bsl\\\"}},\\\"end\\\":\\\"(?i:(Тогда|Then))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.conditional.bsl\\\"}},\\\"name\\\":\\\"meta.conditional.bsl\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#basic\\\"},{\\\"include\\\":\\\"#miscellaneous\\\"}]},{\\\"begin\\\":\\\"(?i:(?<=;|^)\\\\\\\\s*([\\\\\\\\wа-яё]+))\\\\\\\\s*(=)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.assignment.bsl\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.assignment.bsl\\\"}},\\\"end\\\":\\\"(?i:(?=(;|Иначе|Конец|Els|End)))\\\",\\\"name\\\":\\\"meta.var-single-variable.bsl\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#basic\\\"},{\\\"include\\\":\\\"#miscellaneous\\\"}]},{\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё.]|^)(КонецПроцедуры|EndProcedure|КонецФункции|EndFunction)(?=[^\\\\\\\\wа-яё.]|$))\\\",\\\"name\\\":\\\"storage.type.bsl\\\"},{\\\"match\\\":\\\"(?i)#(Использовать|Use)(?=[^\\\\\\\\wа-яё.]|$)\\\",\\\"name\\\":\\\"keyword.control.import.bsl\\\"},{\\\"match\\\":\\\"(?i)#native\\\",\\\"name\\\":\\\"keyword.control.native.bsl\\\"},{\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё.]|^)(Прервать|Break|Продолжить|Continue|Возврат|Return)(?=[^\\\\\\\\wа-яё.]|$))\\\",\\\"name\\\":\\\"keyword.control.bsl\\\"},{\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё.]|^)(Если|If|Иначе|Else|ИначеЕсли|ElsIf|Тогда|Then|КонецЕсли|EndIf)(?=[^\\\\\\\\wа-яё.]|$))\\\",\\\"name\\\":\\\"keyword.control.conditional.bsl\\\"},{\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё.]|^)(Попытка|Try|Исключение|Except|КонецПопытки|EndTry|ВызватьИсключение|Raise)(?=[^\\\\\\\\wа-яё.]|$))\\\",\\\"name\\\":\\\"keyword.control.exception.bsl\\\"},{\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё.]|^)(Пока|While|(Для|For)(\\\\\\\\s+(Каждого|Each))?|Из|In|По|To|Цикл|Do|КонецЦикла|EndDo)(?=[^\\\\\\\\wа-яё.]|$))\\\",\\\"name\\\":\\\"keyword.control.repeat.bsl\\\"},{\\\"match\\\":\\\"(?i:&(НаКлиенте((НаСервере(БезКонтекста)?)?)|AtClient((AtServer(NoContext)?)?)|НаСервере(БезКонтекста)?|AtServer(NoContext)?))\\\",\\\"name\\\":\\\"storage.modifier.directive.bsl\\\"},{\\\"include\\\":\\\"#annotations\\\"},{\\\"match\\\":\\\"(?i:#(Если|If|ИначеЕсли|ElsIf|Иначе|Else|КонецЕсли|EndIf).*(Тогда|Then)?)\\\",\\\"name\\\":\\\"keyword.other.preprocessor.bsl\\\"},{\\\"begin\\\":\\\"(?i)(#(Область|Region))(\\\\\\\\s+([\\\\\\\\wа-яё]+))?\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.section.bsl\\\"},\\\"4\\\":{\\\"name\\\":\\\"entity.name.section.bsl\\\"}},\\\"end\\\":\\\"$\\\"},{\\\"match\\\":\\\"(?i)#(КонецОбласти|EndRegion)\\\",\\\"name\\\":\\\"keyword.other.section.bsl\\\"},{\\\"match\\\":\\\"(?i)#(Удаление|Delete)\\\",\\\"name\\\":\\\"keyword.other.section.bsl\\\"},{\\\"match\\\":\\\"(?i)#(КонецУдаления|EndDelete)\\\",\\\"name\\\":\\\"keyword.other.section.bsl\\\"},{\\\"match\\\":\\\"(?i)#(Вставка|Insert)\\\",\\\"name\\\":\\\"keyword.other.section.bsl\\\"},{\\\"match\\\":\\\"(?i)#(КонецВставки|EndInsert)\\\",\\\"name\\\":\\\"keyword.other.section.bsl\\\"}],\\\"repository\\\":{\\\"annotations\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)(&([a-zа-яё0-9_]+))\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.annotation.bsl\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.bracket.begin.bsl\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.bracket.end.bsl\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#basic\\\"},{\\\"match\\\":\\\"(=)\\\",\\\"name\\\":\\\"keyword.operator.assignment.bsl\\\"},{\\\"match\\\":\\\"(?<=[^\\\\\\\\wа-яё.]|^)((?<==)(?i)[a-zа-яё0-9_]+)(?=[^\\\\\\\\wа-яё.]|$)\\\",\\\"name\\\":\\\"invalid.illegal.bsl\\\"},{\\\"match\\\":\\\"(?<=[^\\\\\\\\wа-яё.]|^)((?<==\\\\\\\\s)\\\\\\\\s*(?i)[a-zа-яё0-9_]+)(?=[^\\\\\\\\wа-яё.]|$)\\\",\\\"name\\\":\\\"invalid.illegal.bsl\\\"},{\\\"match\\\":\\\"(?i)[a-zа-яё0-9_]+\\\",\\\"name\\\":\\\"variable.annotation.bsl\\\"}]},{\\\"match\\\":\\\"(?i)(&([a-zа-яё0-9_]+))\\\",\\\"name\\\":\\\"storage.type.annotation.bsl\\\"}]},\\\"basic\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"//\\\",\\\"end\\\":\\\"$\\\",\\\"name\\\":\\\"comment.line.double-slash.bsl\\\"},{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"end\\\":\\\"\\\\\\\"(?!\\\\\\\")\\\",\\\"name\\\":\\\"string.quoted.double.bsl\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#query\\\"},{\\\"match\\\":\\\"\\\\\\\"\\\\\\\"\\\",\\\"name\\\":\\\"constant.character.escape.bsl\\\"},{\\\"match\\\":\\\"(^\\\\\\\\s*//.*$)\\\",\\\"name\\\":\\\"comment.line.double-slash.bsl\\\"}]},{\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё.]|^)(Неопределено|Undefined|Истина|True|Ложь|False|NULL)(?=[^\\\\\\\\wа-яё.]|$))\\\",\\\"name\\\":\\\"constant.language.bsl\\\"},{\\\"match\\\":\\\"(?<=[^\\\\\\\\wа-яё.]|^)(\\\\\\\\d+\\\\\\\\.?\\\\\\\\d*)(?=[^\\\\\\\\wа-яё.]|$)\\\",\\\"name\\\":\\\"constant.numeric.bsl\\\"},{\\\"match\\\":\\\"'((\\\\\\\\d{4}[^\\\\\\\\d']*\\\\\\\\d{2}[^\\\\\\\\d']*\\\\\\\\d{2})([^\\\\\\\\d']*\\\\\\\\d{2}[^\\\\\\\\d']*\\\\\\\\d{2}([^\\\\\\\\d']*\\\\\\\\d{2})?)?)'\\\",\\\"name\\\":\\\"constant.other.date.bsl\\\"},{\\\"match\\\":\\\"(,)\\\",\\\"name\\\":\\\"keyword.operator.bsl\\\"},{\\\"match\\\":\\\"(\\\\\\\\()\\\",\\\"name\\\":\\\"punctuation.bracket.begin.bsl\\\"},{\\\"match\\\":\\\"(\\\\\\\\))\\\",\\\"name\\\":\\\"punctuation.bracket.end.bsl\\\"}]},\\\"miscellaneous\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё.]|^)(НЕ|NOT|И|AND|ИЛИ|OR)(?=[^\\\\\\\\wа-яё.]|$))\\\",\\\"name\\\":\\\"keyword.operator.logical.bsl\\\"},{\\\"match\\\":\\\"<=|>=|[=<>]\\\",\\\"name\\\":\\\"keyword.operator.comparison.bsl\\\"},{\\\"match\\\":\\\"([+\\\\\\\\-*/%])\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.bsl\\\"},{\\\"match\\\":\\\"([;?])\\\",\\\"name\\\":\\\"keyword.operator.bsl\\\"},{\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё.]|^)(Новый|New)(?=[^\\\\\\\\wа-яё.]|$))\\\",\\\"name\\\":\\\"support.function.bsl\\\"},{\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё.]|^)(СтрДлина|StrLen|СокрЛ|TrimL|СокрП|TrimR|СокрЛП|TrimAll|Лев|Left|Прав|Right|Сред|Mid|СтрНайти|StrFind|ВРег|Upper|НРег|Lower|ТРег|Title|Символ|Char|КодСимвола|CharCode|ПустаяСтрока|IsBlankString|СтрЗаменить|StrReplace|СтрЧислоСтрок|StrLineCount|СтрПолучитьСтроку|StrGetLine|СтрЧислоВхождений|StrOccurrenceCount|СтрСравнить|StrCompare|СтрНачинаетсяС|StrStartWith|СтрЗаканчиваетсяНа|StrEndsWith|СтрРазделить|StrSplit|СтрСоединить|StrConcat)\\\\\\\\s*(?=\\\\\\\\())\\\",\\\"name\\\":\\\"support.function.bsl\\\"},{\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё.]|^)(Цел|Int|Окр|Round|ACos|ASin|ATan|Cos|Exp|Log|Log10|Pow|Sin|Sqrt|Tan)\\\\\\\\s*(?=\\\\\\\\())\\\",\\\"name\\\":\\\"support.function.bsl\\\"},{\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё.]|^)(Год|Year|Месяц|Month|День|Day|Час|Hour|Минута|Minute|Секунда|Second|НачалоГода|BegOfYear|НачалоДня|BegOfDay|НачалоКвартала|BegOfQuarter|НачалоМесяца|BegOfMonth|НачалоМинуты|BegOfMinute|НачалоНедели|BegOfWeek|НачалоЧаса|BegOfHour|КонецГода|EndOfYear|КонецДня|EndOfDay|КонецКвартала|EndOfQuarter|КонецМесяца|EndOfMonth|КонецМинуты|EndOfMinute|КонецНедели|EndOfWeek|КонецЧаса|EndOfHour|НеделяГода|WeekOfYear|ДеньГода|DayOfYear|ДеньНедели|WeekDay|ТекущаяДата|CurrentDate|ДобавитьМесяц|AddMonth)\\\\\\\\s*(?=\\\\\\\\())\\\",\\\"name\\\":\\\"support.function.bsl\\\"},{\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё.]|^)(Тип|Type|ТипЗнч|TypeOf)\\\\\\\\s*(?=\\\\\\\\())\\\",\\\"name\\\":\\\"support.function.bsl\\\"},{\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё.]|^)(Булево|Boolean|Число|Number|Строка|String|Дата|Date)\\\\\\\\s*(?=\\\\\\\\())\\\",\\\"name\\\":\\\"support.function.bsl\\\"},{\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё.]|^)(ПоказатьВопрос|ShowQueryBox|Вопрос|DoQueryBox|ПоказатьПредупреждение|ShowMessageBox|Предупреждение|DoMessageBox|Сообщить|Message|ОчиститьСообщения|ClearMessages|ОповеститьОбИзменении|NotifyChanged|Состояние|Status|Сигнал|Beep|ПоказатьЗначение|ShowValue|ОткрытьЗначение|OpenValue|Оповестить|Notify|ОбработкаПрерыванияПользователя|UserInterruptProcessing|ОткрытьСодержаниеСправки|OpenHelpContent|ОткрытьИндексСправки|OpenHelpIndex|ОткрытьСправку|OpenHelp|ПоказатьИнформациюОбОшибке|ShowErrorInfo|КраткоеПредставлениеОшибки|BriefErrorDescription|ПодробноеПредставлениеОшибки|DetailErrorDescription|ПолучитьФорму|GetForm|ЗакрытьСправку|CloseHelp|ПоказатьОповещениеПользователя|ShowUserNotification|ОткрытьФорму|OpenForm|ОткрытьФормуМодально|OpenFormModal|АктивноеОкно|ActiveWindow|ВыполнитьОбработкуОповещения|ExecuteNotifyProcessing)\\\\\\\\s*(?=\\\\\\\\())\\\",\\\"name\\\":\\\"support.function.bsl\\\"},{\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё.]|^)(ПоказатьВводЗначения|ShowInputValue|ВвестиЗначение|InputValue|ПоказатьВводЧисла|ShowInputNumber|ВвестиЧисло|InputNumber|ПоказатьВводСтроки|ShowInputString|ВвестиСтроку|InputString|ПоказатьВводДаты|ShowInputDate|ВвестиДату|InputDate)\\\\\\\\s*(?=\\\\\\\\())\\\",\\\"name\\\":\\\"support.function.bsl\\\"},{\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё.]|^)(Формат|Format|ЧислоПрописью|NumberInWords|НСтр|NStr|ПредставлениеПериода|PeriodPresentation|СтрШаблон|StrTemplate)\\\\\\\\s*(?=\\\\\\\\())\\\",\\\"name\\\":\\\"support.function.bsl\\\"},{\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё.]|^)(ПолучитьОбщийМакет|GetCommonTemplate|ПолучитьОбщуюФорму|GetCommonForm|ПредопределенноеЗначение|PredefinedValue|ПолучитьПолноеИмяПредопределенногоЗначения|GetPredefinedValueFullName)\\\\\\\\s*(?=\\\\\\\\())\\\",\\\"name\\\":\\\"support.function.bsl\\\"},{\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё.]|^)(ПолучитьЗаголовокСистемы|GetCaption|ПолучитьСкоростьКлиентскогоСоединения|GetClientConnectionSpeed|ПодключитьОбработчикОжидания|AttachIdleHandler|УстановитьЗаголовокСистемы|SetCaption|ОтключитьОбработчикОжидания|DetachIdleHandler|ИмяКомпьютера|ComputerName|ЗавершитьРаботуСистемы|Exit|ИмяПользователя|UserName|ПрекратитьРаботуСистемы|Terminate|ПолноеИмяПользователя|UserFullName|ЗаблокироватьРаботуПользователя|LockApplication|КаталогПрограммы|BinDir|КаталогВременныхФайлов|TempFilesDir|ПравоДоступа|AccessRight|РольДоступна|IsInRole|ТекущийЯзык|CurrentLanguage|ТекущийКодЛокализации|CurrentLocaleCode|СтрокаСоединенияИнформационнойБазы|InfoBaseConnectionString|ПодключитьОбработчикОповещения|AttachNotificationHandler|ОтключитьОбработчикОповещения|DetachNotificationHandler|ПолучитьСообщенияПользователю|GetUserMessages|ПараметрыДоступа|AccessParameters|ПредставлениеПриложения|ApplicationPresentation|ТекущийЯзыкСистемы|CurrentSystemLanguage|ЗапуститьСистему|RunSystem|ТекущийРежимЗапуска|CurrentRunMode|УстановитьЧасовойПоясСеанса|SetSessionTimeZone|ЧасовойПоясСеанса|SessionTimeZone|ТекущаяДатаСеанса|CurrentSessionDate|УстановитьКраткийЗаголовокПриложения|SetShortApplicationCaption|ПолучитьКраткийЗаголовокПриложения|GetShortApplicationCaption|ПредставлениеПрава|RightPresentation|ВыполнитьПроверкуПравДоступа|VerifyAccessRights|РабочийКаталогДанныхПользователя|UserDataWorkDir|КаталогДокументов|DocumentsDir|ПолучитьИнформациюЭкрановКлиента|GetClientDisplaysInformation|ТекущийВариантОсновногоШрифтаКлиентскогоПриложения|ClientApplicationBaseFontCurrentVariant|ТекущийВариантИнтерфейсаКлиентскогоПриложения|ClientApplicationInterfaceCurrentVariant|УстановитьЗаголовокКлиентскогоПриложения|SetClientApplicationCaption|ПолучитьЗаголовокКлиентскогоПриложения|GetClientApplicationCaption|НачатьПолучениеКаталогаВременныхФайлов|BeginGettingTempFilesDir|НачатьПолучениеКаталогаДокументов|BeginGettingDocumentsDir|НачатьПолучениеРабочегоКаталогаДанныхПользователя|BeginGettingUserDataWorkDir|ПодключитьОбработчикЗапросаНастроекКлиентаЛицензирования|AttachLicensingClientParametersRequestHandler|ОтключитьОбработчикЗапросаНастроекКлиентаЛицензирования|DetachLicensingClientParametersRequestHandler|КаталогБиблиотекиМобильногоУстройства|MobileDeviceLibraryDir)\\\\\\\\s*(?=\\\\\\\\())\\\",\\\"name\\\":\\\"support.function.bsl\\\"},{\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё.]|^)(ЗначениеВСтрокуВнутр|ValueToStringInternal|ЗначениеИзСтрокиВнутр|ValueFromStringInternal|ЗначениеВФайл|ValueToFile|ЗначениеИзФайла|ValueFromFile)\\\\\\\\s*(?=\\\\\\\\())\\\",\\\"name\\\":\\\"support.function.bsl\\\"},{\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё.]|^)(КомандаСистемы|System|ЗапуститьПриложение|RunApp|ПолучитьCOMОбъект|GetCOMObject|ПользователиОС|OSUsers|НачатьЗапускПриложения|BeginRunningApplication)\\\\\\\\s*(?=\\\\\\\\())\\\",\\\"name\\\":\\\"support.function.bsl\\\"},{\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё.]|^)(ПодключитьВнешнююКомпоненту|AttachAddIn|НачатьУстановкуВнешнейКомпоненты|BeginInstallAddIn|УстановитьВнешнююКомпоненту|InstallAddIn|НачатьПодключениеВнешнейКомпоненты|BeginAttachingAddIn)\\\\\\\\s*(?=\\\\\\\\())\\\",\\\"name\\\":\\\"support.function.bsl\\\"},{\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё.]|^)(КопироватьФайл|FileCopy|ПереместитьФайл|MoveFile|УдалитьФайлы|DeleteFiles|НайтиФайлы|FindFiles|СоздатьКаталог|CreateDirectory|ПолучитьИмяВременногоФайла|GetTempFileName|РазделитьФайл|SplitFile|ОбъединитьФайлы|MergeFiles|ПолучитьФайл|GetFile|НачатьПомещениеФайла|BeginPutFile|ПоместитьФайл|PutFile|ЭтоАдресВременногоХранилища|IsTempStorageURL|УдалитьИзВременногоХранилища|DeleteFromTempStorage|ПолучитьИзВременногоХранилища|GetFromTempStorage|ПоместитьВоВременноеХранилище|PutToTempStorage|ПодключитьРасширениеРаботыСФайлами|AttachFileSystemExtension|НачатьУстановкуРасширенияРаботыСФайлами|BeginInstallFileSystemExtension|УстановитьРасширениеРаботыСФайлами|InstallFileSystemExtension|ПолучитьФайлы|GetFiles|ПоместитьФайлы|PutFiles|ЗапроситьРазрешениеПользователя|RequestUserPermission|ПолучитьМаскуВсеФайлы|GetAllFilesMask|ПолучитьМаскуВсеФайлыКлиента|GetClientAllFilesMask|ПолучитьМаскуВсеФайлыСервера|GetServerAllFilesMask|ПолучитьРазделительПути|GetPathSeparator|ПолучитьРазделительПутиКлиента|GetClientPathSeparator|ПолучитьРазделительПутиСервера|GetServerPathSeparator|НачатьПодключениеРасширенияРаботыСФайлами|BeginAttachingFileSystemExtension|НачатьЗапросРазрешенияПользователя|BeginRequestingUserPermission|НачатьПоискФайлов|BeginFindingFiles|НачатьСозданиеКаталога|BeginCreatingDirectory|НачатьКопированиеФайла|BeginCopyingFile|НачатьПеремещениеФайла|BeginMovingFile|НачатьУдалениеФайлов|BeginDeletingFiles|НачатьПолучениеФайлов|BeginGettingFiles|НачатьПомещениеФайлов|BeginPuttingFiles|НачатьСозданиеДвоичныхДанныхИзФайла|BeginCreateBinaryDataFromFile)\\\\\\\\s*(?=\\\\\\\\())\\\",\\\"name\\\":\\\"support.function.bsl\\\"},{\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё.]|^)(НачатьТранзакцию|BeginTransaction|ЗафиксироватьТранзакцию|CommitTransaction|ОтменитьТранзакцию|RollbackTransaction|УстановитьМонопольныйРежим|SetExclusiveMode|МонопольныйРежим|ExclusiveMode|ПолучитьОперативнуюОтметкуВремени|GetRealTimeTimestamp|ПолучитьСоединенияИнформационнойБазы|GetInfoBaseConnections|НомерСоединенияИнформационнойБазы|InfoBaseConnectionNumber|КонфигурацияИзменена|ConfigurationChanged|КонфигурацияБазыДанныхИзмененаДинамически|DataBaseConfigurationChangedDynamically|УстановитьВремяОжиданияБлокировкиДанных|SetLockWaitTime|ОбновитьНумерациюОбъектов|RefreshObjectsNumbering|ПолучитьВремяОжиданияБлокировкиДанных|GetLockWaitTime|КодЛокализацииИнформационнойБазы|InfoBaseLocaleCode|УстановитьМинимальнуюДлинуПаролейПользователей|SetUserPasswordMinLength|ПолучитьМинимальнуюДлинуПаролейПользователей|GetUserPasswordMinLength|ИнициализироватьПредопределенныеДанные|InitializePredefinedData|УдалитьДанныеИнформационнойБазы|EraseInfoBaseData|УстановитьПроверкуСложностиПаролейПользователей|SetUserPasswordStrengthCheck|ПолучитьПроверкуСложностиПаролейПользователей|GetUserPasswordStrengthCheck|ПолучитьСтруктуруХраненияБазыДанных|GetDBStorageStructureInfo|УстановитьПривилегированныйРежим|SetPrivilegedMode|ПривилегированныйРежим|PrivilegedMode|ТранзакцияАктивна|TransactionActive|НеобходимостьЗавершенияСоединения|ConnectionStopRequest|НомерСеансаИнформационнойБазы|InfoBaseSessionNumber|ПолучитьСеансыИнформационнойБазы|GetInfoBaseSessions|ЗаблокироватьДанныеДляРедактирования|LockDataForEdit|УстановитьСоединениеСВнешнимИсточникомДанных|ConnectExternalDataSource|РазблокироватьДанныеДляРедактирования|UnlockDataForEdit|РазорватьСоединениеСВнешнимИсточникомДанных|DisconnectExternalDataSource|ПолучитьБлокировкуСеансов|GetSessionsLock|УстановитьБлокировкуСеансов|SetSessionsLock|ОбновитьПовторноИспользуемыеЗначения|RefreshReusableValues|УстановитьБезопасныйРежим|SetSafeMode|БезопасныйРежим|SafeMode|ПолучитьДанныеВыбора|GetChoiceData|УстановитьЧасовойПоясИнформационнойБазы|SetInfoBaseTimeZone|ПолучитьЧасовойПоясИнформационнойБазы|GetInfoBaseTimeZone|ПолучитьОбновлениеКонфигурацииБазыДанных|GetDataBaseConfigurationUpdate|УстановитьБезопасныйРежимРазделенияДанных|SetDataSeparationSafeMode|БезопасныйРежимРазделенияДанных|DataSeparationSafeMode|УстановитьВремяЗасыпанияПассивногоСеанса|SetPassiveSessionHibernateTime|ПолучитьВремяЗасыпанияПассивногоСеанса|GetPassiveSessionHibernateTime|УстановитьВремяЗавершенияСпящегоСеанса|SetHibernateSessionTerminateTime|ПолучитьВремяЗавершенияСпящегоСеанса|GetHibernateSessionTerminateTime|ПолучитьТекущийСеансИнформационнойБазы|GetCurrentInfoBaseSession|ПолучитьИдентификаторКонфигурации|GetConfigurationID|УстановитьНастройкиКлиентаЛицензирования|SetLicensingClientParameters|ПолучитьИмяКлиентаЛицензирования|GetLicensingClientName|ПолучитьДополнительныйПараметрКлиентаЛицензирования|GetLicensingClientAdditionalParameter|ПолучитьОтключениеБезопасногоРежима|GetSafeModeDisabled|УстановитьОтключениеБезопасногоРежима|SetSafeModeDisabled)\\\\\\\\s*(?=\\\\\\\\())\\\",\\\"name\\\":\\\"support.function.bsl\\\"},{\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё.]|^)(НайтиПомеченныеНаУдаление|FindMarkedForDeletion|НайтиПоСсылкам|FindByRef|УдалитьОбъекты|DeleteObjects|УстановитьОбновлениеПредопределенныхДанныхИнформационнойБазы|SetInfoBasePredefinedDataUpdate|ПолучитьОбновлениеПредопределенныхДанныхИнформационнойБазы|GetInfoBasePredefinedData)\\\\\\\\s*(?=\\\\\\\\())\\\",\\\"name\\\":\\\"support.function.bsl\\\"},{\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё.]|^)(XMLСтрока|XMLString|XMLЗначение|XMLValue|XMLТип|XMLType|XMLТипЗнч|XMLTypeOf|ИзXMLТипа|FromXMLType|ВозможностьЧтенияXML|CanReadXML|ПолучитьXMLТип|GetXMLType|ПрочитатьXML|ReadXML|ЗаписатьXML|WriteXML|НайтиНедопустимыеСимволыXML|FindDisallowedXMLCharacters|ИмпортМоделиXDTO|ImportXDTOModel|СоздатьФабрикуXDTO|CreateXDTOFactory)\\\\\\\\s*(?=\\\\\\\\())\\\",\\\"name\\\":\\\"support.function.bsl\\\"},{\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё.]|^)(ЗаписатьJSON|WriteJSON|ПрочитатьJSON|ReadJSON|ПрочитатьДатуJSON|ReadJSONDate|ЗаписатьДатуJSON|WriteJSONDate)\\\\\\\\s*(?=\\\\\\\\())\\\",\\\"name\\\":\\\"support.function.bsl\\\"},{\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё.]|^)(ЗаписьЖурналаРегистрации|WriteLogEvent|ПолучитьИспользованиеЖурналаРегистрации|GetEventLogUsing|УстановитьИспользованиеЖурналаРегистрации|SetEventLogUsing|ПредставлениеСобытияЖурналаРегистрации|EventLogEventPresentation|ВыгрузитьЖурналРегистрации|UnloadEventLog|ПолучитьЗначенияОтбораЖурналаРегистрации|GetEventLogFilterValues|УстановитьИспользованиеСобытияЖурналаРегистрации|SetEventLogEventUse|ПолучитьИспользованиеСобытияЖурналаРегистрации|GetEventLogEventUse|СкопироватьЖурналРегистрации|CopyEventLog|ОчиститьЖурналРегистрации|ClearEventLog)\\\\\\\\s*(?=\\\\\\\\())\\\",\\\"name\\\":\\\"support.function.bsl\\\"},{\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё.]|^)(ЗначениеВДанныеФормы|ValueToFormData|ДанныеФормыВЗначение|FormDataToValue|КопироватьДанныеФормы|CopyFormData|УстановитьСоответствиеОбъектаИФормы|SetObjectAndFormConformity|ПолучитьСоответствиеОбъектаИФормы|GetObjectAndFormConformity)\\\\\\\\s*(?=\\\\\\\\())\\\",\\\"name\\\":\\\"support.function.bsl\\\"},{\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё.]|^)(ПолучитьФункциональнуюОпцию|GetFunctionalOption|ПолучитьФункциональнуюОпциюИнтерфейса|GetInterfaceFunctionalOption|УстановитьПараметрыФункциональныхОпцийИнтерфейса|SetInterfaceFunctionalOptionParameters|ПолучитьПараметрыФункциональныхОпцийИнтерфейса|GetInterfaceFunctionalOptionParameters|ОбновитьИнтерфейс|RefreshInterface)\\\\\\\\s*(?=\\\\\\\\())\\\",\\\"name\\\":\\\"support.function.bsl\\\"},{\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё.]|^)(УстановитьРасширениеРаботыСКриптографией|InstallCryptoExtension|НачатьУстановкуРасширенияРаботыСКриптографией|BeginInstallCryptoExtension|ПодключитьРасширениеРаботыСКриптографией|AttachCryptoExtension|НачатьПодключениеРасширенияРаботыСКриптографией|BeginAttachingCryptoExtension)\\\\\\\\s*(?=\\\\\\\\())\\\",\\\"name\\\":\\\"support.function.bsl\\\"},{\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё.]|^)(УстановитьСоставСтандартногоИнтерфейсаOData|SetStandardODataInterfaceContent|ПолучитьСоставСтандартногоИнтерфейсаOData|GetStandardODataInterfaceContent)\\\\\\\\s*(?=\\\\\\\\())\\\",\\\"name\\\":\\\"support.function.bsl\\\"},{\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё.]|^)(СоединитьБуферыДвоичныхДанных|ConcatBinaryDataBuffers)\\\\\\\\s*(?=\\\\\\\\())\\\",\\\"name\\\":\\\"support.function.bsl\\\"},{\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё.]|^)(Мин|Min|Макс|Max|ОписаниеОшибки|ErrorDescription|Вычислить|Eval|ИнформацияОбОшибке|ErrorInfo|Base64Значение|Base64Value|Base64Строка|Base64String|ЗаполнитьЗначенияСвойств|FillPropertyValues|ЗначениеЗаполнено|ValueIsFilled|ПолучитьПредставленияНавигационныхСсылок|GetURLsPresentations|НайтиОкноПоНавигационнойСсылке|FindWindowByURL|ПолучитьОкна|GetWindows|ПерейтиПоНавигационнойСсылке|GotoURL|ПолучитьНавигационнуюСсылку|GetURL|ПолучитьДопустимыеКодыЛокализации|GetAvailableLocaleCodes|ПолучитьНавигационнуюСсылкуИнформационнойБазы|GetInfoBaseURL|ПредставлениеКодаЛокализации|LocaleCodePresentation|ПолучитьДопустимыеЧасовыеПояса|GetAvailableTimeZones|ПредставлениеЧасовогоПояса|TimeZonePresentation|ТекущаяУниверсальнаяДата|CurrentUniversalDate|ТекущаяУниверсальнаяДатаВМиллисекундах|CurrentUniversalDateInMilliseconds|МестноеВремя|ToLocalTime|УниверсальноеВремя|ToUniversalTime|ЧасовойПояс|TimeZone|СмещениеЛетнегоВремени|DaylightTimeOffset|СмещениеСтандартногоВремени|StandardTimeOffset|КодироватьСтроку|EncodeString|РаскодироватьСтроку|DecodeString|Найти|Find|ПродолжитьВызов|ProceedWithCall)\\\\\\\\s*(?=\\\\\\\\())\\\",\\\"name\\\":\\\"support.function.bsl\\\"},{\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё.]|^)(ПередНачаломРаботыСистемы|BeforeStart|ПриНачалеРаботыСистемы|OnStart|ПередЗавершениемРаботыСистемы|BeforeExit|ПриЗавершенииРаботыСистемы|OnExit|ОбработкаВнешнегоСобытия|ExternEventProcessing|УстановкаПараметровСеанса|SessionParametersSetting|ПриИзмененииПараметровЭкрана|OnChangeDisplaySettings)\\\\\\\\s*(?=\\\\\\\\())\\\",\\\"name\\\":\\\"support.function.bsl\\\"},{\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё.]|^)(WSСсылки|WSReferences|БиблиотекаКартинок|PictureLib|БиблиотекаМакетовОформленияКомпоновкиДанных|DataCompositionAppearanceTemplateLib|БиблиотекаСтилей|StyleLib|БизнесПроцессы|BusinessProcesses|ВнешниеИсточникиДанных|ExternalDataSources|ВнешниеОбработки|ExternalDataProcessors|ВнешниеОтчеты|ExternalReports|Документы|Documents|ДоставляемыеУведомления|DeliverableNotifications|ЖурналыДокументов|DocumentJournals|Задачи|Tasks|ИнформацияОбИнтернетСоединении|InternetConnectionInformation|ИспользованиеРабочейДаты|WorkingDateUse|ИсторияРаботыПользователя|UserWorkHistory|Константы|Constants|КритерииОтбора|FilterCriteria|Метаданные|Metadata|Обработки|DataProcessors|ОтправкаДоставляемыхУведомлений|DeliverableNotificationSend|Отчеты|Reports|ПараметрыСеанса|SessionParameters|Перечисления|Enums|ПланыВидовРасчета|ChartsOfCalculationTypes|ПланыВидовХарактеристик|ChartsOfCharacteristicTypes|ПланыОбмена|ExchangePlans|ПланыСчетов|ChartsOfAccounts|ПолнотекстовыйПоиск|FullTextSearch|ПользователиИнформационнойБазы|InfoBaseUsers|Последовательности|Sequences|РасширенияКонфигурации|ConfigurationExtensions|РегистрыБухгалтерии|AccountingRegisters|РегистрыНакопления|AccumulationRegisters|РегистрыРасчета|CalculationRegisters|РегистрыСведений|InformationRegisters|РегламентныеЗадания|ScheduledJobs|СериализаторXDTO|XDTOSerializer|Справочники|Catalogs|СредстваГеопозиционирования|LocationTools|СредстваКриптографии|CryptoToolsManager|СредстваМультимедиа|MultimediaTools|СредстваОтображенияРекламы|AdvertisingPresentationTools|СредстваПочты|MailTools|СредстваТелефонии|TelephonyTools|ФабрикаXDTO|XDTOFactory|ФайловыеПотоки|FileStreams|ФоновыеЗадания|BackgroundJobs|ХранилищаНастроек|SettingsStorages|ВстроенныеПокупки|InAppPurchases|ОтображениеРекламы|AdRepresentation|ПанельЗадачОС|OSTaskbar|ПроверкаВстроенныхПокупок|InAppPurchasesValidation)(?=[^\\\\\\\\wа-яё]|$))\\\",\\\"name\\\":\\\"support.class.bsl\\\"},{\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё.]|^)(ГлавныйИнтерфейс|MainInterface|ГлавныйСтиль|MainStyle|ПараметрЗапуска|LaunchParameter|РабочаяДата|WorkingDate|ХранилищеВариантовОтчетов|ReportsVariantsStorage|ХранилищеНастроекДанныхФорм|FormDataSettingsStorage|ХранилищеОбщихНастроек|CommonSettingsStorage|ХранилищеПользовательскихНастроекДинамическихСписков|DynamicListsUserSettingsStorage|ХранилищеПользовательскихНастроекОтчетов|ReportsUserSettingsStorage|ХранилищеСистемныхНастроек|SystemSettingsStorage)(?=[^\\\\\\\\wа-яё]|$))\\\",\\\"name\\\":\\\"support.variable.bsl\\\"}]},\\\"query\\\":{\\\"begin\\\":\\\"(?i)(?<=[^\\\\\\\\wа-яё.]|^)(Выбрать|Select(\\\\\\\\s+Разрешенные|\\\\\\\\s+Allowed)?(\\\\\\\\s+Различные|\\\\\\\\s+Distinct)?(\\\\\\\\s+Первые|\\\\\\\\s+Top)?)(?=[^\\\\\\\\wа-яё.]|$)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.sdbl\\\"}},\\\"end\\\":\\\"(?=\\\\\\\"[^\\\\\\\"])\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*//\\\",\\\"end\\\":\\\"$\\\",\\\"name\\\":\\\"comment.line.double-slash.bsl\\\"},{\\\"match\\\":\\\"(//((\\\\\\\"\\\\\\\")|[^\\\\\\\"])*)\\\",\\\"name\\\":\\\"comment.line.double-slash.sdbl\\\"},{\\\"match\\\":\\\"\\\\\\\"\\\\\\\"[^\\\\\\\"]*\\\\\\\"\\\\\\\"\\\",\\\"name\\\":\\\"string.quoted.double.sdbl\\\"},{\\\"include\\\":\\\"source.sdbl\\\"}]}},\\\"scopeName\\\":\\\"source.bsl\\\",\\\"embeddedLangs\\\":[\\\"sdbl\\\"],\\\"aliases\\\":[\\\"1c\\\"]}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\n..._sdbl_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/bsl.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/sdbl.mjs":
/*!***************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/sdbl.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"1C (Query)\\\",\\\"fileTypes\\\":[\\\"sdbl\\\",\\\"query\\\"],\\\"firstLineMatch\\\":\\\"(?i)Выбрать|Select(\\\\\\\\s+Разрешенные|\\\\\\\\s+Allowed)?(\\\\\\\\s+Различные|\\\\\\\\s+Distinct)?(\\\\\\\\s+Первые|\\\\\\\\s+Top)?.*\\\",\\\"name\\\":\\\"sdbl\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(^\\\\\\\\s*//.*$)\\\",\\\"name\\\":\\\"comment.line.double-slash.sdbl\\\"},{\\\"begin\\\":\\\"//\\\",\\\"end\\\":\\\"$\\\",\\\"name\\\":\\\"comment.line.double-slash.sdbl\\\"},{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"end\\\":\\\"\\\\\\\"(?!\\\\\\\")\\\",\\\"name\\\":\\\"string.quoted.double.sdbl\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\"\\\\\\\"\\\",\\\"name\\\":\\\"constant.character.escape.sdbl\\\"},{\\\"match\\\":\\\"(^\\\\\\\\s*//.*$)\\\",\\\"name\\\":\\\"comment.line.double-slash.sdbl\\\"}]},{\\\"match\\\":\\\"(?i)(?<=[^\\\\\\\\wа-яё.]|^)(Неопределено|Undefined|Истина|True|Ложь|False|NULL)(?=[^\\\\\\\\wа-яё.]|$)\\\",\\\"name\\\":\\\"constant.language.sdbl\\\"},{\\\"match\\\":\\\"(?<=[^\\\\\\\\wа-яё.]|^)(\\\\\\\\d+\\\\\\\\.?\\\\\\\\d*)(?=[^\\\\\\\\wа-яё.]|$)\\\",\\\"name\\\":\\\"constant.numeric.sdbl\\\"},{\\\"match\\\":\\\"(?i)(?<=[^\\\\\\\\wа-яё.]|^)(Выбор|Case|Когда|When|Тогда|Then|Иначе|Else|Конец|End)(?=[^\\\\\\\\wа-яё.]|$)\\\",\\\"name\\\":\\\"keyword.control.conditional.sdbl\\\"},{\\\"match\\\":\\\"(?i)(?<!КАК\\\\\\\\s|AS\\\\\\\\s)(?<=[^\\\\\\\\wа-яё.]|^)(НЕ|NOT|И|AND|ИЛИ|OR|В\\\\\\\\s+ИЕРАРХИИ|IN\\\\\\\\s+HIERARCHY|В|In|Между|Between|Есть(\\\\\\\\s+НЕ)?\\\\\\\\s+NULL|Is(\\\\\\\\s+NOT)?\\\\\\\\s+NULL|Ссылка|Refs|Подобно|Like)(?=[^\\\\\\\\wа-яё.]|$)\\\",\\\"name\\\":\\\"keyword.operator.logical.sdbl\\\"},{\\\"match\\\":\\\"<=|>=|[=<>]\\\",\\\"name\\\":\\\"keyword.operator.comparison.sdbl\\\"},{\\\"match\\\":\\\"([+\\\\\\\\-*/%])\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.sdbl\\\"},{\\\"match\\\":\\\"([,;])\\\",\\\"name\\\":\\\"keyword.operator.sdbl\\\"},{\\\"match\\\":\\\"(?i)(?<=[^\\\\\\\\wа-яё.]|^)(Выбрать|Select|Разрешенные|Allowed|Различные|Distinct|Первые|Top|Как|As|ПустаяТаблица|EmptyTable|Поместить|Into|Уничтожить|Drop|Из|From|((Левое|Left|Правое|Right|Полное|Full)\\\\\\\\s+(Внешнее\\\\\\\\s+|Outer\\\\\\\\s+)?Соединение|Join)|((Внутреннее|Inner)\\\\\\\\s+Соединение|Join)|Где|Where|(Сгруппировать\\\\\\\\s+По(\\\\\\\\s+Группирующим\\\\\\\\s+Наборам)?)|(Group\\\\\\\\s+By(\\\\\\\\s+Grouping\\\\\\\\s+Set)?)|Имеющие|Having|Объединить(\\\\\\\\s+Все)?|Union(\\\\\\\\s+All)?|(Упорядочить\\\\\\\\s+По)|(Order\\\\\\\\s+By)|Автоупорядочивание|Autoorder|Итоги|Totals|По(\\\\\\\\s+Общие)?|By(\\\\\\\\s+Overall)?|(Только\\\\\\\\s+)?Иерархия|(Only\\\\\\\\s+)?Hierarchy|Периодами|Periods|Индексировать|Index|Выразить|Cast|Возр|Asc|Убыв|Desc|Для\\\\\\\\s+Изменения|(For\\\\\\\\s+Update(\\\\\\\\s+Of)?)|Спецсимвол|Escape|СгруппированоПо|GroupedBy)(?=[^\\\\\\\\wа-яё.]|$)\\\",\\\"name\\\":\\\"keyword.control.sdbl\\\"},{\\\"match\\\":\\\"(?i)(?<=[^\\\\\\\\wа-яё.]|^)(Значение|Value|ДатаВремя|DateTime|Тип|Type)(?=\\\\\\\\()\\\",\\\"name\\\":\\\"support.function.sdbl\\\"},{\\\"match\\\":\\\"(?i)(?<=[^\\\\\\\\wа-яё.]|^)(Подстрока|Substring|НРег|Lower|ВРег|Upper|Лев|Left|Прав|Right|ДлинаСтроки|StringLength|СтрНайти|StrFind|СтрЗаменить|StrReplace|СокрЛП|TrimAll|СокрЛ|TrimL|СокрП|TrimR)(?=\\\\\\\\()\\\",\\\"name\\\":\\\"support.function.sdbl\\\"},{\\\"match\\\":\\\"(?i)(?<=[^\\\\\\\\wа-яё.]|^)(Год|Year|Квартал|Quarter|Месяц|Month|ДеньГода|DayOfYear|День|Day|Неделя|Week|ДеньНедели|Weekday|Час|Hour|Минута|Minute|Секунда|Second|НачалоПериода|BeginOfPeriod|КонецПериода|EndOfPeriod|ДобавитьКДате|DateAdd|РазностьДат|DateDiff|Полугодие|HalfYear|Декада|TenDays)(?=\\\\\\\\()\\\",\\\"name\\\":\\\"support.function.sdbl\\\"},{\\\"match\\\":\\\"(?i)(?<=[^\\\\\\\\wа-яё.]|^)(ACOS|COS|ASIN|SIN|ATAN|TAN|EXP|POW|LOG|LOG10|Цел|Int|Окр|Round|SQRT)(?=\\\\\\\\()\\\",\\\"name\\\":\\\"support.function.sdbl\\\"},{\\\"match\\\":\\\"(?i)(?<=[^\\\\\\\\wа-яё.]|^)(Сумма|Sum|Среднее|Avg|Минимум|Min|Максимум|Max|Количество|Count)(?=\\\\\\\\()\\\",\\\"name\\\":\\\"support.function.sdbl\\\"},{\\\"match\\\":\\\"(?i)(?<=[^\\\\\\\\wа-яё.]|^)(ЕстьNULL|IsNULL|Представление|Presentation|ПредставлениеСсылки|RefPresentation|ТипЗначения|ValueType|АвтономерЗаписи|RecordAutoNumber|РазмерХранимыхДанных|StoredDataSize|УникальныйИдентификатор|UUID)(?=\\\\\\\\()\\\",\\\"name\\\":\\\"support.function.sdbl\\\"},{\\\"match\\\":\\\"(?i)(?<=[^\\\\\\\\wа-яё.])(Число|Number|Строка|String|Дата|Date|Булево|Boolean)(?=[^\\\\\\\\wа-яё.]|$)\\\",\\\"name\\\":\\\"support.type.sdbl\\\"},{\\\"match\\\":\\\"(&[\\\\\\\\wа-яё]+)\\\",\\\"name\\\":\\\"variable.parameter.sdbl\\\"}],\\\"scopeName\\\":\\\"source.sdbl\\\",\\\"aliases\\\":[\\\"1c-query\\\"]}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/sdbl.mjs\n"));

/***/ })

}]);