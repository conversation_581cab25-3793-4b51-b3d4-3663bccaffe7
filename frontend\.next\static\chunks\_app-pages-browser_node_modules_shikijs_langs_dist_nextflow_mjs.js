"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_nextflow_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/nextflow.mjs":
/*!*******************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/nextflow.mjs ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Nextflow\\\",\\\"name\\\":\\\"nextflow\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#nextflow\\\"}],\\\"repository\\\":{\\\"enum-def\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*(enum)\\\\\\\\s+(\\\\\\\\w+)\\\\\\\\s*\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.nextflow\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.type.groovy\\\"}},\\\"end\\\":\\\"}\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.nextflow-groovy#comments\\\"},{\\\"include\\\":\\\"#enum-values\\\"}]},\\\"enum-values\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=;|^)\\\\\\\\s*\\\\\\\\b([A-Z0-9_]+)(?=\\\\\\\\s*(?:[,}(]|$))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.enum.name.groovy\\\"}},\\\"end\\\":\\\",|(?=})|^(?!\\\\\\\\s*\\\\\\\\w+\\\\\\\\s*(?:,|$))\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"name\\\":\\\"meta.enum.value.groovy\\\",\\\"patterns\\\":[{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.definition.seperator.parameter.groovy\\\"},{\\\"include\\\":\\\"#groovy-code\\\"}]}]}]},\\\"function-body\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\s\\\"},{\\\"begin\\\":\\\"(?=[\\\\\\\\w<][^(]*\\\\\\\\s+[\\\\\\\\w$<]+\\\\\\\\s*\\\\\\\\()\\\",\\\"end\\\":\\\"(?=[\\\\\\\\w$]+\\\\\\\\s*\\\\\\\\()\\\",\\\"name\\\":\\\"meta.method.return-type.java\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.nextflow-groovy#types\\\"}]},{\\\"begin\\\":\\\"([\\\\\\\\w$]+)\\\\\\\\s*\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.nextflow\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"name\\\":\\\"meta.definition.method.signature.java\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?=[^)])\\\",\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"name\\\":\\\"meta.method.parameters.groovy\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?=[^,)])\\\",\\\"end\\\":\\\"(?=[,)])\\\",\\\"name\\\":\\\"meta.method.parameter.groovy\\\",\\\"patterns\\\":[{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.definition.separator.groovy\\\"},{\\\"begin\\\":\\\"=\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.assignment.groovy\\\"}},\\\"end\\\":\\\"(?=[,)])\\\",\\\"name\\\":\\\"meta.parameter.default.groovy\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.nextflow-groovy#groovy-code\\\"}]},{\\\"include\\\":\\\"source.nextflow-groovy#parameters\\\"}]}]}]},{\\\"begin\\\":\\\"(?=<)\\\",\\\"end\\\":\\\"(?=\\\\\\\\s)\\\",\\\"name\\\":\\\"meta.method.paramerised-type.groovy\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"<\\\",\\\"end\\\":\\\">\\\",\\\"name\\\":\\\"storage.type.parameters.groovy\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.nextflow-groovy#types\\\"},{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.definition.seperator.groovy\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"end\\\":\\\"(?=})\\\",\\\"name\\\":\\\"meta.method.body.java\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.nextflow-groovy#groovy-code\\\"}]}]},\\\"function-def\\\":{\\\"applyEndPatternLast\\\":1,\\\"begin\\\":\\\"(?<=;|^|\\\\\\\\{)(?=\\\\\\\\s*(?:def|(?:(?:boolean|byte|char|short|int|float|long|double)|@?(?:[a-zA-Z]\\\\\\\\w*\\\\\\\\.)*[A-Z]+\\\\\\\\w*)[\\\\\\\\[\\\\\\\\]]*(?:<.*>)?n)\\\\\\\\s+([^=]+\\\\\\\\s+)?\\\\\\\\w+\\\\\\\\s*\\\\\\\\()\\\",\\\"end\\\":\\\"}|(?=[^{])\\\",\\\"name\\\":\\\"meta.definition.method.groovy\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-body\\\"}]},\\\"include-decl\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"^\\\\\\\\b(include)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.nextflow\\\"},{\\\"match\\\":\\\"\\\\\\\\b(from)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.nextflow\\\"}]},\\\"nextflow\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#enum-def\\\"},{\\\"include\\\":\\\"#function-def\\\"},{\\\"include\\\":\\\"#process-def\\\"},{\\\"include\\\":\\\"#workflow-def\\\"},{\\\"include\\\":\\\"#output-def\\\"},{\\\"include\\\":\\\"#include-decl\\\"},{\\\"include\\\":\\\"source.nextflow-groovy\\\"}]},\\\"output-def\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*(output)\\\\\\\\s*\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.nextflow\\\"}},\\\"end\\\":\\\"}\\\",\\\"name\\\":\\\"output.nextflow\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.nextflow-groovy#groovy\\\"}]},\\\"process-body\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?:input|output|when|script|shell|exec):\\\",\\\"name\\\":\\\"constant.block.nextflow\\\"},{\\\"match\\\":\\\"\\\\\\\\b(val|env|file|path|stdin|stdout|tuple)([(\\\\\\\\s])\\\",\\\"name\\\":\\\"entity.name.function.nextflow\\\"},{\\\"include\\\":\\\"source.nextflow-groovy#groovy\\\"}]},\\\"process-def\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*(process)\\\\\\\\s+(\\\\\\\\w+)\\\\\\\\s*\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.nextflow\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.nextflow\\\"}},\\\"end\\\":\\\"}\\\",\\\"name\\\":\\\"process.nextflow\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#process-body\\\"}]},\\\"workflow-body\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?:take|main|emit|publish):\\\",\\\"name\\\":\\\"constant.block.nextflow\\\"},{\\\"include\\\":\\\"source.nextflow-groovy#groovy\\\"}]},\\\"workflow-def\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*(workflow)(?:\\\\\\\\s+(\\\\\\\\w+))?\\\\\\\\s*\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.nextflow\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.nextflow\\\"}},\\\"end\\\":\\\"}\\\",\\\"name\\\":\\\"workflow.nextflow\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#workflow-body\\\"}]}},\\\"scopeName\\\":\\\"source.nextflow\\\",\\\"aliases\\\":[\\\"nf\\\"]}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/nextflow.mjs\n"));

/***/ })

}]);