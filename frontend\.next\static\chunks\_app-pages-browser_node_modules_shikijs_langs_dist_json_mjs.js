"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_json_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/json.mjs":
/*!***************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/json.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"JSON\\\",\\\"name\\\":\\\"json\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#value\\\"}],\\\"repository\\\":{\\\"array\\\":{\\\"begin\\\":\\\"\\\\\\\\[\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.array.begin.json\\\"}},\\\"end\\\":\\\"]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.array.end.json\\\"}},\\\"name\\\":\\\"meta.structure.array.json\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#value\\\"},{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.separator.array.json\\\"},{\\\"match\\\":\\\"[^\\\\\\\\s\\\\\\\\]]\\\",\\\"name\\\":\\\"invalid.illegal.expected-array-separator.json\\\"}]},\\\"comments\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"/\\\\\\\\*\\\\\\\\*(?!/)\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.json\\\"}},\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"name\\\":\\\"comment.block.documentation.json\\\"},{\\\"begin\\\":\\\"/\\\\\\\\*\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.json\\\"}},\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"name\\\":\\\"comment.block.json\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.json\\\"}},\\\"match\\\":\\\"(//).*$\\\\\\\\n?\\\",\\\"name\\\":\\\"comment.line.double-slash.js\\\"}]},\\\"constant\\\":{\\\"match\\\":\\\"\\\\\\\\b(?:true|false|null)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.json\\\"},\\\"number\\\":{\\\"match\\\":\\\"-?(?:0|[1-9]\\\\\\\\d*)(?:(?:\\\\\\\\.\\\\\\\\d+)?(?:[eE][+-]?\\\\\\\\d+)?)?\\\",\\\"name\\\":\\\"constant.numeric.json\\\"},\\\"object\\\":{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.dictionary.begin.json\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.dictionary.end.json\\\"}},\\\"name\\\":\\\"meta.structure.dictionary.json\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#objectkey\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"begin\\\":\\\":\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.separator.dictionary.key-value.json\\\"}},\\\"end\\\":\\\"(,)|(?=})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.dictionary.pair.json\\\"}},\\\"name\\\":\\\"meta.structure.dictionary.value.json\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#value\\\"},{\\\"match\\\":\\\"[^\\\\\\\\s,]\\\",\\\"name\\\":\\\"invalid.illegal.expected-dictionary-separator.json\\\"}]},{\\\"match\\\":\\\"[^\\\\\\\\s}]\\\",\\\"name\\\":\\\"invalid.illegal.expected-dictionary-separator.json\\\"}]},\\\"objectkey\\\":{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.support.type.property-name.begin.json\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.support.type.property-name.end.json\\\"}},\\\"name\\\":\\\"string.json support.type.property-name.json\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#stringcontent\\\"}]},\\\"string\\\":{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.json\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.json\\\"}},\\\"name\\\":\\\"string.quoted.double.json\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#stringcontent\\\"}]},\\\"stringcontent\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\(?:[\\\\\\\"\\\\\\\\\\\\\\\\/bfnrt]|u\\\\\\\\h{4})\\\",\\\"name\\\":\\\"constant.character.escape.json\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"invalid.illegal.unrecognized-string-escape.json\\\"}]},\\\"value\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#constant\\\"},{\\\"include\\\":\\\"#number\\\"},{\\\"include\\\":\\\"#string\\\"},{\\\"include\\\":\\\"#array\\\"},{\\\"include\\\":\\\"#object\\\"},{\\\"include\\\":\\\"#comments\\\"}]}},\\\"scopeName\\\":\\\"source.json\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ac2hpa2lqcy9sYW5ncy9kaXN0L2pzb24ubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSx3Q0FBd0MsMERBQTBELHVCQUF1QixrQkFBa0IsV0FBVyx1Q0FBdUMsT0FBTyxzREFBc0QsZ0NBQWdDLE9BQU8sb0RBQW9ELHVEQUF1RCx1QkFBdUIsRUFBRSw4REFBOEQsRUFBRSx1RkFBdUYsRUFBRSxlQUFlLGVBQWUsNkNBQTZDLE9BQU8sa0RBQWtELGtFQUFrRSxFQUFFLG1DQUFtQyxPQUFPLGtEQUFrRCxvREFBb0QsRUFBRSxjQUFjLE9BQU8sa0RBQWtELHVFQUF1RSxFQUFFLGVBQWUsZ0ZBQWdGLGFBQWEsOEdBQThHLGFBQWEsaUJBQWlCLHNCQUFzQixPQUFPLDJEQUEyRCxZQUFZLG9CQUFvQixPQUFPLHlEQUF5RCw0REFBNEQsMkJBQTJCLEVBQUUsMEJBQTBCLEVBQUUsbUNBQW1DLE9BQU8sOERBQThELG1CQUFtQixxQkFBcUIsT0FBTyx5REFBeUQsa0VBQWtFLHVCQUF1QixFQUFFLHdGQUF3RixFQUFFLEVBQUUsb0JBQW9CLG9FQUFvRSxFQUFFLGdCQUFnQixzQ0FBc0MsT0FBTyxnRUFBZ0UsbUNBQW1DLE9BQU8sOERBQThELHlFQUF5RSwrQkFBK0IsRUFBRSxhQUFhLHNDQUFzQyxPQUFPLHVEQUF1RCxtQ0FBbUMsT0FBTyxxREFBcUQsdURBQXVELCtCQUErQixFQUFFLG9CQUFvQixlQUFlLG1EQUFtRCxFQUFFLGdEQUFnRCxFQUFFLHFGQUFxRixFQUFFLFlBQVksZUFBZSwwQkFBMEIsRUFBRSx3QkFBd0IsRUFBRSx3QkFBd0IsRUFBRSx1QkFBdUIsRUFBRSx3QkFBd0IsRUFBRSwwQkFBMEIsR0FBRywrQkFBK0I7O0FBRWhtRyxpRUFBZTtBQUNmO0FBQ0EsQ0FBQyIsInNvdXJjZXMiOlsiRDpcXHNvZnRcXFNlYVByb2plY3RcXGdpdGh1Yl9vcGVuX3Byb2plY3Rfc3VjY2Vzc1xcc3VuYVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcQHNoaWtpanNcXGxhbmdzXFxkaXN0XFxqc29uLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBsYW5nID0gT2JqZWN0LmZyZWV6ZShKU09OLnBhcnNlKFwie1xcXCJkaXNwbGF5TmFtZVxcXCI6XFxcIkpTT05cXFwiLFxcXCJuYW1lXFxcIjpcXFwianNvblxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3ZhbHVlXFxcIn1dLFxcXCJyZXBvc2l0b3J5XFxcIjp7XFxcImFycmF5XFxcIjp7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXFxbXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLmFycmF5LmJlZ2luLmpzb25cXFwifX0sXFxcImVuZFxcXCI6XFxcIl1cXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLmFycmF5LmVuZC5qc29uXFxcIn19LFxcXCJuYW1lXFxcIjpcXFwibWV0YS5zdHJ1Y3R1cmUuYXJyYXkuanNvblxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3ZhbHVlXFxcIn0se1xcXCJtYXRjaFxcXCI6XFxcIixcXFwiLFxcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uc2VwYXJhdG9yLmFycmF5Lmpzb25cXFwifSx7XFxcIm1hdGNoXFxcIjpcXFwiW15cXFxcXFxcXHNcXFxcXFxcXF1dXFxcIixcXFwibmFtZVxcXCI6XFxcImludmFsaWQuaWxsZWdhbC5leHBlY3RlZC1hcnJheS1zZXBhcmF0b3IuanNvblxcXCJ9XX0sXFxcImNvbW1lbnRzXFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJiZWdpblxcXCI6XFxcIi9cXFxcXFxcXCpcXFxcXFxcXCooPyEvKVxcXCIsXFxcImNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24uY29tbWVudC5qc29uXFxcIn19LFxcXCJlbmRcXFwiOlxcXCJcXFxcXFxcXCovXFxcIixcXFwibmFtZVxcXCI6XFxcImNvbW1lbnQuYmxvY2suZG9jdW1lbnRhdGlvbi5qc29uXFxcIn0se1xcXCJiZWdpblxcXCI6XFxcIi9cXFxcXFxcXCpcXFwiLFxcXCJjYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLmNvbW1lbnQuanNvblxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiXFxcXFxcXFwqL1xcXCIsXFxcIm5hbWVcXFwiOlxcXCJjb21tZW50LmJsb2NrLmpzb25cXFwifSx7XFxcImNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24uY29tbWVudC5qc29uXFxcIn19LFxcXCJtYXRjaFxcXCI6XFxcIigvLykuKiRcXFxcXFxcXG4/XFxcIixcXFwibmFtZVxcXCI6XFxcImNvbW1lbnQubGluZS5kb3VibGUtc2xhc2guanNcXFwifV19LFxcXCJjb25zdGFudFxcXCI6e1xcXCJtYXRjaFxcXCI6XFxcIlxcXFxcXFxcYig/OnRydWV8ZmFsc2V8bnVsbClcXFxcXFxcXGJcXFwiLFxcXCJuYW1lXFxcIjpcXFwiY29uc3RhbnQubGFuZ3VhZ2UuanNvblxcXCJ9LFxcXCJudW1iZXJcXFwiOntcXFwibWF0Y2hcXFwiOlxcXCItPyg/OjB8WzEtOV1cXFxcXFxcXGQqKSg/Oig/OlxcXFxcXFxcLlxcXFxcXFxcZCspPyg/OltlRV1bKy1dP1xcXFxcXFxcZCspPyk/XFxcIixcXFwibmFtZVxcXCI6XFxcImNvbnN0YW50Lm51bWVyaWMuanNvblxcXCJ9LFxcXCJvYmplY3RcXFwiOntcXFwiYmVnaW5cXFwiOlxcXCJcXFxcXFxcXHtcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24uZGljdGlvbmFyeS5iZWdpbi5qc29uXFxcIn19LFxcXCJlbmRcXFwiOlxcXCJ9XFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uZGVmaW5pdGlvbi5kaWN0aW9uYXJ5LmVuZC5qc29uXFxcIn19LFxcXCJuYW1lXFxcIjpcXFwibWV0YS5zdHJ1Y3R1cmUuZGljdGlvbmFyeS5qc29uXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjb2JqZWN0a2V5XFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2NvbW1lbnRzXFxcIn0se1xcXCJiZWdpblxcXCI6XFxcIjpcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLnNlcGFyYXRvci5kaWN0aW9uYXJ5LmtleS12YWx1ZS5qc29uXFxcIn19LFxcXCJlbmRcXFwiOlxcXCIoLCl8KD89fSlcXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5zZXBhcmF0b3IuZGljdGlvbmFyeS5wYWlyLmpzb25cXFwifX0sXFxcIm5hbWVcXFwiOlxcXCJtZXRhLnN0cnVjdHVyZS5kaWN0aW9uYXJ5LnZhbHVlLmpzb25cXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiN2YWx1ZVxcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCJbXlxcXFxcXFxccyxdXFxcIixcXFwibmFtZVxcXCI6XFxcImludmFsaWQuaWxsZWdhbC5leHBlY3RlZC1kaWN0aW9uYXJ5LXNlcGFyYXRvci5qc29uXFxcIn1dfSx7XFxcIm1hdGNoXFxcIjpcXFwiW15cXFxcXFxcXHN9XVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJpbnZhbGlkLmlsbGVnYWwuZXhwZWN0ZWQtZGljdGlvbmFyeS1zZXBhcmF0b3IuanNvblxcXCJ9XX0sXFxcIm9iamVjdGtleVxcXCI6e1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFwiXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5zdXBwb3J0LnR5cGUucHJvcGVydHktbmFtZS5iZWdpbi5qc29uXFxcIn19LFxcXCJlbmRcXFwiOlxcXCJcXFxcXFxcIlxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLnN1cHBvcnQudHlwZS5wcm9wZXJ0eS1uYW1lLmVuZC5qc29uXFxcIn19LFxcXCJuYW1lXFxcIjpcXFwic3RyaW5nLmpzb24gc3VwcG9ydC50eXBlLnByb3BlcnR5LW5hbWUuanNvblxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3N0cmluZ2NvbnRlbnRcXFwifV19LFxcXCJzdHJpbmdcXFwiOntcXFwiYmVnaW5cXFwiOlxcXCJcXFxcXFxcIlxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uZGVmaW5pdGlvbi5zdHJpbmcuYmVnaW4uanNvblxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiXFxcXFxcXCJcXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLnN0cmluZy5lbmQuanNvblxcXCJ9fSxcXFwibmFtZVxcXCI6XFxcInN0cmluZy5xdW90ZWQuZG91YmxlLmpzb25cXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNzdHJpbmdjb250ZW50XFxcIn1dfSxcXFwic3RyaW5nY29udGVudFxcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwibWF0Y2hcXFwiOlxcXCJcXFxcXFxcXFxcXFxcXFxcKD86W1xcXFxcXFwiXFxcXFxcXFxcXFxcXFxcXC9iZm5ydF18dVxcXFxcXFxcaHs0fSlcXFwiLFxcXCJuYW1lXFxcIjpcXFwiY29uc3RhbnQuY2hhcmFjdGVyLmVzY2FwZS5qc29uXFxcIn0se1xcXCJtYXRjaFxcXCI6XFxcIlxcXFxcXFxcXFxcXFxcXFwuXFxcIixcXFwibmFtZVxcXCI6XFxcImludmFsaWQuaWxsZWdhbC51bnJlY29nbml6ZWQtc3RyaW5nLWVzY2FwZS5qc29uXFxcIn1dfSxcXFwidmFsdWVcXFwiOntcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjY29uc3RhbnRcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjbnVtYmVyXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3N0cmluZ1xcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNhcnJheVxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNvYmplY3RcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjY29tbWVudHNcXFwifV19fSxcXFwic2NvcGVOYW1lXFxcIjpcXFwic291cmNlLmpzb25cXFwifVwiKSlcblxuZXhwb3J0IGRlZmF1bHQgW1xubGFuZ1xuXVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/json.mjs\n"));

/***/ })

}]);