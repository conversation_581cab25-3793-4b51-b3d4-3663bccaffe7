"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@vercel";
exports.ids = ["vendor-chunks/@vercel"];
exports.modules = {

/***/ "(rsc)/./node_modules/@vercel/analytics/dist/react/index.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/@vercel/analytics/dist/react/index.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Analytics: () => (/* binding */ Analytics),
/* harmony export */   track: () => (/* binding */ track)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");

const Analytics = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Analytics() from the server but Analytics is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\soft\\SeaProject\\github_open_project_success\\suna\\frontend\\node_modules\\@vercel\\analytics\\dist\\react\\index.mjs",
"Analytics",
);const track = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call track() from the server but track is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\soft\\SeaProject\\github_open_project_success\\suna\\frontend\\node_modules\\@vercel\\analytics\\dist\\react\\index.mjs",
"track",
);

/***/ }),

/***/ "(rsc)/./node_modules/@vercel/speed-insights/dist/next/index.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/@vercel/speed-insights/dist/next/index.mjs ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SpeedInsights: () => (/* binding */ SpeedInsights)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");

const SpeedInsights = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SpeedInsights() from the server but SpeedInsights is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\soft\\SeaProject\\github_open_project_success\\suna\\frontend\\node_modules\\@vercel\\speed-insights\\dist\\next\\index.mjs",
"SpeedInsights",
);

/***/ }),

/***/ "(ssr)/./node_modules/@vercel/analytics/dist/react/index.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/@vercel/analytics/dist/react/index.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Analytics: () => (/* binding */ Analytics),\n/* harmony export */   track: () => (/* binding */ track)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* __next_internal_client_entry_do_not_use__ Analytics,track auto */ // src/react/index.tsx\n\n// package.json\nvar name = \"@vercel/analytics\";\nvar version = \"1.5.0\";\n// src/queue.ts\nvar initQueue = ()=>{\n    if (window.va) return;\n    window.va = function a(...params) {\n        (window.vaq = window.vaq || []).push(params);\n    };\n};\n// src/utils.ts\nfunction isBrowser() {\n    return \"undefined\" !== \"undefined\";\n}\nfunction detectEnvironment() {\n    try {\n        const env = \"development\";\n        if (env === \"development\" || env === \"test\") {\n            return \"development\";\n        }\n    } catch (e) {}\n    return \"production\";\n}\nfunction setMode(mode = \"auto\") {\n    if (mode === \"auto\") {\n        window.vam = detectEnvironment();\n        return;\n    }\n    window.vam = mode;\n}\nfunction getMode() {\n    const mode = isBrowser() ? window.vam : detectEnvironment();\n    return mode || \"production\";\n}\nfunction isProduction() {\n    return getMode() === \"production\";\n}\nfunction isDevelopment() {\n    return getMode() === \"development\";\n}\nfunction removeKey(key, { [key]: _, ...rest }) {\n    return rest;\n}\nfunction parseProperties(properties, options) {\n    if (!properties) return void 0;\n    let props = properties;\n    const errorProperties = [];\n    for (const [key, value] of Object.entries(properties)){\n        if (typeof value === \"object\" && value !== null) {\n            if (options.strip) {\n                props = removeKey(key, props);\n            } else {\n                errorProperties.push(key);\n            }\n        }\n    }\n    if (errorProperties.length > 0 && !options.strip) {\n        throw Error(`The following properties are not valid: ${errorProperties.join(\", \")}. Only strings, numbers, booleans, and null are allowed.`);\n    }\n    return props;\n}\nfunction getScriptSrc(props) {\n    if (props.scriptSrc) {\n        return props.scriptSrc;\n    }\n    if (isDevelopment()) {\n        return \"https://va.vercel-scripts.com/v1/script.debug.js\";\n    }\n    if (props.basePath) {\n        return `${props.basePath}/insights/script.js`;\n    }\n    return \"/_vercel/insights/script.js\";\n}\n// src/generic.ts\nfunction inject(props = {\n    debug: true\n}) {\n    var _a;\n    if (!isBrowser()) return;\n    setMode(props.mode);\n    initQueue();\n    if (props.beforeSend) {\n        (_a = window.va) == null ? void 0 : _a.call(window, \"beforeSend\", props.beforeSend);\n    }\n    const src = getScriptSrc(props);\n    if (document.head.querySelector(`script[src*=\"${src}\"]`)) return;\n    const script = document.createElement(\"script\");\n    script.src = src;\n    script.defer = true;\n    script.dataset.sdkn = name + (props.framework ? `/${props.framework}` : \"\");\n    script.dataset.sdkv = version;\n    if (props.disableAutoTrack) {\n        script.dataset.disableAutoTrack = \"1\";\n    }\n    if (props.endpoint) {\n        script.dataset.endpoint = props.endpoint;\n    } else if (props.basePath) {\n        script.dataset.endpoint = `${props.basePath}/insights`;\n    }\n    if (props.dsn) {\n        script.dataset.dsn = props.dsn;\n    }\n    script.onerror = ()=>{\n        const errorMessage = isDevelopment() ? \"Please check if any ad blockers are enabled and try again.\" : \"Be sure to enable Web Analytics for your project and deploy again. See https://vercel.com/docs/analytics/quickstart for more information.\";\n        console.log(`[Vercel Web Analytics] Failed to load script from ${src}. ${errorMessage}`);\n    };\n    if (isDevelopment() && props.debug === false) {\n        script.dataset.debug = \"false\";\n    }\n    document.head.appendChild(script);\n}\nfunction track(name2, properties, options) {\n    var _a, _b;\n    if (!isBrowser()) {\n        const msg = \"[Vercel Web Analytics] Please import `track` from `@vercel/analytics/server` when using this function in a server environment\";\n        if (isProduction()) {\n            console.warn(msg);\n        } else {\n            throw new Error(msg);\n        }\n        return;\n    }\n    if (!properties) {\n        (_a = window.va) == null ? void 0 : _a.call(window, \"event\", {\n            name: name2,\n            options\n        });\n        return;\n    }\n    try {\n        const props = parseProperties(properties, {\n            strip: isProduction()\n        });\n        (_b = window.va) == null ? void 0 : _b.call(window, \"event\", {\n            name: name2,\n            data: props,\n            options\n        });\n    } catch (err) {\n        if (err instanceof Error && isDevelopment()) {\n            console.error(err);\n        }\n    }\n}\nfunction pageview({ route, path }) {\n    var _a;\n    (_a = window.va) == null ? void 0 : _a.call(window, \"pageview\", {\n        route,\n        path\n    });\n}\n// src/react/utils.ts\nfunction getBasePath() {\n    if (typeof process === \"undefined\" || typeof process.env === \"undefined\") {\n        return void 0;\n    }\n    return process.env.REACT_APP_VERCEL_OBSERVABILITY_BASEPATH;\n}\n// src/react/index.tsx\nfunction Analytics(props) {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"Analytics.useEffect\": ()=>{\n            var _a;\n            if (props.beforeSend) {\n                (_a = window.va) == null ? void 0 : _a.call(window, \"beforeSend\", props.beforeSend);\n            }\n        }\n    }[\"Analytics.useEffect\"], [\n        props.beforeSend\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"Analytics.useEffect\": ()=>{\n            inject({\n                framework: props.framework || \"react\",\n                basePath: props.basePath ?? getBasePath(),\n                ...props.route !== void 0 && {\n                    disableAutoTrack: true\n                },\n                ...props\n            });\n        }\n    }[\"Analytics.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"Analytics.useEffect\": ()=>{\n            if (props.route && props.path) {\n                pageview({\n                    route: props.route,\n                    path: props.path\n                });\n            }\n        }\n    }[\"Analytics.useEffect\"], [\n        props.route,\n        props.path\n    ]);\n    return null;\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@vercel/analytics/dist/react/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@vercel/speed-insights/dist/next/index.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/@vercel/speed-insights/dist/next/index.mjs ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SpeedInsights: () => (/* binding */ SpeedInsights2)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var next_navigation_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation.js */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ SpeedInsights auto */ // src/nextjs/index.tsx\n\n// src/react/index.tsx\n\n// package.json\nvar name = \"@vercel/speed-insights\";\nvar version = \"1.2.0\";\n// src/queue.ts\nvar initQueue = ()=>{\n    if (window.si) return;\n    window.si = function a(...params) {\n        (window.siq = window.siq || []).push(params);\n    };\n};\n// src/utils.ts\nfunction isBrowser() {\n    return \"undefined\" !== \"undefined\";\n}\nfunction detectEnvironment() {\n    try {\n        const env = \"development\";\n        if (env === \"development\" || env === \"test\") {\n            return \"development\";\n        }\n    } catch (e) {}\n    return \"production\";\n}\nfunction isDevelopment() {\n    return detectEnvironment() === \"development\";\n}\nfunction computeRoute(pathname, pathParams) {\n    if (!pathname || !pathParams) {\n        return pathname;\n    }\n    let result = pathname;\n    try {\n        const entries = Object.entries(pathParams);\n        for (const [key, value] of entries){\n            if (!Array.isArray(value)) {\n                const matcher = turnValueToRegExp(value);\n                if (matcher.test(result)) {\n                    result = result.replace(matcher, `/[${key}]`);\n                }\n            }\n        }\n        for (const [key, value] of entries){\n            if (Array.isArray(value)) {\n                const matcher = turnValueToRegExp(value.join(\"/\"));\n                if (matcher.test(result)) {\n                    result = result.replace(matcher, `/[...${key}]`);\n                }\n            }\n        }\n        return result;\n    } catch (e) {\n        return pathname;\n    }\n}\nfunction turnValueToRegExp(value) {\n    return new RegExp(`/${escapeRegExp(value)}(?=[/?#]|$)`);\n}\nfunction escapeRegExp(string) {\n    return string.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\");\n}\nfunction getScriptSrc(props) {\n    if (props.scriptSrc) {\n        return props.scriptSrc;\n    }\n    if (isDevelopment()) {\n        return \"https://va.vercel-scripts.com/v1/speed-insights/script.debug.js\";\n    }\n    if (props.dsn) {\n        return \"https://va.vercel-scripts.com/v1/speed-insights/script.js\";\n    }\n    if (props.basePath) {\n        return `${props.basePath}/speed-insights/script.js`;\n    }\n    return \"/_vercel/speed-insights/script.js\";\n}\n// src/generic.ts\nfunction injectSpeedInsights(props = {}) {\n    var _a;\n    if (!isBrowser() || props.route === null) return null;\n    initQueue();\n    const src = getScriptSrc(props);\n    if (document.head.querySelector(`script[src*=\"${src}\"]`)) return null;\n    if (props.beforeSend) {\n        (_a = window.si) == null ? void 0 : _a.call(window, \"beforeSend\", props.beforeSend);\n    }\n    const script = document.createElement(\"script\");\n    script.src = src;\n    script.defer = true;\n    script.dataset.sdkn = name + (props.framework ? `/${props.framework}` : \"\");\n    script.dataset.sdkv = version;\n    if (props.sampleRate) {\n        script.dataset.sampleRate = props.sampleRate.toString();\n    }\n    if (props.route) {\n        script.dataset.route = props.route;\n    }\n    if (props.endpoint) {\n        script.dataset.endpoint = props.endpoint;\n    } else if (props.basePath) {\n        script.dataset.endpoint = `${props.basePath}/speed-insights/vitals`;\n    }\n    if (props.dsn) {\n        script.dataset.dsn = props.dsn;\n    }\n    if (isDevelopment() && props.debug === false) {\n        script.dataset.debug = \"false\";\n    }\n    script.onerror = ()=>{\n        console.log(`[Vercel Speed Insights] Failed to load script from ${src}. Please check if any content blockers are enabled and try again.`);\n    };\n    document.head.appendChild(script);\n    return {\n        setRoute: (route)=>{\n            script.dataset.route = route ?? void 0;\n        }\n    };\n}\n// src/react/utils.ts\nfunction getBasePath() {\n    if (typeof process === \"undefined\" || typeof process.env === \"undefined\") {\n        return void 0;\n    }\n    return process.env.REACT_APP_VERCEL_OBSERVABILITY_BASEPATH;\n}\n// src/react/index.tsx\nfunction SpeedInsights(props) {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"SpeedInsights.useEffect\": ()=>{\n            var _a;\n            if (props.beforeSend) {\n                (_a = window.si) == null ? void 0 : _a.call(window, \"beforeSend\", props.beforeSend);\n            }\n        }\n    }[\"SpeedInsights.useEffect\"], [\n        props.beforeSend\n    ]);\n    const setScriptRoute = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"SpeedInsights.useEffect\": ()=>{\n            if (!setScriptRoute.current) {\n                const script = injectSpeedInsights({\n                    framework: props.framework ?? \"react\",\n                    basePath: props.basePath ?? getBasePath(),\n                    ...props\n                });\n                if (script) {\n                    setScriptRoute.current = script.setRoute;\n                }\n            } else if (props.route) {\n                setScriptRoute.current(props.route);\n            }\n        }\n    }[\"SpeedInsights.useEffect\"], [\n        props.route\n    ]);\n    return null;\n}\n// src/nextjs/utils.ts\n\nvar useRoute = ()=>{\n    const params = (0,next_navigation_js__WEBPACK_IMPORTED_MODULE_1__.useParams)();\n    const searchParams = (0,next_navigation_js__WEBPACK_IMPORTED_MODULE_1__.useSearchParams)() || new URLSearchParams();\n    const path = (0,next_navigation_js__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    if (!params) {\n        return null;\n    }\n    const finalParams = Object.keys(params).length ? params : Object.fromEntries(searchParams.entries());\n    return computeRoute(path, finalParams);\n};\nfunction getBasePath2() {\n    if (typeof process === \"undefined\" || typeof process.env === \"undefined\") {\n        return void 0;\n    }\n    return process.env.NEXT_PUBLIC_VERCEL_OBSERVABILITY_BASEPATH;\n}\n// src/nextjs/index.tsx\nfunction SpeedInsightsComponent(props) {\n    const route = useRoute();\n    return /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(SpeedInsights, {\n        route,\n        ...props,\n        framework: \"next\",\n        basePath: getBasePath2()\n    });\n}\nfunction SpeedInsights2(props) {\n    return /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Suspense, {\n        fallback: null\n    }, /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(SpeedInsightsComponent, {\n        ...props\n    }));\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@vercel/speed-insights/dist/next/index.mjs\n");

/***/ })

};
;