"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hast-util-sanitize";
exports.ids = ["vendor-chunks/hast-util-sanitize"];
exports.modules = {

/***/ "(ssr)/./node_modules/hast-util-sanitize/lib/index.js":
/*!******************************************************!*\
  !*** ./node_modules/hast-util-sanitize/lib/index.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   sanitize: () => (/* binding */ sanitize)\n/* harmony export */ });\n/* harmony import */ var _ungap_structured_clone__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ungap/structured-clone */ \"(ssr)/./node_modules/@ungap/structured-clone/esm/index.js\");\n/* harmony import */ var unist_util_position__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! unist-util-position */ \"(ssr)/./node_modules/unist-util-position/lib/index.js\");\n/* harmony import */ var _schema_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./schema.js */ \"(ssr)/./node_modules/hast-util-sanitize/lib/schema.js\");\n/**\n * @import {\n *   Comment,\n *   Doctype,\n *   ElementContent,\n *   Element,\n *   Nodes,\n *   Properties,\n *   RootContent,\n *   Root,\n *   Text\n * } from 'hast'\n */\n\n/**\n * @typedef {[string, ...Array<Exclude<Properties[keyof Properties], Array<any>> | RegExp>] | string} PropertyDefinition\n *   Definition for a property.\n *\n * @typedef Schema\n *   Schema that defines what nodes and properties are allowed.\n *\n *   The default schema is `defaultSchema`, which follows how GitHub cleans.\n *   If any top-level key is missing in the given schema, the corresponding\n *   value of the default schema is used.\n *\n *   To extend the standard schema with a few changes, clone `defaultSchema`\n *   like so:\n *\n *   ```js\n *   import deepmerge from 'deepmerge'\n *   import {h} from 'hastscript'\n *   import {defaultSchema, sanitize} from 'hast-util-sanitize'\n *\n *   // This allows `className` on all elements.\n *   const schema = deepmerge(defaultSchema, {attributes: {'*': ['className']}})\n *\n *   const tree = sanitize(h('div', {className: ['foo']}), schema)\n *\n *   // `tree` still has `className`.\n *   console.log(tree)\n *   // {\n *   //   type: 'element',\n *   //   tagName: 'div',\n *   //   properties: {className: ['foo']},\n *   //   children: []\n *   // }\n *   ```\n * @property {boolean | null | undefined} [allowComments=false]\n *   Whether to allow comment nodes (default: `false`).\n *\n *   For example:\n *\n *   ```js\n *   allowComments: true\n *   ```\n * @property {boolean | null | undefined} [allowDoctypes=false]\n *   Whether to allow doctype nodes (default: `false`).\n *\n *   For example:\n *\n *   ```js\n *   allowDoctypes: true\n *   ```\n * @property {Record<string, Array<string>> | null | undefined} [ancestors]\n *   Map of tag names to a list of tag names which are required ancestors\n *   (default: `defaultSchema.ancestors`).\n *\n *   Elements with these tag names will be ignored if they occur outside of one\n *   of their allowed parents.\n *\n *   For example:\n *\n *   ```js\n *   ancestors: {\n *     tbody: ['table'],\n *     // …\n *     tr: ['table']\n *   }\n *   ```\n * @property {Record<string, Array<PropertyDefinition>> | null | undefined} [attributes]\n *   Map of tag names to allowed property names (default:\n *   `defaultSchema.attributes`).\n *\n *   The special key `'*'` as a tag name defines property names allowed on all\n *   elements.\n *\n *   The special value `'data*'` as a property name can be used to allow all\n *   `data` properties.\n *\n *   For example:\n *\n *   ```js\n *   attributes: {\n *     'ariaDescribedBy', 'ariaLabel', 'ariaLabelledBy', …, 'href'\n *     // …\n *     '*': [\n *       'abbr',\n *       'accept',\n *       'acceptCharset',\n *       // …\n *       'vAlign',\n *       'value',\n *       'width'\n *     ]\n *   }\n *   ```\n *\n *   Instead of a single string in the array, which allows any property value\n *   for the field, you can use an array to allow several values.\n *   For example, `input: ['type']` allows `type` set to any value on `input`s.\n *   But `input: [['type', 'checkbox', 'radio']]` allows `type` when set to\n *   `'checkbox'` or `'radio'`.\n *\n *   You can use regexes, so for example `span: [['className', /^hljs-/]]`\n *   allows any class that starts with `hljs-` on `span`s.\n *\n *   When comma- or space-separated values are used (such as `className`), each\n *   value in is checked individually.\n *   For example, to allow certain classes on `span`s for syntax highlighting,\n *   use `span: [['className', 'number', 'operator', 'token']]`.\n *   This will allow `'number'`, `'operator'`, and `'token'` classes, but drop\n *   others.\n * @property {Array<string> | null | undefined} [clobber]\n *   List of property names that clobber (default: `defaultSchema.clobber`).\n *\n *   For example:\n *\n *   ```js\n *   clobber: ['ariaDescribedBy', 'ariaLabelledBy', 'id', 'name']\n *   ```\n * @property {string | null | undefined} [clobberPrefix]\n *   Prefix to use before clobbering properties (default:\n *   `defaultSchema.clobberPrefix`).\n *\n *   For example:\n *\n *   ```js\n *   clobberPrefix: 'user-content-'\n *   ```\n * @property {Record<string, Array<string> | null | undefined> | null | undefined} [protocols]\n *   Map of *property names* to allowed protocols (default:\n *   `defaultSchema.protocols`).\n *\n *   This defines URLs that are always allowed to have local URLs (relative to\n *   the current website, such as `this`, `#this`, `/this`, or `?this`), and\n *   only allowed to have remote URLs (such as `https://example.com`) if they\n *   use a known protocol.\n *\n *   For example:\n *\n *   ```js\n *   protocols: {\n *     cite: ['http', 'https'],\n *     // …\n *     src: ['http', 'https']\n *   }\n *   ```\n * @property {Record<string, Record<string, Properties[keyof Properties]>> | null | undefined} [required]\n *   Map of tag names to required property names with a default value\n *   (default: `defaultSchema.required`).\n *\n *   This defines properties that must be set.\n *   If a field does not exist (after the element was made safe), these will be\n *   added with the given value.\n *\n *   For example:\n *\n *   ```js\n *   required: {\n *     input: {disabled: true, type: 'checkbox'}\n *   }\n *   ```\n *\n *   > 👉 **Note**: properties are first checked based on `schema.attributes`,\n *   > then on `schema.required`.\n *   > That means properties could be removed by `attributes` and then added\n *   > again with `required`.\n * @property {Array<string> | null | undefined} [strip]\n *   List of tag names to strip from the tree (default: `defaultSchema.strip`).\n *\n *   By default, unsafe elements (those not in `schema.tagNames`) are replaced\n *   by what they contain.\n *   This option can drop their contents.\n *\n *   For example:\n *\n *   ```js\n *   strip: ['script']\n *   ```\n * @property {Array<string> | null | undefined} [tagNames]\n *   List of allowed tag names (default: `defaultSchema.tagNames`).\n *\n *   For example:\n *\n *   ```js\n *   tagNames: [\n *     'a',\n *     'b',\n *     // …\n *     'ul',\n *     'var'\n *   ]\n *   ```\n *\n * @typedef State\n *   Info passed around.\n * @property {Readonly<Schema>} schema\n *   Schema.\n * @property {Array<string>} stack\n *   Tag names of ancestors.\n */\n\n\n\n\n\nconst own = {}.hasOwnProperty\n\n/**\n * Sanitize a tree.\n *\n * @param {Readonly<Nodes>} node\n *   Unsafe tree.\n * @param {Readonly<Schema> | null | undefined} [options]\n *   Configuration (default: `defaultSchema`).\n * @returns {Nodes}\n *   New, safe tree.\n */\nfunction sanitize(node, options) {\n  /** @type {Nodes} */\n  let result = {type: 'root', children: []}\n\n  /** @type {State} */\n  const state = {\n    schema: options ? {..._schema_js__WEBPACK_IMPORTED_MODULE_0__.defaultSchema, ...options} : _schema_js__WEBPACK_IMPORTED_MODULE_0__.defaultSchema,\n    stack: []\n  }\n  const replace = transform(state, node)\n\n  if (replace) {\n    if (Array.isArray(replace)) {\n      if (replace.length === 1) {\n        result = replace[0]\n      } else {\n        result.children = replace\n      }\n    } else {\n      result = replace\n    }\n  }\n\n  return result\n}\n\n/**\n * Sanitize `node`.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Readonly<unknown>} node\n *   Unsafe node.\n * @returns {Array<ElementContent> | Nodes | undefined}\n *   Safe result.\n */\nfunction transform(state, node) {\n  if (node && typeof node === 'object') {\n    const unsafe = /** @type {Record<string, Readonly<unknown>>} */ (node)\n    const type = typeof unsafe.type === 'string' ? unsafe.type : ''\n\n    switch (type) {\n      case 'comment': {\n        return comment(state, unsafe)\n      }\n\n      case 'doctype': {\n        return doctype(state, unsafe)\n      }\n\n      case 'element': {\n        return element(state, unsafe)\n      }\n\n      case 'root': {\n        return root(state, unsafe)\n      }\n\n      case 'text': {\n        return text(state, unsafe)\n      }\n\n      default:\n    }\n  }\n}\n\n/**\n * Make a safe comment.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Readonly<Record<string, Readonly<unknown>>>} unsafe\n *   Unsafe comment-like value.\n * @returns {Comment | undefined}\n *   Safe comment (if with `allowComments`).\n */\nfunction comment(state, unsafe) {\n  if (state.schema.allowComments) {\n    // See <https://html.spec.whatwg.org/multipage/parsing.html#serialising-html-fragments>\n    const result = typeof unsafe.value === 'string' ? unsafe.value : ''\n    const index = result.indexOf('-->')\n    const value = index < 0 ? result : result.slice(0, index)\n\n    /** @type {Comment} */\n    const node = {type: 'comment', value}\n\n    patch(node, unsafe)\n\n    return node\n  }\n}\n\n/**\n * Make a safe doctype.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Readonly<Record<string, Readonly<unknown>>>} unsafe\n *   Unsafe doctype-like value.\n * @returns {Doctype | undefined}\n *   Safe doctype (if with `allowDoctypes`).\n */\nfunction doctype(state, unsafe) {\n  if (state.schema.allowDoctypes) {\n    /** @type {Doctype} */\n    const node = {type: 'doctype'}\n\n    patch(node, unsafe)\n\n    return node\n  }\n}\n\n/**\n * Make a safe element.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Readonly<Record<string, Readonly<unknown>>>} unsafe\n *   Unsafe element-like value.\n * @returns {Array<ElementContent> | Element | undefined}\n *   Safe element.\n */\nfunction element(state, unsafe) {\n  const name = typeof unsafe.tagName === 'string' ? unsafe.tagName : ''\n\n  state.stack.push(name)\n\n  const content = /** @type {Array<ElementContent>} */ (\n    children(state, unsafe.children)\n  )\n  const properties_ = properties(state, unsafe.properties)\n\n  state.stack.pop()\n\n  let safeElement = false\n\n  if (\n    name &&\n    name !== '*' &&\n    (!state.schema.tagNames || state.schema.tagNames.includes(name))\n  ) {\n    safeElement = true\n\n    // Some nodes can break out of their context if they don’t have a certain\n    // ancestor.\n    if (state.schema.ancestors && own.call(state.schema.ancestors, name)) {\n      const ancestors = state.schema.ancestors[name]\n      let index = -1\n\n      safeElement = false\n\n      while (++index < ancestors.length) {\n        if (state.stack.includes(ancestors[index])) {\n          safeElement = true\n        }\n      }\n    }\n  }\n\n  if (!safeElement) {\n    return state.schema.strip && !state.schema.strip.includes(name)\n      ? content\n      : undefined\n  }\n\n  /** @type {Element} */\n  const node = {\n    type: 'element',\n    tagName: name,\n    properties: properties_,\n    children: content\n  }\n\n  patch(node, unsafe)\n\n  return node\n}\n\n/**\n * Make a safe root.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Readonly<Record<string, Readonly<unknown>>>} unsafe\n *   Unsafe root-like value.\n * @returns {Root}\n *   Safe root.\n */\nfunction root(state, unsafe) {\n  const content = /** @type {Array<RootContent>} */ (\n    children(state, unsafe.children)\n  )\n\n  /** @type {Root} */\n  const node = {type: 'root', children: content}\n\n  patch(node, unsafe)\n\n  return node\n}\n\n/**\n * Make a safe text.\n *\n * @param {State} _\n *   Info passed around.\n * @param {Readonly<Record<string, Readonly<unknown>>>} unsafe\n *   Unsafe text-like value.\n * @returns {Text}\n *   Safe text.\n */\nfunction text(_, unsafe) {\n  const value = typeof unsafe.value === 'string' ? unsafe.value : ''\n  /** @type {Text} */\n  const node = {type: 'text', value}\n\n  patch(node, unsafe)\n\n  return node\n}\n\n/**\n * Make children safe.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Readonly<unknown>} children\n *   Unsafe value.\n * @returns {Array<Nodes>}\n *   Safe children.\n */\nfunction children(state, children) {\n  /** @type {Array<Nodes>} */\n  const results = []\n\n  if (Array.isArray(children)) {\n    const childrenUnknown = /** @type {Array<Readonly<unknown>>} */ (children)\n    let index = -1\n\n    while (++index < childrenUnknown.length) {\n      const value = transform(state, childrenUnknown[index])\n\n      if (value) {\n        if (Array.isArray(value)) {\n          results.push(...value)\n        } else {\n          results.push(value)\n        }\n      }\n    }\n  }\n\n  return results\n}\n\n/**\n * Make element properties safe.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Readonly<unknown>} properties\n *   Unsafe value.\n * @returns {Properties}\n *   Safe value.\n */\nfunction properties(state, properties) {\n  const tagName = state.stack[state.stack.length - 1]\n  const attributes = state.schema.attributes\n  const required = state.schema.required\n  const specific =\n    attributes && own.call(attributes, tagName)\n      ? attributes[tagName]\n      : undefined\n  const defaults =\n    attributes && own.call(attributes, '*') ? attributes['*'] : undefined\n  const properties_ =\n    /** @type {Readonly<Record<string, Readonly<unknown>>>} */ (\n      properties && typeof properties === 'object' ? properties : {}\n    )\n  /** @type {Properties} */\n  const result = {}\n  /** @type {string} */\n  let key\n\n  for (key in properties_) {\n    if (own.call(properties_, key)) {\n      const unsafe = properties_[key]\n      let safe = propertyValue(\n        state,\n        findDefinition(specific, key),\n        key,\n        unsafe\n      )\n\n      if (safe === null || safe === undefined) {\n        safe = propertyValue(state, findDefinition(defaults, key), key, unsafe)\n      }\n\n      if (safe !== null && safe !== undefined) {\n        result[key] = safe\n      }\n    }\n  }\n\n  if (required && own.call(required, tagName)) {\n    const properties = required[tagName]\n\n    for (key in properties) {\n      if (own.call(properties, key) && !own.call(result, key)) {\n        result[key] = properties[key]\n      }\n    }\n  }\n\n  return result\n}\n\n/**\n * Sanitize a property value.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Readonly<PropertyDefinition> | undefined} definition\n *   Definition.\n * @param {string} key\n *   Field name.\n * @param {Readonly<unknown>} value\n *   Unsafe value (but an array).\n * @returns {Array<number | string> | boolean | number | string | undefined}\n *   Safe value.\n */\nfunction propertyValue(state, definition, key, value) {\n  return definition\n    ? Array.isArray(value)\n      ? propertyValueMany(state, definition, key, value)\n      : propertyValuePrimitive(state, definition, key, value)\n    : undefined\n}\n\n/**\n * Sanitize a property value which is a list.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Readonly<PropertyDefinition>} definition\n *   Definition.\n * @param {string} key\n *   Field name.\n * @param {Readonly<Array<Readonly<unknown>>>} values\n *   Unsafe value (but an array).\n * @returns {Array<number | string>}\n *   Safe value.\n */\nfunction propertyValueMany(state, definition, key, values) {\n  let index = -1\n  /** @type {Array<number | string>} */\n  const result = []\n\n  while (++index < values.length) {\n    const value = propertyValuePrimitive(state, definition, key, values[index])\n\n    if (typeof value === 'number' || typeof value === 'string') {\n      result.push(value)\n    }\n  }\n\n  return result\n}\n\n/**\n * Sanitize a property value which is a primitive.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Readonly<PropertyDefinition>} definition\n *   Definition.\n * @param {string} key\n *   Field name.\n * @param {Readonly<unknown>} value\n *   Unsafe value (but not an array).\n * @returns {boolean | number | string | undefined}\n *   Safe value.\n */\nfunction propertyValuePrimitive(state, definition, key, value) {\n  if (\n    typeof value !== 'boolean' &&\n    typeof value !== 'number' &&\n    typeof value !== 'string'\n  ) {\n    return\n  }\n\n  if (!safeProtocol(state, key, value)) {\n    return\n  }\n\n  // Just a string, or only one item in an array, means all values are OK.\n  // More than one item means an allow list.\n  if (typeof definition === 'object' && definition.length > 1) {\n    let ok = false\n    let index = 0 // Ignore `key`, which is the first item.\n\n    while (++index < definition.length) {\n      const allowed = definition[index]\n\n      // Expression.\n      if (allowed && typeof allowed === 'object' && 'flags' in allowed) {\n        if (allowed.test(String(value))) {\n          ok = true\n          break\n        }\n      }\n      // Primitive.\n      else if (allowed === value) {\n        ok = true\n        break\n      }\n    }\n\n    if (!ok) return\n  }\n\n  return state.schema.clobber &&\n    state.schema.clobberPrefix &&\n    state.schema.clobber.includes(key)\n    ? state.schema.clobberPrefix + value\n    : value\n}\n\n/**\n * Check whether `value` is a safe URL.\n *\n * @param {State} state\n *   Info passed around.\n * @param {string} key\n *   Field name.\n * @param {Readonly<unknown>} value\n *   Unsafe value.\n * @returns {boolean}\n *   Whether it’s a safe value.\n */\nfunction safeProtocol(state, key, value) {\n  const protocols =\n    state.schema.protocols && own.call(state.schema.protocols, key)\n      ? state.schema.protocols[key]\n      : undefined\n\n  // No protocols defined? Then everything is fine.\n  if (!protocols || protocols.length === 0) {\n    return true\n  }\n\n  const url = String(value)\n  const colon = url.indexOf(':')\n  const questionMark = url.indexOf('?')\n  const numberSign = url.indexOf('#')\n  const slash = url.indexOf('/')\n\n  if (\n    colon < 0 ||\n    // If the first colon is after a `?`, `#`, or `/`, it’s not a protocol.\n    (slash > -1 && colon > slash) ||\n    (questionMark > -1 && colon > questionMark) ||\n    (numberSign > -1 && colon > numberSign)\n  ) {\n    return true\n  }\n\n  let index = -1\n\n  while (++index < protocols.length) {\n    const protocol = protocols[index]\n\n    if (\n      colon === protocol.length &&\n      url.slice(0, protocol.length) === protocol\n    ) {\n      return true\n    }\n  }\n\n  return false\n}\n\n/**\n * Add data and position.\n *\n * @param {Nodes} node\n *   Node to patch safe data and position on.\n * @param {Readonly<Record<string, Readonly<unknown>>>} unsafe\n *   Unsafe node-like value.\n * @returns {undefined}\n *   Nothing.\n */\nfunction patch(node, unsafe) {\n  const cleanPosition = (0,unist_util_position__WEBPACK_IMPORTED_MODULE_1__.position)(\n    // @ts-expect-error: looks like a node.\n    unsafe\n  )\n\n  if (unsafe.data) {\n    node.data = (0,_ungap_structured_clone__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(unsafe.data)\n  }\n\n  if (cleanPosition) node.position = cleanPosition\n}\n\n/**\n *\n * @param {Readonly<Array<PropertyDefinition>> | undefined} definitions\n * @param {string} key\n * @returns {Readonly<PropertyDefinition> | undefined}\n */\nfunction findDefinition(definitions, key) {\n  /** @type {PropertyDefinition | undefined} */\n  let dataDefault\n  let index = -1\n\n  if (definitions) {\n    while (++index < definitions.length) {\n      const entry = definitions[index]\n      const name = typeof entry === 'string' ? entry : entry[0]\n\n      if (name === key) {\n        return entry\n      }\n\n      if (name === 'data*') dataDefault = entry\n    }\n  }\n\n  if (key.length > 4 && key.slice(0, 4).toLowerCase() === 'data') {\n    return dataDefault\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-sanitize/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-sanitize/lib/schema.js":
/*!*******************************************************!*\
  !*** ./node_modules/hast-util-sanitize/lib/schema.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultSchema: () => (/* binding */ defaultSchema)\n/* harmony export */ });\n/**\n * @import {Schema} from 'hast-util-sanitize'\n */\n\n// Couple of ARIA attributes allowed in several, but not all, places.\nconst aria = ['ariaDescribedBy', 'ariaLabel', 'ariaLabelledBy']\n\n/**\n * Default schema.\n *\n * Follows GitHub style sanitation.\n *\n * @type {Schema}\n */\nconst defaultSchema = {\n  ancestors: {\n    tbody: ['table'],\n    td: ['table'],\n    th: ['table'],\n    thead: ['table'],\n    tfoot: ['table'],\n    tr: ['table']\n  },\n  attributes: {\n    a: [\n      ...aria,\n      // Note: these 3 are used by GFM footnotes, they do work on all links.\n      'dataFootnoteBackref',\n      'dataFootnoteRef',\n      ['className', 'data-footnote-backref'],\n      'href'\n    ],\n    blockquote: ['cite'],\n    // Note: this class is not normally allowed by GH, when manually writing\n    // `code` as HTML in markdown, they adds it some other way.\n    // We can’t do that, so we have to allow it.\n    code: [['className', /^language-./]],\n    del: ['cite'],\n    div: ['itemScope', 'itemType'],\n    dl: [...aria],\n    // Note: this is used by GFM footnotes.\n    h2: [['className', 'sr-only']],\n    img: [...aria, 'longDesc', 'src'],\n    // Note: `input` is not normally allowed by GH, when manually writing\n    // it in markdown, they add it from tasklists some other way.\n    // We can’t do that, so we have to allow it.\n    input: [\n      ['disabled', true],\n      ['type', 'checkbox']\n    ],\n    ins: ['cite'],\n    // Note: this class is not normally allowed by GH, when manually writing\n    // `li` as HTML in markdown, they adds it some other way.\n    // We can’t do that, so we have to allow it.\n    li: [['className', 'task-list-item']],\n    // Note: this class is not normally allowed by GH, when manually writing\n    // `ol` as HTML in markdown, they adds it some other way.\n    // We can’t do that, so we have to allow it.\n    ol: [...aria, ['className', 'contains-task-list']],\n    q: ['cite'],\n    section: ['dataFootnotes', ['className', 'footnotes']],\n    source: ['srcSet'],\n    summary: [...aria],\n    table: [...aria],\n    // Note: this class is not normally allowed by GH, when manually writing\n    // `ol` as HTML in markdown, they adds it some other way.\n    // We can’t do that, so we have to allow it.\n    ul: [...aria, ['className', 'contains-task-list']],\n    '*': [\n      'abbr',\n      'accept',\n      'acceptCharset',\n      'accessKey',\n      'action',\n      'align',\n      'alt',\n      'axis',\n      'border',\n      'cellPadding',\n      'cellSpacing',\n      'char',\n      'charOff',\n      'charSet',\n      'checked',\n      'clear',\n      'colSpan',\n      'color',\n      'cols',\n      'compact',\n      'coords',\n      'dateTime',\n      'dir',\n      // Note: `disabled` is technically allowed on all elements by GH.\n      // But it is useless on everything except `input`.\n      // Because `input`s are normally not allowed, but we allow them for\n      // checkboxes due to tasklists, we allow `disabled` only there.\n      'encType',\n      'frame',\n      'hSpace',\n      'headers',\n      'height',\n      'hrefLang',\n      'htmlFor',\n      'id',\n      'isMap',\n      'itemProp',\n      'label',\n      'lang',\n      'maxLength',\n      'media',\n      'method',\n      'multiple',\n      'name',\n      'noHref',\n      'noShade',\n      'noWrap',\n      'open',\n      'prompt',\n      'readOnly',\n      'rev',\n      'rowSpan',\n      'rows',\n      'rules',\n      'scope',\n      'selected',\n      'shape',\n      'size',\n      'span',\n      'start',\n      'summary',\n      'tabIndex',\n      'title',\n      'useMap',\n      'vAlign',\n      'value',\n      'width'\n    ]\n  },\n  clobber: ['ariaDescribedBy', 'ariaLabelledBy', 'id', 'name'],\n  clobberPrefix: 'user-content-',\n  protocols: {\n    cite: ['http', 'https'],\n    href: ['http', 'https', 'irc', 'ircs', 'mailto', 'xmpp'],\n    longDesc: ['http', 'https'],\n    src: ['http', 'https']\n  },\n  required: {\n    input: {disabled: true, type: 'checkbox'}\n  },\n  strip: ['script'],\n  tagNames: [\n    'a',\n    'b',\n    'blockquote',\n    'br',\n    'code',\n    'dd',\n    'del',\n    'details',\n    'div',\n    'dl',\n    'dt',\n    'em',\n    'h1',\n    'h2',\n    'h3',\n    'h4',\n    'h5',\n    'h6',\n    'hr',\n    'i',\n    'img',\n    // Note: `input` is not normally allowed by GH, when manually writing\n    // it in markdown, they add it from tasklists some other way.\n    // We can’t do that, so we have to allow it.\n    'input',\n    'ins',\n    'kbd',\n    'li',\n    'ol',\n    'p',\n    'picture',\n    'pre',\n    'q',\n    'rp',\n    'rt',\n    'ruby',\n    's',\n    'samp',\n    'section',\n    'source',\n    'span',\n    'strike',\n    'strong',\n    'sub',\n    'summary',\n    'sup',\n    'table',\n    'tbody',\n    'td',\n    'tfoot',\n    'th',\n    'thead',\n    'tr',\n    'tt',\n    'ul',\n    'var'\n  ]\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-sanitize/lib/schema.js\n");

/***/ })

};
;