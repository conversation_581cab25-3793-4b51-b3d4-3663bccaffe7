"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_bibtex_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/bibtex.mjs":
/*!*****************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/bibtex.mjs ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"BibTeX\\\",\\\"name\\\":\\\"bibtex\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.bibtex\\\"}},\\\"match\\\":\\\"@(?i:comment)(?=[\\\\\\\\s{(])\\\",\\\"name\\\":\\\"comment.block.at-sign.bibtex\\\"},{\\\"include\\\":\\\"#preamble\\\"},{\\\"include\\\":\\\"#string\\\"},{\\\"include\\\":\\\"#entry\\\"},{\\\"begin\\\":\\\"[^@\\\\\\\\n]\\\",\\\"end\\\":\\\"(?=@)\\\",\\\"name\\\":\\\"comment.block.bibtex\\\"}],\\\"repository\\\":{\\\"entry\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"((@)[a-zA-Z!$\\\\\\\\&*+\\\\\\\\-./:;<>?@\\\\\\\\[\\\\\\\\\\\\\\\\\\\\\\\\]^_`|~][a-zA-Z0-9!$\\\\\\\\&*+\\\\\\\\-./:;<>?@\\\\\\\\[\\\\\\\\\\\\\\\\\\\\\\\\]^_`|~]*)\\\\\\\\s*(\\\\\\\\{)\\\\\\\\s*([^\\\\\\\\s,}]*)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.entry-type.bibtex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.bibtex\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.section.entry.begin.bibtex\\\"},\\\"4\\\":{\\\"name\\\":\\\"entity.name.type.entry-key.bibtex\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.entry.end.bibtex\\\"}},\\\"name\\\":\\\"meta.entry.braces.bibtex\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"([a-zA-Z!$\\\\\\\\&*+\\\\\\\\-./:;<>?@\\\\\\\\[\\\\\\\\\\\\\\\\\\\\\\\\]^_`|~][a-zA-Z0-9!$\\\\\\\\&*+\\\\\\\\-./:;<>?@\\\\\\\\[\\\\\\\\\\\\\\\\\\\\\\\\]^_`|~]*)\\\\\\\\s*(=)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.key.bibtex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.key-value.bibtex\\\"}},\\\"end\\\":\\\"(?=[,}])\\\",\\\"name\\\":\\\"meta.key-assignment.bibtex\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#field_value\\\"}]}]},{\\\"begin\\\":\\\"((@)[a-zA-Z!$\\\\\\\\&*+\\\\\\\\-./:;<>?@\\\\\\\\[\\\\\\\\\\\\\\\\\\\\\\\\]^_`|~][a-zA-Z0-9!$\\\\\\\\&*+\\\\\\\\-./:;<>?@\\\\\\\\[\\\\\\\\\\\\\\\\\\\\\\\\]^_`|~]*)\\\\\\\\s*(\\\\\\\\()\\\\\\\\s*([^\\\\\\\\s,]*)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.entry-type.bibtex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.bibtex\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.section.entry.begin.bibtex\\\"},\\\"4\\\":{\\\"name\\\":\\\"entity.name.type.entry-key.bibtex\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.entry.end.bibtex\\\"}},\\\"name\\\":\\\"meta.entry.parenthesis.bibtex\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"([a-zA-Z!$\\\\\\\\&*+\\\\\\\\-./:;<>?@\\\\\\\\[\\\\\\\\\\\\\\\\\\\\\\\\]^_`|~][a-zA-Z0-9!$\\\\\\\\&*+\\\\\\\\-./:;<>?@\\\\\\\\[\\\\\\\\\\\\\\\\\\\\\\\\]^_`|~]*)\\\\\\\\s*(=)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.key.bibtex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.key-value.bibtex\\\"}},\\\"end\\\":\\\"(?=[,)])\\\",\\\"name\\\":\\\"meta.key-assignment.bibtex\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#field_value\\\"}]}]}]},\\\"field_value\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#string_content\\\"},{\\\"include\\\":\\\"#integer\\\"},{\\\"include\\\":\\\"#string_var\\\"},{\\\"match\\\":\\\"#\\\",\\\"name\\\":\\\"keyword.operator.bibtex\\\"}]},\\\"integer\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.numeric.bibtex\\\"}},\\\"match\\\":\\\"\\\\\\\\s*(\\\\\\\\d+)\\\\\\\\s*\\\"},\\\"nested_braces\\\":{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.bibtex\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.bibtex\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#nested_braces\\\"}]},\\\"preamble\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"((@)(?i:preamble))\\\\\\\\s*(\\\\\\\\{)\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.preamble.bibtex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.bibtex\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.section.preamble.begin.bibtex\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.preamble.end.bibtex\\\"}},\\\"name\\\":\\\"meta.preamble.braces.bibtex\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#field_value\\\"}]},{\\\"begin\\\":\\\"((@)(?i:preamble))\\\\\\\\s*(\\\\\\\\()\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.preamble.bibtex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.bibtex\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.section.preamble.begin.bibtex\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.preamble.end.bibtex\\\"}},\\\"name\\\":\\\"meta.preamble.parenthesis.bibtex\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#field_value\\\"}]}]},\\\"string\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"((@)(?i:string))\\\\\\\\s*(\\\\\\\\{)\\\\\\\\s*([a-zA-Z!$\\\\\\\\&*+\\\\\\\\-./:;<>?@\\\\\\\\[\\\\\\\\\\\\\\\\\\\\\\\\]^_`|~][a-zA-Z0-9!$\\\\\\\\&*+\\\\\\\\-./:;<>?@\\\\\\\\[\\\\\\\\\\\\\\\\\\\\\\\\]^_`|~]*)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.string-constant.bibtex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.bibtex\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.section.string-constant.begin.bibtex\\\"},\\\"4\\\":{\\\"name\\\":\\\"variable.other.bibtex\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.string-constant.end.bibtex\\\"}},\\\"name\\\":\\\"meta.string-constant.braces.bibtex\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#field_value\\\"}]},{\\\"begin\\\":\\\"((@)(?i:string))\\\\\\\\s*(\\\\\\\\()\\\\\\\\s*([a-zA-Z!$\\\\\\\\&*+\\\\\\\\-./:;<>?@\\\\\\\\[\\\\\\\\\\\\\\\\\\\\\\\\]^_`|~][a-zA-Z0-9!$\\\\\\\\&*+\\\\\\\\-./:;<>?@\\\\\\\\[\\\\\\\\\\\\\\\\\\\\\\\\]^_`|~]*)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.string-constant.bibtex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.bibtex\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.section.string-constant.begin.bibtex\\\"},\\\"4\\\":{\\\"name\\\":\\\"variable.other.bibtex\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.string-constant.end.bibtex\\\"}},\\\"name\\\":\\\"meta.string-constant.parenthesis.bibtex\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#field_value\\\"}]}]},\\\"string_content\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.bibtex\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.bibtex\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#nested_braces\\\"}]},{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.bibtex\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.bibtex\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#nested_braces\\\"}]}]},\\\"string_var\\\":{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.variable.bibtex\\\"}},\\\"match\\\":\\\"[a-zA-Z!$\\\\\\\\&*+\\\\\\\\-./:;<>?@\\\\\\\\[\\\\\\\\\\\\\\\\\\\\\\\\]^_`|~][a-zA-Z0-9!$\\\\\\\\&*+\\\\\\\\-./:;<>?@\\\\\\\\[\\\\\\\\\\\\\\\\\\\\\\\\]^_`|~]*\\\"}},\\\"scopeName\\\":\\\"text.bibtex\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/bibtex.mjs\n"));

/***/ })

}]);