"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_rel_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/rel.mjs":
/*!**************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/rel.mjs ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Rel\\\",\\\"name\\\":\\\"rel\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#strings\\\"},{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#single-line-comment-consuming-line-ending\\\"},{\\\"include\\\":\\\"#deprecated-temporary\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"include\\\":\\\"#symbols\\\"},{\\\"include\\\":\\\"#keywords\\\"},{\\\"include\\\":\\\"#otherkeywords\\\"},{\\\"include\\\":\\\"#types\\\"},{\\\"include\\\":\\\"#constants\\\"}],\\\"repository\\\":{\\\"comment\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"/\\\\\\\\*\\\\\\\\*(?!/)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.rel\\\"}},\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.rel\\\"}},\\\"name\\\":\\\"comment.block.documentation.rel\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#docblock\\\"}]},{\\\"begin\\\":\\\"(/\\\\\\\\*)(?:\\\\\\\\s*((@)internal)(?=\\\\\\\\s|(\\\\\\\\*/)))?\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.rel\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.type.internaldeclaration.rel\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.decorator.internaldeclaration.rel\\\"}},\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.rel\\\"}},\\\"name\\\":\\\"comment.block.rel\\\"},{\\\"begin\\\":\\\"doc\\\\\\\"\\\\\\\"\\\\\\\"\\\",\\\"end\\\":\\\"\\\\\\\"\\\\\\\"\\\\\\\"\\\",\\\"name\\\":\\\"comment.block.documentation.rel\\\"},{\\\"begin\\\":\\\"(^[ \\\\\\\\t]+)?((//)(?:\\\\\\\\s*((@)internal)(?=\\\\\\\\s|$))?)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.comment.leading.rel\\\"},\\\"2\\\":{\\\"name\\\":\\\"comment.line.double-slash.rel\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.comment.rel\\\"},\\\"4\\\":{\\\"name\\\":\\\"storage.type.internaldeclaration.rel\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.decorator.internaldeclaration.rel\\\"}},\\\"contentName\\\":\\\"comment.line.double-slash.rel\\\",\\\"end\\\":\\\"(?=$)\\\"}]},\\\"constants\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(\\\\\\\\b(true|false)\\\\\\\\b)\\\",\\\"name\\\":\\\"constant.language.rel\\\"}]},\\\"deprecated-temporary\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"@inspect\\\",\\\"name\\\":\\\"keyword.other.rel\\\"}]},\\\"keywords\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(\\\\\\\\b(def|entity|bound|include|ic|forall|exists|[∀∃]|return|module|^end)\\\\\\\\b)|(((<)?\\\\\\\\|(>)?)|[∀∃])\\\",\\\"name\\\":\\\"keyword.control.rel\\\"}]},\\\"operators\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(\\\\\\\\b(if|then|else|and|or|not|eq|neq|lt|lt_eq|gt|gt_eq)\\\\\\\\b)|([+\\\\\\\\-*/÷^%=]|!=|[≠<]|<=|[≤>]|>=|[≥\\\\\\\\&])|\\\\\\\\s+(end)\\\",\\\"name\\\":\\\"keyword.other.rel\\\"}]},\\\"otherkeywords\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\s*(@inline)\\\\\\\\s*|\\\\\\\\s*(@auto_number)\\\\\\\\s*|\\\\\\\\s*(function)\\\\\\\\s|(\\\\\\\\b(implies|select|from|∈|where|for|in)\\\\\\\\b)|(((<)?\\\\\\\\|(>)?)|∈)\\\",\\\"name\\\":\\\"keyword.other.rel\\\"}]},\\\"single-line-comment-consuming-line-ending\\\":{\\\"begin\\\":\\\"(^[ \\\\\\\\t]+)?((//)(?:\\\\\\\\s*((@)internal)(?=\\\\\\\\s|$))?)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.comment.leading.rel\\\"},\\\"2\\\":{\\\"name\\\":\\\"comment.line.double-slash.rel\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.comment.rel\\\"},\\\"4\\\":{\\\"name\\\":\\\"storage.type.internaldeclaration.rel\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.decorator.internaldeclaration.rel\\\"}},\\\"contentName\\\":\\\"comment.line.double-slash.rel\\\",\\\"end\\\":\\\"(?=^)\\\"},\\\"strings\\\":{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"end\\\":\\\"\\\\\\\"\\\",\\\"name\\\":\\\"string.quoted.double.rel\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"constant.character.escape.rel\\\"}]},\\\"symbols\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(:[\\\\\\\\[_$[:alpha:]](]|[_$[:alnum:]]*))\\\",\\\"name\\\":\\\"variable.parameter.rel\\\"}]},\\\"types\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(\\\\\\\\b(Symbol|Char|Bool|Rational|FixedDecimal|Float16|Float32|Float64|Int8|Int16|Int32|Int64|Int128|UInt8|UInt16|UInt32|UInt64|UInt128|Date|DateTime|Day|Week|Month|Year|Nanosecond|Microsecond|Millisecond|Second|Minute|Hour|FilePos|HashValue|AutoNumberValue)\\\\\\\\b)\\\",\\\"name\\\":\\\"entity.name.type.rel\\\"}]}},\\\"scopeName\\\":\\\"source.rel\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/rel.mjs\n"));

/***/ })

}]);