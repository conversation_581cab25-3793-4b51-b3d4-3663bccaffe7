"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_jssm_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/jssm.mjs":
/*!***************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/jssm.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"JSSM\\\",\\\"fileTypes\\\":[\\\"jssm\\\",\\\"jssm_state\\\"],\\\"name\\\":\\\"jssm\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"/\\\\\\\\*\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.mn\\\"}},\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"name\\\":\\\"comment.block.jssm\\\"},{\\\"begin\\\":\\\"//\\\",\\\"end\\\":\\\"$\\\",\\\"name\\\":\\\"comment.line.jssm\\\"},{\\\"begin\\\":\\\"\\\\\\\\$\\\\\\\\{\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"entity.name.function\\\"}},\\\"end\\\":\\\"}\\\",\\\"name\\\":\\\"keyword.other\\\"},{\\\"match\\\":\\\"([0-9]*)(\\\\\\\\.)([0-9]*)(\\\\\\\\.)([0-9]*)\\\",\\\"name\\\":\\\"constant.numeric\\\"},{\\\"match\\\":\\\"graph_layout(\\\\\\\\s*)(:)\\\",\\\"name\\\":\\\"constant.language.jssmLanguage\\\"},{\\\"match\\\":\\\"machine_name(\\\\\\\\s*)(:)\\\",\\\"name\\\":\\\"constant.language.jssmLanguage\\\"},{\\\"match\\\":\\\"machine_version(\\\\\\\\s*)(:)\\\",\\\"name\\\":\\\"constant.language.jssmLanguage\\\"},{\\\"match\\\":\\\"jssm_version(\\\\\\\\s*)(:)\\\",\\\"name\\\":\\\"constant.language.jssmLanguage\\\"},{\\\"match\\\":\\\"<->\\\",\\\"name\\\":\\\"keyword.control.transition.jssmArrow.legal_legal\\\"},{\\\"match\\\":\\\"<-\\\",\\\"name\\\":\\\"keyword.control.transition.jssmArrow.legal_none\\\"},{\\\"match\\\":\\\"->\\\",\\\"name\\\":\\\"keyword.control.transition.jssmArrow.none_legal\\\"},{\\\"match\\\":\\\"<=>\\\",\\\"name\\\":\\\"keyword.control.transition.jssmArrow.main_main\\\"},{\\\"match\\\":\\\"=>\\\",\\\"name\\\":\\\"keyword.control.transition.jssmArrow.none_main\\\"},{\\\"match\\\":\\\"<=\\\",\\\"name\\\":\\\"keyword.control.transition.jssmArrow.main_none\\\"},{\\\"match\\\":\\\"<~>\\\",\\\"name\\\":\\\"keyword.control.transition.jssmArrow.forced_forced\\\"},{\\\"match\\\":\\\"~>\\\",\\\"name\\\":\\\"keyword.control.transition.jssmArrow.none_forced\\\"},{\\\"match\\\":\\\"<~\\\",\\\"name\\\":\\\"keyword.control.transition.jssmArrow.forced_none\\\"},{\\\"match\\\":\\\"<-=>\\\",\\\"name\\\":\\\"keyword.control.transition.jssmArrow.legal_main\\\"},{\\\"match\\\":\\\"<=->\\\",\\\"name\\\":\\\"keyword.control.transition.jssmArrow.main_legal\\\"},{\\\"match\\\":\\\"<-~>\\\",\\\"name\\\":\\\"keyword.control.transition.jssmArrow.legal_forced\\\"},{\\\"match\\\":\\\"<~->\\\",\\\"name\\\":\\\"keyword.control.transition.jssmArrow.forced_legal\\\"},{\\\"match\\\":\\\"<=~>\\\",\\\"name\\\":\\\"keyword.control.transition.jssmArrow.main_forced\\\"},{\\\"match\\\":\\\"<~=>\\\",\\\"name\\\":\\\"keyword.control.transition.jssmArrow.forced_main\\\"},{\\\"match\\\":\\\"([0-9]+)%\\\",\\\"name\\\":\\\"constant.numeric.jssmProbability\\\"},{\\\"match\\\":\\\"'[^']*'\\\",\\\"name\\\":\\\"constant.character.jssmAction\\\"},{\\\"match\\\":\\\"\\\\\\\"[^\\\\\\\"]*\\\\\\\"\\\",\\\"name\\\":\\\"entity.name.tag.jssmLabel.doublequoted\\\"},{\\\"match\\\":\\\"([a-zA-Z0-9_.+\\\\\\\\&()#@!?,])\\\",\\\"name\\\":\\\"entity.name.tag.jssmLabel.atom\\\"}],\\\"scopeName\\\":\\\"source.jssm\\\",\\\"aliases\\\":[\\\"fsl\\\"]}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/jssm.mjs\n"));

/***/ })

}]);