"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_verilog_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/verilog.mjs":
/*!******************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/verilog.mjs ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Verilog\\\",\\\"fileTypes\\\":[\\\"v\\\",\\\"vh\\\"],\\\"name\\\":\\\"verilog\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#module_pattern\\\"},{\\\"include\\\":\\\"#keywords\\\"},{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#strings\\\"},{\\\"include\\\":\\\"#operators\\\"}],\\\"repository\\\":{\\\"comments\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(^[ \\\\\\\\t]+)?(?=//)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.comment.leading.verilog\\\"}},\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"//\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.verilog\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.line.double-slash.verilog\\\"}]},{\\\"begin\\\":\\\"/\\\\\\\\*\\\",\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"name\\\":\\\"comment.block.c-style.verilog\\\"}]},\\\"constants\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"`(?!(celldefine|endcelldefine|default_nettype|define|undef|ifdef|ifndef|else|endif|include|resetall|timescale|unconnected_drive|nounconnected_drive))[a-z_A-Z][a-zA-Z0-9_$]*\\\",\\\"name\\\":\\\"variable.other.constant.verilog\\\"},{\\\"match\\\":\\\"[0-9]*'[bBoOdDhH][_xXzZ\\\\\\\\h]+\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.sized_integer.verilog\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.numeric.integer.verilog\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.range.verilog\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.numeric.integer.verilog\\\"}},\\\"match\\\":\\\"\\\\\\\\b(\\\\\\\\d+)(:)(\\\\\\\\d+)\\\\\\\\b\\\",\\\"name\\\":\\\"meta.block.numeric.range.verilog\\\"},{\\\"match\\\":\\\"\\\\\\\\b\\\\\\\\d[\\\\\\\\d_]*(?i:e\\\\\\\\d+)?\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.integer.verilog\\\"},{\\\"match\\\":\\\"\\\\\\\\b\\\\\\\\d+\\\\\\\\.\\\\\\\\d+(?i:e\\\\\\\\d+)?\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.real.verilog\\\"},{\\\"match\\\":\\\"#\\\\\\\\d+\\\",\\\"name\\\":\\\"constant.numeric.delay.verilog\\\"},{\\\"match\\\":\\\"\\\\\\\\b[01xXzZ]+\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.logic.verilog\\\"}]},\\\"instantiation_patterns\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#keywords\\\"},{\\\"begin\\\":\\\"^\\\\\\\\s*(?!always|and|assign|output|input|inout|wire|module)([a-zA-Z][a-zA-Z0-9_]*)\\\\\\\\s+([a-zA-Z][a-zA-Z0-9_]*)(?<!begin|if)\\\\\\\\s*(?=\\\\\\\\(|$)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.tag.module.reference.verilog\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.tag.module.identifier.verilog\\\"}},\\\"end\\\":\\\";\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.terminator.expression.verilog\\\"}},\\\"name\\\":\\\"meta.block.instantiation.parameterless.verilog\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#strings\\\"}]},{\\\"begin\\\":\\\"^\\\\\\\\s*([a-zA-Z][a-zA-Z0-9_]*)\\\\\\\\s*(#)(?=\\\\\\\\s*\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.tag.module.reference.verilog\\\"}},\\\"end\\\":\\\";\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.terminator.expression.verilog\\\"}},\\\"name\\\":\\\"meta.block.instantiation.with.parameters.verilog\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#parenthetical_list\\\"},{\\\"match\\\":\\\"[a-zA-Z][a-zA-Z0-9_]*\\\",\\\"name\\\":\\\"entity.name.tag.module.identifier.verilog\\\"}]}]},\\\"keywords\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(always|and|assign|attribute|begin|buf|bufif0|bufif1|case[xz]?|cmos|deassign|default|defparam|disable|edge|else|end(attribute|case|function|generate|module|primitive|specify|table|task)?|event|for|force|forever|fork|function|generate|genvar|highz(01)|if(none)?|initial|inout|input|integer|join|localparam|medium|module|large|macromodule|nand|negedge|nmos|nor|not|notif(01)|or|output|parameter|pmos|posedge|primitive|pull0|pull1|pulldown|pullup|rcmos|real|realtime|reg|release|repeat|rnmos|rpmos|rtran|rtranif(01)|scalared|signed|small|specify|specparam|strength|strong0|strong1|supply0|supply1|table|task|time|tran|tranif(01)|tri(01)?|tri(and|or|reg)|unsigned|vectored|wait|wand|weak(01)|while|wire|wor|xnor|xor)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.verilog\\\"},{\\\"match\\\":\\\"^\\\\\\\\s*`((cell)?define|default_(decay_time|nettype|trireg_strength)|delay_mode_(path|unit|zero)|ifdef|ifndef|include|end(if|celldefine)|else|(no)?unconnected_drive|resetall|timescale|undef)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.compiler.directive.verilog\\\"},{\\\"match\\\":\\\"\\\\\\\\$(f(open|close)|readmem([bh])|timeformat|printtimescale|stop|finish|(s|real)?time|realtobits|bitstoreal|rtoi|itor|(f)?(display|write([hb])))\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.system.console.tasks.verilog\\\"},{\\\"match\\\":\\\"\\\\\\\\$(random|dist_(chi_square|erlang|exponential|normal|poisson|t|uniform))\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.system.random_number.tasks.verilog\\\"},{\\\"match\\\":\\\"\\\\\\\\$((a)?sync\\\\\\\\$((n)?and|(n)or)\\\\\\\\$(array|plane))\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.system.pld_modeling.tasks.verilog\\\"},{\\\"match\\\":\\\"\\\\\\\\$(q_(initialize|add|remove|full|exam))\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.system.stochastic.tasks.verilog\\\"},{\\\"match\\\":\\\"\\\\\\\\$(hold|nochange|period|recovery|setup(hold)?|skew|width)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.system.timing.tasks.verilog\\\"},{\\\"match\\\":\\\"\\\\\\\\$(dump(file|vars|off|on|all|limit|flush))\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.system.vcd.tasks.verilog\\\"},{\\\"match\\\":\\\"\\\\\\\\$(countdrivers|list|input|scope|showscopes|(no)?(key|log)|reset(_(?:count|value))?|(inc)?save|restart|showvars|getpattern|sreadmem([bh])|scale)\\\",\\\"name\\\":\\\"support.function.non-standard.tasks.verilog\\\"}]},\\\"module_pattern\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\b(module)\\\\\\\\s+([a-zA-Z][a-zA-Z0-9_]*)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.module.verilog\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.type.module.verilog\\\"}},\\\"end\\\":\\\"\\\\\\\\bendmodule\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"storage.type.module.verilog\\\"}},\\\"name\\\":\\\"meta.block.module.verilog\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#keywords\\\"},{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#strings\\\"},{\\\"include\\\":\\\"#instantiation_patterns\\\"},{\\\"include\\\":\\\"#operators\\\"}]}]},\\\"operators\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"[+\\\\\\\\-*/%]|([<>])=?|([!=])?==?|!|&&?|\\\\\\\\|\\\\\\\\|?|\\\\\\\\^?~|~\\\\\\\\^?\\\",\\\"name\\\":\\\"keyword.operator.verilog\\\"}]},\\\"parenthetical_list\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.list.verilog\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.list.verilog\\\"}},\\\"name\\\":\\\"meta.block.parenthetical_list.verilog\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#parenthetical_list\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#keywords\\\"},{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#strings\\\"}]}]},\\\"strings\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"end\\\":\\\"\\\\\\\"\\\",\\\"name\\\":\\\"string.quoted.double.verilog\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"constant.character.escape.verilog\\\"}]}]}},\\\"scopeName\\\":\\\"source.verilog\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/verilog.mjs\n"));

/***/ })

}]);