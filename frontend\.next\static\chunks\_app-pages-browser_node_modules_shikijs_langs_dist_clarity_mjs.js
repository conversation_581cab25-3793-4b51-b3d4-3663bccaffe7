"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_clarity_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/clarity.mjs":
/*!******************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/clarity.mjs ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Clarity\\\",\\\"name\\\":\\\"clarity\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"},{\\\"include\\\":\\\"#define-constant\\\"},{\\\"include\\\":\\\"#define-data-var\\\"},{\\\"include\\\":\\\"#define-map\\\"},{\\\"include\\\":\\\"#define-function\\\"},{\\\"include\\\":\\\"#define-fungible-token\\\"},{\\\"include\\\":\\\"#define-non-fungible-token\\\"},{\\\"include\\\":\\\"#define-trait\\\"},{\\\"include\\\":\\\"#use-trait\\\"}],\\\"repository\\\":{\\\"built-in-func\\\":{\\\"begin\\\":\\\"(\\\\\\\\()\\\\\\\\s*([-+]|<=|>=|[<>*/]|and|append|as-contract|as-max-len\\\\\\\\?|asserts!|at-block|begin|bit-and|bit-not|bit-or|bit-shift-left|bit-shift-right|bit-xor|buff-to-int-be|buff-to-int-le|buff-to-uint-be|buff-to-uint-le|concat|contract-call\\\\\\\\?|contract-of|default-to|element-at|element-at\\\\\\\\?|filter|fold|from-consensus-buff\\\\\\\\?|ft-burn\\\\\\\\?|ft-get-balance|ft-get-supply|ft-mint\\\\\\\\?|ft-transfer\\\\\\\\?|get-block-info\\\\\\\\?|get-burn-block-info\\\\\\\\?|get-stacks-block-info\\\\\\\\?|get-tenure-info\\\\\\\\?|get-burn-block-info\\\\\\\\?|hash160|if|impl-trait|index-of|index-of\\\\\\\\?|int-to-ascii|int-to-utf8|is-eq|is-err|is-none|is-ok|is-some|is-standard|keccak256|len|log2|map|match|merge|mod|nft-burn\\\\\\\\?|nft-get-owner\\\\\\\\?|nft-mint\\\\\\\\?|nft-transfer\\\\\\\\?|not|or|pow|principal-construct\\\\\\\\?|principal-destruct\\\\\\\\?|principal-of\\\\\\\\?|print|replace-at\\\\\\\\?|secp256k1-recover\\\\\\\\?|secp256k1-verify|sha256|sha512|sha512/256|slice\\\\\\\\?|sqrti|string-to-int\\\\\\\\?|string-to-uint\\\\\\\\?|stx-account|stx-burn\\\\\\\\?|stx-get-balance|stx-transfer-memo\\\\\\\\?|stx-transfer\\\\\\\\?|to-consensus-buff\\\\\\\\?|to-int|to-uint|try!|unwrap!|unwrap-err!|unwrap-err-panic|unwrap-panic|xor)\\\\\\\\s+\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.built-in-function.start.clarity\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.declaration.built-in-function.clarity\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.built-in-function.end.clarity\\\"}},\\\"name\\\":\\\"meta.built-in-function\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"},{\\\"include\\\":\\\"#user-func\\\"}]},\\\"comment\\\":{\\\"match\\\":\\\"(?<=^|[()\\\\\\\\[\\\\\\\\]{}\\\\\\\",'`;\\\\\\\\s])(;).*$\\\",\\\"name\\\":\\\"comment.line.semicolon.clarity\\\"},\\\"data-type\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"match\\\":\\\"\\\\\\\\b(uint|int)\\\\\\\\b\\\",\\\"name\\\":\\\"entity.name.type.numeric.clarity\\\"},{\\\"match\\\":\\\"\\\\\\\\b(principal)\\\\\\\\b\\\",\\\"name\\\":\\\"entity.name.type.principal.clarity\\\"},{\\\"match\\\":\\\"\\\\\\\\b(bool)\\\\\\\\b\\\",\\\"name\\\":\\\"entity.name.type.bool.clarity\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.string_type-def.start.clarity\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.type.string_type.clarity\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.numeric.string_type-len.clarity\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.string_type-def.end.clarity\\\"}},\\\"match\\\":\\\"(\\\\\\\\()\\\\\\\\s*(string-(?:ascii|utf8))\\\\\\\\s+(\\\\\\\\d+)\\\\\\\\s*(\\\\\\\\))\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.buff-def.start.clarity\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.type.buff.clarity\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.numeric.buf-len.clarity\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.buff-def.end.clarity\\\"}},\\\"match\\\":\\\"(\\\\\\\\()\\\\\\\\s*(buff)\\\\\\\\s+(\\\\\\\\d+)\\\\\\\\s*(\\\\\\\\))\\\"},{\\\"begin\\\":\\\"(\\\\\\\\()\\\\\\\\s*(optional)\\\\\\\\s+\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.optional-def.start.clarity\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.type.modifier\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.optional-def.end.clarity\\\"}},\\\"name\\\":\\\"meta.optional-def\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#data-type\\\"}]},{\\\"begin\\\":\\\"(\\\\\\\\()\\\\\\\\s*(response)\\\\\\\\s+\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.response-def.start.clarity\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.type.modifier\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.response-def.end.clarity\\\"}},\\\"name\\\":\\\"meta.response-def\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#data-type\\\"}]},{\\\"begin\\\":\\\"(\\\\\\\\()\\\\\\\\s*(list)\\\\\\\\s+(\\\\\\\\d+)\\\\\\\\s+\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.list-def.start.clarity\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.type.list.clarity\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.numeric.list-len.clarity\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.list-def.end.clarity\\\"}},\\\"name\\\":\\\"meta.list-def\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#data-type\\\"}]},{\\\"begin\\\":\\\"(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.tuple-def.start.clarity\\\"}},\\\"end\\\":\\\"(})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.tuple-def.end.clarity\\\"}},\\\"name\\\":\\\"meta.tuple-def\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"([a-zA-Z][\\\\\\\\w?!-]*)(?=:)\\\",\\\"name\\\":\\\"entity.name.tag.tuple-data-type-key.clarity\\\"},{\\\"include\\\":\\\"#data-type\\\"}]}]},\\\"define-constant\\\":{\\\"begin\\\":\\\"(\\\\\\\\()\\\\\\\\s*(define-constant)\\\\\\\\s+([a-zA-Z][\\\\\\\\w?!-]*)\\\\\\\\s+\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.define-constant.start.clarity\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.declaration.define-constant.clarity\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.constant-name.clarity variable.other.clarity\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.define-constant.end.clarity\\\"}},\\\"name\\\":\\\"meta.define-constant\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},\\\"define-data-var\\\":{\\\"begin\\\":\\\"(\\\\\\\\()\\\\\\\\s*(define-data-var)\\\\\\\\s+([a-zA-Z][\\\\\\\\w?!-]*)\\\\\\\\s+\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.define-data-var.start.clarity\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.declaration.define-data-var.clarity\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.data-var-name.clarity variable.other.clarity\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.define-data-var.end.clarity\\\"}},\\\"name\\\":\\\"meta.define-data-var\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#data-type\\\"},{\\\"include\\\":\\\"#expression\\\"}]},\\\"define-function\\\":{\\\"begin\\\":\\\"(\\\\\\\\()\\\\\\\\s*(define-(?:public|private|read-only))\\\\\\\\s+\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.define-function.start.clarity\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.declaration.define-function.clarity\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.define-function.end.clarity\\\"}},\\\"name\\\":\\\"meta.define-function\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"},{\\\"begin\\\":\\\"(\\\\\\\\()\\\\\\\\s*([a-zA-Z][\\\\\\\\w?!-]*)\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.function-signature.start.clarity\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.clarity\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.function-signature.end.clarity\\\"}},\\\"name\\\":\\\"meta.define-function-signature\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(\\\\\\\\()\\\\\\\\s*([a-zA-Z][\\\\\\\\w?!-]*)\\\\\\\\s+\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.function-argument.start.clarity\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.parameter.clarity\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.function-argument.end.clarity\\\"}},\\\"name\\\":\\\"meta.function-argument\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#data-type\\\"}]}]},{\\\"include\\\":\\\"#user-func\\\"}]},\\\"define-fungible-token\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.define-fungible-token.start.clarity\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.declaration.define-fungible-token.clarity\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.fungible-token-name.clarity variable.other.clarity\\\"},\\\"4\\\":{\\\"name\\\":\\\"constant.numeric.fungible-token-total-supply.clarity\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.define-fungible-token.end.clarity\\\"}},\\\"match\\\":\\\"(\\\\\\\\()\\\\\\\\s*(define-fungible-token)\\\\\\\\s+([a-zA-Z][\\\\\\\\w?!-]*)(?:\\\\\\\\s+(u\\\\\\\\d+))?\\\"},\\\"define-map\\\":{\\\"begin\\\":\\\"(\\\\\\\\()\\\\\\\\s*(define-map)\\\\\\\\s+([a-zA-Z][\\\\\\\\w?!-]*)\\\\\\\\s+\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.define-map.start.clarity\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.declaration.define-map.clarity\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.map-name.clarity variable.other.clarity\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.define-map.end.clarity\\\"}},\\\"name\\\":\\\"meta.define-map\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#data-type\\\"},{\\\"include\\\":\\\"#expression\\\"}]},\\\"define-non-fungible-token\\\":{\\\"begin\\\":\\\"(\\\\\\\\()\\\\\\\\s*(define-non-fungible-token)\\\\\\\\s+([a-zA-Z][\\\\\\\\w?!-]*)\\\\\\\\s+\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.define-non-fungible-token.start.clarity\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.declaration.define-non-fungible-token.clarity\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.non-fungible-token-name.clarity variable.other.clarity\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.define-non-fungible-token.end.clarity\\\"}},\\\"name\\\":\\\"meta.define-non-fungible-token\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#data-type\\\"}]},\\\"define-trait\\\":{\\\"begin\\\":\\\"(\\\\\\\\()\\\\\\\\s*(define-trait)\\\\\\\\s+([a-zA-Z][\\\\\\\\w?!-]*)\\\\\\\\s+\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.define-trait.start.clarity\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.declaration.define-trait.clarity\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.trait-name.clarity variable.other.clarity\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.define-trait.end.clarity\\\"}},\\\"name\\\":\\\"meta.define-trait\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(\\\\\\\\()\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.define-trait-body.start.clarity\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.define-trait-body.end.clarity\\\"}},\\\"name\\\":\\\"meta.define-trait-body\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"},{\\\"begin\\\":\\\"(\\\\\\\\()\\\\\\\\s*([a-zA-Z][\\\\\\\\w!?-]*)\\\\\\\\s+\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.trait-function.start.clarity\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.clarity\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.trait-function.end.clarity\\\"}},\\\"name\\\":\\\"meta.trait-function\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#data-type\\\"},{\\\"begin\\\":\\\"(\\\\\\\\()\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.trait-function-args.start.clarity\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.trait-function-args.end.clarity\\\"}},\\\"name\\\":\\\"meta.trait-function-args\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#data-type\\\"}]}]}]}]},\\\"expression\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#keyword\\\"},{\\\"include\\\":\\\"#literal\\\"},{\\\"include\\\":\\\"#let-func\\\"},{\\\"include\\\":\\\"#built-in-func\\\"},{\\\"include\\\":\\\"#get-set-func\\\"}]},\\\"get-set-func\\\":{\\\"begin\\\":\\\"(\\\\\\\\()\\\\\\\\s*(var-get|var-set|map-get\\\\\\\\?|map-set|map-insert|map-delete|get)\\\\\\\\s+([a-zA-Z][\\\\\\\\w?!-]*)\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.get-set-func.start.clarity\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.clarity\\\"},\\\"3\\\":{\\\"name\\\":\\\"variable.other.clarity\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.get-set-func.end.clarity\\\"}},\\\"name\\\":\\\"meta.get-set-func\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},\\\"keyword\\\":{\\\"match\\\":\\\"(?<!\\\\\\\\S)(?!-)\\\\\\\\b(?:block-height|burn-block-height|chain-id|contract-caller|is-in-regtest|stacks-block-height|stx-liquid-supply|tenure-height|tx-sender|tx-sponsor?)\\\\\\\\b(?!\\\\\\\\s*-)\\\",\\\"name\\\":\\\"constant.language.clarity\\\"},\\\"let-func\\\":{\\\"begin\\\":\\\"(\\\\\\\\()\\\\\\\\s*(let)\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.let-function.start.clarity\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.declaration.let-function.clarity\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.let-function.end.clarity\\\"}},\\\"name\\\":\\\"meta.let-function\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"},{\\\"include\\\":\\\"#user-func\\\"},{\\\"begin\\\":\\\"(\\\\\\\\()\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.let-var.start.clarity\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.let-var.end.clarity\\\"}},\\\"name\\\":\\\"meta.let-var\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(\\\\\\\\()([a-zA-Z][\\\\\\\\w?!-]*)\\\\\\\\s+\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.let-local-var.start.clarity\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.let-local-var-name.clarity variable.parameter.clarity\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.let-local-var.end.clarity\\\"}},\\\"name\\\":\\\"meta.let-local-var\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"},{\\\"include\\\":\\\"#user-func\\\"}]},{\\\"include\\\":\\\"#expression\\\"}]}]},\\\"literal\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#number-literal\\\"},{\\\"include\\\":\\\"#bool-literal\\\"},{\\\"include\\\":\\\"#string-literal\\\"},{\\\"include\\\":\\\"#tuple-literal\\\"},{\\\"include\\\":\\\"#principal-literal\\\"},{\\\"include\\\":\\\"#list-literal\\\"},{\\\"include\\\":\\\"#optional-literal\\\"},{\\\"include\\\":\\\"#response-literal\\\"}],\\\"repository\\\":{\\\"bool-literal\\\":{\\\"match\\\":\\\"(?<!\\\\\\\\S)(?!-)\\\\\\\\b(true|false)\\\\\\\\b(?!\\\\\\\\s*-)\\\",\\\"name\\\":\\\"constant.language.bool.clarity\\\"},\\\"list-literal\\\":{\\\"begin\\\":\\\"(\\\\\\\\()\\\\\\\\s*(list)\\\\\\\\s+\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.list.start.clarity\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.type.list.clarity\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"names\\\":\\\"punctuation.list.end.clarity\\\"}},\\\"name\\\":\\\"meta.list\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"},{\\\"include\\\":\\\"#user-func\\\"}]},\\\"number-literal\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?<!\\\\\\\\S)(?!-)\\\\\\\\bu\\\\\\\\d+\\\\\\\\b(?!\\\\\\\\s*-)\\\",\\\"name\\\":\\\"constant.numeric.uint.clarity\\\"},{\\\"match\\\":\\\"(?<!\\\\\\\\S)(?!-)\\\\\\\\b\\\\\\\\d+\\\\\\\\b(?!\\\\\\\\s*-)\\\",\\\"name\\\":\\\"constant.numeric.int.clarity\\\"},{\\\"match\\\":\\\"(?<!\\\\\\\\S)(?!-)\\\\\\\\b0x[0-9a-f]*\\\\\\\\b(?!\\\\\\\\s*-)\\\",\\\"name\\\":\\\"constant.numeric.hex.clarity\\\"}]},\\\"optional-literal\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?<!\\\\\\\\S)(?!-)\\\\\\\\b(none)\\\\\\\\b(?!\\\\\\\\s*-)\\\",\\\"name\\\":\\\"constant.language.none.clarity\\\"},{\\\"begin\\\":\\\"(\\\\\\\\()\\\\\\\\s*(some)\\\\\\\\s+\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.some.start.clarity\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.language.some.clarity\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.some.end.clarity\\\"}},\\\"name\\\":\\\"meta.some\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]}]},\\\"principal-literal\\\":{\\\"match\\\":\\\"'[0-9A-Z]{28,41}(:?\\\\\\\\.[a-zA-Z][a-zA-Z0-9-]+){0,2}|(\\\\\\\\.[a-zA-Z][a-zA-Z0-9-]*){1,2}(?=[\\\\\\\\s(){},]|$)\\\",\\\"name\\\":\\\"constant.other.principal.clarity\\\"},\\\"response-literal\\\":{\\\"begin\\\":\\\"(\\\\\\\\()\\\\\\\\s*(ok|err)\\\\\\\\s+\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.response.start.clarity\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.language.ok-err.clarity\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.response.end.clarity\\\"}},\\\"name\\\":\\\"meta.response\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"},{\\\"include\\\":\\\"#user-func\\\"}]},\\\"string-literal\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(u?)(\\\\\\\")\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"string.quoted.utf8.clarity\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.clarity\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.clarity\\\"}},\\\"name\\\":\\\"string.quoted.double.clarity\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"constant.character.escape.quote\\\"}]}]},\\\"tuple-literal\\\":{\\\"begin\\\":\\\"(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.tuple.start.clarity\\\"}},\\\"end\\\":\\\"(})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.tuple.end.clarity\\\"}},\\\"name\\\":\\\"meta.tuple\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"([a-zA-Z][\\\\\\\\w?!-]*)(?=:)\\\",\\\"name\\\":\\\"entity.name.tag.tuple-key.clarity\\\"},{\\\"include\\\":\\\"#expression\\\"},{\\\"include\\\":\\\"#user-func\\\"}]}}},\\\"use-trait\\\":{\\\"begin\\\":\\\"(\\\\\\\\()\\\\\\\\s*(use-trait)\\\\\\\\s+([a-zA-Z][\\\\\\\\w?!-]*)\\\\\\\\s+\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.use-trait.start.clarity\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.declaration.use-trait.clarity\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.trait-alias.clarity variable.other.clarity\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.use-trait.end.clarity\\\"}},\\\"name\\\":\\\"meta.use-trait\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#literal\\\"}]},\\\"user-func\\\":{\\\"begin\\\":\\\"(\\\\\\\\()\\\\\\\\s*(([a-zA-Z][\\\\\\\\w?!-]*))\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.user-function.start.clarity\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.clarity\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.user-function.end.clarity\\\"}},\\\"name\\\":\\\"meta.user-function\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"},{\\\"include\\\":\\\"$self\\\"}]}},\\\"scopeName\\\":\\\"source.clar\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/clarity.mjs\n"));

/***/ })

}]);